<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Lovable.DIY - Local AI Development Tool</title>
    <meta name="description" content="Build apps and websites locally with AI - supporting OpenRouter, Google Gemini, and OpenAI" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
  </head>
  <body>
    <div id="root">
      <div style="min-height: 100vh; background: #1a1a2e; color: white; display: flex; align-items: center; justify-content: center; font-family: system-ui;">
        <div style="text-align: center;">
          <h1 style="font-size: 2rem; margin-bottom: 1rem;">Lovable.DIY</h1>
          <p style="margin-bottom: 1rem;">Loading React application...</p>
          <div style="width: 40px; height: 40px; border: 4px solid #333; border-top: 4px solid #667eea; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto;"></div>
        </div>
      </div>
    </div>
    <style>
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
