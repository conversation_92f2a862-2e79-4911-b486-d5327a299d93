# Development Guide

This guide covers the development setup, architecture, and contribution guidelines for Lovable.DIY.

## Architecture Overview

Lovable.DIY is built with modern web technologies and follows a modular architecture:

### Tech Stack
- **Frontend**: React 18 + TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **State Management**: React Context + Zustand (for complex state)
- **Code Editor**: CodeMirror 6
- **AI Integration**: Vercel AI SDK
- **Testing**: Vitest + React Testing Library
- **UI Components**: Radix UI primitives

### Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ChatInput.tsx
│   ├── ChatPanel.tsx
│   ├── CodeEditor.tsx
│   ├── FileExplorer.tsx
│   ├── Header.tsx
│   ├── MessageList.tsx
│   ├── PreviewPanel.tsx
│   ├── ProviderSelector.tsx
│   ├── SettingsDialog.tsx
│   ├── Terminal.tsx
│   └── ProjectActions.tsx
├── contexts/            # React contexts for global state
│   ├── ChatContext.tsx
│   └── SettingsContext.tsx
├── lib/                 # Utilities and services
│   ├── ai/             # AI provider integration
│   │   ├── chat.ts
│   │   └── providers.ts
│   ├── constants.ts
│   ├── localServer.ts
│   └── project.ts
├── pages/              # Page components
│   ├── HomePage.tsx
│   └── WorkspacePage.tsx
├── types/              # TypeScript type definitions
│   └── index.ts
└── test/               # Test utilities
    └── setup.ts
```

## Key Components

### 1. AI Provider System (`src/lib/ai/`)
- **providers.ts**: Manages multiple AI providers (OpenAI, Google, OpenRouter)
- **chat.ts**: Handles streaming chat, code parsing, and file operations

### 2. Project Management (`src/lib/project.ts`)
- Create, update, and manage project files
- Import/export functionality
- Project statistics and structure generation

### 3. Chat System (`src/contexts/ChatContext.tsx`)
- Message management
- Project state synchronization
- File content updates

### 4. Settings Management (`src/contexts/SettingsContext.tsx`)
- API key storage
- Theme management
- Editor preferences

### 5. Code Editor (`src/components/CodeEditor.tsx`)
- Syntax highlighting for multiple languages
- Auto-completion and error detection
- File saving and modification tracking

### 6. Terminal (`src/components/Terminal.tsx`)
- Local development server simulation
- Command execution
- Project management commands

## Development Setup

### Prerequisites
- Node.js 18+
- npm or pnpm

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd lovable.diy

# Install dependencies
npm install

# Start development server
npm run dev
```

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run test` - Run tests in watch mode
- `npm run test:run` - Run tests once
- `npm run test:ui` - Run tests with UI
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run type-check` - Run TypeScript type checking

## Testing

### Running Tests
```bash
# Run all tests
npm test

# Run tests once
npm run test:run

# Run tests with UI
npm run test:ui

# Run specific test file
npm test -- project.test.ts
```

### Test Structure
- Unit tests for utilities and services
- Component tests for UI components
- Integration tests for complex workflows

### Writing Tests
```typescript
import { describe, it, expect } from 'vitest'
import { createProject } from '../project'

describe('Project Management', () => {
  it('should create a new project', () => {
    const project = createProject('Test Project')
    expect(project.name).toBe('Test Project')
  })
})
```

## Contributing

### Code Style
- Use TypeScript for all new code
- Follow ESLint configuration
- Use Prettier for code formatting
- Write meaningful commit messages

### Pull Request Process
1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes
4. Add tests for new functionality
5. Run tests: `npm test`
6. Run linting: `npm run lint`
7. Commit changes: `git commit -m 'Add amazing feature'`
8. Push to branch: `git push origin feature/amazing-feature`
9. Open a Pull Request

### Coding Guidelines

#### Component Structure
```typescript
interface ComponentProps {
  // Props interface
}

export function Component({ prop1, prop2 }: ComponentProps) {
  // Hooks at the top
  const [state, setState] = useState()
  
  // Event handlers
  const handleClick = () => {
    // Handler logic
  }
  
  // Render
  return (
    <div>
      {/* JSX */}
    </div>
  )
}
```

#### State Management
- Use React Context for global state
- Use useState for local component state
- Consider Zustand for complex state management

#### Error Handling
```typescript
try {
  await riskyOperation()
} catch (error) {
  console.error('Operation failed:', error)
  // Handle error appropriately
}
```

## Adding New AI Providers

To add a new AI provider:

1. **Update constants** (`src/lib/constants.ts`):
```typescript
export const AI_PROVIDERS: AIProvider[] = [
  // ... existing providers
  {
    id: 'new-provider',
    name: 'New Provider',
    description: 'Description of the new provider',
    icon: '🤖',
    requiresApiKey: true,
    apiKeyUrl: 'https://provider.com/api-keys',
    models: [
      // Provider models
    ],
  },
]
```

2. **Update provider service** (`src/lib/ai/providers.ts`):
```typescript
createProvider(providerId: string) {
  // ... existing cases
  case 'new-provider':
    return newProvider({
      apiKey,
    })
}
```

3. **Add provider SDK** to dependencies:
```bash
npm install @provider/ai-sdk
```

## Deployment

### Building for Production
```bash
npm run build
```

### Environment Variables
Create a `.env.local` file for local development:
```
VITE_APP_NAME=Lovable.DIY
VITE_APP_VERSION=1.0.0
```

### Deployment Platforms
- **Vercel**: Connect GitHub repository for automatic deployments
- **Netlify**: Drag and drop `dist` folder or connect repository
- **GitHub Pages**: Use GitHub Actions for automated deployment

## Troubleshooting

### Common Issues

**Build Errors**
- Check TypeScript errors: `npm run type-check`
- Verify all dependencies are installed: `npm install`

**Test Failures**
- Update test snapshots if UI changes: `npm test -- -u`
- Check test setup in `src/test/setup.ts`

**Development Server Issues**
- Clear node_modules and reinstall: `rm -rf node_modules && npm install`
- Check port availability (default: 3000)

### Getting Help
- Check existing issues on GitHub
- Create a new issue with detailed description
- Include error messages and steps to reproduce

## Performance Considerations

### Code Splitting
- Use dynamic imports for large components
- Lazy load AI provider SDKs

### Bundle Size
- Monitor bundle size with `npm run build`
- Use tree shaking for unused code
- Optimize images and assets

### Memory Management
- Clean up event listeners in useEffect
- Avoid memory leaks in streaming operations
- Use React.memo for expensive components

## Security

### API Key Management
- Store API keys securely in localStorage
- Never commit API keys to version control
- Validate API keys before use

### Content Security
- Sanitize user input
- Validate file operations
- Implement proper error boundaries

---

Happy coding! 🚀
