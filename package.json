{"name": "lovable.diy", "version": "1.0.0", "description": "Local AI-powered development tool with multi-provider support", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@ai-sdk/anthropic": "^0.0.39", "@ai-sdk/google": "^0.0.52", "@ai-sdk/openai": "^1.1.2", "@ai-sdk/react": "^1.2.12", "@codemirror/autocomplete": "^6.18.3", "@codemirror/commands": "^6.7.1", "@codemirror/lang-css": "^6.3.1", "@codemirror/lang-html": "^6.4.9", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-markdown": "^6.3.1", "@codemirror/lang-python": "^6.1.6", "@codemirror/language": "^6.10.6", "@codemirror/search": "^6.5.8", "@codemirror/state": "^6.4.1", "@codemirror/view": "^6.35.0", "@openrouter/ai-sdk-provider": "^0.0.5", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.4", "@uiw/codemirror-theme-vscode": "^4.23.6", "ai": "^4.3.16", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "framer-motion": "^11.12.0", "lucide-react": "^0.485.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-resizable-panels": "^2.1.7", "react-router-dom": "^6.28.0", "tailwind-merge": "^2.2.1", "zustand": "^5.0.3"}, "devDependencies": {"@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "postcss": "^8.5.0", "tailwindcss": "^3.4.15", "typescript": "^5.7.2", "vite": "^5.4.11"}}