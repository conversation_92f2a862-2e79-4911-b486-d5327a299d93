<svg xmlns="http://www.w3.org/2000/svg" width="95" height="83" fill="none"><g filter="url(#a)"><path fill="url(#b)" d="M66.657 0H28.343a7.948 7.948 0 0 0-6.887 3.979L2.288 37.235a7.948 7.948 0 0 0 0 7.938L21.456 78.43a7.948 7.948 0 0 0 6.887 3.979h38.314a7.948 7.948 0 0 0 6.886-3.98l19.17-33.256a7.948 7.948 0 0 0 0-7.938L73.542 3.98A7.948 7.948 0 0 0 66.657 0Z"/></g><g filter="url(#c)"><path fill="#fff" fill-rule="evenodd" d="M50.642 59.608c-3.468 0-6.873-1.261-8.827-3.973l-.69 3.198-12.729 6.762 1.374-6.762 9.27-42.04h11.35l-3.279 14.818c2.649-2.9 5.108-3.973 8.26-3.973 6.81 0 11.35 4.477 11.35 12.675 0 8.45-5.233 19.295-16.079 19.295Zm4.351-16.9c0 3.91-2.774 6.874-6.368 6.874-2.018 0-3.847-.757-5.045-2.08l1.766-7.757c1.324-1.324 2.837-2.08 4.603-2.08 2.711 0 5.044 2.017 5.044 5.044Z" clip-rule="evenodd"/></g><defs><filter id="a" width="92.549" height="82.409" x="1.226" y="0" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset/><feGaussianBlur stdDeviation="1.717"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.65 0"/><feBlend in2="shape" result="effect1_innerShadow_615_13380"/></filter><filter id="c" width="38.326" height="48.802" x="28.396" y="16.793" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset/><feGaussianBlur stdDeviation=".072"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.95 0"/><feBlend in2="shape" result="effect1_innerShadow_615_13380"/></filter>
<linearGradient id="b" x1="47.5" x2="47.5" y1="0" y2="82.409" gradientUnits="userSpaceOnUse">
    <stop offset="0%" stop-color="#F8F5FF" />
    <stop offset="10%" stop-color="#F0EBFF" />
    <stop offset="20%" stop-color="#E1D6FF" />
    <stop offset="30%" stop-color="#CEBEFF" />
    <stop offset="40%" stop-color="#B69EFF" />
    <stop offset="50%" stop-color="#9C7DFF" />
    <stop offset="60%" stop-color="#8A5FFF" />
    <stop offset="70%" stop-color="#7645E8" />
    <stop offset="80%" stop-color="#6234BB" />
    <stop offset="90%" stop-color="#502D93" />
    <stop offset="100%" stop-color="#2D1959" />
</linearGradient>
</defs></svg>
