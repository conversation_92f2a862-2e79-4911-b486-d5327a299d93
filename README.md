# Lovable.DIY

A local AI-powered development tool that replicates Lovable's functionality with support for multiple AI providers including OpenRouter, Google Gemini, and OpenAI.

![Lovable.DIY Screenshot](https://via.placeholder.com/800x400/667eea/ffffff?text=Lovable.DIY)

## Features

- 🤖 **Multi-AI Provider Support**: Choose from OpenAI, Google Gemini, or OpenRouter
- 💻 **Local Development**: Everything runs locally for maximum privacy
- 🎨 **Beautiful UI**: Clean, modern interface inspired by the original Lovable
- 📝 **Code Editor**: Full-featured editor with syntax highlighting and auto-completion
- 🔄 **Real-time Preview**: See your applications come to life instantly
- 📁 **Project Management**: Organize and manage your generated projects
- 🌙 **Dark Theme**: Easy on the eyes for long coding sessions

## Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (version 18 or higher)
- **npm** or **pnpm** (package manager)

### Installing Node.js

#### On Ubuntu/Debian:
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

#### On macOS:
```bash
# Using Homebrew
brew install node

# Or download from https://nodejs.org/
```

#### On Windows:
Download and install from [nodejs.org](https://nodejs.org/)

## Installation

1. **Clone or download this repository**:
   ```bash
   git clone <repository-url>
   cd lovable.diy
   ```

2. **Install dependencies**:
   ```bash
   npm install
   # or
   pnpm install
   ```

3. **Start the development server**:
   ```bash
   npm run dev
   # or
   pnpm dev
   ```

4. **Open your browser** and navigate to `http://localhost:3000`

## Configuration

### Setting up API Keys

1. Open the application in your browser
2. Click the **Settings** button in the header
3. Navigate to the **AI Providers** tab
4. Enter your API keys for the providers you want to use:

#### OpenAI
- Get your API key from: https://platform.openai.com/api-keys
- Supports: GPT-4o, GPT-4o Mini, GPT-3.5 Turbo

#### Google Gemini
- Get your API key from: https://aistudio.google.com/app/apikey
- Supports: Gemini 1.5 Pro, Gemini 1.5 Flash

#### OpenRouter
- Get your API key from: https://openrouter.ai/keys
- Supports: Claude 3.5 Sonnet, GPT-4o, Gemini Pro 1.5, and many more

### Editor Settings

Customize your coding experience:
- **Font Size**: Adjust the editor font size (10-24px)
- **Tab Size**: Choose between 2, 4, or 8 spaces
- **Word Wrap**: Enable/disable word wrapping
- **Minimap**: Show/hide the code minimap

## Usage

### Creating Your First Project

1. **Start a conversation**: Type your project idea in the chat input
   ```
   "Create a landing page for a coffee shop with a modern design"
   ```

2. **Watch the magic happen**: The AI will generate complete HTML, CSS, and JavaScript files

3. **Explore your project**: 
   - View files in the **File Explorer**
   - Edit code in the **Code Editor**
   - See live preview in the **Preview Panel**

4. **Iterate and improve**: Continue chatting to modify and enhance your project

### Example Prompts

- "Build a React todo app with local storage"
- "Create a portfolio website with dark mode toggle"
- "Make a simple calculator with a clean UI"
- "Build a weather app that shows current conditions"

## Project Structure

```
lovable.diy/
├── src/
│   ├── components/          # React components
│   ├── contexts/           # React contexts
│   ├── lib/               # Utilities and services
│   ├── pages/             # Page components
│   ├── types/             # TypeScript types
│   └── main.tsx           # Application entry point
├── public/                # Static assets
├── package.json           # Dependencies and scripts
└── README.md             # This file
```

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking

### Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes
4. Run tests and linting: `npm run lint`
5. Commit your changes: `git commit -m 'Add feature'`
6. Push to the branch: `git push origin feature-name`
7. Submit a pull request

## Troubleshooting

### Common Issues

**API Key Not Working**
- Ensure your API key is correctly entered
- Check that you have sufficient credits/quota
- Use the "Test" button to verify connectivity

**Preview Not Loading**
- Make sure your project has an `index.html` file
- Check the browser console for errors
- Try refreshing the preview

**Editor Not Responding**
- Clear your browser cache
- Restart the development server
- Check for JavaScript errors in the console

### Getting Help

If you encounter issues:
1. Check the browser console for error messages
2. Verify your API keys are valid
3. Ensure you have a stable internet connection
4. Try with a different AI provider

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Inspired by the original Lovable platform
- Built with React, TypeScript, and Vite
- Uses CodeMirror for the code editor
- AI integration powered by Vercel AI SDK

---

**Happy coding with Lovable.DIY!** 🚀
