import React, { createContext, useContext, useState } from 'react'
import { ChatMessage, ChatState, Project, FileItem } from '@/types'

interface ChatContextType {
  chatState: ChatState
  addMessage: (message: Omit<ChatMessage, 'id' | 'timestamp'>) => void
  setLoading: (loading: boolean) => void
  setCurrentProject: (project: Project | undefined) => void
  setSelectedFile: (file: FileItem | undefined) => void
  clearMessages: () => void
  updateFileContent: (filePath: string, content: string) => void
}

const ChatContext = createContext<ChatContextType | undefined>(undefined)

export function useChat() {
  const context = useContext(ChatContext)
  if (!context) {
    throw new Error('useChat must be used within a ChatProvider')
  }
  return context
}

interface ChatProviderProps {
  children: React.ReactNode
}

export function ChatProvider({ children }: ChatProviderProps) {
  const [chatState, setChatState] = useState<ChatState>({
    messages: [],
    isLoading: false,
    currentProject: undefined,
    selectedFile: undefined,
  })

  const addMessage = (message: Omit<ChatMessage, 'id' | 'timestamp'>) => {
    const newMessage: ChatMessage = {
      ...message,
      id: crypto.randomUUID(),
      timestamp: new Date(),
    }
    
    setChatState(prev => ({
      ...prev,
      messages: [...prev.messages, newMessage],
    }))
  }

  const setLoading = (loading: boolean) => {
    setChatState(prev => ({
      ...prev,
      isLoading: loading,
    }))
  }

  const setCurrentProject = (project: Project | undefined) => {
    setChatState(prev => ({
      ...prev,
      currentProject: project,
    }))
  }

  const setSelectedFile = (file: FileItem | undefined) => {
    setChatState(prev => ({
      ...prev,
      selectedFile: file,
    }))
  }

  const clearMessages = () => {
    setChatState(prev => ({
      ...prev,
      messages: [],
    }))
  }

  const updateFileContent = (filePath: string, content: string) => {
    setChatState(prev => {
      if (!prev.currentProject) return prev

      const updateFileInTree = (files: FileItem[]): FileItem[] => {
        return files.map(file => {
          if (file.path === filePath) {
            return { ...file, content, isModified: true }
          }
          if (file.children) {
            return { ...file, children: updateFileInTree(file.children) }
          }
          return file
        })
      }

      const updatedProject = {
        ...prev.currentProject,
        files: updateFileInTree(prev.currentProject.files),
        updatedAt: new Date(),
      }

      return {
        ...prev,
        currentProject: updatedProject,
      }
    })
  }

  const value: ChatContextType = {
    chatState,
    addMessage,
    setLoading,
    setCurrentProject,
    setSelectedFile,
    clearMessages,
    updateFileContent,
  }

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  )
}
