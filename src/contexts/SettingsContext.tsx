import React, { createContext, useContext, useEffect, useState } from 'react'
import { Settings } from '@/types'
import { DEFAULT_SETTINGS } from '@/lib/constants'

interface SettingsContextType {
  settings: Settings
  updateSettings: (updates: Partial<Settings>) => void
  updateApiKey: (provider: string, apiKey: string) => void
  getApiKey: (provider: string) => string | undefined
}

const SettingsContext = createContext<SettingsContextType | undefined>(undefined)

export function useSettings() {
  const context = useContext(SettingsContext)
  if (!context) {
    throw new Error('useSettings must be used within a SettingsProvider')
  }
  return context
}

interface SettingsProviderProps {
  children: React.ReactNode
}

export function SettingsProvider({ children }: SettingsProviderProps) {
  const [settings, setSettings] = useState<Settings>(DEFAULT_SETTINGS)

  // Load settings from localStorage on mount
  useEffect(() => {
    const savedSettings = localStorage.getItem('lovable-diy-settings')
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings)
        setSettings({ ...DEFAULT_SETTINGS, ...parsed })
      } catch (error) {
        console.error('Failed to parse saved settings:', error)
      }
    }

    // Apply theme on load
    applyTheme(settings.theme)
  }, [])

  // Apply theme whenever it changes
  useEffect(() => {
    applyTheme(settings.theme)
  }, [settings.theme])

  const applyTheme = (theme: 'light' | 'dark' | 'system') => {
    const root = document.documentElement

    if (theme === 'system') {
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      root.classList.toggle('dark', prefersDark)
    } else {
      root.classList.toggle('dark', theme === 'dark')
    }
  }

  // Save settings to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('lovable-diy-settings', JSON.stringify(settings))
  }, [settings])

  const updateSettings = (updates: Partial<Settings>) => {
    setSettings(prev => ({ ...prev, ...updates }))
  }

  const updateApiKey = (provider: string, apiKey: string) => {
    setSettings(prev => ({
      ...prev,
      apiKeys: {
        ...prev.apiKeys,
        [provider]: apiKey,
      },
    }))
  }

  const getApiKey = (provider: string): string | undefined => {
    return settings.apiKeys[provider]
  }

  const value: SettingsContextType = {
    settings,
    updateSettings,
    updateApiKey,
    getApiKey,
  }

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  )
}
