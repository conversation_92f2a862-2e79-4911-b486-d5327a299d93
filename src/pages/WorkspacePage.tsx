import { useState } from 'react'
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels'
import { Terminal as TerminalIcon } from 'lucide-react'
import { Header } from '@/components/Header'
import { ChatPanel } from '@/components/ChatPanel'
import { FileExplorer } from '@/components/FileExplorer'
import { CodeEditor } from '@/components/CodeEditor'
import { PreviewPanel } from '@/components/PreviewPanel'
import { Terminal } from '@/components/Terminal'
import { useChat } from '@/contexts/ChatContext'

export function WorkspacePage() {
  const { chatState } = useChat()
  const [activeTab, setActiveTab] = useState<'code' | 'preview'>('code')
  const [isTerminalOpen, setIsTerminalOpen] = useState(false)

  return (
    <div className="h-screen flex flex-col bg-gray-900">
      <Header />

      <div className="flex-1 overflow-hidden">
        <PanelGroup direction="horizontal">
          {/* Chat Panel */}
          <Panel defaultSize={30} minSize={25} maxSize={50}>
            <ChatPanel />
          </Panel>

          <PanelResizeHandle className="w-1 bg-gray-700 hover:bg-gray-600 transition-colors" />

          {/* File Explorer */}
          <Panel defaultSize={20} minSize={15} maxSize={30}>
            <FileExplorer />
          </Panel>

          <PanelResizeHandle className="w-1 bg-gray-700 hover:bg-gray-600 transition-colors" />

          {/* Main Content Area */}
          <Panel defaultSize={50} minSize={30}>
            <div className="h-full flex flex-col">
              {/* Tabs */}
              <div className="flex items-center justify-between border-b border-gray-700 bg-gray-800">
                <div className="flex">
                  <button
                    onClick={() => setActiveTab('code')}
                    className={`px-4 py-2 text-sm font-medium transition-colors ${
                      activeTab === 'code'
                        ? 'text-white border-b-2 border-blue-500'
                        : 'text-gray-400 hover:text-white'
                    }`}
                  >
                    Code
                  </button>
                  <button
                    onClick={() => setActiveTab('preview')}
                    className={`px-4 py-2 text-sm font-medium transition-colors ${
                      activeTab === 'preview'
                        ? 'text-white border-b-2 border-blue-500'
                        : 'text-gray-400 hover:text-white'
                    }`}
                  >
                    Preview
                  </button>
                </div>

                <div className="flex items-center gap-2 px-4">
                  <button
                    onClick={() => setIsTerminalOpen(!isTerminalOpen)}
                    className={`flex items-center gap-1 px-2 py-1 text-sm rounded transition-colors ${
                      isTerminalOpen
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-400 hover:text-white hover:bg-gray-700'
                    }`}
                    title="Toggle terminal"
                  >
                    <TerminalIcon size={14} />
                    Terminal
                  </button>
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 overflow-hidden">
                {activeTab === 'code' ? (
                  <CodeEditor file={chatState.selectedFile} />
                ) : (
                  <PreviewPanel project={chatState.currentProject} />
                )}
              </div>
            </div>
          </Panel>
        </PanelGroup>
      </div>

      {/* Terminal */}
      <Terminal
        isOpen={isTerminalOpen}
        onClose={() => setIsTerminalOpen(false)}
      />
    </div>
  )
}
