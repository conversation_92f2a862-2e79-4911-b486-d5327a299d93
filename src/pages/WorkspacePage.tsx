import { Link } from 'react-router-dom'

export function WorkspacePage() {
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <header className="p-4 border-b border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-6">
            <Link to="/" className="flex items-center gap-2 font-bold text-lg">
              <span className="text-2xl">❤️</span>
              <span>Lovable</span>
              <span className="text-blue-400">.DIY</span>
            </Link>
            <Link
              to="/"
              className="px-3 py-1.5 text-sm text-gray-400 hover:text-white hover:bg-gray-800 rounded transition-colors"
            >
              ← Back to Home
            </Link>
          </div>
          <button className="px-4 py-2 bg-gray-800 hover:bg-gray-700 rounded transition-colors">
            Settings
          </button>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex h-[calc(100vh-73px)]">
        {/* Chat Panel */}
        <div className="w-1/3 border-r border-gray-700 bg-gray-800">
          <div className="p-4 border-b border-gray-700">
            <h2 className="font-semibold">Chat</h2>
          </div>
          <div className="flex-1 p-4">
            <div className="text-center text-gray-400 mt-8">
              <div className="text-4xl mb-4">💬</div>
              <p className="text-lg font-medium mb-2">Start a conversation</p>
              <p className="text-sm">Ask me to build something amazing!</p>
            </div>
          </div>
          <div className="p-4 border-t border-gray-700">
            <div className="bg-gray-700 rounded-lg p-3">
              <input
                type="text"
                placeholder="Describe what you want to build..."
                className="w-full bg-transparent text-white placeholder-gray-400 outline-none"
              />
            </div>
          </div>
        </div>

        {/* File Explorer */}
        <div className="w-1/5 border-r border-gray-700 bg-gray-800">
          <div className="p-4 border-b border-gray-700">
            <h2 className="font-semibold">Files</h2>
          </div>
          <div className="flex-1 p-4">
            <div className="text-center text-gray-400 mt-8">
              <div className="text-4xl mb-4">📁</div>
              <p className="text-lg font-medium mb-2">No project loaded</p>
              <p className="text-sm">Start a chat to create a project</p>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col">
          {/* Tabs */}
          <div className="flex border-b border-gray-700 bg-gray-800">
            <button className="px-4 py-2 text-sm font-medium text-white border-b-2 border-blue-500">
              Code
            </button>
            <button className="px-4 py-2 text-sm font-medium text-gray-400 hover:text-white">
              Preview
            </button>
          </div>

          {/* Content */}
          <div className="flex-1 bg-gray-900">
            <div className="h-full flex items-center justify-center">
              <div className="text-center text-gray-400">
                <div className="text-4xl mb-4">📝</div>
                <p className="text-lg font-medium mb-2">No file selected</p>
                <p className="text-sm">Select a file from the explorer to start editing</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
