import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Header } from '@/components/Header'
import { ChatInput } from '@/components/ChatInput'
import { ProviderSelector } from '@/components/ProviderSelector'
import { useSettings } from '@/contexts/SettingsContext'
import { useChat } from '@/contexts/ChatContext'

export function HomePage() {
  const navigate = useNavigate()
  const { settings } = useSettings()
  const { addMessage, setLoading } = useChat()
  const [input, setInput] = useState('')

  const handleSubmit = async (message: string) => {
    if (!message.trim()) return

    // Add user message
    addMessage({
      role: 'user',
      content: message,
    })

    setLoading(true)

    // Navigate to workspace
    navigate('/workspace')

    // The actual AI processing will happen in the workspace
    setTimeout(() => {
      setLoading(false)
    }, 500)
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      {/* Main Content */}
      <main className="flex-1 flex flex-col items-center justify-center px-4">
        {/* Background Gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-purple-600 to-purple-800 opacity-90" />
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-black/20 to-black/40" />

        {/* Content */}
        <div className="relative z-10 w-full max-w-4xl mx-auto text-center text-white">
          <div className="mb-12">
            <h1 className="text-6xl font-bold mb-6">
              Build something{' '}
              <span className="bg-gradient-to-r from-blue-300 to-purple-300 bg-clip-text text-transparent">
                Lovable
              </span>
            </h1>
            <p className="text-xl text-blue-100 max-w-2xl mx-auto">
              Create apps and websites by chatting with AI
            </p>
          </div>

          {/* Provider Selector */}
          <div className="mb-8">
            <ProviderSelector />
          </div>

          {/* Chat Input */}
          <div className="w-full max-w-2xl mx-auto mb-16">
            <ChatInput
              value={input}
              onChange={setInput}
              onSubmit={handleSubmit}
              placeholder="Ask Lovable to create a landing page for..."
              disabled={false}
            />
          </div>

          {/* Features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-left">
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-6">
              <div className="text-2xl mb-3">🚀</div>
              <h3 className="font-semibold mb-2">Instant Development</h3>
              <p className="text-sm text-blue-100">
                Generate complete applications with just a description
              </p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-6">
              <div className="text-2xl mb-3">🔧</div>
              <h3 className="font-semibold mb-2">Local & Private</h3>
              <p className="text-sm text-blue-100">
                Everything runs locally on your machine for maximum privacy
              </p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-6">
              <div className="text-2xl mb-3">🤖</div>
              <h3 className="font-semibold mb-2">Multi-AI Support</h3>
              <p className="text-sm text-blue-100">
                Choose from OpenAI, Google Gemini, or OpenRouter models
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
