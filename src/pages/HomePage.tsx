import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Header } from '@/components/Header'
import { ChatInput } from '@/components/ChatInput'
import { ProviderSelector } from '@/components/ProviderSelector'
import { useSettings } from '@/contexts/SettingsContext'
import { useChat } from '@/contexts/ChatContext'

export function HomePage() {
  const navigate = useNavigate()
  const { settings } = useSettings()
  const { addMessage, setLoading } = useChat()
  const [input, setInput] = useState('')

  const handleSubmit = async (message: string) => {
    if (!message.trim()) return

    // Add user message
    addMessage({
      role: 'user',
      content: message,
    })

    setLoading(true)
    
    // Navigate to workspace
    navigate('/workspace')
    
    // TODO: Process the message with AI
    setTimeout(() => {
      addMessage({
        role: 'assistant',
        content: 'I understand you want to build something! Let me help you create that application.',
        metadata: {
          provider: settings.selectedProvider,
          model: settings.selectedModel,
        },
      })
      setLoading(false)
    }, 1000)
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      {/* Main Content */}
      <main className="flex-1 flex flex-col items-center justify-center px-4">
        {/* Background Gradient */}
        <div className="absolute inset-0 gradient-bg opacity-80" />
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-background/50 to-background" />
        
        {/* Content */}
        <div className="relative z-10 w-full max-w-4xl mx-auto text-center">
          <div className="mb-12">
            <h1 className="text-6xl font-bold mb-6 bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
              Build something{' '}
              <span className="bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent">
                Lovable
              </span>
            </h1>
            <p className="text-xl text-foreground-secondary max-w-2xl mx-auto">
              Create apps and websites by chatting with AI
            </p>
          </div>

          {/* Provider Selector */}
          <div className="mb-8">
            <ProviderSelector />
          </div>

          {/* Chat Input */}
          <div className="w-full max-w-2xl mx-auto">
            <ChatInput
              value={input}
              onChange={setInput}
              onSubmit={handleSubmit}
              placeholder="Ask Lovable to create a landing page for..."
              disabled={false}
            />
          </div>

          {/* Features */}
          <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 text-left">
            <div className="glass-effect rounded-lg p-6">
              <div className="text-2xl mb-3">🚀</div>
              <h3 className="font-semibold mb-2">Instant Development</h3>
              <p className="text-sm text-foreground-secondary">
                Generate complete applications with just a description
              </p>
            </div>
            <div className="glass-effect rounded-lg p-6">
              <div className="text-2xl mb-3">🔧</div>
              <h3 className="font-semibold mb-2">Local & Private</h3>
              <p className="text-sm text-foreground-secondary">
                Everything runs locally on your machine for maximum privacy
              </p>
            </div>
            <div className="glass-effect rounded-lg p-6">
              <div className="text-2xl mb-3">🤖</div>
              <h3 className="font-semibold mb-2">Multi-AI Support</h3>
              <p className="text-sm text-foreground-secondary">
                Choose from OpenAI, Google Gemini, or OpenRouter models
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
