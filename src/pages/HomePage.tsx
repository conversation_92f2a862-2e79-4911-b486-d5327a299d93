import { useState } from 'react'
import { useNavigate } from 'react-router-dom'

export function HomePage() {
  const navigate = useNavigate()
  const [input, setInput] = useState('')

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (input.trim()) {
      navigate('/workspace')
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-600 via-purple-600 to-purple-800 text-white">
      {/* Header */}
      <header className="p-4 border-b border-white/20">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 font-bold text-lg">
            <span className="text-2xl">❤️</span>
            <span>Lovable</span>
            <span className="text-blue-300">.DIY</span>
          </div>
          <button className="px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg transition-colors">
            Settings
          </button>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 flex flex-col items-center justify-center px-4 py-16">
        <div className="w-full max-w-4xl mx-auto text-center">
          <div className="mb-12">
            <h1 className="text-6xl font-bold mb-6">
              Build something{' '}
              <span className="bg-gradient-to-r from-blue-300 to-purple-300 bg-clip-text text-transparent">
                Lovable
              </span>
            </h1>
            <p className="text-xl text-blue-100 max-w-2xl mx-auto">
              Create apps and websites by chatting with AI
            </p>
          </div>

          {/* Chat Input */}
          <form onSubmit={handleSubmit} className="w-full max-w-2xl mx-auto mb-16">
            <div className="relative bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20 overflow-hidden">
              <div className="flex items-center gap-2 p-4">
                <input
                  type="text"
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder="Ask Lovable to create a landing page for..."
                  className="flex-1 bg-transparent text-white placeholder-blue-200 outline-none"
                />
                <button
                  type="submit"
                  disabled={!input.trim()}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
                >
                  Send
                </button>
              </div>
            </div>
          </form>

          {/* Features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-left">
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-6">
              <div className="text-2xl mb-3">🚀</div>
              <h3 className="font-semibold mb-2">Instant Development</h3>
              <p className="text-sm text-blue-100">
                Generate complete applications with just a description
              </p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-6">
              <div className="text-2xl mb-3">🔧</div>
              <h3 className="font-semibold mb-2">Local & Private</h3>
              <p className="text-sm text-blue-100">
                Everything runs locally on your machine for maximum privacy
              </p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-6">
              <div className="text-2xl mb-3">🤖</div>
              <h3 className="font-semibold mb-2">Multi-AI Support</h3>
              <p className="text-sm text-blue-100">
                Choose from OpenAI, Google Gemini, or OpenRouter models
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
