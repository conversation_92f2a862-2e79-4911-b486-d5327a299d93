import { Routes, Route } from 'react-router-dom'
import { HomePage } from './pages/HomePage'
import { WorkspacePage } from './pages/WorkspacePage'
import { SettingsProvider } from './contexts/SettingsContext'
import { ChatProvider } from './contexts/ChatContext'

function App() {
  return (
    <SettingsProvider>
      <ChatProvider>
        <div className="min-h-screen bg-background">
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/workspace" element={<WorkspacePage />} />
          </Routes>
        </div>
      </ChatProvider>
    </SettingsProvider>
  )
}

export default App
