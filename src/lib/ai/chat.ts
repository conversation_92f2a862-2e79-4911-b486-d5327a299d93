// Temporarily disable AI imports to fix blank screen
// import { streamText } from 'ai'
import { aiProviderService } from './providers'
import { ChatMessage } from '@/types'
import { SYSTEM_PROMPT, FILE_EXTENSIONS } from '@/lib/constants'

export interface ChatOptions {
  provider: string
  model: string
  messages: ChatMessage[]
  onUpdate?: (content: string) => void
  onFinish?: (content: string) => void
  onError?: (error: Error) => void
}

export async function streamChat({
  provider,
  model,
  messages,
  onUpdate,
  onFinish,
  onError,
}: ChatOptions) {
  try {
    // Temporarily simulate AI response to fix blank screen
    const simulatedResponse = `I'll help you create that! Here's a simple example:

\`\`\`html index.html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My App</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>Hello World!</h1>
        <p>This is a sample application created with Lovable.DIY</p>
        <button onclick="showAlert()">Click me!</button>
    </div>
    <script src="script.js"></script>
</body>
</html>
\`\`\`

\`\`\`css style.css
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

button {
    background: #667eea;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
}

button:hover {
    background: #764ba2;
}
\`\`\`

\`\`\`javascript script.js
function showAlert() {
    alert('Hello from Lovable.DIY!');
}
\`\`\`

This creates a simple web application with HTML, CSS, and JavaScript. You can modify the code in the editor to customize it further!`

    // Simulate streaming
    let currentContent = ''
    const words = simulatedResponse.split(' ')

    for (let i = 0; i < words.length; i++) {
      currentContent += (i > 0 ? ' ' : '') + words[i]
      onUpdate?.(currentContent)
      await new Promise(resolve => setTimeout(resolve, 50)) // Simulate typing delay
    }

    onFinish?.(simulatedResponse)
    return simulatedResponse
  } catch (error) {
    console.error('Chat streaming error:', error)
    const errorMessage = error instanceof Error ? error : new Error('Unknown error occurred')
    onError?.(errorMessage)
    throw errorMessage
  }
}

export async function generateText({
  provider,
  model,
  prompt,
  maxTokens = 1000,
}: {
  provider: string
  model: string
  prompt: string
  maxTokens?: number
}) {
  try {
    const aiProvider = aiProviderService.createProvider(provider)
    const aiModel = aiProvider(model)

    const result = await aiModel.generateText({
      prompt,
      maxTokens,
      temperature: 0.7,
    })

    return result.text
  } catch (error) {
    console.error('Text generation error:', error)
    throw error
  }
}

export function parseCodeBlocks(content: string): Array<{
  language: string
  filename?: string
  code: string
}> {
  const codeBlockRegex = /```(\w+)(?:\s+(.+?))?\n([\s\S]*?)```/g
  const blocks: Array<{ language: string; filename?: string; code: string }> = []
  
  let match
  while ((match = codeBlockRegex.exec(content)) !== null) {
    const [, language, filename, code] = match
    blocks.push({
      language,
      filename: filename?.trim(),
      code: code.trim(),
    })
  }
  
  return blocks
}

export function extractFileOperations(content: string): Array<{
  operation: 'create' | 'update' | 'delete'
  path: string
  content?: string
  language?: string
}> {
  const operations: Array<{
    operation: 'create' | 'update' | 'delete'
    path: string
    content?: string
    language?: string
  }> = []

  // Extract code blocks first
  const codeBlocks = parseCodeBlocks(content)

  // If we have code blocks with filenames, create operations for them
  codeBlocks.forEach(block => {
    if (block.filename) {
      operations.push({
        operation: 'create',
        path: block.filename,
        content: block.code,
        language: block.language,
      })
    }
  })

  // Look for explicit file creation patterns in text
  const createFileRegex = /(?:create|add|make)\s+(?:a\s+)?(?:new\s+)?file\s+[`"]?([^`"\s]+)[`"]?/gi
  const updateFileRegex = /(?:update|modify|edit)\s+(?:the\s+)?file\s+[`"]?([^`"\s]+)[`"]?/gi
  const deleteFileRegex = /(?:delete|remove)\s+(?:the\s+)?file\s+[`"]?([^`"\s]+)[`"]?/gi

  let match
  while ((match = createFileRegex.exec(content)) !== null) {
    const path = match[1]
    // Only add if not already added from code blocks
    if (!operations.find(op => op.path === path)) {
      operations.push({
        operation: 'create',
        path,
        content: '',
        language: getLanguageFromPath(path),
      })
    }
  }

  while ((match = updateFileRegex.exec(content)) !== null) {
    const path = match[1]
    operations.push({
      operation: 'update',
      path,
      language: getLanguageFromPath(path),
    })
  }

  while ((match = deleteFileRegex.exec(content)) !== null) {
    const path = match[1]
    operations.push({
      operation: 'delete',
      path,
    })
  }

  return operations
}

function getLanguageFromPath(path: string): string {
  const extension = '.' + path.split('.').pop()?.toLowerCase()
  return FILE_EXTENSIONS[extension as keyof typeof FILE_EXTENSIONS] || 'text'
}
