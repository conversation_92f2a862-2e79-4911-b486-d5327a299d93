import { streamText } from 'ai'
import { aiProviderService } from './providers'
import { ChatMessage } from '@/types'
import { SYSTEM_PROMPT, FILE_EXTENSIONS } from '@/lib/constants'

export interface ChatOptions {
  provider: string
  model: string
  messages: ChatMessage[]
  onUpdate?: (content: string) => void
  onFinish?: (content: string) => void
  onError?: (error: Error) => void
}

export async function streamChat({
  provider,
  model,
  messages,
  onUpdate,
  onFinish,
  onError,
}: ChatOptions) {
  try {
    const aiProvider = aiProviderService.createProvider(provider)
    const aiModel = aiProvider(model)

    // Convert our messages to the AI SDK format
    const formattedMessages = [
      { role: 'system' as const, content: SYSTEM_PROMPT },
      ...messages.map(msg => ({
        role: msg.role as 'user' | 'assistant',
        content: msg.content,
      })),
    ]

    const result = await streamText({
      model: aiModel,
      messages: formattedMessages,
      maxTokens: 4000,
      temperature: 0.7,
    })

    let fullContent = ''

    for await (const delta of result.textStream) {
      fullContent += delta
      onUpdate?.(fullContent)
    }

    onFinish?.(fullContent)
    return fullContent
  } catch (error) {
    console.error('Chat streaming error:', error)
    const errorMessage = error instanceof Error ? error : new Error('Unknown error occurred')
    onError?.(errorMessage)
    throw errorMessage
  }
}

export async function generateText({
  provider,
  model,
  prompt,
  maxTokens = 1000,
}: {
  provider: string
  model: string
  prompt: string
  maxTokens?: number
}) {
  try {
    const aiProvider = aiProviderService.createProvider(provider)
    const aiModel = aiProvider(model)

    const result = await aiModel.generateText({
      prompt,
      maxTokens,
      temperature: 0.7,
    })

    return result.text
  } catch (error) {
    console.error('Text generation error:', error)
    throw error
  }
}

export function parseCodeBlocks(content: string): Array<{
  language: string
  filename?: string
  code: string
}> {
  const codeBlockRegex = /```(\w+)(?:\s+(.+?))?\n([\s\S]*?)```/g
  const blocks: Array<{ language: string; filename?: string; code: string }> = []
  
  let match
  while ((match = codeBlockRegex.exec(content)) !== null) {
    const [, language, filename, code] = match
    blocks.push({
      language,
      filename: filename?.trim(),
      code: code.trim(),
    })
  }
  
  return blocks
}

export function extractFileOperations(content: string): Array<{
  operation: 'create' | 'update' | 'delete'
  path: string
  content?: string
  language?: string
}> {
  const operations: Array<{
    operation: 'create' | 'update' | 'delete'
    path: string
    content?: string
    language?: string
  }> = []

  // Extract code blocks first
  const codeBlocks = parseCodeBlocks(content)

  // If we have code blocks with filenames, create operations for them
  codeBlocks.forEach(block => {
    if (block.filename) {
      operations.push({
        operation: 'create',
        path: block.filename,
        content: block.code,
        language: block.language,
      })
    }
  })

  // Look for explicit file creation patterns in text
  const createFileRegex = /(?:create|add|make)\s+(?:a\s+)?(?:new\s+)?file\s+[`"]?([^`"\s]+)[`"]?/gi
  const updateFileRegex = /(?:update|modify|edit)\s+(?:the\s+)?file\s+[`"]?([^`"\s]+)[`"]?/gi
  const deleteFileRegex = /(?:delete|remove)\s+(?:the\s+)?file\s+[`"]?([^`"\s]+)[`"]?/gi

  let match
  while ((match = createFileRegex.exec(content)) !== null) {
    const path = match[1]
    // Only add if not already added from code blocks
    if (!operations.find(op => op.path === path)) {
      operations.push({
        operation: 'create',
        path,
        content: '',
        language: getLanguageFromPath(path),
      })
    }
  }

  while ((match = updateFileRegex.exec(content)) !== null) {
    const path = match[1]
    operations.push({
      operation: 'update',
      path,
      language: getLanguageFromPath(path),
    })
  }

  while ((match = deleteFileRegex.exec(content)) !== null) {
    const path = match[1]
    operations.push({
      operation: 'delete',
      path,
    })
  }

  return operations
}

function getLanguageFromPath(path: string): string {
  const extension = '.' + path.split('.').pop()?.toLowerCase()
  return FILE_EXTENSIONS[extension as keyof typeof FILE_EXTENSIONS] || 'text'
}
