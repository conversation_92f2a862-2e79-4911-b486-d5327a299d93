// Temporarily disable AI SDK imports to fix blank screen
// import { openai } from '@ai-sdk/openai'
// import { google } from '@ai-sdk/google'
// import { openRouter } from '@openrouter/ai-sdk-provider'
import { AIProvider, AIModel } from '@/types'

export class AIProviderService {
  private apiKeys: Record<string, string> = {}

  setApiKey(provider: string, apiKey: string) {
    this.apiKeys[provider] = apiKey
  }

  getApiKey(provider: string): string | undefined {
    return this.apiKeys[provider]
  }

  createProvider(providerId: string) {
    const apiKey = this.getApiKey(providerId)

    if (!apiKey) {
      throw new Error(`API key not found for provider: ${providerId}`)
    }

    // Temporarily return a mock provider to fix blank screen
    return {
      generateText: async (options: any) => ({
        text: 'This is a simulated response. Please configure AI providers in settings.'
      })
    }
  }

  async testConnection(providerId: string, apiKey: string): Promise<boolean> {
    try {
      // Simulate connection test
      await new Promise(resolve => setTimeout(resolve, 1000))
      return apiKey.length > 10 // Simple validation
    } catch (error) {
      console.error(`Connection test failed for ${providerId}:`, error)
      return false
    }
  }

  private getDefaultModel(providerId: string): string {
    switch (providerId) {
      case 'openai':
        return 'gpt-3.5-turbo'
      case 'google':
        return 'gemini-1.5-flash'
      case 'openrouter':
        return 'openai/gpt-3.5-turbo'
      default:
        throw new Error(`No default model for provider: ${providerId}`)
    }
  }

  async getAvailableModels(providerId: string): Promise<AIModel[]> {
    // For now, return static models. In a real implementation,
    // you might fetch dynamic models from the provider's API
    const provider = this.getProviderById(providerId)
    return provider?.models || []
  }

  private getProviderById(providerId: string): AIProvider | undefined {
    // This would typically come from your constants or configuration
    const providers: AIProvider[] = [
      {
        id: 'openai',
        name: 'OpenAI',
        description: 'GPT models from OpenAI',
        icon: '🤖',
        requiresApiKey: true,
        apiKeyUrl: 'https://platform.openai.com/api-keys',
        models: [
          {
            id: 'gpt-4o',
            name: 'GPT-4o',
            provider: 'openai',
            maxTokens: 128000,
            supportsStreaming: true,
            supportsTools: true,
          },
          {
            id: 'gpt-4o-mini',
            name: 'GPT-4o Mini',
            provider: 'openai',
            maxTokens: 128000,
            supportsStreaming: true,
            supportsTools: true,
          },
        ],
      },
      // Add other providers...
    ]
    
    return providers.find(p => p.id === providerId)
  }
}

export const aiProviderService = new AIProviderService()
