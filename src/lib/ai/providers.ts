import { openai } from '@ai-sdk/openai'
import { google } from '@ai-sdk/google'
import { openRouter } from '@openrouter/ai-sdk-provider'
import { AIProvider, AIModel } from '@/types'

export class AIProviderService {
  private apiKeys: Record<string, string> = {}

  setApiKey(provider: string, apiKey: string) {
    this.apiKeys[provider] = apiKey
  }

  getApiKey(provider: string): string | undefined {
    return this.apiKeys[provider]
  }

  createProvider(providerId: string) {
    const apiKey = this.getApiKey(providerId)
    
    if (!apiKey) {
      throw new Error(`API key not found for provider: ${providerId}`)
    }

    switch (providerId) {
      case 'openai':
        return openai({
          apiKey,
        })
      
      case 'google':
        return google({
          apiKey,
        })
      
      case 'openrouter':
        return openRouter({
          apiKey,
        })
      
      default:
        throw new Error(`Unsupported provider: ${providerId}`)
    }
  }

  async testConnection(providerId: string, apiKey: string): Promise<boolean> {
    try {
      // Temporarily set the API key for testing
      const originalKey = this.apiKeys[providerId]
      this.setApiKey(providerId, apiKey)
      
      const provider = this.createProvider(providerId)
      
      // Test with a simple completion
      const model = this.getDefaultModel(providerId)
      const result = await provider(model).generateText({
        prompt: 'Hello',
        maxTokens: 5,
      })
      
      // Restore original key
      if (originalKey) {
        this.setApiKey(providerId, originalKey)
      } else {
        delete this.apiKeys[providerId]
      }
      
      return !!result.text
    } catch (error) {
      console.error(`Connection test failed for ${providerId}:`, error)
      return false
    }
  }

  private getDefaultModel(providerId: string): string {
    switch (providerId) {
      case 'openai':
        return 'gpt-3.5-turbo'
      case 'google':
        return 'gemini-1.5-flash'
      case 'openrouter':
        return 'openai/gpt-3.5-turbo'
      default:
        throw new Error(`No default model for provider: ${providerId}`)
    }
  }

  async getAvailableModels(providerId: string): Promise<AIModel[]> {
    // For now, return static models. In a real implementation,
    // you might fetch dynamic models from the provider's API
    const provider = this.getProviderById(providerId)
    return provider?.models || []
  }

  private getProviderById(providerId: string): AIProvider | undefined {
    // This would typically come from your constants or configuration
    const providers: AIProvider[] = [
      {
        id: 'openai',
        name: 'OpenAI',
        description: 'GPT models from OpenAI',
        icon: '🤖',
        requiresApiKey: true,
        apiKeyUrl: 'https://platform.openai.com/api-keys',
        models: [
          {
            id: 'gpt-4o',
            name: 'GPT-4o',
            provider: 'openai',
            maxTokens: 128000,
            supportsStreaming: true,
            supportsTools: true,
          },
          {
            id: 'gpt-4o-mini',
            name: 'GPT-4o Mini',
            provider: 'openai',
            maxTokens: 128000,
            supportsStreaming: true,
            supportsTools: true,
          },
        ],
      },
      // Add other providers...
    ]
    
    return providers.find(p => p.id === providerId)
  }
}

export const aiProviderService = new AIProviderService()
