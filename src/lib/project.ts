import { Project, FileItem } from '@/types'
import { FILE_EXTENSIONS } from './constants'

export function createProject(name: string, description?: string): Project {
  return {
    id: crypto.randomUUID(),
    name,
    description,
    files: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  }
}

export function createFileItem(
  name: string,
  path: string,
  content: string = '',
  type: 'file' | 'folder' = 'file'
): FileItem {
  const extension = '.' + name.split('.').pop()?.toLowerCase()
  const language = FILE_EXTENSIONS[extension as keyof typeof FILE_EXTENSIONS]

  return {
    id: crypto.randomUUID(),
    name,
    path,
    type,
    content: type === 'file' ? content : undefined,
    language: type === 'file' ? language : undefined,
    children: type === 'folder' ? [] : undefined,
    isModified: false,
    isOpen: false,
  }
}

export function addFileToProject(project: Project, file: FileItem): Project {
  const updatedFiles = [...project.files]
  
  // Check if file already exists
  const existingIndex = updatedFiles.findIndex(f => f.path === file.path)
  if (existingIndex >= 0) {
    updatedFiles[existingIndex] = file
  } else {
    updatedFiles.push(file)
  }

  return {
    ...project,
    files: updatedFiles,
    updatedAt: new Date(),
  }
}

export function removeFileFromProject(project: Project, filePath: string): Project {
  const updatedFiles = project.files.filter(f => f.path !== filePath)
  
  return {
    ...project,
    files: updatedFiles,
    updatedAt: new Date(),
  }
}

export function updateFileInProject(
  project: Project,
  filePath: string,
  updates: Partial<FileItem>
): Project {
  const updateFileInTree = (files: FileItem[]): FileItem[] => {
    return files.map(file => {
      if (file.path === filePath) {
        return { ...file, ...updates }
      }
      if (file.children) {
        return { ...file, children: updateFileInTree(file.children) }
      }
      return file
    })
  }

  return {
    ...project,
    files: updateFileInTree(project.files),
    updatedAt: new Date(),
  }
}

export function findFileInProject(project: Project, filePath: string): FileItem | undefined {
  const findInTree = (files: FileItem[]): FileItem | undefined => {
    for (const file of files) {
      if (file.path === filePath) {
        return file
      }
      if (file.children) {
        const found = findInTree(file.children)
        if (found) return found
      }
    }
    return undefined
  }

  return findInTree(project.files)
}

export function createProjectFromFiles(
  name: string,
  fileOperations: Array<{
    operation: 'create' | 'update' | 'delete'
    path: string
    content?: string
    language?: string
  }>
): Project {
  const project = createProject(name)
  
  fileOperations.forEach(op => {
    if (op.operation === 'create' && op.content) {
      const file = createFileItem(
        op.path.split('/').pop() || op.path,
        op.path,
        op.content
      )
      project.files.push(file)
    }
  })

  return project
}

export function generateProjectStructure(files: FileItem[]): string {
  const generateTree = (files: FileItem[], depth = 0): string => {
    return files
      .map(file => {
        const indent = '  '.repeat(depth)
        const icon = file.type === 'folder' ? '📁' : '📄'
        let result = `${indent}${icon} ${file.name}`
        
        if (file.children && file.children.length > 0) {
          result += '\n' + generateTree(file.children, depth + 1)
        }
        
        return result
      })
      .join('\n')
  }

  return generateTree(files)
}

export function exportProject(project: Project): string {
  const exportData = {
    name: project.name,
    description: project.description,
    files: project.files,
    createdAt: project.createdAt,
    updatedAt: project.updatedAt,
    exportedAt: new Date(),
  }

  return JSON.stringify(exportData, null, 2)
}

export function importProject(jsonData: string): Project {
  try {
    const data = JSON.parse(jsonData)
    return {
      id: crypto.randomUUID(), // Generate new ID for imported project
      name: data.name,
      description: data.description,
      files: data.files || [],
      createdAt: new Date(data.createdAt),
      updatedAt: new Date(data.updatedAt),
    }
  } catch (error) {
    throw new Error('Invalid project file format')
  }
}

export function getProjectStats(project: Project) {
  let fileCount = 0
  let totalLines = 0
  let totalSize = 0

  const countFiles = (files: FileItem[]) => {
    files.forEach(file => {
      if (file.type === 'file') {
        fileCount++
        if (file.content) {
          totalLines += file.content.split('\n').length
          totalSize += file.content.length
        }
      } else if (file.children) {
        countFiles(file.children)
      }
    })
  }

  countFiles(project.files)

  return {
    fileCount,
    totalLines,
    totalSize,
    sizeFormatted: formatBytes(totalSize),
  }
}

function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
