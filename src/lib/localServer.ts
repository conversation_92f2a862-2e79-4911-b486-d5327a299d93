import { Project, FileItem } from '@/types'

export interface ServerStatus {
  isRunning: boolean
  port?: number
  url?: string
  error?: string
}

export class LocalServer {
  private status: ServerStatus = { isRunning: false }
  private project?: Project
  private listeners: ((status: ServerStatus) => void)[] = []

  constructor() {
    // In a real implementation, this would interface with a local development server
    // For now, we'll simulate server behavior
  }

  onStatusChange(callback: (status: ServerStatus) => void) {
    this.listeners.push(callback)
    return () => {
      this.listeners = this.listeners.filter(l => l !== callback)
    }
  }

  private notifyListeners() {
    this.listeners.forEach(listener => listener(this.status))
  }

  async start(project: Project): Promise<void> {
    this.project = project
    
    try {
      // Simulate server startup
      this.status = { isRunning: false }
      this.notifyListeners()

      // Check if project has the necessary files
      const hasHtml = project.files.some(f => f.name.endsWith('.html'))
      const hasPackageJson = project.files.some(f => f.name === 'package.json')

      if (!hasHtml && !hasPackageJson) {
        throw new Error('No HTML files or package.json found. Cannot start server.')
      }

      // Simulate startup delay
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Simulate port assignment
      const port = 3000 + Math.floor(Math.random() * 1000)
      
      this.status = {
        isRunning: true,
        port,
        url: `http://localhost:${port}`,
      }
      
      this.notifyListeners()
    } catch (error) {
      this.status = {
        isRunning: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      }
      this.notifyListeners()
      throw error
    }
  }

  async stop(): Promise<void> {
    this.status = { isRunning: false }
    this.notifyListeners()
  }

  async restart(): Promise<void> {
    if (this.project) {
      await this.stop()
      await this.start(this.project)
    }
  }

  getStatus(): ServerStatus {
    return { ...this.status }
  }

  async installDependencies(): Promise<void> {
    if (!this.project) {
      throw new Error('No project loaded')
    }

    const packageJson = this.project.files.find(f => f.name === 'package.json')
    if (!packageJson) {
      throw new Error('No package.json found')
    }

    // Simulate npm install
    await new Promise(resolve => setTimeout(resolve, 2000))
  }

  async runCommand(command: string): Promise<string> {
    if (!this.status.isRunning) {
      throw new Error('Server is not running')
    }

    // Simulate command execution
    await new Promise(resolve => setTimeout(resolve, 500))

    switch (command.toLowerCase()) {
      case 'npm start':
      case 'yarn start':
        return 'Starting development server...\nServer is running on http://localhost:3000'
      
      case 'npm run build':
      case 'yarn build':
        return 'Building for production...\nBuild completed successfully!'
      
      case 'npm test':
      case 'yarn test':
        return 'Running tests...\nAll tests passed!'
      
      case 'npm install':
      case 'yarn install':
        return 'Installing dependencies...\nDependencies installed successfully!'
      
      default:
        return `Command executed: ${command}\nOutput would appear here in a real implementation.`
    }
  }

  getProjectStructure(): string {
    if (!this.project) {
      return 'No project loaded'
    }

    const generateTree = (files: FileItem[], depth = 0): string => {
      return files
        .map(file => {
          const indent = '  '.repeat(depth)
          const icon = file.type === 'folder' ? '📁' : '📄'
          let result = `${indent}${icon} ${file.name}`
          
          if (file.children && file.children.length > 0) {
            result += '\n' + generateTree(file.children, depth + 1)
          }
          
          return result
        })
        .join('\n')
    }

    return generateTree(this.project.files)
  }

  async detectProjectType(): Promise<string> {
    if (!this.project) {
      return 'unknown'
    }

    const files = this.project.files
    const fileNames = files.map(f => f.name)

    // Check for React
    if (fileNames.some(name => name.endsWith('.jsx') || name.endsWith('.tsx'))) {
      return 'react'
    }

    // Check for Vue
    if (fileNames.some(name => name.endsWith('.vue'))) {
      return 'vue'
    }

    // Check for Node.js
    if (fileNames.includes('package.json')) {
      const packageJson = files.find(f => f.name === 'package.json')
      if (packageJson?.content) {
        try {
          const pkg = JSON.parse(packageJson.content)
          if (pkg.dependencies?.express || pkg.dependencies?.fastify) {
            return 'node-server'
          }
          if (pkg.dependencies?.react) {
            return 'react'
          }
          if (pkg.dependencies?.vue) {
            return 'vue'
          }
        } catch (e) {
          // Invalid JSON
        }
      }
      return 'node'
    }

    // Check for static HTML
    if (fileNames.some(name => name.endsWith('.html'))) {
      return 'static'
    }

    return 'unknown'
  }

  async getRecommendedCommands(): Promise<string[]> {
    const projectType = await this.detectProjectType()

    switch (projectType) {
      case 'react':
        return ['npm start', 'npm run build', 'npm test']
      case 'vue':
        return ['npm run serve', 'npm run build', 'npm run test']
      case 'node':
      case 'node-server':
        return ['npm start', 'npm run dev', 'npm test']
      case 'static':
        return ['python -m http.server 8000', 'npx serve .']
      default:
        return ['ls', 'pwd', 'cat package.json']
    }
  }
}

export const localServer = new LocalServer()
