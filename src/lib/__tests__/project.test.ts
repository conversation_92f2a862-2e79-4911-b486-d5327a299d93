import { describe, it, expect } from 'vitest'
import {
  createProject,
  createFileItem,
  addFileToProject,
  removeFileFromProject,
  updateFileInProject,
  findFileInProject,
  createProjectFromFiles,
  getProjectStats,
} from '../project'

describe('Project Management', () => {
  describe('createProject', () => {
    it('should create a new project with basic properties', () => {
      const project = createProject('Test Project', 'A test project')
      
      expect(project.name).toBe('Test Project')
      expect(project.description).toBe('A test project')
      expect(project.files).toEqual([])
      expect(project.id).toBeDefined()
      expect(project.createdAt).toBeInstanceOf(Date)
      expect(project.updatedAt).toBeInstanceOf(Date)
    })

    it('should create a project without description', () => {
      const project = createProject('Test Project')
      
      expect(project.name).toBe('Test Project')
      expect(project.description).toBeUndefined()
    })
  })

  describe('createFileItem', () => {
    it('should create a file item with correct properties', () => {
      const file = createFileItem('test.js', '/test.js', 'console.log("hello")')
      
      expect(file.name).toBe('test.js')
      expect(file.path).toBe('/test.js')
      expect(file.content).toBe('console.log("hello")')
      expect(file.type).toBe('file')
      expect(file.language).toBe('javascript')
      expect(file.isModified).toBe(false)
      expect(file.isOpen).toBe(false)
    })

    it('should create a folder item', () => {
      const folder = createFileItem('src', '/src', '', 'folder')
      
      expect(folder.name).toBe('src')
      expect(folder.type).toBe('folder')
      expect(folder.content).toBeUndefined()
      expect(folder.children).toEqual([])
    })

    it('should detect language from file extension', () => {
      const jsFile = createFileItem('app.js', '/app.js')
      const tsFile = createFileItem('app.ts', '/app.ts')
      const htmlFile = createFileItem('index.html', '/index.html')
      const cssFile = createFileItem('style.css', '/style.css')
      
      expect(jsFile.language).toBe('javascript')
      expect(tsFile.language).toBe('typescript')
      expect(htmlFile.language).toBe('html')
      expect(cssFile.language).toBe('css')
    })
  })

  describe('addFileToProject', () => {
    it('should add a new file to project', () => {
      const project = createProject('Test')
      const file = createFileItem('test.js', '/test.js', 'test content')
      
      const updatedProject = addFileToProject(project, file)
      
      expect(updatedProject.files).toHaveLength(1)
      expect(updatedProject.files[0]).toEqual(file)
      expect(updatedProject.updatedAt.getTime()).toBeGreaterThan(project.updatedAt.getTime())
    })

    it('should replace existing file with same path', () => {
      const project = createProject('Test')
      const file1 = createFileItem('test.js', '/test.js', 'content 1')
      const file2 = createFileItem('test.js', '/test.js', 'content 2')
      
      let updatedProject = addFileToProject(project, file1)
      updatedProject = addFileToProject(updatedProject, file2)
      
      expect(updatedProject.files).toHaveLength(1)
      expect(updatedProject.files[0].content).toBe('content 2')
    })
  })

  describe('removeFileFromProject', () => {
    it('should remove file from project', () => {
      const project = createProject('Test')
      const file = createFileItem('test.js', '/test.js', 'test content')
      
      let updatedProject = addFileToProject(project, file)
      updatedProject = removeFileFromProject(updatedProject, '/test.js')
      
      expect(updatedProject.files).toHaveLength(0)
    })
  })

  describe('findFileInProject', () => {
    it('should find file by path', () => {
      const project = createProject('Test')
      const file = createFileItem('test.js', '/test.js', 'test content')
      const updatedProject = addFileToProject(project, file)
      
      const foundFile = findFileInProject(updatedProject, '/test.js')
      
      expect(foundFile).toEqual(file)
    })

    it('should return undefined for non-existent file', () => {
      const project = createProject('Test')
      
      const foundFile = findFileInProject(project, '/nonexistent.js')
      
      expect(foundFile).toBeUndefined()
    })
  })

  describe('createProjectFromFiles', () => {
    it('should create project from file operations', () => {
      const fileOperations = [
        {
          operation: 'create' as const,
          path: 'index.html',
          content: '<html></html>',
          language: 'html',
        },
        {
          operation: 'create' as const,
          path: 'style.css',
          content: 'body { margin: 0; }',
          language: 'css',
        },
      ]
      
      const project = createProjectFromFiles('Web App', fileOperations)
      
      expect(project.name).toBe('Web App')
      expect(project.files).toHaveLength(2)
      expect(project.files[0].name).toBe('index.html')
      expect(project.files[1].name).toBe('style.css')
    })
  })

  describe('getProjectStats', () => {
    it('should calculate project statistics', () => {
      const project = createProject('Test')
      const file1 = createFileItem('test1.js', '/test1.js', 'line1\nline2\nline3')
      const file2 = createFileItem('test2.js', '/test2.js', 'single line')
      
      let updatedProject = addFileToProject(project, file1)
      updatedProject = addFileToProject(updatedProject, file2)
      
      const stats = getProjectStats(updatedProject)
      
      expect(stats.fileCount).toBe(2)
      expect(stats.totalLines).toBe(4) // 3 lines + 1 line
      expect(stats.totalSize).toBe(25) // Total character count
      expect(stats.sizeFormatted).toContain('Bytes')
    })
  })
})
