import { describe, it, expect } from 'vitest'
import { parseCodeBlocks, extractFileOperations } from '../ai/chat'

describe('AI Chat Utilities', () => {
  describe('parseCodeBlocks', () => {
    it('should parse single code block', () => {
      const content = `
Here's a simple HTML file:

\`\`\`html
<!DOCTYPE html>
<html>
<head>
  <title>Test</title>
</head>
<body>
  <h1>Hello World</h1>
</body>
</html>
\`\`\`
      `
      
      const blocks = parseCodeBlocks(content)
      
      expect(blocks).toHaveLength(1)
      expect(blocks[0].language).toBe('html')
      expect(blocks[0].code).toContain('<!DOCTYPE html>')
      expect(blocks[0].code).toContain('<h1>Hello World</h1>')
    })

    it('should parse multiple code blocks', () => {
      const content = `
Here's the HTML:

\`\`\`html index.html
<!DOCTYPE html>
<html>
<body>
  <h1>Hello</h1>
</body>
</html>
\`\`\`

And here's the CSS:

\`\`\`css style.css
body {
  margin: 0;
  font-family: Arial;
}
\`\`\`
      `
      
      const blocks = parseCodeBlocks(content)
      
      expect(blocks).toHaveLength(2)
      expect(blocks[0].language).toBe('html')
      expect(blocks[0].filename).toBe('index.html')
      expect(blocks[1].language).toBe('css')
      expect(blocks[1].filename).toBe('style.css')
    })

    it('should handle code blocks without language', () => {
      const content = `
\`\`\`
console.log('hello')
\`\`\`
      `
      
      const blocks = parseCodeBlocks(content)
      
      expect(blocks).toHaveLength(1)
      expect(blocks[0].language).toBe('')
      expect(blocks[0].code).toBe("console.log('hello')")
    })
  })

  describe('extractFileOperations', () => {
    it('should extract file operations from code blocks with filenames', () => {
      const content = `
I'll create two files for you:

\`\`\`html index.html
<!DOCTYPE html>
<html>
<body>
  <h1>Hello World</h1>
</body>
</html>
\`\`\`

\`\`\`css style.css
body {
  margin: 0;
  padding: 20px;
}
\`\`\`
      `
      
      const operations = extractFileOperations(content)
      
      expect(operations).toHaveLength(2)
      expect(operations[0].operation).toBe('create')
      expect(operations[0].path).toBe('index.html')
      expect(operations[0].content).toContain('<!DOCTYPE html>')
      expect(operations[0].language).toBe('html')
      
      expect(operations[1].operation).toBe('create')
      expect(operations[1].path).toBe('style.css')
      expect(operations[1].content).toContain('margin: 0')
      expect(operations[1].language).toBe('css')
    })

    it('should extract explicit file creation commands', () => {
      const content = `
Let me create a new file called config.json for you.

\`\`\`json
{
  "name": "my-app",
  "version": "1.0.0"
}
\`\`\`

I'll also create a package.json file.
      `
      
      const operations = extractFileOperations(content)
      
      // Should find the explicit mention of config.json and package.json
      expect(operations.length).toBeGreaterThan(0)
      
      const configOp = operations.find(op => op.path === 'config.json')
      expect(configOp).toBeDefined()
      expect(configOp?.operation).toBe('create')
      
      const packageOp = operations.find(op => op.path === 'package.json')
      expect(packageOp).toBeDefined()
      expect(packageOp?.operation).toBe('create')
    })

    it('should handle update operations', () => {
      const content = `
Now let's update the existing index.html file to add some styling.
      `
      
      const operations = extractFileOperations(content)
      
      const updateOp = operations.find(op => op.operation === 'update')
      expect(updateOp).toBeDefined()
      expect(updateOp?.path).toBe('index.html')
    })

    it('should handle delete operations', () => {
      const content = `
Let's remove the old config.json file since we don't need it anymore.
      `
      
      const operations = extractFileOperations(content)
      
      const deleteOp = operations.find(op => op.operation === 'delete')
      expect(deleteOp).toBeDefined()
      expect(deleteOp?.path).toBe('config.json')
    })

    it('should return empty array for content without file operations', () => {
      const content = `
This is just a regular conversation without any file operations.
Let me explain how to use this application.
      `
      
      const operations = extractFileOperations(content)
      
      expect(operations).toHaveLength(0)
    })
  })
})
