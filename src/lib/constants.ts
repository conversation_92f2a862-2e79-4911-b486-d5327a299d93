import { AIProvider } from '@/types'

export const AI_PROVIDERS: AIProvider[] = [
  {
    id: 'openai',
    name: 'OpenAI',
    description: 'GPT models from OpenAI',
    icon: '🤖',
    requiresApiKey: true,
    apiKeyUrl: 'https://platform.openai.com/api-keys',
    models: [
      {
        id: 'gpt-4o',
        name: 'GPT-4o',
        provider: 'openai',
        maxTokens: 128000,
        supportsStreaming: true,
        supportsTools: true,
      },
      {
        id: 'gpt-4o-mini',
        name: 'GPT-4o Mini',
        provider: 'openai',
        maxTokens: 128000,
        supportsStreaming: true,
        supportsTools: true,
      },
      {
        id: 'gpt-3.5-turbo',
        name: 'GPT-3.5 Turbo',
        provider: 'openai',
        maxTokens: 16385,
        supportsStreaming: true,
        supportsTools: true,
      },
    ],
  },
  {
    id: 'google',
    name: 'Google Gemini',
    description: 'Gemini models from Google',
    icon: '✨',
    requiresApiKey: true,
    apiKeyUrl: 'https://aistudio.google.com/app/apikey',
    models: [
      {
        id: 'gemini-1.5-pro',
        name: 'Gemini 1.5 Pro',
        provider: 'google',
        maxTokens: 2097152,
        supportsStreaming: true,
        supportsTools: true,
      },
      {
        id: 'gemini-1.5-flash',
        name: 'Gemini 1.5 Flash',
        provider: 'google',
        maxTokens: 1048576,
        supportsStreaming: true,
        supportsTools: true,
      },
    ],
  },
  {
    id: 'openrouter',
    name: 'OpenRouter',
    description: 'Access to multiple AI models via OpenRouter',
    icon: '🌐',
    requiresApiKey: true,
    apiKeyUrl: 'https://openrouter.ai/keys',
    models: [
      {
        id: 'anthropic/claude-3.5-sonnet',
        name: 'Claude 3.5 Sonnet',
        provider: 'openrouter',
        maxTokens: 200000,
        supportsStreaming: true,
        supportsTools: true,
      },
      {
        id: 'openai/gpt-4o',
        name: 'GPT-4o (OpenRouter)',
        provider: 'openrouter',
        maxTokens: 128000,
        supportsStreaming: true,
        supportsTools: true,
      },
      {
        id: 'google/gemini-pro-1.5',
        name: 'Gemini Pro 1.5 (OpenRouter)',
        provider: 'openrouter',
        maxTokens: 2097152,
        supportsStreaming: true,
        supportsTools: true,
      },
    ],
  },
]

export const DEFAULT_SETTINGS = {
  apiKeys: {},
  selectedProvider: 'openai',
  selectedModel: 'gpt-4o-mini',
  theme: 'dark' as const,
  editorSettings: {
    fontSize: 14,
    tabSize: 2,
    wordWrap: true,
    minimap: false,
  },
}

export const SYSTEM_PROMPT = `You are Lovable.DIY, an AI assistant that helps users build web applications locally. You can:

1. Generate complete web applications with HTML, CSS, and JavaScript
2. Create React components and applications
3. Write Python scripts and applications
4. Build Node.js applications and APIs
5. Create configuration files and documentation

When creating applications:
- Use modern, clean code with best practices
- Include proper error handling
- Add helpful comments
- Create responsive designs
- Use semantic HTML and accessible components

Always provide complete, working code that can be run locally. Focus on creating functional, well-structured applications that users can immediately use and modify.`

export const FILE_EXTENSIONS = {
  '.js': 'javascript',
  '.jsx': 'javascript',
  '.ts': 'typescript',
  '.tsx': 'typescript',
  '.html': 'html',
  '.css': 'css',
  '.scss': 'scss',
  '.sass': 'sass',
  '.json': 'json',
  '.md': 'markdown',
  '.py': 'python',
  '.yml': 'yaml',
  '.yaml': 'yaml',
  '.xml': 'xml',
  '.svg': 'xml',
}
