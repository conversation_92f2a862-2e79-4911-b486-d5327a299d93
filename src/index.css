@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-size: 200% 200%;
    animation: gradient 8s ease infinite;
  }
  
  .glass-effect {
    @apply backdrop-blur-sm bg-white/10 border border-white/20;
  }
  
  .code-editor {
    font-family: 'JetBrains Mono', 'Consolas', 'Monaco', monospace;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
