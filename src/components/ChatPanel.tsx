import { useState, useRef, useEffect } from 'react'
import { RotateCcw, Download, Share } from 'lucide-react'
import { ChatInput } from './ChatInput'
import { MessageList } from './MessageList'
import { useChat } from '@/contexts/ChatContext'
import { useSettings } from '@/contexts/SettingsContext'
import { streamChat, extractFileOperations } from '@/lib/ai/chat'
import { aiProviderService } from '@/lib/ai/providers'
import { createProjectFromFiles } from '@/lib/project'

export function ChatPanel() {
  const { chatState, addMessage, setLoading, clearMessages, setCurrentProject } = useChat()
  const { settings, getApiKey } = useSettings()
  const [input, setInput] = useState('')
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [chatState.messages])

  // Set up API key in the provider service
  useEffect(() => {
    const apiKey = getApiKey(settings.selectedProvider)
    if (apiKey) {
      aiProviderService.setApiKey(settings.selectedProvider, apiKey)
    }
  }, [settings.selectedProvider, getApiKey])

  const handleSubmit = async (message: string) => {
    if (!message.trim()) return

    const apiKey = getApiKey(settings.selectedProvider)
    if (!apiKey) {
      addMessage({
        role: 'assistant',
        content: `Please set up your API key for ${settings.selectedProvider} in the settings to continue.`,
      })
      return
    }

    // Add user message
    addMessage({
      role: 'user',
      content: message,
    })

    setLoading(true)

    try {
      let assistantContent = ''

      await streamChat({
        provider: settings.selectedProvider,
        model: settings.selectedModel,
        messages: [...chatState.messages, {
          id: crypto.randomUUID(),
          role: 'user',
          content: message,
          timestamp: new Date()
        }],
        onUpdate: (content) => {
          assistantContent = content
          // In a real implementation, you'd update the streaming message here
        },
        onFinish: (content) => {
          // Add the complete assistant message
          addMessage({
            role: 'assistant',
            content,
            metadata: {
              provider: settings.selectedProvider,
              model: settings.selectedModel,
            },
          })

          // Extract file operations and create project if needed
          const fileOperations = extractFileOperations(content)
          if (fileOperations.length > 0) {
            const projectName = extractProjectName(message) || 'New Project'
            const project = createProjectFromFiles(projectName, fileOperations)
            setCurrentProject(project)
          }

          setLoading(false)
        },
        onError: (error) => {
          addMessage({
            role: 'assistant',
            content: `Sorry, I encountered an error: ${error.message}`,
          })
          setLoading(false)
        },
      })
    } catch (error) {
      console.error('Chat error:', error)
      addMessage({
        role: 'assistant',
        content: 'Sorry, I encountered an error while processing your request.',
      })
      setLoading(false)
    }
  }

  const handleClearChat = () => {
    clearMessages()
  }

  const extractProjectName = (message: string): string | null => {
    // Look for common patterns that indicate a project name
    const patterns = [
      /create\s+(?:a\s+)?(?:new\s+)?(?:project|app|website|application)\s+(?:called\s+|named\s+)?["`']?([^"`'\s]+)["`']?/i,
      /build\s+(?:a\s+)?(?:new\s+)?(?:project|app|website|application)\s+(?:called\s+|named\s+)?["`']?([^"`'\s]+)["`']?/i,
      /make\s+(?:a\s+)?(?:new\s+)?(?:project|app|website|application)\s+(?:called\s+|named\s+)?["`']?([^"`'\s]+)["`']?/i,
      /(?:project|app|website|application)\s+(?:called\s+|named\s+)?["`']?([^"`'\s]+)["`']?/i,
    ]

    for (const pattern of patterns) {
      const match = message.match(pattern)
      if (match && match[1]) {
        return match[1]
      }
    }

    return null
  }

  const handleExportChat = () => {
    const chatData = {
      messages: chatState.messages,
      timestamp: new Date().toISOString(),
      provider: settings.selectedProvider,
      model: settings.selectedModel,
    }

    const blob = new Blob([JSON.stringify(chatData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `lovable-chat-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  return (
    <div className="h-full flex flex-col bg-background-secondary">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <h2 className="font-semibold">Chat</h2>
        <div className="flex items-center gap-2">
          <button
            onClick={handleClearChat}
            className="p-1.5 text-foreground-secondary hover:text-foreground hover:bg-background-tertiary rounded-md transition-colors"
            title="Clear chat"
          >
            <RotateCcw size={16} />
          </button>
          <button
            onClick={handleExportChat}
            disabled={chatState.messages.length === 0}
            className="p-1.5 text-foreground-secondary hover:text-foreground hover:bg-background-tertiary disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors"
            title="Export chat"
          >
            <Download size={16} />
          </button>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4">
        {chatState.messages.length === 0 ? (
          <div className="h-full flex items-center justify-center">
            <div className="text-center text-foreground-secondary">
              <div className="text-4xl mb-4">💬</div>
              <p className="text-lg font-medium mb-2">Start a conversation</p>
              <p className="text-sm">Ask me to build something amazing!</p>
            </div>
          </div>
        ) : (
          <>
            <MessageList messages={chatState.messages} isLoading={chatState.isLoading} />
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Input */}
      <div className="p-4 border-t border-border">
        <ChatInput
          value={input}
          onChange={setInput}
          onSubmit={handleSubmit}
          placeholder="Describe what you want to build..."
          disabled={chatState.isLoading}
        />
      </div>
    </div>
  )
}
