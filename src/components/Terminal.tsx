import { useState, useRef, useEffect } from 'react'
import { Terminal as TerminalIcon, X, Minimize2, Maximize2 } from 'lucide-react'
import { useChat } from '@/contexts/ChatContext'
import { localServer } from '@/lib/localServer'

interface TerminalProps {
  isOpen: boolean
  onClose: () => void
}

export function Terminal({ isOpen, onClose }: TerminalProps) {
  const { chatState } = useChat()
  const [isMinimized, setIsMinimized] = useState(false)
  const [output, setOutput] = useState<string[]>([
    'Welcome to Lovable.DIY Terminal',
    'Type "help" for available commands',
    '',
  ])
  const [input, setInput] = useState('')
  const [history, setHistory] = useState<string[]>([])
  const [historyIndex, setHistoryIndex] = useState(-1)
  const [isExecuting, setIsExecuting] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const outputRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (isOpen && !isMinimized && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isOpen, isMinimized])

  useEffect(() => {
    if (outputRef.current) {
      outputRef.current.scrollTop = outputRef.current.scrollHeight
    }
  }, [output])

  const executeCommand = async (command: string) => {
    const trimmedCommand = command.trim()
    if (!trimmedCommand) return

    // Add command to history
    setHistory(prev => [...prev, trimmedCommand])
    setHistoryIndex(-1)

    // Add command to output
    setOutput(prev => [...prev, `$ ${trimmedCommand}`])
    setIsExecuting(true)

    try {
      // Process command
      switch (trimmedCommand.toLowerCase()) {
        case 'help':
          setOutput(prev => [...prev,
            'Available commands:',
            '  help       - Show this help message',
            '  clear      - Clear the terminal',
            '  ls         - List files in current directory',
            '  pwd        - Show current directory',
            '  tree       - Show project structure',
            '  server     - Show server status',
            '  start      - Start development server',
            '  stop       - Stop development server',
            '  restart    - Restart development server',
            '  npm <cmd>  - Run npm commands',
            '  echo <text>- Echo text',
            '  date       - Show current date and time',
            '  whoami     - Show current user',
            ''
          ])
          break

        case 'clear':
          setOutput(['Welcome to Lovable.DIY Terminal', ''])
          break

        case 'ls':
          if (chatState.currentProject) {
            const files = chatState.currentProject.files.map(f => f.name)
            setOutput(prev => [...prev, ...files, ''])
          } else {
            setOutput(prev => [...prev, 'No project loaded', ''])
          }
          break

        case 'tree':
          if (chatState.currentProject) {
            const structure = localServer.getProjectStructure()
            setOutput(prev => [...prev, structure, ''])
          } else {
            setOutput(prev => [...prev, 'No project loaded', ''])
          }
          break

        case 'pwd':
          setOutput(prev => [...prev, '/workspace/project', ''])
          break

        case 'server':
          const status = localServer.getStatus()
          if (status.isRunning) {
            setOutput(prev => [...prev,
              `Server is running on ${status.url}`,
              `Port: ${status.port}`,
              ''
            ])
          } else {
            setOutput(prev => [...prev, 'Server is not running', ''])
          }
          break

        case 'start':
          if (chatState.currentProject) {
            try {
              await localServer.start(chatState.currentProject)
              const newStatus = localServer.getStatus()
              setOutput(prev => [...prev,
                'Development server started!',
                `Server running on ${newStatus.url}`,
                ''
              ])
            } catch (error) {
              setOutput(prev => [...prev,
                `Error starting server: ${error instanceof Error ? error.message : 'Unknown error'}`,
                ''
              ])
            }
          } else {
            setOutput(prev => [...prev, 'No project loaded', ''])
          }
          break

        case 'stop':
          await localServer.stop()
          setOutput(prev => [...prev, 'Development server stopped', ''])
          break

        case 'restart':
          if (chatState.currentProject) {
            await localServer.restart()
            setOutput(prev => [...prev, 'Development server restarted', ''])
          } else {
            setOutput(prev => [...prev, 'No project loaded', ''])
          }
          break

        case 'date':
          setOutput(prev => [...prev, new Date().toString(), ''])
          break

        case 'whoami':
          setOutput(prev => [...prev, 'developer', ''])
          break

        default:
          if (trimmedCommand.startsWith('echo ')) {
            const text = trimmedCommand.substring(5)
            setOutput(prev => [...prev, text, ''])
          } else if (trimmedCommand.startsWith('npm ')) {
            try {
              const result = await localServer.runCommand(trimmedCommand)
              setOutput(prev => [...prev, result, ''])
            } catch (error) {
              setOutput(prev => [...prev,
                `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
                ''
              ])
            }
          } else {
            setOutput(prev => [...prev, `Command not found: ${trimmedCommand}`, ''])
          }
      }
    } catch (error) {
      setOutput(prev => [...prev,
        `Error executing command: ${error instanceof Error ? error.message : 'Unknown error'}`,
        ''
      ])
    } finally {
      setIsExecuting(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isExecuting) {
      executeCommand(input)
      setInput('')
    } else if (e.key === 'ArrowUp') {
      e.preventDefault()
      if (history.length > 0) {
        const newIndex = historyIndex === -1 ? history.length - 1 : Math.max(0, historyIndex - 1)
        setHistoryIndex(newIndex)
        setInput(history[newIndex])
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault()
      if (historyIndex !== -1) {
        const newIndex = historyIndex + 1
        if (newIndex >= history.length) {
          setHistoryIndex(-1)
          setInput('')
        } else {
          setHistoryIndex(newIndex)
          setInput(history[newIndex])
        }
      }
    }
  }

  if (!isOpen) return null

  return (
    <div className={`fixed bottom-0 left-0 right-0 bg-background-secondary border-t border-border z-40 transition-all duration-200 ${
      isMinimized ? 'h-10' : 'h-64'
    }`}>
      {/* Terminal Header */}
      <div className="flex items-center justify-between px-4 py-2 bg-background-tertiary border-b border-border">
        <div className="flex items-center gap-2">
          <TerminalIcon size={16} />
          <span className="text-sm font-medium">Terminal</span>
        </div>
        <div className="flex items-center gap-1">
          <button
            onClick={() => setIsMinimized(!isMinimized)}
            className="p-1 hover:bg-background rounded transition-colors"
            title={isMinimized ? 'Maximize' : 'Minimize'}
          >
            {isMinimized ? <Maximize2 size={14} /> : <Minimize2 size={14} />}
          </button>
          <button
            onClick={onClose}
            className="p-1 hover:bg-background rounded transition-colors"
            title="Close terminal"
          >
            <X size={14} />
          </button>
        </div>
      </div>

      {/* Terminal Content */}
      {!isMinimized && (
        <div className="h-52 flex flex-col">
          {/* Output */}
          <div 
            ref={outputRef}
            className="flex-1 p-4 overflow-y-auto font-mono text-sm bg-black text-green-400"
          >
            {output.map((line, index) => (
              <div key={index} className="whitespace-pre-wrap">
                {line}
              </div>
            ))}
          </div>

          {/* Input */}
          <div className="flex items-center px-4 py-2 bg-black border-t border-gray-700">
            <span className="text-green-400 font-mono text-sm mr-2">$</span>
            <input
              ref={inputRef}
              type="text"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              disabled={isExecuting}
              className="flex-1 bg-transparent text-green-400 font-mono text-sm outline-none disabled:opacity-50"
              placeholder={isExecuting ? "Executing..." : "Type a command..."}
            />
            {isExecuting && (
              <div className="ml-2 w-4 h-4 border-2 border-green-400/30 border-t-green-400 rounded-full animate-spin" />
            )}
          </div>
        </div>
      )}
    </div>
  )
}
