import { useState } from 'react'
import { Download, Upload, Share2, Folder<PERSON><PERSON> } from 'lucide-react'
import { useChat } from '@/contexts/ChatContext'
import { exportProject, importProject } from '@/lib/project'

export function ProjectActions() {
  const { chatState, setCurrentProject } = useChat()
  const [isImporting, setIsImporting] = useState(false)

  const handleExportProject = () => {
    if (!chatState.currentProject) return

    const exportData = exportProject(chatState.currentProject)
    const blob = new Blob([exportData], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    
    const a = document.createElement('a')
    a.href = url
    a.download = `${chatState.currentProject.name.replace(/\s+/g, '-').toLowerCase()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const handleImportProject = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.json'
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (!file) return

      setIsImporting(true)
      try {
        const text = await file.text()
        const project = importProject(text)
        setCurrentProject(project)
      } catch (error) {
        console.error('Import error:', error)
        alert('Failed to import project. Please check the file format.')
      } finally {
        setIsImporting(false)
      }
    }
    input.click()
  }

  const handleShareProject = async () => {
    if (!chatState.currentProject) return

    const exportData = exportProject(chatState.currentProject)
    
    if (navigator.share) {
      try {
        const blob = new Blob([exportData], { type: 'application/json' })
        const file = new File([blob], `${chatState.currentProject.name}.json`, {
          type: 'application/json',
        })
        
        await navigator.share({
          title: `Lovable.DIY Project: ${chatState.currentProject.name}`,
          text: `Check out this project created with Lovable.DIY!`,
          files: [file],
        })
      } catch (error) {
        // Fallback to copying to clipboard
        await navigator.clipboard.writeText(exportData)
        alert('Project data copied to clipboard!')
      }
    } else {
      // Fallback to copying to clipboard
      await navigator.clipboard.writeText(exportData)
      alert('Project data copied to clipboard!')
    }
  }

  const handleOpenFolder = () => {
    // In a real implementation, this would open the project folder
    // For now, we'll just show an alert
    alert('This feature would open the project folder in your file manager.')
  }

  if (!chatState.currentProject) {
    return null
  }

  return (
    <div className="flex items-center gap-2">
      <button
        onClick={handleExportProject}
        className="flex items-center gap-1 px-2 py-1 text-sm text-foreground-secondary hover:text-foreground hover:bg-background-tertiary rounded transition-colors"
        title="Export project"
      >
        <Download size={14} />
        Export
      </button>
      
      <button
        onClick={handleImportProject}
        disabled={isImporting}
        className="flex items-center gap-1 px-2 py-1 text-sm text-foreground-secondary hover:text-foreground hover:bg-background-tertiary disabled:opacity-50 rounded transition-colors"
        title="Import project"
      >
        <Upload size={14} />
        {isImporting ? 'Importing...' : 'Import'}
      </button>
      
      <button
        onClick={handleShareProject}
        className="flex items-center gap-1 px-2 py-1 text-sm text-foreground-secondary hover:text-foreground hover:bg-background-tertiary rounded transition-colors"
        title="Share project"
      >
        <Share2 size={14} />
        Share
      </button>
      
      <button
        onClick={handleOpenFolder}
        className="flex items-center gap-1 px-2 py-1 text-sm text-foreground-secondary hover:text-foreground hover:bg-background-tertiary rounded transition-colors"
        title="Open in file manager"
      >
        <FolderOpen size={14} />
        Open
      </button>
    </div>
  )
}
