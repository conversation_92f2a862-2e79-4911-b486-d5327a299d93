import { useState } from 'react'
import { X, Eye, EyeOff, Check, AlertCircle } from 'lucide-react'
import * as Dialog from '@radix-ui/react-dialog'
import * as Tabs from '@radix-ui/react-tabs'
import { useSettings } from '@/contexts/SettingsContext'
import { AI_PROVIDERS } from '@/lib/constants'
import { aiProviderService } from '@/lib/ai/providers'

interface SettingsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function SettingsDialog({ open, onOpenChange }: SettingsDialogProps) {
  const { settings, updateSettings, updateApiKey, getApiKey } = useSettings()
  const [showApiKeys, setShowApiKeys] = useState<Record<string, boolean>>({})
  const [testingConnection, setTestingConnection] = useState<Record<string, boolean>>({})
  const [connectionStatus, setConnectionStatus] = useState<Record<string, 'success' | 'error' | null>>({})

  const toggleApiKeyVisibility = (providerId: string) => {
    setShowApiKeys(prev => ({
      ...prev,
      [providerId]: !prev[providerId],
    }))
  }

  const handleApiKeyChange = (providerId: string, apiKey: string) => {
    updateApiKey(providerId, apiKey)
    // Clear connection status when API key changes
    setConnectionStatus(prev => ({
      ...prev,
      [providerId]: null,
    }))
  }

  const testConnection = async (providerId: string) => {
    const apiKey = getApiKey(providerId)
    if (!apiKey) return

    setTestingConnection(prev => ({ ...prev, [providerId]: true }))
    
    try {
      const isValid = await aiProviderService.testConnection(providerId, apiKey)
      setConnectionStatus(prev => ({
        ...prev,
        [providerId]: isValid ? 'success' : 'error',
      }))
    } catch (error) {
      setConnectionStatus(prev => ({
        ...prev,
        [providerId]: 'error',
      }))
    } finally {
      setTestingConnection(prev => ({ ...prev, [providerId]: false }))
    }
  }

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50" />
        <Dialog.Content className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-2xl max-h-[80vh] bg-background-secondary border border-border rounded-lg shadow-xl z-50">
          <div className="flex items-center justify-between p-6 border-b border-border">
            <Dialog.Title className="text-lg font-semibold">Settings</Dialog.Title>
            <Dialog.Close className="p-1 rounded-md hover:bg-background-tertiary transition-colors">
              <X size={20} />
            </Dialog.Close>
          </div>

          <Tabs.Root defaultValue="providers" className="flex-1">
            <Tabs.List className="flex border-b border-border">
              <Tabs.Trigger
                value="providers"
                className="px-4 py-2 text-sm font-medium data-[state=active]:text-primary-400 data-[state=active]:border-b-2 data-[state=active]:border-primary-400 hover:text-foreground transition-colors"
              >
                AI Providers
              </Tabs.Trigger>
              <Tabs.Trigger
                value="editor"
                className="px-4 py-2 text-sm font-medium data-[state=active]:text-primary-400 data-[state=active]:border-b-2 data-[state=active]:border-primary-400 hover:text-foreground transition-colors"
              >
                Editor
              </Tabs.Trigger>
            </Tabs.List>

            <div className="p-6 max-h-[60vh] overflow-y-auto">
              <Tabs.Content value="providers" className="space-y-6">
                {AI_PROVIDERS.map((provider) => {
                  const apiKey = getApiKey(provider.id) || ''
                  const isVisible = showApiKeys[provider.id]
                  const isTesting = testingConnection[provider.id]
                  const status = connectionStatus[provider.id]

                  return (
                    <div key={provider.id} className="space-y-3">
                      <div className="flex items-center gap-3">
                        <span className="text-2xl">{provider.icon}</span>
                        <div>
                          <h3 className="font-medium">{provider.name}</h3>
                          <p className="text-sm text-foreground-secondary">{provider.description}</p>
                        </div>
                      </div>

                      {provider.requiresApiKey && (
                        <div className="space-y-2">
                          <label className="text-sm font-medium">API Key</label>
                          <div className="flex gap-2">
                            <div className="relative flex-1">
                              <input
                                type={isVisible ? 'text' : 'password'}
                                value={apiKey}
                                onChange={(e) => handleApiKeyChange(provider.id, e.target.value)}
                                placeholder="Enter your API key"
                                className="w-full px-3 py-2 bg-background border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 pr-10"
                              />
                              <button
                                type="button"
                                onClick={() => toggleApiKeyVisibility(provider.id)}
                                className="absolute right-2 top-1/2 -translate-y-1/2 p-1 text-foreground-secondary hover:text-foreground"
                              >
                                {isVisible ? <EyeOff size={16} /> : <Eye size={16} />}
                              </button>
                            </div>
                            <button
                              onClick={() => testConnection(provider.id)}
                              disabled={!apiKey || isTesting}
                              className="px-3 py-2 bg-primary-600 hover:bg-primary-700 disabled:bg-background-tertiary disabled:text-foreground-secondary text-white rounded-md transition-colors flex items-center gap-2"
                            >
                              {isTesting ? (
                                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                              ) : (
                                'Test'
                              )}
                            </button>
                          </div>
                          
                          {status && (
                            <div className={`flex items-center gap-2 text-sm ${
                              status === 'success' ? 'text-green-400' : 'text-red-400'
                            }`}>
                              {status === 'success' ? <Check size={16} /> : <AlertCircle size={16} />}
                              {status === 'success' ? 'Connection successful' : 'Connection failed'}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  )
                })}
              </Tabs.Content>

              <Tabs.Content value="editor" className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Font Size</label>
                  <input
                    type="range"
                    min="10"
                    max="24"
                    value={settings.editorSettings.fontSize}
                    onChange={(e) => updateSettings({
                      editorSettings: {
                        ...settings.editorSettings,
                        fontSize: parseInt(e.target.value),
                      },
                    })}
                    className="w-full"
                  />
                  <div className="text-sm text-foreground-secondary mt-1">
                    {settings.editorSettings.fontSize}px
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Tab Size</label>
                  <select
                    value={settings.editorSettings.tabSize}
                    onChange={(e) => updateSettings({
                      editorSettings: {
                        ...settings.editorSettings,
                        tabSize: parseInt(e.target.value),
                      },
                    })}
                    className="w-full px-3 py-2 bg-background border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value={2}>2 spaces</option>
                    <option value={4}>4 spaces</option>
                    <option value={8}>8 spaces</option>
                  </select>
                </div>
              </Tabs.Content>
            </div>
          </Tabs.Root>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  )
}
