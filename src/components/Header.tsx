import { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { Settings, Home } from 'lucide-react'
import { SettingsDialog } from './SettingsDialog'

export function Header() {
  const location = useLocation()
  const [showSettings, setShowSettings] = useState(false)
  const isWorkspace = location.pathname === '/workspace'

  return (
    <>
      <header className="h-14 border-b border-border bg-background-secondary/50 backdrop-blur-sm">
        <div className="h-full px-4 flex items-center justify-between">
          {/* Logo and Navigation */}
          <div className="flex items-center gap-6">
            <Link to="/" className="flex items-center gap-2 font-bold text-lg">
              <span className="text-2xl">❤️</span>
              <span>Lovable</span>
              <span className="text-primary-400">.DIY</span>
            </Link>
            
            {isWorkspace && (
              <Link
                to="/"
                className="flex items-center gap-2 px-3 py-1.5 rounded-md text-sm text-foreground-secondary hover:text-foreground hover:bg-background-tertiary transition-colors"
              >
                <Home size={16} />
                Home
              </Link>
            )}
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">
            <button
              onClick={() => setShowSettings(true)}
              className="flex items-center gap-2 px-3 py-1.5 rounded-md text-sm text-foreground-secondary hover:text-foreground hover:bg-background-tertiary transition-colors"
            >
              <Settings size={16} />
              Settings
            </button>
          </div>
        </div>
      </header>

      <SettingsDialog open={showSettings} onOpenChange={setShowSettings} />
    </>
  )
}
