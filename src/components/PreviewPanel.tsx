import { useState, useEffect, useRef } from 'react'
import { RefreshCw, ExternalLink, Smartphone, Tablet, Monitor } from 'lucide-react'
import { Project } from '@/types'

interface PreviewPanelProps {
  project?: Project
}

export function PreviewPanel({ project }: PreviewPanelProps) {
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')
  const [isLoading, setIsLoading] = useState(false)
  const iframeRef = useRef<HTMLIFrameElement>(null)

  const getPreviewDimensions = () => {
    switch (previewMode) {
      case 'mobile':
        return { width: '375px', height: '667px' }
      case 'tablet':
        return { width: '768px', height: '1024px' }
      default:
        return { width: '100%', height: '100%' }
    }
  }

  const generatePreviewHTML = () => {
    if (!project) return ''

    // Find the main HTML file
    const htmlFile = project.files.find(f => f.name === 'index.html' || f.name.endsWith('.html'))
    const cssFiles = project.files.filter(f => f.name.endsWith('.css'))
    const jsFiles = project.files.filter(f => f.name.endsWith('.js'))

    if (!htmlFile) {
      return `
        <!DOCTYPE html>
        <html>
          <head>
            <title>Preview</title>
            <style>
              body { 
                font-family: system-ui, sans-serif; 
                padding: 2rem; 
                text-align: center;
                color: #666;
              }
            </style>
          </head>
          <body>
            <h2>No HTML file found</h2>
            <p>Create an index.html file to see the preview</p>
          </body>
        </html>
      `
    }

    let html = htmlFile.content || ''

    // Inject CSS files
    const cssInjects = cssFiles.map(file => 
      `<style>${file.content}</style>`
    ).join('\n')

    // Inject JS files
    const jsInjects = jsFiles.map(file => 
      `<script>${file.content}</script>`
    ).join('\n')

    // Insert CSS and JS into the HTML
    if (html.includes('</head>')) {
      html = html.replace('</head>', `${cssInjects}\n</head>`)
    } else {
      html = `<head>${cssInjects}</head>${html}`
    }

    if (html.includes('</body>')) {
      html = html.replace('</body>', `${jsInjects}\n</body>`)
    } else {
      html = `${html}\n${jsInjects}`
    }

    return html
  }

  const refreshPreview = () => {
    if (!iframeRef.current) return
    
    setIsLoading(true)
    const html = generatePreviewHTML()
    const blob = new Blob([html], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    
    iframeRef.current.src = url
    
    // Clean up the blob URL after a delay
    setTimeout(() => {
      URL.revokeObjectURL(url)
      setIsLoading(false)
    }, 1000)
  }

  const openInNewTab = () => {
    const html = generatePreviewHTML()
    const blob = new Blob([html], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    window.open(url, '_blank')
    
    // Clean up after opening
    setTimeout(() => URL.revokeObjectURL(url), 1000)
  }

  useEffect(() => {
    if (project) {
      refreshPreview()
    }
  }, [project])

  const dimensions = getPreviewDimensions()

  return (
    <div className="h-full flex flex-col bg-background">
      {/* Preview Header */}
      <div className="flex items-center justify-between px-4 py-2 border-b border-border bg-background-secondary">
        <div className="flex items-center gap-4">
          <span className="font-medium">Preview</span>
          
          {/* Device Mode Selector */}
          <div className="flex items-center gap-1 bg-background rounded-md p-1">
            <button
              onClick={() => setPreviewMode('desktop')}
              className={`p-1.5 rounded transition-colors ${
                previewMode === 'desktop' 
                  ? 'bg-primary-600 text-white' 
                  : 'text-foreground-secondary hover:text-foreground'
              }`}
              title="Desktop view"
            >
              <Monitor size={16} />
            </button>
            <button
              onClick={() => setPreviewMode('tablet')}
              className={`p-1.5 rounded transition-colors ${
                previewMode === 'tablet' 
                  ? 'bg-primary-600 text-white' 
                  : 'text-foreground-secondary hover:text-foreground'
              }`}
              title="Tablet view"
            >
              <Tablet size={16} />
            </button>
            <button
              onClick={() => setPreviewMode('mobile')}
              className={`p-1.5 rounded transition-colors ${
                previewMode === 'mobile' 
                  ? 'bg-primary-600 text-white' 
                  : 'text-foreground-secondary hover:text-foreground'
              }`}
              title="Mobile view"
            >
              <Smartphone size={16} />
            </button>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={refreshPreview}
            disabled={isLoading}
            className="flex items-center gap-1 px-2 py-1 text-sm text-foreground-secondary hover:text-foreground disabled:opacity-50 transition-colors"
            title="Refresh preview"
          >
            <RefreshCw size={14} className={isLoading ? 'animate-spin' : ''} />
            Refresh
          </button>
          <button
            onClick={openInNewTab}
            className="flex items-center gap-1 px-2 py-1 text-sm text-foreground-secondary hover:text-foreground transition-colors"
            title="Open in new tab"
          >
            <ExternalLink size={14} />
            Open
          </button>
        </div>
      </div>

      {/* Preview Content */}
      <div className="flex-1 overflow-auto bg-gray-100 p-4">
        {project ? (
          <div className="h-full flex items-center justify-center">
            <div 
              className="bg-white shadow-lg rounded-lg overflow-hidden"
              style={{
                width: dimensions.width,
                height: dimensions.height,
                maxWidth: '100%',
                maxHeight: '100%',
              }}
            >
              <iframe
                ref={iframeRef}
                className="w-full h-full border-none"
                title="Preview"
                sandbox="allow-scripts allow-same-origin"
              />
            </div>
          </div>
        ) : (
          <div className="h-full flex items-center justify-center">
            <div className="text-center text-gray-500">
              <div className="text-4xl mb-4">👁️</div>
              <p className="text-lg font-medium mb-2">No preview available</p>
              <p className="text-sm">Create a project to see the preview</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
