import { useState, useEffect, useRef } from 'react'
import { RefreshCw, ExternalLink, Smartphone, Tablet, Monitor } from 'lucide-react'
import { Project } from '@/types'

interface PreviewPanelProps {
  project?: Project
}

export function PreviewPanel({ project }: PreviewPanelProps) {
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')
  const [isLoading, setIsLoading] = useState(false)
  const iframeRef = useRef<HTMLIFrameElement>(null)

  const getPreviewDimensions = () => {
    switch (previewMode) {
      case 'mobile':
        return { width: '375px', height: '667px' }
      case 'tablet':
        return { width: '768px', height: '1024px' }
      default:
        return { width: '100%', height: '100%' }
    }
  }

  const generatePreviewHTML = () => {
    if (!project) return ''

    // Find the main HTML file
    const htmlFile = project.files.find(f =>
      f.name === 'index.html' ||
      f.name === 'main.html' ||
      f.name.endsWith('.html')
    )

    const cssFiles = project.files.filter(f =>
      f.name.endsWith('.css') ||
      f.name.endsWith('.scss') ||
      f.name.endsWith('.sass')
    )

    const jsFiles = project.files.filter(f =>
      f.name.endsWith('.js') ||
      f.name.endsWith('.jsx') ||
      f.name.endsWith('.ts') ||
      f.name.endsWith('.tsx')
    )

    // If no HTML file, try to create a basic one from other files
    if (!htmlFile) {
      // Check if this is a React project
      const reactFiles = project.files.filter(f =>
        f.name.endsWith('.jsx') || f.name.endsWith('.tsx')
      )

      if (reactFiles.length > 0) {
        return generateReactPreview(reactFiles, cssFiles)
      }

      // Check if this is a simple CSS/JS project
      if (cssFiles.length > 0 || jsFiles.length > 0) {
        return generateBasicPreview(cssFiles, jsFiles)
      }

      return `
        <!DOCTYPE html>
        <html lang="en">
          <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Preview</title>
            <style>
              body {
                font-family: system-ui, sans-serif;
                padding: 2rem;
                text-align: center;
                color: #666;
                background: #f5f5f5;
              }
              .container {
                max-width: 600px;
                margin: 0 auto;
                background: white;
                padding: 2rem;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
              }
            </style>
          </head>
          <body>
            <div class="container">
              <h2>No HTML file found</h2>
              <p>Create an <code>index.html</code> file to see the preview</p>
              <p>Or create React components (.jsx/.tsx files) for a React preview</p>
            </div>
          </body>
        </html>
      `
    }

    let html = htmlFile.content || ''

    // Inject CSS files
    const cssInjects = cssFiles.map(file => {
      // Handle different CSS preprocessors
      if (file.name.endsWith('.scss') || file.name.endsWith('.sass')) {
        // For now, treat as regular CSS (in a real app, you'd compile SCSS)
        return `<style>/* ${file.name} */\n${file.content}</style>`
      }
      return `<style>/* ${file.name} */\n${file.content}</style>`
    }).join('\n')

    // Inject JS files
    const jsInjects = jsFiles.map(file => {
      // Handle different JS types
      if (file.name.endsWith('.tsx') || file.name.endsWith('.jsx')) {
        // For JSX/TSX, we'd need a transpiler - for now, show as comment
        return `<script>/* ${file.name} - JSX/TSX files need transpilation */</script>`
      }
      return `<script>/* ${file.name} */\n${file.content}</script>`
    }).join('\n')

    // Ensure proper HTML structure
    if (!html.includes('<!DOCTYPE html>')) {
      html = `<!DOCTYPE html>\n<html lang="en">\n${html}\n</html>`
    }

    // Insert CSS and JS into the HTML
    if (html.includes('</head>')) {
      html = html.replace('</head>', `${cssInjects}\n</head>`)
    } else if (html.includes('<head>')) {
      html = html.replace('<head>', `<head>\n${cssInjects}`)
    } else {
      html = html.replace('<html>', `<html>\n<head>\n${cssInjects}\n</head>`)
    }

    if (html.includes('</body>')) {
      html = html.replace('</body>', `${jsInjects}\n</body>`)
    } else {
      html = `${html}\n${jsInjects}`
    }

    return html
  }

  const generateReactPreview = (reactFiles: any[], cssFiles: any[]) => {
    return `
      <!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>React Preview</title>
          <style>
            body {
              font-family: system-ui, sans-serif;
              padding: 2rem;
              text-align: center;
              color: #666;
              background: #f5f5f5;
            }
            .container {
              max-width: 600px;
              margin: 0 auto;
              background: white;
              padding: 2rem;
              border-radius: 8px;
              box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            ${cssFiles.map(f => f.content).join('\n')}
          </style>
        </head>
        <body>
          <div class="container">
            <h2>React Preview</h2>
            <p>React components detected: ${reactFiles.map(f => f.name).join(', ')}</p>
            <p>React components need transpilation to run in the browser.</p>
            <p>In a full implementation, this would use Babel or similar to transpile JSX.</p>
          </div>
        </body>
      </html>
    `
  }

  const generateBasicPreview = (cssFiles: any[], jsFiles: any[]) => {
    return `
      <!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Preview</title>
          <style>
            body {
              font-family: system-ui, sans-serif;
              padding: 2rem;
              margin: 0;
            }
            ${cssFiles.map(f => f.content).join('\n')}
          </style>
        </head>
        <body>
          <div id="app">
            <h1>Welcome to your application!</h1>
            <p>This preview was generated from your CSS and JavaScript files.</p>
          </div>
          <script>
            ${jsFiles.map(f => f.content).join('\n')}
          </script>
        </body>
      </html>
    `
  }

  const refreshPreview = () => {
    if (!iframeRef.current) return

    setIsLoading(true)

    try {
      const html = generatePreviewHTML()
      const blob = new Blob([html], { type: 'text/html' })
      const url = URL.createObjectURL(blob)

      iframeRef.current.src = url

      // Clean up the blob URL after a delay
      setTimeout(() => {
        URL.revokeObjectURL(url)
        setIsLoading(false)
      }, 1000)
    } catch (error) {
      console.error('Preview generation error:', error)
      setIsLoading(false)
    }
  }

  const openInNewTab = () => {
    const html = generatePreviewHTML()
    const blob = new Blob([html], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    window.open(url, '_blank')
    
    // Clean up after opening
    setTimeout(() => URL.revokeObjectURL(url), 1000)
  }

  useEffect(() => {
    if (project) {
      refreshPreview()
    }
  }, [project])

  const dimensions = getPreviewDimensions()

  return (
    <div className="h-full flex flex-col bg-background">
      {/* Preview Header */}
      <div className="flex items-center justify-between px-4 py-2 border-b border-border bg-background-secondary">
        <div className="flex items-center gap-4">
          <span className="font-medium">Preview</span>
          
          {/* Device Mode Selector */}
          <div className="flex items-center gap-1 bg-background rounded-md p-1">
            <button
              onClick={() => setPreviewMode('desktop')}
              className={`p-1.5 rounded transition-colors ${
                previewMode === 'desktop' 
                  ? 'bg-primary-600 text-white' 
                  : 'text-foreground-secondary hover:text-foreground'
              }`}
              title="Desktop view"
            >
              <Monitor size={16} />
            </button>
            <button
              onClick={() => setPreviewMode('tablet')}
              className={`p-1.5 rounded transition-colors ${
                previewMode === 'tablet' 
                  ? 'bg-primary-600 text-white' 
                  : 'text-foreground-secondary hover:text-foreground'
              }`}
              title="Tablet view"
            >
              <Tablet size={16} />
            </button>
            <button
              onClick={() => setPreviewMode('mobile')}
              className={`p-1.5 rounded transition-colors ${
                previewMode === 'mobile' 
                  ? 'bg-primary-600 text-white' 
                  : 'text-foreground-secondary hover:text-foreground'
              }`}
              title="Mobile view"
            >
              <Smartphone size={16} />
            </button>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={refreshPreview}
            disabled={isLoading}
            className="flex items-center gap-1 px-2 py-1 text-sm text-foreground-secondary hover:text-foreground disabled:opacity-50 transition-colors"
            title="Refresh preview"
          >
            <RefreshCw size={14} className={isLoading ? 'animate-spin' : ''} />
            Refresh
          </button>
          <button
            onClick={openInNewTab}
            className="flex items-center gap-1 px-2 py-1 text-sm text-foreground-secondary hover:text-foreground transition-colors"
            title="Open in new tab"
          >
            <ExternalLink size={14} />
            Open
          </button>
        </div>
      </div>

      {/* Preview Content */}
      <div className="flex-1 overflow-auto bg-gray-100 p-4">
        {project ? (
          <div className="h-full flex items-center justify-center">
            <div 
              className="bg-white shadow-lg rounded-lg overflow-hidden"
              style={{
                width: dimensions.width,
                height: dimensions.height,
                maxWidth: '100%',
                maxHeight: '100%',
              }}
            >
              <iframe
                ref={iframeRef}
                className="w-full h-full border-none"
                title="Preview"
                sandbox="allow-scripts allow-same-origin"
              />
            </div>
          </div>
        ) : (
          <div className="h-full flex items-center justify-center">
            <div className="text-center text-gray-500">
              <div className="text-4xl mb-4">👁️</div>
              <p className="text-lg font-medium mb-2">No preview available</p>
              <p className="text-sm">Create a project to see the preview</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
