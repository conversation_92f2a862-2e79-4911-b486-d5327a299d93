import { useState, useRef, useEffect } from 'react'
import { Send, Paperclip, Mic } from 'lucide-react'

interface ChatInputProps {
  value: string
  onChange: (value: string) => void
  onSubmit: (message: string) => void
  placeholder?: string
  disabled?: boolean
}

export function ChatInput({ value, onChange, onSubmit, placeholder, disabled }: ChatInputProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const [isRecording, setIsRecording] = useState(false)

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current
    if (textarea) {
      textarea.style.height = 'auto'
      textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`
    }
  }, [value])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (value.trim() && !disabled) {
      onSubmit(value.trim())
      onChange('')
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  const handleFileUpload = () => {
    // TODO: Implement file upload
    console.log('File upload clicked')
  }

  const handleVoiceInput = () => {
    // TODO: Implement voice input
    setIsRecording(!isRecording)
    console.log('Voice input clicked')
  }

  return (
    <form onSubmit={handleSubmit} className="w-full">
      <div className="relative glass-effect rounded-2xl border border-white/20 overflow-hidden">
        <div className="flex items-end gap-2 p-4">
          {/* File Upload Button */}
          <button
            type="button"
            onClick={handleFileUpload}
            className="flex-shrink-0 p-2 text-foreground-secondary hover:text-foreground hover:bg-white/10 rounded-lg transition-colors"
            title="Attach file"
          >
            <Paperclip size={20} />
          </button>

          {/* Text Input */}
          <textarea
            ref={textareaRef}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder || "Ask Lovable to create something..."}
            disabled={disabled}
            className="flex-1 bg-transparent text-foreground placeholder-foreground-secondary resize-none outline-none min-h-[24px] max-h-[200px] py-1"
            rows={1}
          />

          {/* Voice Input Button */}
          <button
            type="button"
            onClick={handleVoiceInput}
            className={`flex-shrink-0 p-2 rounded-lg transition-colors ${
              isRecording
                ? 'text-red-400 bg-red-400/20'
                : 'text-foreground-secondary hover:text-foreground hover:bg-white/10'
            }`}
            title="Voice input"
          >
            <Mic size={20} />
          </button>

          {/* Send Button */}
          <button
            type="submit"
            disabled={!value.trim() || disabled}
            className="flex-shrink-0 p-2 bg-primary-600 hover:bg-primary-700 disabled:bg-background-tertiary disabled:text-foreground-secondary text-white rounded-lg transition-colors"
            title="Send message"
          >
            <Send size={20} />
          </button>
        </div>

        {/* Character count or other info */}
        {value.length > 0 && (
          <div className="px-4 pb-2">
            <div className="text-xs text-foreground-secondary">
              {value.length} characters
            </div>
          </div>
        )}
      </div>
    </form>
  )
}
