import { ChatMessage } from '@/types'
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'lucide-react'
import { useState } from 'react'

interface MessageListProps {
  messages: ChatMessage[]
  isLoading: boolean
}

export function MessageList({ messages, isLoading }: MessageListProps) {
  const [copiedMessageId, setCopiedMessageId] = useState<string | null>(null)

  const copyToClipboard = async (text: string, messageId: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedMessageId(messageId)
      setTimeout(() => setCopiedMessageId(null), 2000)
    } catch (error) {
      console.error('Failed to copy text:', error)
    }
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  return (
    <div className="space-y-4">
      {messages.map((message) => (
        <div
          key={message.id}
          className={`flex gap-3 ${
            message.role === 'user' ? 'justify-end' : 'justify-start'
          }`}
        >
          {message.role === 'assistant' && (
            <div className="flex-shrink-0 w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
              <Bot size={16} className="text-white" />
            </div>
          )}
          
          <div
            className={`max-w-[80%] rounded-lg p-3 ${
              message.role === 'user'
                ? 'bg-primary-600 text-white'
                : 'bg-background-tertiary text-foreground'
            }`}
          >
            <div className="prose prose-sm max-w-none">
              <MessageContent content={message.content} />
            </div>
            
            <div className="flex items-center justify-between mt-2 pt-2 border-t border-white/10">
              <div className="text-xs opacity-70">
                {formatTime(message.timestamp)}
                {message.metadata?.model && (
                  <span className="ml-2">• {message.metadata.model}</span>
                )}
              </div>
              
              <button
                onClick={() => copyToClipboard(message.content, message.id)}
                className="p-1 opacity-70 hover:opacity-100 transition-opacity"
                title="Copy message"
              >
                {copiedMessageId === message.id ? (
                  <Check size={14} />
                ) : (
                  <Copy size={14} />
                )}
              </button>
            </div>
          </div>
          
          {message.role === 'user' && (
            <div className="flex-shrink-0 w-8 h-8 bg-background-tertiary rounded-full flex items-center justify-center">
              <User size={16} className="text-foreground" />
            </div>
          )}
        </div>
      ))}
      
      {isLoading && (
        <div className="flex gap-3 justify-start">
          <div className="flex-shrink-0 w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
            <Bot size={16} className="text-white" />
          </div>
          <div className="bg-background-tertiary rounded-lg p-3">
            <div className="flex items-center gap-2">
              <div className="flex gap-1">
                <div className="w-2 h-2 bg-foreground-secondary rounded-full animate-pulse" />
                <div className="w-2 h-2 bg-foreground-secondary rounded-full animate-pulse delay-100" />
                <div className="w-2 h-2 bg-foreground-secondary rounded-full animate-pulse delay-200" />
              </div>
              <span className="text-sm text-foreground-secondary">Thinking...</span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

interface MessageContentProps {
  content: string
}

function MessageContent({ content }: MessageContentProps) {
  // Simple markdown-like rendering for code blocks
  const renderContent = (text: string) => {
    // Split by code blocks
    const parts = text.split(/(```[\s\S]*?```)/g)
    
    return parts.map((part, index) => {
      if (part.startsWith('```') && part.endsWith('```')) {
        // Extract language and code
        const lines = part.slice(3, -3).split('\n')
        const language = lines[0].trim()
        const code = lines.slice(1).join('\n')
        
        return (
          <pre key={index} className="bg-background rounded p-3 overflow-x-auto my-2">
            {language && (
              <div className="text-xs text-foreground-secondary mb-2 font-mono">
                {language}
              </div>
            )}
            <code className="text-sm font-mono">{code}</code>
          </pre>
        )
      }
      
      // Regular text with basic formatting
      return (
        <div key={index} className="whitespace-pre-wrap">
          {part}
        </div>
      )
    })
  }

  return <div>{renderContent(content)}</div>
}
