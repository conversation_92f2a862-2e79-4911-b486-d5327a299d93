import { useEffect, useRef, useState } from 'react'
import { EditorView, basicSetup } from 'codemirror'
import { EditorState } from '@codemirror/state'
import { javascript } from '@codemirror/lang-javascript'
import { html } from '@codemirror/lang-html'
import { css } from '@codemirror/lang-css'
import { json } from '@codemirror/lang-json'
import { markdown } from '@codemirror/lang-markdown'
import { python } from '@codemirror/lang-python'
import { vscodeDark } from '@uiw/codemirror-theme-vscode'
import { FileItem } from '@/types'
import { useChat } from '@/contexts/ChatContext'
import { useSettings } from '@/contexts/SettingsContext'
import { FILE_EXTENSIONS } from '@/lib/constants'
import { Save, RotateCcw } from 'lucide-react'

interface CodeEditorProps {
  file?: FileItem
}

export function CodeEditor({ file }: CodeEditorProps) {
  const { updateFileContent } = useChat()
  const { settings } = useSettings()
  const editorRef = useRef<HTMLDivElement>(null)
  const viewRef = useRef<EditorView | null>(null)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  const getLanguageExtension = (filename: string) => {
    const extension = '.' + filename.split('.').pop()?.toLowerCase()
    const language = FILE_EXTENSIONS[extension as keyof typeof FILE_EXTENSIONS]
    
    switch (language) {
      case 'javascript':
      case 'typescript':
        return javascript({ jsx: true, typescript: language === 'typescript' })
      case 'html':
        return html()
      case 'css':
      case 'scss':
      case 'sass':
        return css()
      case 'json':
        return json()
      case 'markdown':
        return markdown()
      case 'python':
        return python()
      default:
        return []
    }
  }

  useEffect(() => {
    if (!editorRef.current) return

    // Clean up previous editor
    if (viewRef.current) {
      viewRef.current.destroy()
    }

    if (!file) {
      // Show empty state
      editorRef.current.innerHTML = `
        <div class="h-full flex items-center justify-center text-foreground-secondary">
          <div class="text-center">
            <div class="text-4xl mb-4">📝</div>
            <p class="text-lg font-medium mb-2">No file selected</p>
            <p class="text-sm">Select a file from the explorer to start editing</p>
          </div>
        </div>
      `
      return
    }

    const languageExtension = getLanguageExtension(file.name)
    
    const state = EditorState.create({
      doc: file.content || '',
      extensions: [
        basicSetup,
        languageExtension,
        vscodeDark,
        EditorView.theme({
          '&': {
            fontSize: `${settings.editorSettings.fontSize}px`,
            fontFamily: 'JetBrains Mono, Consolas, Monaco, monospace',
          },
          '.cm-content': {
            padding: '16px',
            minHeight: '100%',
          },
          '.cm-focused': {
            outline: 'none',
          },
          '.cm-editor': {
            height: '100%',
          },
          '.cm-scroller': {
            fontFamily: 'inherit',
          },
        }),
        EditorView.updateListener.of((update) => {
          if (update.docChanged) {
            const newContent = update.state.doc.toString()
            setHasUnsavedChanges(newContent !== file.content)
          }
        }),
      ],
    })

    const view = new EditorView({
      state,
      parent: editorRef.current,
    })

    viewRef.current = view

    return () => {
      view.destroy()
    }
  }, [file, settings.editorSettings.fontSize])

  const handleSave = () => {
    if (!file || !viewRef.current) return
    
    const content = viewRef.current.state.doc.toString()
    updateFileContent(file.path, content)
    setHasUnsavedChanges(false)
  }

  const handleRevert = () => {
    if (!file || !viewRef.current) return
    
    const transaction = viewRef.current.state.update({
      changes: {
        from: 0,
        to: viewRef.current.state.doc.length,
        insert: file.content || '',
      },
    })
    
    viewRef.current.dispatch(transaction)
    setHasUnsavedChanges(false)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if ((e.ctrlKey || e.metaKey) && e.key === 's') {
      e.preventDefault()
      handleSave()
    }
  }

  return (
    <div className="h-full flex flex-col bg-background">
      {/* Editor Header */}
      {file && (
        <div className="flex items-center justify-between px-4 py-2 border-b border-border bg-background-secondary">
          <div className="flex items-center gap-2">
            <span className="font-medium">{file.name}</span>
            {hasUnsavedChanges && (
              <div className="w-2 h-2 bg-orange-400 rounded-full" title="Unsaved changes" />
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={handleRevert}
              disabled={!hasUnsavedChanges}
              className="flex items-center gap-1 px-2 py-1 text-sm text-foreground-secondary hover:text-foreground disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              title="Revert changes"
            >
              <RotateCcw size={14} />
              Revert
            </button>
            <button
              onClick={handleSave}
              disabled={!hasUnsavedChanges}
              className="flex items-center gap-1 px-3 py-1 bg-primary-600 hover:bg-primary-700 disabled:bg-background-tertiary disabled:text-foreground-secondary text-white text-sm rounded transition-colors"
              title="Save file (Ctrl+S)"
            >
              <Save size={14} />
              Save
            </button>
          </div>
        </div>
      )}

      {/* Editor */}
      <div 
        ref={editorRef} 
        className="flex-1 overflow-hidden code-editor"
        onKeyDown={handleKeyDown}
      />
    </div>
  )
}
