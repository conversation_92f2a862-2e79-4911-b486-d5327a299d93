import { useState } from 'react'
import { 
  Folder, 
  FolderOpen, 
  File, 
  Plus, 
  MoreHorizontal,
  FileText,
  Code,
  Image,
  Settings
} from 'lucide-react'
import { FileItem } from '@/types'
import { useChat } from '@/contexts/ChatContext'

export function FileExplorer() {
  const { chatState, setSelectedFile } = useChat()
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set())

  const toggleFolder = (path: string) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev)
      if (newSet.has(path)) {
        newSet.delete(path)
      } else {
        newSet.add(path)
      }
      return newSet
    })
  }

  const getFileIcon = (file: FileItem) => {
    if (file.type === 'folder') {
      return expandedFolders.has(file.path) ? (
        <FolderOpen size={16} className="text-blue-400" />
      ) : (
        <Folder size={16} className="text-blue-400" />
      )
    }

    const extension = file.name.split('.').pop()?.toLowerCase()
    switch (extension) {
      case 'js':
      case 'jsx':
      case 'ts':
      case 'tsx':
        return <Code size={16} className="text-yellow-400" />
      case 'html':
      case 'css':
      case 'scss':
        return <Code size={16} className="text-green-400" />
      case 'json':
        return <Settings size={16} className="text-orange-400" />
      case 'md':
        return <FileText size={16} className="text-blue-400" />
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'svg':
        return <Image size={16} className="text-purple-400" />
      default:
        return <File size={16} className="text-foreground-secondary" />
    }
  }

  const renderFileTree = (files: FileItem[], depth = 0) => {
    return files.map((file) => (
      <div key={file.path}>
        <div
          className={`flex items-center gap-2 px-2 py-1 hover:bg-background-tertiary cursor-pointer group ${
            chatState.selectedFile?.path === file.path ? 'bg-primary-600/20 text-primary-400' : ''
          }`}
          style={{ paddingLeft: `${8 + depth * 16}px` }}
          onClick={() => {
            if (file.type === 'folder') {
              toggleFolder(file.path)
            } else {
              setSelectedFile(file)
            }
          }}
        >
          {getFileIcon(file)}
          <span className="flex-1 text-sm truncate">{file.name}</span>
          {file.isModified && (
            <div className="w-2 h-2 bg-orange-400 rounded-full" title="Modified" />
          )}
          <button
            className="opacity-0 group-hover:opacity-100 p-1 hover:bg-background rounded transition-opacity"
            onClick={(e) => {
              e.stopPropagation()
              // TODO: Show context menu
            }}
          >
            <MoreHorizontal size={12} />
          </button>
        </div>
        
        {file.type === 'folder' && 
         file.children && 
         expandedFolders.has(file.path) && 
         renderFileTree(file.children, depth + 1)}
      </div>
    ))
  }

  return (
    <div className="h-full flex flex-col bg-background-secondary">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <h2 className="font-semibold">Files</h2>
        <div className="flex items-center gap-1">
          <button
            className="p-1.5 text-foreground-secondary hover:text-foreground hover:bg-background-tertiary rounded-md transition-colors"
            title="New file"
            onClick={() => {
              // TODO: Implement new file creation
            }}
          >
            <Plus size={16} />
          </button>
        </div>
      </div>

      {/* File Tree */}
      <div className="flex-1 overflow-y-auto">
        {chatState.currentProject ? (
          <div className="p-2">
            {renderFileTree(chatState.currentProject.files)}
          </div>
        ) : (
          <div className="h-full flex items-center justify-center">
            <div className="text-center text-foreground-secondary">
              <div className="text-4xl mb-4">📁</div>
              <p className="text-lg font-medium mb-2">No project loaded</p>
              <p className="text-sm">Start a chat to create a project</p>
            </div>
          </div>
        )}
      </div>

      {/* Project Info */}
      {chatState.currentProject && (
        <div className="p-4 border-t border-border">
          <div className="text-sm">
            <div className="font-medium truncate" title={chatState.currentProject.name}>
              {chatState.currentProject.name}
            </div>
            <div className="text-foreground-secondary text-xs mt-1">
              {chatState.currentProject.files.length} files
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
