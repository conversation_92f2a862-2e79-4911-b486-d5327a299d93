import { useState } from 'react'
import { ChevronDown, Key, ExternalLink } from 'lucide-react'
import * as Select from '@radix-ui/react-select'
import { useSettings } from '@/contexts/SettingsContext'
import { AI_PROVIDERS } from '@/lib/constants'

export function ProviderSelector() {
  const { settings, updateSettings, getApiKey } = useSettings()
  const [showApiKeyInput, setShowApiKeyInput] = useState(false)

  const selectedProvider = AI_PROVIDERS.find(p => p.id === settings.selectedProvider)
  const selectedModel = selectedProvider?.models.find(m => m.id === settings.selectedModel)
  const hasApiKey = !!getApiKey(settings.selectedProvider)

  const handleProviderChange = (providerId: string) => {
    const provider = AI_PROVIDERS.find(p => p.id === providerId)
    if (provider) {
      updateSettings({
        selectedProvider: providerId,
        selectedModel: provider.models[0]?.id || '',
      })
    }
  }

  const handleModelChange = (modelId: string) => {
    updateSettings({ selectedModel: modelId })
  }

  return (
    <div className="flex items-center gap-4 p-4 glass-effect rounded-lg">
      {/* Provider Selection */}
      <div className="flex items-center gap-2">
        <span className="text-sm text-foreground-secondary">Provider:</span>
        <Select.Root value={settings.selectedProvider} onValueChange={handleProviderChange}>
          <Select.Trigger className="flex items-center gap-2 px-3 py-2 bg-background-secondary border border-border rounded-md hover:bg-background-tertiary transition-colors">
            <span className="text-lg">{selectedProvider?.icon}</span>
            <Select.Value />
            <ChevronDown size={16} />
          </Select.Trigger>
          <Select.Portal>
            <Select.Content className="bg-background-secondary border border-border rounded-md shadow-lg z-50">
              <Select.Viewport className="p-1">
                {AI_PROVIDERS.map((provider) => (
                  <Select.Item
                    key={provider.id}
                    value={provider.id}
                    className="flex items-center gap-2 px-3 py-2 text-sm rounded cursor-pointer hover:bg-background-tertiary focus:bg-background-tertiary outline-none"
                  >
                    <span className="text-lg">{provider.icon}</span>
                    <div>
                      <div className="font-medium">{provider.name}</div>
                      <div className="text-xs text-foreground-secondary">{provider.description}</div>
                    </div>
                  </Select.Item>
                ))}
              </Select.Viewport>
            </Select.Content>
          </Select.Portal>
        </Select.Root>
      </div>

      {/* Model Selection */}
      {selectedProvider && (
        <div className="flex items-center gap-2">
          <span className="text-sm text-foreground-secondary">Model:</span>
          <Select.Root value={settings.selectedModel} onValueChange={handleModelChange}>
            <Select.Trigger className="flex items-center gap-2 px-3 py-2 bg-background-secondary border border-border rounded-md hover:bg-background-tertiary transition-colors">
              <Select.Value />
              <ChevronDown size={16} />
            </Select.Trigger>
            <Select.Portal>
              <Select.Content className="bg-background-secondary border border-border rounded-md shadow-lg z-50">
                <Select.Viewport className="p-1">
                  {selectedProvider.models.map((model) => (
                    <Select.Item
                      key={model.id}
                      value={model.id}
                      className="px-3 py-2 text-sm rounded cursor-pointer hover:bg-background-tertiary focus:bg-background-tertiary outline-none"
                    >
                      <div>
                        <div className="font-medium">{model.name}</div>
                        <div className="text-xs text-foreground-secondary">
                          {model.maxTokens.toLocaleString()} tokens
                        </div>
                      </div>
                    </Select.Item>
                  ))}
                </Select.Viewport>
              </Select.Content>
            </Select.Portal>
          </Select.Root>
        </div>
      )}

      {/* API Key Status */}
      {selectedProvider?.requiresApiKey && (
        <div className="flex items-center gap-2">
          {hasApiKey ? (
            <div className="flex items-center gap-1 text-green-400 text-sm">
              <Key size={14} />
              <span>API Key Set</span>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1 text-yellow-400 text-sm">
                <Key size={14} />
                <span>API Key Required</span>
              </div>
              {selectedProvider.apiKeyUrl && (
                <a
                  href={selectedProvider.apiKeyUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-1 text-primary-400 hover:text-primary-300 text-sm transition-colors"
                >
                  <span>Get Key</span>
                  <ExternalLink size={12} />
                </a>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  )
}
