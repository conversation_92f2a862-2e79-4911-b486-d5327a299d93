import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ChatInput } from '../ChatInput'

describe('ChatInput', () => {
  const defaultProps = {
    value: '',
    onChange: vi.fn(),
    onSubmit: vi.fn(),
    placeholder: 'Type a message...',
    disabled: false,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders with placeholder text', () => {
    render(<ChatInput {...defaultProps} />)
    
    expect(screen.getByPlaceholderText('Type a message...')).toBeInTheDocument()
  })

  it('displays the current value', () => {
    render(<ChatInput {...defaultProps} value="Hello world" />)
    
    expect(screen.getByDisplayValue('Hello world')).toBeInTheDocument()
  })

  it('calls onChange when typing', async () => {
    const user = userEvent.setup()
    const onChange = vi.fn()
    
    render(<ChatInput {...defaultProps} onChange={onChange} />)
    
    const input = screen.getByRole('textbox')
    await user.type(input, 'Hello')
    
    expect(onChange).toHaveBeenCalledTimes(5) // Once for each character
    expect(onChange).toHaveBeenLastCalledWith('Hello')
  })

  it('calls onSubmit when Enter is pressed', async () => {
    const user = userEvent.setup()
    const onSubmit = vi.fn()
    
    render(<ChatInput {...defaultProps} value="Test message" onSubmit={onSubmit} />)
    
    const input = screen.getByRole('textbox')
    await user.type(input, '{Enter}')
    
    expect(onSubmit).toHaveBeenCalledWith('Test message')
  })

  it('does not submit when Shift+Enter is pressed', async () => {
    const user = userEvent.setup()
    const onSubmit = vi.fn()
    
    render(<ChatInput {...defaultProps} value="Test message" onSubmit={onSubmit} />)
    
    const input = screen.getByRole('textbox')
    await user.type(input, '{Shift>}{Enter}{/Shift}')
    
    expect(onSubmit).not.toHaveBeenCalled()
  })

  it('calls onSubmit when send button is clicked', async () => {
    const user = userEvent.setup()
    const onSubmit = vi.fn()
    
    render(<ChatInput {...defaultProps} value="Test message" onSubmit={onSubmit} />)
    
    const sendButton = screen.getByTitle('Send message')
    await user.click(sendButton)
    
    expect(onSubmit).toHaveBeenCalledWith('Test message')
  })

  it('does not submit empty messages', async () => {
    const user = userEvent.setup()
    const onSubmit = vi.fn()
    
    render(<ChatInput {...defaultProps} value="" onSubmit={onSubmit} />)
    
    const sendButton = screen.getByTitle('Send message')
    await user.click(sendButton)
    
    expect(onSubmit).not.toHaveBeenCalled()
  })

  it('does not submit whitespace-only messages', async () => {
    const user = userEvent.setup()
    const onSubmit = vi.fn()
    
    render(<ChatInput {...defaultProps} value="   " onSubmit={onSubmit} />)
    
    const sendButton = screen.getByTitle('Send message')
    await user.click(sendButton)
    
    expect(onSubmit).not.toHaveBeenCalled()
  })

  it('disables input when disabled prop is true', () => {
    render(<ChatInput {...defaultProps} disabled={true} />)
    
    const input = screen.getByRole('textbox')
    const sendButton = screen.getByTitle('Send message')
    
    expect(input).toBeDisabled()
    expect(sendButton).toBeDisabled()
  })

  it('shows character count when there is content', () => {
    render(<ChatInput {...defaultProps} value="Hello world" />)
    
    expect(screen.getByText('11 characters')).toBeInTheDocument()
  })

  it('does not show character count when empty', () => {
    render(<ChatInput {...defaultProps} value="" />)
    
    expect(screen.queryByText(/characters/)).not.toBeInTheDocument()
  })

  it('has file upload button', () => {
    render(<ChatInput {...defaultProps} />)
    
    expect(screen.getByTitle('Attach file')).toBeInTheDocument()
  })

  it('has voice input button', () => {
    render(<ChatInput {...defaultProps} />)
    
    expect(screen.getByTitle('Voice input')).toBeInTheDocument()
  })

  it('auto-resizes textarea based on content', () => {
    const { rerender } = render(<ChatInput {...defaultProps} value="" />)
    
    const textarea = screen.getByRole('textbox')
    const initialHeight = textarea.style.height
    
    rerender(<ChatInput {...defaultProps} value="Line 1\nLine 2\nLine 3\nLine 4\nLine 5" />)
    
    // Height should change when content increases
    // Note: In a real test environment, you might need to mock scrollHeight
    expect(textarea).toBeInTheDocument()
  })
})
