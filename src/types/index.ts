export interface AIProvider {
  id: string
  name: string
  description: string
  icon: string
  models: AIModel[]
  requiresApiKey: boolean
  apiKeyUrl?: string
  baseUrl?: string
}

export interface AIModel {
  id: string
  name: string
  provider: string
  maxTokens: number
  supportsStreaming: boolean
  supportsTools: boolean
}

export interface ChatMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: Date
  metadata?: {
    model?: string
    provider?: string
    tokens?: number
  }
}

export interface FileItem {
  id: string
  name: string
  path: string
  type: 'file' | 'folder'
  content?: string
  language?: string
  children?: FileItem[]
  isModified?: boolean
  isOpen?: boolean
}

export interface Project {
  id: string
  name: string
  description?: string
  files: FileItem[]
  createdAt: Date
  updatedAt: Date
}

export interface Settings {
  apiKeys: Record<string, string>
  selectedProvider: string
  selectedModel: string
  theme: 'light' | 'dark' | 'system'
  editorSettings: {
    fontSize: number
    tabSize: number
    wordWrap: boolean
    minimap: boolean
  }
}

export interface ChatState {
  messages: ChatMessage[]
  isLoading: boolean
  currentProject?: Project
  selectedFile?: FileItem
}

export interface ProviderConfig {
  apiKey: string
  baseUrl?: string
  models?: AIModel[]
}
