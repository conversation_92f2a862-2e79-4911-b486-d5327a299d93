# Deployment Guide

This guide covers various deployment options for Lovable.DIY.

## Quick Deployment Options

### 1. Vercel (Recommended)

Vercel provides the easiest deployment experience with automatic builds and deployments.

#### Steps:
1. Push your code to GitHub
2. Visit [vercel.com](https://vercel.com)
3. Click "New Project"
4. Import your GitHub repository
5. Configure build settings:
   - **Framework Preset**: Vite
   - **Build Command**: `npm run build`
   - **Output Directory**: `dist`
6. Click "Deploy"

#### Environment Variables:
Add these in Vercel dashboard under Settings > Environment Variables:
```
VITE_APP_NAME=Lovable.DIY
VITE_APP_VERSION=1.0.0
```

### 2. Netlify

Netlify offers similar ease of deployment with great static site hosting.

#### Steps:
1. Build your project: `npm run build`
2. Visit [netlify.com](https://netlify.com)
3. Drag and drop the `dist` folder to deploy
4. Or connect your GitHub repository for automatic deployments

#### Netlify Configuration (`netlify.toml`):
```toml
[build]
  publish = "dist"
  command = "npm run build"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

### 3. GitHub Pages

Deploy directly from your GitHub repository.

#### Steps:
1. Create `.github/workflows/deploy.yml`:
```yaml
name: Deploy to GitHub Pages

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build
      run: npm run build
    
    - name: Deploy to GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./dist
```

2. Enable GitHub Pages in repository settings
3. Select "GitHub Actions" as source

## Self-Hosting Options

### 1. Docker Deployment

#### Dockerfile:
```dockerfile
# Build stage
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

# Production stage
FROM nginx:alpine

COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### nginx.conf:
```nginx
events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        location / {
            try_files $uri $uri/ /index.html;
        }

        # Enable gzip compression
        gzip on;
        gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    }
}
```

#### Build and run:
```bash
docker build -t lovable-diy .
docker run -p 8080:80 lovable-diy
```

### 2. Traditional Web Server

#### Apache (.htaccess):
```apache
Options -MultiViews
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^ index.html [QSA,L]
```

#### Nginx:
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

## Environment Configuration

### Production Environment Variables

Create a `.env.production` file:
```
VITE_APP_NAME=Lovable.DIY
VITE_APP_VERSION=1.0.0
VITE_API_BASE_URL=https://your-api.com
```

### Build Optimization

#### Vite Configuration for Production:
```typescript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ai: ['ai', '@ai-sdk/openai', '@ai-sdk/google'],
          editor: ['@codemirror/state', '@codemirror/view'],
        },
      },
    },
    chunkSizeWarningLimit: 1000,
  },
})
```

## Performance Optimization

### 1. Bundle Analysis
```bash
npm run build
npx vite-bundle-analyzer dist
```

### 2. Compression
Enable gzip/brotli compression on your server:

#### Nginx:
```nginx
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
```

### 3. Caching Headers
```nginx
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

## Security Considerations

### 1. Content Security Policy
Add to your HTML head:
```html
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  script-src 'self' 'unsafe-inline';
  style-src 'self' 'unsafe-inline';
  connect-src 'self' https://api.openai.com https://generativelanguage.googleapis.com https://openrouter.ai;
  img-src 'self' data: https:;
">
```

### 2. HTTPS Configuration
Always use HTTPS in production. Most hosting providers offer free SSL certificates.

### 3. API Key Security
- Never expose API keys in client-side code
- Use environment variables for sensitive data
- Implement proper API key validation

## Monitoring and Analytics

### 1. Error Tracking
Add error tracking service:
```typescript
// src/lib/errorTracking.ts
export function trackError(error: Error, context?: any) {
  // Send to your error tracking service
  console.error('Error:', error, context)
}
```

### 2. Performance Monitoring
```typescript
// src/lib/performance.ts
export function trackPerformance(metric: string, value: number) {
  // Send to your analytics service
  console.log(`Performance: ${metric} = ${value}ms`)
}
```

## Troubleshooting Deployment Issues

### Common Problems

**Build Failures**
- Check Node.js version compatibility
- Verify all dependencies are installed
- Run `npm run type-check` to catch TypeScript errors

**Routing Issues**
- Ensure your server is configured for SPA routing
- Add proper redirects for client-side routing

**API Connection Issues**
- Verify CORS settings
- Check API endpoints are accessible from production domain
- Validate API keys are properly configured

**Performance Issues**
- Enable compression on your server
- Optimize images and assets
- Use CDN for static assets

### Health Check Endpoint

Add a simple health check:
```typescript
// src/pages/HealthCheck.tsx
export function HealthCheck() {
  return (
    <div>
      <h1>Lovable.DIY Health Check</h1>
      <p>Status: OK</p>
      <p>Version: {import.meta.env.VITE_APP_VERSION}</p>
      <p>Build Time: {new Date().toISOString()}</p>
    </div>
  )
}
```

## Backup and Recovery

### 1. Data Backup
Since Lovable.DIY stores data in localStorage, consider:
- Implementing export/import functionality
- Regular data synchronization
- Cloud storage integration

### 2. Deployment Rollback
- Keep previous build artifacts
- Use deployment platforms with rollback features
- Implement feature flags for gradual rollouts

---

Choose the deployment method that best fits your needs and infrastructure. For most users, Vercel or Netlify provide the easiest and most reliable deployment experience.
