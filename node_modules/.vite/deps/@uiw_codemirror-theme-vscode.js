import {
  EditorView,
  HighlightStyle,
  syntaxHighlighting,
  tags
} from "./chunk-BDGW7IEX.js";
import "./chunk-JEVQZFNC.js";
import "./chunk-G3PMV62Z.js";

// node_modules/@babel/runtime/helpers/esm/extends.js
function _extends() {
  return _extends = Object.assign ? Object.assign.bind() : function(n) {
    for (var e = 1; e < arguments.length; e++) {
      var t = arguments[e];
      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);
    }
    return n;
  }, _extends.apply(null, arguments);
}

// node_modules/@uiw/codemirror-themes/esm/index.js
var createTheme = (_ref) => {
  var {
    theme,
    settings = {},
    styles = []
  } = _ref;
  var themeOptions = {
    ".cm-gutters": {}
  };
  var baseStyle = {};
  if (settings.background) {
    baseStyle.backgroundColor = settings.background;
  }
  if (settings.backgroundImage) {
    baseStyle.backgroundImage = settings.backgroundImage;
  }
  if (settings.foreground) {
    baseStyle.color = settings.foreground;
  }
  if (settings.fontSize) {
    baseStyle.fontSize = settings.fontSize;
  }
  if (settings.background || settings.foreground) {
    themeOptions["&"] = baseStyle;
  }
  if (settings.fontFamily) {
    themeOptions["&.cm-editor .cm-scroller"] = {
      fontFamily: settings.fontFamily
    };
  }
  if (settings.gutterBackground) {
    themeOptions[".cm-gutters"].backgroundColor = settings.gutterBackground;
  }
  if (settings.gutterForeground) {
    themeOptions[".cm-gutters"].color = settings.gutterForeground;
  }
  if (settings.gutterBorder) {
    themeOptions[".cm-gutters"].borderRightColor = settings.gutterBorder;
  }
  if (settings.caret) {
    themeOptions[".cm-content"] = {
      caretColor: settings.caret
    };
    themeOptions[".cm-cursor, .cm-dropCursor"] = {
      borderLeftColor: settings.caret
    };
  }
  var activeLineGutterStyle = {};
  if (settings.gutterActiveForeground) {
    activeLineGutterStyle.color = settings.gutterActiveForeground;
  }
  if (settings.lineHighlight) {
    themeOptions[".cm-activeLine"] = {
      backgroundColor: settings.lineHighlight
    };
    activeLineGutterStyle.backgroundColor = settings.lineHighlight;
  }
  themeOptions[".cm-activeLineGutter"] = activeLineGutterStyle;
  if (settings.selection) {
    themeOptions["&.cm-focused .cm-selectionBackground, & .cm-line::selection, & .cm-selectionLayer .cm-selectionBackground, .cm-content ::selection"] = {
      background: settings.selection + " !important"
    };
  }
  if (settings.selectionMatch) {
    themeOptions["& .cm-selectionMatch"] = {
      backgroundColor: settings.selectionMatch
    };
  }
  var themeExtension = EditorView.theme(themeOptions, {
    dark: theme === "dark"
  });
  var highlightStyle = HighlightStyle.define(styles);
  var extension = [themeExtension, syntaxHighlighting(highlightStyle)];
  return extension;
};

// node_modules/@uiw/codemirror-theme-vscode/esm/light.js
var defaultSettingsVscodeLight = {
  background: "#ffffff",
  foreground: "#383a42",
  caret: "#000",
  selection: "#add6ff",
  selectionMatch: "#a8ac94",
  lineHighlight: "#99999926",
  gutterBackground: "#fff",
  gutterForeground: "#237893",
  gutterActiveForeground: "#0b216f",
  fontFamily: 'Menlo, Monaco, Consolas, "Andale Mono", "Ubuntu Mono", "Courier New", monospace'
};
var vscodeLightStyle = [{
  tag: [tags.keyword, tags.operatorKeyword, tags.modifier, tags.color, tags.constant(tags.name), tags.standard(tags.name), tags.standard(tags.tagName), tags.special(tags.brace), tags.atom, tags.bool, tags.special(tags.variableName)],
  color: "#0000ff"
}, {
  tag: [tags.moduleKeyword, tags.controlKeyword],
  color: "#af00db"
}, {
  tag: [tags.name, tags.deleted, tags.character, tags.macroName, tags.propertyName, tags.variableName, tags.labelName, tags.definition(tags.name)],
  color: "#0070c1"
}, {
  tag: tags.heading,
  fontWeight: "bold",
  color: "#0070c1"
}, {
  tag: [tags.typeName, tags.className, tags.tagName, tags.number, tags.changed, tags.annotation, tags.self, tags.namespace],
  color: "#267f99"
}, {
  tag: [tags.function(tags.variableName), tags.function(tags.propertyName)],
  color: "#795e26"
}, {
  tag: [tags.number],
  color: "#098658"
}, {
  tag: [tags.operator, tags.punctuation, tags.separator, tags.url, tags.escape, tags.regexp],
  color: "#383a42"
}, {
  tag: [tags.regexp],
  color: "#af00db"
}, {
  tag: [tags.special(tags.string), tags.processingInstruction, tags.string, tags.inserted],
  color: "#a31515"
}, {
  tag: [tags.angleBracket],
  color: "#383a42"
}, {
  tag: tags.strong,
  fontWeight: "bold"
}, {
  tag: tags.emphasis,
  fontStyle: "italic"
}, {
  tag: tags.strikethrough,
  textDecoration: "line-through"
}, {
  tag: [tags.meta, tags.comment],
  color: "#008000"
}, {
  tag: tags.link,
  color: "#4078f2",
  textDecoration: "underline"
}, {
  tag: tags.invalid,
  color: "#e45649"
}];
function vscodeLightInit(options) {
  var {
    theme = "light",
    settings = {},
    styles = []
  } = options || {};
  return createTheme({
    theme,
    settings: _extends({}, defaultSettingsVscodeLight, settings),
    styles: [...vscodeLightStyle, ...styles]
  });
}
var vscodeLight = vscodeLightInit();

// node_modules/@uiw/codemirror-theme-vscode/esm/dark.js
var defaultSettingsVscodeDark = {
  background: "#1e1e1e",
  foreground: "#9cdcfe",
  caret: "#c6c6c6",
  selection: "#6199ff2f",
  selectionMatch: "#72a1ff59",
  lineHighlight: "#ffffff0f",
  gutterBackground: "#1e1e1e",
  gutterForeground: "#838383",
  gutterActiveForeground: "#fff",
  fontFamily: 'Menlo, Monaco, Consolas, "Andale Mono", "Ubuntu Mono", "Courier New", monospace'
};
var vscodeDarkStyle = [{
  tag: [tags.keyword, tags.operatorKeyword, tags.modifier, tags.color, tags.constant(tags.name), tags.standard(tags.name), tags.standard(tags.tagName), tags.special(tags.brace), tags.atom, tags.bool, tags.special(tags.variableName)],
  color: "#569cd6"
}, {
  tag: [tags.controlKeyword, tags.moduleKeyword],
  color: "#c586c0"
}, {
  tag: [tags.name, tags.deleted, tags.character, tags.macroName, tags.propertyName, tags.variableName, tags.labelName, tags.definition(tags.name)],
  color: "#9cdcfe"
}, {
  tag: tags.heading,
  fontWeight: "bold",
  color: "#9cdcfe"
}, {
  tag: [tags.typeName, tags.className, tags.tagName, tags.number, tags.changed, tags.annotation, tags.self, tags.namespace],
  color: "#4ec9b0"
}, {
  tag: [tags.function(tags.variableName), tags.function(tags.propertyName)],
  color: "#dcdcaa"
}, {
  tag: [tags.number],
  color: "#b5cea8"
}, {
  tag: [tags.operator, tags.punctuation, tags.separator, tags.url, tags.escape, tags.regexp],
  color: "#d4d4d4"
}, {
  tag: [tags.regexp],
  color: "#d16969"
}, {
  tag: [tags.special(tags.string), tags.processingInstruction, tags.string, tags.inserted],
  color: "#ce9178"
}, {
  tag: [tags.angleBracket],
  color: "#808080"
}, {
  tag: tags.strong,
  fontWeight: "bold"
}, {
  tag: tags.emphasis,
  fontStyle: "italic"
}, {
  tag: tags.strikethrough,
  textDecoration: "line-through"
}, {
  tag: [tags.meta, tags.comment],
  color: "#6a9955"
}, {
  tag: tags.link,
  color: "#6a9955",
  textDecoration: "underline"
}, {
  tag: tags.invalid,
  color: "#ff0000"
}];
function vscodeDarkInit(options) {
  var {
    theme = "dark",
    settings = {},
    styles = []
  } = options || {};
  return createTheme({
    theme,
    settings: _extends({}, defaultSettingsVscodeDark, settings),
    styles: [...vscodeDarkStyle, ...styles]
  });
}
var vscodeDark = vscodeDarkInit();
export {
  defaultSettingsVscodeDark,
  defaultSettingsVscodeLight,
  vscodeDark,
  vscodeDarkInit,
  vscodeDarkStyle,
  vscodeLight,
  vscodeLightInit,
  vscodeLightStyle
};
//# sourceMappingURL=@uiw_codemirror-theme-vscode.js.map
