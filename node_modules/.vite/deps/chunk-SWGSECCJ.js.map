{"version": 3, "sources": ["../../@lezer/html/dist/index.js", "../../@codemirror/lang-html/dist/index.js"], "sourcesContent": ["import { ContextTracker, ExternalTokenizer, LRParser } from '@lezer/lr';\nimport { styleTags, tags } from '@lezer/highlight';\nimport { parseMixed } from '@lezer/common';\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst scriptText = 54,\n  StartCloseScriptTag = 1,\n  styleText = 55,\n  StartCloseStyleTag = 2,\n  textareaText = 56,\n  StartCloseTextareaTag = 3,\n  EndTag = 4,\n  SelfClosingEndTag = 5,\n  StartTag = 6,\n  StartScriptTag = 7,\n  StartStyleTag = 8,\n  StartTextareaTag = 9,\n  StartSelfClosingTag = 10,\n  StartCloseTag = 11,\n  NoMatchStartCloseTag = 12,\n  MismatchedStartCloseTag = 13,\n  missingCloseTag = 57,\n  IncompleteCloseTag = 14,\n  commentContent$1 = 58,\n  Element = 20,\n  TagName = 22,\n  Attribute = 23,\n  AttributeName = 24,\n  AttributeValue = 26,\n  UnquotedAttributeValue = 27,\n  ScriptText = 28,\n  StyleText = 31,\n  TextareaText = 34,\n  OpenTag = 36,\n  CloseTag = 37,\n  Dialect_noMatch = 0,\n  Dialect_selfClosing = 1;\n\n/* Hand-written tokenizers for HTML. */\n\nconst selfClosers = {\n  area: true, base: true, br: true, col: true, command: true,\n  embed: true, frame: true, hr: true, img: true, input: true,\n  keygen: true, link: true, meta: true, param: true, source: true,\n  track: true, wbr: true, menuitem: true\n};\n\nconst implicitlyClosed = {\n  dd: true, li: true, optgroup: true, option: true, p: true,\n  rp: true, rt: true, tbody: true, td: true, tfoot: true,\n  th: true, tr: true\n};\n\nconst closeOnOpen = {\n  dd: {dd: true, dt: true},\n  dt: {dd: true, dt: true},\n  li: {li: true},\n  option: {option: true, optgroup: true},\n  optgroup: {optgroup: true},\n  p: {\n    address: true, article: true, aside: true, blockquote: true, dir: true,\n    div: true, dl: true, fieldset: true, footer: true, form: true,\n    h1: true, h2: true, h3: true, h4: true, h5: true, h6: true,\n    header: true, hgroup: true, hr: true, menu: true, nav: true, ol: true,\n    p: true, pre: true, section: true, table: true, ul: true\n  },\n  rp: {rp: true, rt: true},\n  rt: {rp: true, rt: true},\n  tbody: {tbody: true, tfoot: true},\n  td: {td: true, th: true},\n  tfoot: {tbody: true},\n  th: {td: true, th: true},\n  thead: {tbody: true, tfoot: true},\n  tr: {tr: true}\n};\n\nfunction nameChar(ch) {\n  return ch == 45 || ch == 46 || ch == 58 || ch >= 65 && ch <= 90 || ch == 95 || ch >= 97 && ch <= 122 || ch >= 161\n}\n\nfunction isSpace(ch) {\n  return ch == 9 || ch == 10 || ch == 13 || ch == 32\n}\n\nlet cachedName = null, cachedInput = null, cachedPos = 0;\nfunction tagNameAfter(input, offset) {\n  let pos = input.pos + offset;\n  if (cachedPos == pos && cachedInput == input) return cachedName\n  let next = input.peek(offset);\n  while (isSpace(next)) next = input.peek(++offset);\n  let name = \"\";\n  for (;;) {\n    if (!nameChar(next)) break\n    name += String.fromCharCode(next);\n    next = input.peek(++offset);\n  }\n  // Undefined to signal there's a <? or <!, null for just missing\n  cachedInput = input; cachedPos = pos;\n  return cachedName = name ? name.toLowerCase() : next == question || next == bang ? undefined : null\n}\n\nconst lessThan = 60, greaterThan = 62, slash = 47, question = 63, bang = 33, dash = 45;\n\nfunction ElementContext(name, parent) {\n  this.name = name;\n  this.parent = parent;\n}\n\nconst startTagTerms = [StartTag, StartSelfClosingTag, StartScriptTag, StartStyleTag, StartTextareaTag];\n\nconst elementContext = new ContextTracker({\n  start: null,\n  shift(context, term, stack, input) {\n    return startTagTerms.indexOf(term) > -1 ? new ElementContext(tagNameAfter(input, 1) || \"\", context) : context\n  },\n  reduce(context, term) {\n    return term == Element && context ? context.parent : context\n  },\n  reuse(context, node, stack, input) {\n    let type = node.type.id;\n    return type == StartTag || type == OpenTag\n      ? new ElementContext(tagNameAfter(input, 1) || \"\", context) : context\n  },\n  strict: false\n});\n\nconst tagStart = new ExternalTokenizer((input, stack) => {\n  if (input.next != lessThan) {\n    // End of file, close any open tags\n    if (input.next < 0 && stack.context) input.acceptToken(missingCloseTag);\n    return\n  }\n  input.advance();\n  let close = input.next == slash;\n  if (close) input.advance();\n  let name = tagNameAfter(input, 0);\n  if (name === undefined) return\n  if (!name) return input.acceptToken(close ? IncompleteCloseTag : StartTag)\n\n  let parent = stack.context ? stack.context.name : null;\n  if (close) {\n    if (name == parent) return input.acceptToken(StartCloseTag)\n    if (parent && implicitlyClosed[parent]) return input.acceptToken(missingCloseTag, -2)\n    if (stack.dialectEnabled(Dialect_noMatch)) return input.acceptToken(NoMatchStartCloseTag)\n    for (let cx = stack.context; cx; cx = cx.parent) if (cx.name == name) return\n    input.acceptToken(MismatchedStartCloseTag);\n  } else {\n    if (name == \"script\") return input.acceptToken(StartScriptTag)\n    if (name == \"style\") return input.acceptToken(StartStyleTag)\n    if (name == \"textarea\") return input.acceptToken(StartTextareaTag)\n    if (selfClosers.hasOwnProperty(name)) return input.acceptToken(StartSelfClosingTag)\n    if (parent && closeOnOpen[parent] && closeOnOpen[parent][name]) input.acceptToken(missingCloseTag, -1);\n    else input.acceptToken(StartTag);\n  }\n}, {contextual: true});\n\nconst commentContent = new ExternalTokenizer(input => {\n  for (let dashes = 0, i = 0;; i++) {\n    if (input.next < 0) {\n      if (i) input.acceptToken(commentContent$1);\n      break\n    }\n    if (input.next == dash) {\n      dashes++;\n    } else if (input.next == greaterThan && dashes >= 2) {\n      if (i >= 3) input.acceptToken(commentContent$1, -2);\n      break\n    } else {\n      dashes = 0;\n    }\n    input.advance();\n  }\n});\n\nfunction inForeignElement(context) {\n  for (; context; context = context.parent)\n    if (context.name == \"svg\" || context.name == \"math\") return true\n  return false\n}\n\nconst endTag = new ExternalTokenizer((input, stack) => {\n  if (input.next == slash && input.peek(1) == greaterThan) {\n    let selfClosing = stack.dialectEnabled(Dialect_selfClosing) || inForeignElement(stack.context);\n    input.acceptToken(selfClosing ? SelfClosingEndTag : EndTag, 2);\n  } else if (input.next == greaterThan) {\n    input.acceptToken(EndTag, 1);\n  }\n});\n\nfunction contentTokenizer(tag, textToken, endToken) {\n  let lastState = 2 + tag.length;\n  return new ExternalTokenizer(input => {\n    // state means:\n    // - 0 nothing matched\n    // - 1 '<' matched\n    // - 2 '</' + possibly whitespace matched\n    // - 3-(1+tag.length) part of the tag matched\n    // - lastState whole tag + possibly whitespace matched\n    for (let state = 0, matchedLen = 0, i = 0;; i++) {\n      if (input.next < 0) {\n        if (i) input.acceptToken(textToken);\n        break\n      }\n      if (state == 0 && input.next == lessThan ||\n          state == 1 && input.next == slash ||\n          state >= 2 && state < lastState && input.next == tag.charCodeAt(state - 2)) {\n        state++;\n        matchedLen++;\n      } else if ((state == 2 || state == lastState) && isSpace(input.next)) {\n        matchedLen++;\n      } else if (state == lastState && input.next == greaterThan) {\n        if (i > matchedLen)\n          input.acceptToken(textToken, -matchedLen);\n        else\n          input.acceptToken(endToken, -(matchedLen - 2));\n        break\n      } else if ((input.next == 10 /* '\\n' */ || input.next == 13 /* '\\r' */) && i) {\n        input.acceptToken(textToken, 1);\n        break\n      } else {\n        state = matchedLen = 0;\n      }\n      input.advance();\n    }\n  })\n}\n\nconst scriptTokens = contentTokenizer(\"script\", scriptText, StartCloseScriptTag);\n\nconst styleTokens = contentTokenizer(\"style\", styleText, StartCloseStyleTag);\n\nconst textareaTokens = contentTokenizer(\"textarea\", textareaText, StartCloseTextareaTag);\n\nconst htmlHighlighting = styleTags({\n  \"Text RawText\": tags.content,\n  \"StartTag StartCloseTag SelfClosingEndTag EndTag\": tags.angleBracket,\n  TagName: tags.tagName,\n  \"MismatchedCloseTag/TagName\": [tags.tagName,  tags.invalid],\n  AttributeName: tags.attributeName,\n  \"AttributeValue UnquotedAttributeValue\": tags.attributeValue,\n  Is: tags.definitionOperator,\n  \"EntityReference CharacterReference\": tags.character,\n  Comment: tags.blockComment,\n  ProcessingInst: tags.processingInstruction,\n  DoctypeDecl: tags.documentMeta\n});\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst parser = LRParser.deserialize({\n  version: 14,\n  states: \",xOVO!rOOO!WQ#tO'#CqO!]Q#tO'#CzO!bQ#tO'#C}O!gQ#tO'#DQO!lQ#tO'#DSO!qOaO'#CpO!|ObO'#CpO#XOdO'#CpO$eO!rO'#CpOOO`'#Cp'#CpO$lO$fO'#DTO$tQ#tO'#DVO$yQ#tO'#DWOOO`'#Dk'#DkOOO`'#DY'#DYQVO!rOOO%OQ&rO,59]O%ZQ&rO,59fO%fQ&rO,59iO%qQ&rO,59lO%|Q&rO,59nOOOa'#D^'#D^O&XOaO'#CxO&dOaO,59[OOOb'#D_'#D_O&lObO'#C{O&wObO,59[OOOd'#D`'#D`O'POdO'#DOO'[OdO,59[OOO`'#Da'#DaO'dO!rO,59[O'kQ#tO'#DROOO`,59[,59[OOOp'#Db'#DbO'pO$fO,59oOOO`,59o,59oO'xQ#|O,59qO'}Q#|O,59rOOO`-E7W-E7WO(SQ&rO'#CsOOQW'#DZ'#DZO(bQ&rO1G.wOOOa1G.w1G.wOOO`1G/Y1G/YO(mQ&rO1G/QOOOb1G/Q1G/QO(xQ&rO1G/TOOOd1G/T1G/TO)TQ&rO1G/WOOO`1G/W1G/WO)`Q&rO1G/YOOOa-E7[-E7[O)kQ#tO'#CyOOO`1G.v1G.vOOOb-E7]-E7]O)pQ#tO'#C|OOOd-E7^-E7^O)uQ#tO'#DPOOO`-E7_-E7_O)zQ#|O,59mOOOp-E7`-E7`OOO`1G/Z1G/ZOOO`1G/]1G/]OOO`1G/^1G/^O*PQ,UO,59_OOQW-E7X-E7XOOOa7+$c7+$cOOO`7+$t7+$tOOOb7+$l7+$lOOOd7+$o7+$oOOO`7+$r7+$rO*[Q#|O,59eO*aQ#|O,59hO*fQ#|O,59kOOO`1G/X1G/XO*kO7[O'#CvO*|OMhO'#CvOOQW1G.y1G.yOOO`1G/P1G/POOO`1G/S1G/SOOO`1G/V1G/VOOOO'#D['#D[O+_O7[O,59bOOQW,59b,59bOOOO'#D]'#D]O+pOMhO,59bOOOO-E7Y-E7YOOQW1G.|1G.|OOOO-E7Z-E7Z\",\n  stateData: \",]~O!^OS~OUSOVPOWQOXROYTO[]O][O^^O`^Oa^Ob^Oc^Ox^O{_O!dZO~OfaO~OfbO~OfcO~OfdO~OfeO~O!WfOPlP!ZlP~O!XiOQoP!ZoP~O!YlORrP!ZrP~OUSOVPOWQOXROYTOZqO[]O][O^^O`^Oa^Ob^Oc^Ox^O!dZO~O!ZrO~P#dO![sO!euO~OfvO~OfwO~OS|OT}OhyO~OS!POT}OhyO~OS!ROT}OhyO~OS!TOT}OhyO~OS}OT}OhyO~O!WfOPlX!ZlX~OP!WO!Z!XO~O!XiOQoX!ZoX~OQ!ZO!Z!XO~O!YlORrX!ZrX~OR!]O!Z!XO~O!Z!XO~P#dOf!_O~O![sO!e!aO~OS!bO~OS!cO~Oi!dOSgXTgXhgX~OS!fOT!gOhyO~OS!hOT!gOhyO~OS!iOT!gOhyO~OS!jOT!gOhyO~OS!gOT!gOhyO~Of!kO~Of!lO~Of!mO~OS!nO~Ok!qO!`!oO!b!pO~OS!rO~OS!sO~OS!tO~Oa!uOb!uOc!uO!`!wO!a!uO~Oa!xOb!xOc!xO!b!wO!c!xO~Oa!uOb!uOc!uO!`!{O!a!uO~Oa!xOb!xOc!xO!b!{O!c!xO~OT~bac!dx{!d~\",\n  goto: \"%p!`PPPPPPPPPPPPPPPPPPPP!a!gP!mPP!yP!|#P#S#Y#]#`#f#i#l#r#x!aP!a!aP$O$U$l$r$x%O%U%[%bPPPPPPPP%hX^OX`pXUOX`pezabcde{!O!Q!S!UR!q!dRhUR!XhXVOX`pRkVR!XkXWOX`pRnWR!XnXXOX`pQrXR!XpXYOX`pQ`ORx`Q{aQ!ObQ!QcQ!SdQ!UeZ!e{!O!Q!S!UQ!v!oR!z!vQ!y!pR!|!yQgUR!VgQjVR!YjQmWR![mQpXR!^pQtZR!`tS_O`ToXp\",\n  nodeNames: \"⚠ StartCloseTag StartCloseTag StartCloseTag EndTag SelfClosingEndTag StartTag StartTag StartTag StartTag StartTag StartCloseTag StartCloseTag StartCloseTag IncompleteCloseTag Document Text EntityReference CharacterReference InvalidEntity Element OpenTag TagName Attribute AttributeName Is AttributeValue UnquotedAttributeValue ScriptText CloseTag OpenTag StyleText CloseTag OpenTag TextareaText CloseTag OpenTag CloseTag SelfClosingTag Comment ProcessingInst MismatchedCloseTag CloseTag DoctypeDecl\",\n  maxTerm: 67,\n  context: elementContext,\n  nodeProps: [\n    [\"closedBy\", -10,1,2,3,7,8,9,10,11,12,13,\"EndTag\",6,\"EndTag SelfClosingEndTag\",-4,21,30,33,36,\"CloseTag\"],\n    [\"openedBy\", 4,\"StartTag StartCloseTag\",5,\"StartTag\",-4,29,32,35,37,\"OpenTag\"],\n    [\"group\", -9,14,17,18,19,20,39,40,41,42,\"Entity\",16,\"Entity TextContent\",-3,28,31,34,\"TextContent Entity\"],\n    [\"isolate\", -11,21,29,30,32,33,35,36,37,38,41,42,\"ltr\",-3,26,27,39,\"\"]\n  ],\n  propSources: [htmlHighlighting],\n  skippedNodes: [0],\n  repeatNodeCount: 9,\n  tokenData: \"!<p!aR!YOX$qXY,QYZ,QZ[$q[]&X]^,Q^p$qpq,Qqr-_rs3_sv-_vw3}wxHYx}-_}!OH{!O!P-_!P!Q$q!Q![-_![!]Mz!]!^-_!^!_!$S!_!`!;x!`!a&X!a!c-_!c!}Mz!}#R-_#R#SMz#S#T1k#T#oMz#o#s-_#s$f$q$f%W-_%W%oMz%o%p-_%p&aMz&a&b-_&b1pMz1p4U-_4U4dMz4d4e-_4e$ISMz$IS$I`-_$I`$IbMz$Ib$Kh-_$Kh%#tMz%#t&/x-_&/x&EtMz&Et&FV-_&FV;'SMz;'S;:j!#|;:j;=`3X<%l?&r-_?&r?AhMz?Ah?BY$q?BY?MnMz?MnO$q!Z$|c`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr$qrs&}sv$qvw+Pwx(tx!^$q!^!_*V!_!a&X!a#S$q#S#T&X#T;'S$q;'S;=`+z<%lO$q!R&bX`P!a`!cpOr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&Xq'UV`P!cpOv&}wx'kx!^&}!^!_(V!_;'S&};'S;=`(n<%lO&}P'pT`POv'kw!^'k!_;'S'k;'S;=`(P<%lO'kP(SP;=`<%l'kp([S!cpOv(Vx;'S(V;'S;=`(h<%lO(Vp(kP;=`<%l(Vq(qP;=`<%l&}a({W`P!a`Or(trs'ksv(tw!^(t!^!_)e!_;'S(t;'S;=`*P<%lO(t`)jT!a`Or)esv)ew;'S)e;'S;=`)y<%lO)e`)|P;=`<%l)ea*SP;=`<%l(t!Q*^V!a`!cpOr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!Q*vP;=`<%l*V!R*|P;=`<%l&XW+UYkWOX+PZ[+P^p+Pqr+Psw+Px!^+P!a#S+P#T;'S+P;'S;=`+t<%lO+PW+wP;=`<%l+P!Z+}P;=`<%l$q!a,]``P!a`!cp!^^OX&XXY,QYZ,QZ]&X]^,Q^p&Xpq,Qqr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&X!_-ljhS`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx!P-_!P!Q$q!Q!^-_!^!_*V!_!a&X!a#S-_#S#T1k#T#s-_#s$f$q$f;'S-_;'S;=`3X<%l?Ah-_?Ah?BY$q?BY?Mn-_?MnO$q[/ebhSkWOX+PZ[+P^p+Pqr/^sw/^x!P/^!P!Q+P!Q!^/^!a#S/^#S#T0m#T#s/^#s$f+P$f;'S/^;'S;=`1e<%l?Ah/^?Ah?BY+P?BY?Mn/^?MnO+PS0rXhSqr0msw0mx!P0m!Q!^0m!a#s0m$f;'S0m;'S;=`1_<%l?Ah0m?BY?Mn0mS1bP;=`<%l0m[1hP;=`<%l/^!V1vchS`P!a`!cpOq&Xqr1krs&}sv1kvw0mwx(tx!P1k!P!Q&X!Q!^1k!^!_*V!_!a&X!a#s1k#s$f&X$f;'S1k;'S;=`3R<%l?Ah1k?Ah?BY&X?BY?Mn1k?MnO&X!V3UP;=`<%l1k!_3[P;=`<%l-_!Z3hV!`h`P!cpOv&}wx'kx!^&}!^!_(V!_;'S&};'S;=`(n<%lO&}!_4WihSkWc!ROX5uXZ7SZ[5u[^7S^p5uqr8trs7Sst>]tw8twx7Sx!P8t!P!Q5u!Q!]8t!]!^/^!^!a7S!a#S8t#S#T;{#T#s8t#s$f5u$f;'S8t;'S;=`>V<%l?Ah8t?Ah?BY5u?BY?Mn8t?MnO5u!Z5zbkWOX5uXZ7SZ[5u[^7S^p5uqr5urs7Sst+Ptw5uwx7Sx!]5u!]!^7w!^!a7S!a#S5u#S#T7S#T;'S5u;'S;=`8n<%lO5u!R7VVOp7Sqs7St!]7S!]!^7l!^;'S7S;'S;=`7q<%lO7S!R7qOa!R!R7tP;=`<%l7S!Z8OYkWa!ROX+PZ[+P^p+Pqr+Psw+Px!^+P!a#S+P#T;'S+P;'S;=`+t<%lO+P!Z8qP;=`<%l5u!_8{ihSkWOX5uXZ7SZ[5u[^7S^p5uqr8trs7Sst/^tw8twx7Sx!P8t!P!Q5u!Q!]8t!]!^:j!^!a7S!a#S8t#S#T;{#T#s8t#s$f5u$f;'S8t;'S;=`>V<%l?Ah8t?Ah?BY5u?BY?Mn8t?MnO5u!_:sbhSkWa!ROX+PZ[+P^p+Pqr/^sw/^x!P/^!P!Q+P!Q!^/^!a#S/^#S#T0m#T#s/^#s$f+P$f;'S/^;'S;=`1e<%l?Ah/^?Ah?BY+P?BY?Mn/^?MnO+P!V<QchSOp7Sqr;{rs7Sst0mtw;{wx7Sx!P;{!P!Q7S!Q!];{!]!^=]!^!a7S!a#s;{#s$f7S$f;'S;{;'S;=`>P<%l?Ah;{?Ah?BY7S?BY?Mn;{?MnO7S!V=dXhSa!Rqr0msw0mx!P0m!Q!^0m!a#s0m$f;'S0m;'S;=`1_<%l?Ah0m?BY?Mn0m!V>SP;=`<%l;{!_>YP;=`<%l8t!_>dhhSkWOX@OXZAYZ[@O[^AY^p@OqrBwrsAYswBwwxAYx!PBw!P!Q@O!Q!]Bw!]!^/^!^!aAY!a#SBw#S#TE{#T#sBw#s$f@O$f;'SBw;'S;=`HS<%l?AhBw?Ah?BY@O?BY?MnBw?MnO@O!Z@TakWOX@OXZAYZ[@O[^AY^p@Oqr@OrsAYsw@OwxAYx!]@O!]!^Az!^!aAY!a#S@O#S#TAY#T;'S@O;'S;=`Bq<%lO@O!RA]UOpAYq!]AY!]!^Ao!^;'SAY;'S;=`At<%lOAY!RAtOb!R!RAwP;=`<%lAY!ZBRYkWb!ROX+PZ[+P^p+Pqr+Psw+Px!^+P!a#S+P#T;'S+P;'S;=`+t<%lO+P!ZBtP;=`<%l@O!_COhhSkWOX@OXZAYZ[@O[^AY^p@OqrBwrsAYswBwwxAYx!PBw!P!Q@O!Q!]Bw!]!^Dj!^!aAY!a#SBw#S#TE{#T#sBw#s$f@O$f;'SBw;'S;=`HS<%l?AhBw?Ah?BY@O?BY?MnBw?MnO@O!_DsbhSkWb!ROX+PZ[+P^p+Pqr/^sw/^x!P/^!P!Q+P!Q!^/^!a#S/^#S#T0m#T#s/^#s$f+P$f;'S/^;'S;=`1e<%l?Ah/^?Ah?BY+P?BY?Mn/^?MnO+P!VFQbhSOpAYqrE{rsAYswE{wxAYx!PE{!P!QAY!Q!]E{!]!^GY!^!aAY!a#sE{#s$fAY$f;'SE{;'S;=`G|<%l?AhE{?Ah?BYAY?BY?MnE{?MnOAY!VGaXhSb!Rqr0msw0mx!P0m!Q!^0m!a#s0m$f;'S0m;'S;=`1_<%l?Ah0m?BY?Mn0m!VHPP;=`<%lE{!_HVP;=`<%lBw!ZHcW!bx`P!a`Or(trs'ksv(tw!^(t!^!_)e!_;'S(t;'S;=`*P<%lO(t!aIYlhS`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx}-_}!OKQ!O!P-_!P!Q$q!Q!^-_!^!_*V!_!a&X!a#S-_#S#T1k#T#s-_#s$f$q$f;'S-_;'S;=`3X<%l?Ah-_?Ah?BY$q?BY?Mn-_?MnO$q!aK_khS`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx!P-_!P!Q$q!Q!^-_!^!_*V!_!`&X!`!aMS!a#S-_#S#T1k#T#s-_#s$f$q$f;'S-_;'S;=`3X<%l?Ah-_?Ah?BY$q?BY?Mn-_?MnO$q!TM_X`P!a`!cp!eQOr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&X!aNZ!ZhSfQ`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx}-_}!OMz!O!PMz!P!Q$q!Q![Mz![!]Mz!]!^-_!^!_*V!_!a&X!a!c-_!c!}Mz!}#R-_#R#SMz#S#T1k#T#oMz#o#s-_#s$f$q$f$}-_$}%OMz%O%W-_%W%oMz%o%p-_%p&aMz&a&b-_&b1pMz1p4UMz4U4dMz4d4e-_4e$ISMz$IS$I`-_$I`$IbMz$Ib$Je-_$Je$JgMz$Jg$Kh-_$Kh%#tMz%#t&/x-_&/x&EtMz&Et&FV-_&FV;'SMz;'S;:j!#|;:j;=`3X<%l?&r-_?&r?AhMz?Ah?BY$q?BY?MnMz?MnO$q!a!$PP;=`<%lMz!R!$ZY!a`!cpOq*Vqr!$yrs(Vsv*Vwx)ex!a*V!a!b!4t!b;'S*V;'S;=`*s<%lO*V!R!%Q]!a`!cpOr*Vrs(Vsv*Vwx)ex}*V}!O!%y!O!f*V!f!g!']!g#W*V#W#X!0`#X;'S*V;'S;=`*s<%lO*V!R!&QX!a`!cpOr*Vrs(Vsv*Vwx)ex}*V}!O!&m!O;'S*V;'S;=`*s<%lO*V!R!&vV!a`!cp!dPOr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!R!'dX!a`!cpOr*Vrs(Vsv*Vwx)ex!q*V!q!r!(P!r;'S*V;'S;=`*s<%lO*V!R!(WX!a`!cpOr*Vrs(Vsv*Vwx)ex!e*V!e!f!(s!f;'S*V;'S;=`*s<%lO*V!R!(zX!a`!cpOr*Vrs(Vsv*Vwx)ex!v*V!v!w!)g!w;'S*V;'S;=`*s<%lO*V!R!)nX!a`!cpOr*Vrs(Vsv*Vwx)ex!{*V!{!|!*Z!|;'S*V;'S;=`*s<%lO*V!R!*bX!a`!cpOr*Vrs(Vsv*Vwx)ex!r*V!r!s!*}!s;'S*V;'S;=`*s<%lO*V!R!+UX!a`!cpOr*Vrs(Vsv*Vwx)ex!g*V!g!h!+q!h;'S*V;'S;=`*s<%lO*V!R!+xY!a`!cpOr!+qrs!,hsv!+qvw!-Swx!.[x!`!+q!`!a!/j!a;'S!+q;'S;=`!0Y<%lO!+qq!,mV!cpOv!,hvx!-Sx!`!,h!`!a!-q!a;'S!,h;'S;=`!.U<%lO!,hP!-VTO!`!-S!`!a!-f!a;'S!-S;'S;=`!-k<%lO!-SP!-kO{PP!-nP;=`<%l!-Sq!-xS!cp{POv(Vx;'S(V;'S;=`(h<%lO(Vq!.XP;=`<%l!,ha!.aX!a`Or!.[rs!-Ssv!.[vw!-Sw!`!.[!`!a!.|!a;'S!.[;'S;=`!/d<%lO!.[a!/TT!a`{POr)esv)ew;'S)e;'S;=`)y<%lO)ea!/gP;=`<%l!.[!R!/sV!a`!cp{POr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!R!0]P;=`<%l!+q!R!0gX!a`!cpOr*Vrs(Vsv*Vwx)ex#c*V#c#d!1S#d;'S*V;'S;=`*s<%lO*V!R!1ZX!a`!cpOr*Vrs(Vsv*Vwx)ex#V*V#V#W!1v#W;'S*V;'S;=`*s<%lO*V!R!1}X!a`!cpOr*Vrs(Vsv*Vwx)ex#h*V#h#i!2j#i;'S*V;'S;=`*s<%lO*V!R!2qX!a`!cpOr*Vrs(Vsv*Vwx)ex#m*V#m#n!3^#n;'S*V;'S;=`*s<%lO*V!R!3eX!a`!cpOr*Vrs(Vsv*Vwx)ex#d*V#d#e!4Q#e;'S*V;'S;=`*s<%lO*V!R!4XX!a`!cpOr*Vrs(Vsv*Vwx)ex#X*V#X#Y!+q#Y;'S*V;'S;=`*s<%lO*V!R!4{Y!a`!cpOr!4trs!5ksv!4tvw!6Vwx!8]x!a!4t!a!b!:]!b;'S!4t;'S;=`!;r<%lO!4tq!5pV!cpOv!5kvx!6Vx!a!5k!a!b!7W!b;'S!5k;'S;=`!8V<%lO!5kP!6YTO!a!6V!a!b!6i!b;'S!6V;'S;=`!7Q<%lO!6VP!6lTO!`!6V!`!a!6{!a;'S!6V;'S;=`!7Q<%lO!6VP!7QOxPP!7TP;=`<%l!6Vq!7]V!cpOv!5kvx!6Vx!`!5k!`!a!7r!a;'S!5k;'S;=`!8V<%lO!5kq!7yS!cpxPOv(Vx;'S(V;'S;=`(h<%lO(Vq!8YP;=`<%l!5ka!8bX!a`Or!8]rs!6Vsv!8]vw!6Vw!a!8]!a!b!8}!b;'S!8];'S;=`!:V<%lO!8]a!9SX!a`Or!8]rs!6Vsv!8]vw!6Vw!`!8]!`!a!9o!a;'S!8];'S;=`!:V<%lO!8]a!9vT!a`xPOr)esv)ew;'S)e;'S;=`)y<%lO)ea!:YP;=`<%l!8]!R!:dY!a`!cpOr!4trs!5ksv!4tvw!6Vwx!8]x!`!4t!`!a!;S!a;'S!4t;'S;=`!;r<%lO!4t!R!;]V!a`!cpxPOr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!R!;uP;=`<%l!4t!V!<TXiS`P!a`!cpOr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&X\",\n  tokenizers: [scriptTokens, styleTokens, textareaTokens, endTag, tagStart, commentContent, 0, 1, 2, 3, 4, 5],\n  topRules: {\"Document\":[0,15]},\n  dialects: {noMatch: 0, selfClosing: 509},\n  tokenPrec: 511\n});\n\nfunction getAttrs(openTag, input) {\n  let attrs = Object.create(null);\n  for (let att of openTag.getChildren(Attribute)) {\n    let name = att.getChild(AttributeName), value = att.getChild(AttributeValue) || att.getChild(UnquotedAttributeValue);\n    if (name) attrs[input.read(name.from, name.to)] =\n      !value ? \"\" : value.type.id == AttributeValue ? input.read(value.from + 1, value.to - 1) : input.read(value.from, value.to);\n  }\n  return attrs\n}\n\nfunction findTagName(openTag, input) {\n  let tagNameNode = openTag.getChild(TagName);\n  return tagNameNode ? input.read(tagNameNode.from, tagNameNode.to) : \" \"\n}\n\nfunction maybeNest(node, input, tags) {\n  let attrs;\n  for (let tag of tags) {\n    if (!tag.attrs || tag.attrs(attrs || (attrs = getAttrs(node.node.parent.firstChild, input))))\n      return {parser: tag.parser}\n  }\n  return null\n}\n\n// tags?: {\n//   tag: string,\n//   attrs?: ({[attr: string]: string}) => boolean,\n//   parser: Parser\n// }[]\n// attributes?: {\n//   name: string,\n//   tagName?: string,\n//   parser: Parser\n// }[]\n \nfunction configureNesting(tags = [], attributes = []) {\n  let script = [], style = [], textarea = [], other = [];\n  for (let tag of tags) {\n    let array = tag.tag == \"script\" ? script : tag.tag == \"style\" ? style : tag.tag == \"textarea\" ? textarea : other;\n    array.push(tag);\n  }\n  let attrs = attributes.length ? Object.create(null) : null;\n  for (let attr of attributes) (attrs[attr.name] || (attrs[attr.name] = [])).push(attr);\n\n  return parseMixed((node, input) => {\n    let id = node.type.id;\n    if (id == ScriptText) return maybeNest(node, input, script)\n    if (id == StyleText) return maybeNest(node, input, style)\n    if (id == TextareaText) return maybeNest(node, input, textarea)\n\n    if (id == Element && other.length) {\n      let n = node.node, open = n.firstChild, tagName = open && findTagName(open, input), attrs;\n      if (tagName) for (let tag of other) {\n        if (tag.tag == tagName && (!tag.attrs || tag.attrs(attrs || (attrs = getAttrs(open, input))))) {\n          let close = n.lastChild;\n          let to = close.type.id == CloseTag ? close.from : n.to;\n          if (to > open.to)\n            return {parser: tag.parser, overlay: [{from: open.to, to}]}\n        }\n      }\n    }\n\n    if (attrs && id == Attribute) {\n      let n = node.node, nameNode;\n      if (nameNode = n.firstChild) {\n        let matches = attrs[input.read(nameNode.from, nameNode.to)];\n        if (matches) for (let attr of matches) {\n          if (attr.tagName && attr.tagName != findTagName(n.parent, input)) continue\n          let value = n.lastChild;\n          if (value.type.id == AttributeValue) {\n            let from = value.from + 1;\n            let last = value.lastChild, to = value.to - (last && last.isError ? 0 : 1);\n            if (to > from) return {parser: attr.parser, overlay: [{from, to}]}\n          } else if (value.type.id == UnquotedAttributeValue) {\n            return {parser: attr.parser, overlay: [{from: value.from, to: value.to}]}\n          }\n        }\n      }\n    }\n    return null\n  })\n}\n\nexport { configureNesting, parser };\n", "import { parser, configureNesting } from '@lezer/html';\nimport { cssLanguage, css } from '@codemirror/lang-css';\nimport { javascriptLanguage, typescriptLanguage, jsxLanguage, tsxLanguage, javascript } from '@codemirror/lang-javascript';\nimport { EditorView } from '@codemirror/view';\nimport { EditorSelection } from '@codemirror/state';\nimport { syntaxTree, LRLanguage, indentNodeProp, foldNodeProp, bracketMatchingHandle, LanguageSupport } from '@codemirror/language';\n\nconst Targets = [\"_blank\", \"_self\", \"_top\", \"_parent\"];\nconst Charsets = [\"ascii\", \"utf-8\", \"utf-16\", \"latin1\", \"latin1\"];\nconst Methods = [\"get\", \"post\", \"put\", \"delete\"];\nconst Encs = [\"application/x-www-form-urlencoded\", \"multipart/form-data\", \"text/plain\"];\nconst Bool = [\"true\", \"false\"];\nconst S = {}; // Empty tag spec\nconst Tags = {\n    a: {\n        attrs: {\n            href: null, ping: null, type: null,\n            media: null,\n            target: Targets,\n            hreflang: null\n        }\n    },\n    abbr: S,\n    address: S,\n    area: {\n        attrs: {\n            alt: null, coords: null, href: null, target: null, ping: null,\n            media: null, hreflang: null, type: null,\n            shape: [\"default\", \"rect\", \"circle\", \"poly\"]\n        }\n    },\n    article: S,\n    aside: S,\n    audio: {\n        attrs: {\n            src: null, mediagroup: null,\n            crossorigin: [\"anonymous\", \"use-credentials\"],\n            preload: [\"none\", \"metadata\", \"auto\"],\n            autoplay: [\"autoplay\"],\n            loop: [\"loop\"],\n            controls: [\"controls\"]\n        }\n    },\n    b: S,\n    base: { attrs: { href: null, target: Targets } },\n    bdi: S,\n    bdo: S,\n    blockquote: { attrs: { cite: null } },\n    body: S,\n    br: S,\n    button: {\n        attrs: {\n            form: null, formaction: null, name: null, value: null,\n            autofocus: [\"autofocus\"],\n            disabled: [\"autofocus\"],\n            formenctype: Encs,\n            formmethod: Methods,\n            formnovalidate: [\"novalidate\"],\n            formtarget: Targets,\n            type: [\"submit\", \"reset\", \"button\"]\n        }\n    },\n    canvas: { attrs: { width: null, height: null } },\n    caption: S,\n    center: S,\n    cite: S,\n    code: S,\n    col: { attrs: { span: null } },\n    colgroup: { attrs: { span: null } },\n    command: {\n        attrs: {\n            type: [\"command\", \"checkbox\", \"radio\"],\n            label: null, icon: null, radiogroup: null, command: null, title: null,\n            disabled: [\"disabled\"],\n            checked: [\"checked\"]\n        }\n    },\n    data: { attrs: { value: null } },\n    datagrid: { attrs: { disabled: [\"disabled\"], multiple: [\"multiple\"] } },\n    datalist: { attrs: { data: null } },\n    dd: S,\n    del: { attrs: { cite: null, datetime: null } },\n    details: { attrs: { open: [\"open\"] } },\n    dfn: S,\n    div: S,\n    dl: S,\n    dt: S,\n    em: S,\n    embed: { attrs: { src: null, type: null, width: null, height: null } },\n    eventsource: { attrs: { src: null } },\n    fieldset: { attrs: { disabled: [\"disabled\"], form: null, name: null } },\n    figcaption: S,\n    figure: S,\n    footer: S,\n    form: {\n        attrs: {\n            action: null, name: null,\n            \"accept-charset\": Charsets,\n            autocomplete: [\"on\", \"off\"],\n            enctype: Encs,\n            method: Methods,\n            novalidate: [\"novalidate\"],\n            target: Targets\n        }\n    },\n    h1: S, h2: S, h3: S, h4: S, h5: S, h6: S,\n    head: {\n        children: [\"title\", \"base\", \"link\", \"style\", \"meta\", \"script\", \"noscript\", \"command\"]\n    },\n    header: S,\n    hgroup: S,\n    hr: S,\n    html: {\n        attrs: { manifest: null }\n    },\n    i: S,\n    iframe: {\n        attrs: {\n            src: null, srcdoc: null, name: null, width: null, height: null,\n            sandbox: [\"allow-top-navigation\", \"allow-same-origin\", \"allow-forms\", \"allow-scripts\"],\n            seamless: [\"seamless\"]\n        }\n    },\n    img: {\n        attrs: {\n            alt: null, src: null, ismap: null, usemap: null, width: null, height: null,\n            crossorigin: [\"anonymous\", \"use-credentials\"]\n        }\n    },\n    input: {\n        attrs: {\n            alt: null, dirname: null, form: null, formaction: null,\n            height: null, list: null, max: null, maxlength: null, min: null,\n            name: null, pattern: null, placeholder: null, size: null, src: null,\n            step: null, value: null, width: null,\n            accept: [\"audio/*\", \"video/*\", \"image/*\"],\n            autocomplete: [\"on\", \"off\"],\n            autofocus: [\"autofocus\"],\n            checked: [\"checked\"],\n            disabled: [\"disabled\"],\n            formenctype: Encs,\n            formmethod: Methods,\n            formnovalidate: [\"novalidate\"],\n            formtarget: Targets,\n            multiple: [\"multiple\"],\n            readonly: [\"readonly\"],\n            required: [\"required\"],\n            type: [\"hidden\", \"text\", \"search\", \"tel\", \"url\", \"email\", \"password\", \"datetime\", \"date\", \"month\",\n                \"week\", \"time\", \"datetime-local\", \"number\", \"range\", \"color\", \"checkbox\", \"radio\",\n                \"file\", \"submit\", \"image\", \"reset\", \"button\"]\n        }\n    },\n    ins: { attrs: { cite: null, datetime: null } },\n    kbd: S,\n    keygen: {\n        attrs: {\n            challenge: null, form: null, name: null,\n            autofocus: [\"autofocus\"],\n            disabled: [\"disabled\"],\n            keytype: [\"RSA\"]\n        }\n    },\n    label: { attrs: { for: null, form: null } },\n    legend: S,\n    li: { attrs: { value: null } },\n    link: {\n        attrs: {\n            href: null, type: null,\n            hreflang: null,\n            media: null,\n            sizes: [\"all\", \"16x16\", \"16x16 32x32\", \"16x16 32x32 64x64\"]\n        }\n    },\n    map: { attrs: { name: null } },\n    mark: S,\n    menu: { attrs: { label: null, type: [\"list\", \"context\", \"toolbar\"] } },\n    meta: {\n        attrs: {\n            content: null,\n            charset: Charsets,\n            name: [\"viewport\", \"application-name\", \"author\", \"description\", \"generator\", \"keywords\"],\n            \"http-equiv\": [\"content-language\", \"content-type\", \"default-style\", \"refresh\"]\n        }\n    },\n    meter: { attrs: { value: null, min: null, low: null, high: null, max: null, optimum: null } },\n    nav: S,\n    noscript: S,\n    object: {\n        attrs: {\n            data: null, type: null, name: null, usemap: null, form: null, width: null, height: null,\n            typemustmatch: [\"typemustmatch\"]\n        }\n    },\n    ol: { attrs: { reversed: [\"reversed\"], start: null, type: [\"1\", \"a\", \"A\", \"i\", \"I\"] },\n        children: [\"li\", \"script\", \"template\", \"ul\", \"ol\"] },\n    optgroup: { attrs: { disabled: [\"disabled\"], label: null } },\n    option: { attrs: { disabled: [\"disabled\"], label: null, selected: [\"selected\"], value: null } },\n    output: { attrs: { for: null, form: null, name: null } },\n    p: S,\n    param: { attrs: { name: null, value: null } },\n    pre: S,\n    progress: { attrs: { value: null, max: null } },\n    q: { attrs: { cite: null } },\n    rp: S,\n    rt: S,\n    ruby: S,\n    samp: S,\n    script: {\n        attrs: {\n            type: [\"text/javascript\"],\n            src: null,\n            async: [\"async\"],\n            defer: [\"defer\"],\n            charset: Charsets\n        }\n    },\n    section: S,\n    select: {\n        attrs: {\n            form: null, name: null, size: null,\n            autofocus: [\"autofocus\"],\n            disabled: [\"disabled\"],\n            multiple: [\"multiple\"]\n        }\n    },\n    slot: { attrs: { name: null } },\n    small: S,\n    source: { attrs: { src: null, type: null, media: null } },\n    span: S,\n    strong: S,\n    style: {\n        attrs: {\n            type: [\"text/css\"],\n            media: null,\n            scoped: null\n        }\n    },\n    sub: S,\n    summary: S,\n    sup: S,\n    table: S,\n    tbody: S,\n    td: { attrs: { colspan: null, rowspan: null, headers: null } },\n    template: S,\n    textarea: {\n        attrs: {\n            dirname: null, form: null, maxlength: null, name: null, placeholder: null,\n            rows: null, cols: null,\n            autofocus: [\"autofocus\"],\n            disabled: [\"disabled\"],\n            readonly: [\"readonly\"],\n            required: [\"required\"],\n            wrap: [\"soft\", \"hard\"]\n        }\n    },\n    tfoot: S,\n    th: { attrs: { colspan: null, rowspan: null, headers: null, scope: [\"row\", \"col\", \"rowgroup\", \"colgroup\"] } },\n    thead: S,\n    time: { attrs: { datetime: null } },\n    title: S,\n    tr: S,\n    track: {\n        attrs: {\n            src: null, label: null, default: null,\n            kind: [\"subtitles\", \"captions\", \"descriptions\", \"chapters\", \"metadata\"],\n            srclang: null\n        }\n    },\n    ul: { children: [\"li\", \"script\", \"template\", \"ul\", \"ol\"] },\n    var: S,\n    video: {\n        attrs: {\n            src: null, poster: null, width: null, height: null,\n            crossorigin: [\"anonymous\", \"use-credentials\"],\n            preload: [\"auto\", \"metadata\", \"none\"],\n            autoplay: [\"autoplay\"],\n            mediagroup: [\"movie\"],\n            muted: [\"muted\"],\n            controls: [\"controls\"]\n        }\n    },\n    wbr: S\n};\nconst GlobalAttrs = {\n    accesskey: null,\n    class: null,\n    contenteditable: Bool,\n    contextmenu: null,\n    dir: [\"ltr\", \"rtl\", \"auto\"],\n    draggable: [\"true\", \"false\", \"auto\"],\n    dropzone: [\"copy\", \"move\", \"link\", \"string:\", \"file:\"],\n    hidden: [\"hidden\"],\n    id: null,\n    inert: [\"inert\"],\n    itemid: null,\n    itemprop: null,\n    itemref: null,\n    itemscope: [\"itemscope\"],\n    itemtype: null,\n    lang: [\"ar\", \"bn\", \"de\", \"en-GB\", \"en-US\", \"es\", \"fr\", \"hi\", \"id\", \"ja\", \"pa\", \"pt\", \"ru\", \"tr\", \"zh\"],\n    spellcheck: Bool,\n    autocorrect: Bool,\n    autocapitalize: Bool,\n    style: null,\n    tabindex: null,\n    title: null,\n    translate: [\"yes\", \"no\"],\n    rel: [\"stylesheet\", \"alternate\", \"author\", \"bookmark\", \"help\", \"license\", \"next\", \"nofollow\", \"noreferrer\", \"prefetch\", \"prev\", \"search\", \"tag\"],\n    role: /*@__PURE__*/\"alert application article banner button cell checkbox complementary contentinfo dialog document feed figure form grid gridcell heading img list listbox listitem main navigation region row rowgroup search switch tab table tabpanel textbox timer\".split(\" \"),\n    \"aria-activedescendant\": null,\n    \"aria-atomic\": Bool,\n    \"aria-autocomplete\": [\"inline\", \"list\", \"both\", \"none\"],\n    \"aria-busy\": Bool,\n    \"aria-checked\": [\"true\", \"false\", \"mixed\", \"undefined\"],\n    \"aria-controls\": null,\n    \"aria-describedby\": null,\n    \"aria-disabled\": Bool,\n    \"aria-dropeffect\": null,\n    \"aria-expanded\": [\"true\", \"false\", \"undefined\"],\n    \"aria-flowto\": null,\n    \"aria-grabbed\": [\"true\", \"false\", \"undefined\"],\n    \"aria-haspopup\": Bool,\n    \"aria-hidden\": Bool,\n    \"aria-invalid\": [\"true\", \"false\", \"grammar\", \"spelling\"],\n    \"aria-label\": null,\n    \"aria-labelledby\": null,\n    \"aria-level\": null,\n    \"aria-live\": [\"off\", \"polite\", \"assertive\"],\n    \"aria-multiline\": Bool,\n    \"aria-multiselectable\": Bool,\n    \"aria-owns\": null,\n    \"aria-posinset\": null,\n    \"aria-pressed\": [\"true\", \"false\", \"mixed\", \"undefined\"],\n    \"aria-readonly\": Bool,\n    \"aria-relevant\": null,\n    \"aria-required\": Bool,\n    \"aria-selected\": [\"true\", \"false\", \"undefined\"],\n    \"aria-setsize\": null,\n    \"aria-sort\": [\"ascending\", \"descending\", \"none\", \"other\"],\n    \"aria-valuemax\": null,\n    \"aria-valuemin\": null,\n    \"aria-valuenow\": null,\n    \"aria-valuetext\": null\n};\nconst eventAttributes = /*@__PURE__*/(\"beforeunload copy cut dragstart dragover dragleave dragenter dragend \" +\n    \"drag paste focus blur change click load mousedown mouseenter mouseleave \" +\n    \"mouseup keydown keyup resize scroll unload\").split(\" \").map(n => \"on\" + n);\nfor (let a of eventAttributes)\n    GlobalAttrs[a] = null;\nclass Schema {\n    constructor(extraTags, extraAttrs) {\n        this.tags = Object.assign(Object.assign({}, Tags), extraTags);\n        this.globalAttrs = Object.assign(Object.assign({}, GlobalAttrs), extraAttrs);\n        this.allTags = Object.keys(this.tags);\n        this.globalAttrNames = Object.keys(this.globalAttrs);\n    }\n}\nSchema.default = /*@__PURE__*/new Schema;\nfunction elementName(doc, tree, max = doc.length) {\n    if (!tree)\n        return \"\";\n    let tag = tree.firstChild;\n    let name = tag && tag.getChild(\"TagName\");\n    return name ? doc.sliceString(name.from, Math.min(name.to, max)) : \"\";\n}\nfunction findParentElement(tree, skip = false) {\n    for (; tree; tree = tree.parent)\n        if (tree.name == \"Element\") {\n            if (skip)\n                skip = false;\n            else\n                return tree;\n        }\n    return null;\n}\nfunction allowedChildren(doc, tree, schema) {\n    let parentInfo = schema.tags[elementName(doc, findParentElement(tree))];\n    return (parentInfo === null || parentInfo === void 0 ? void 0 : parentInfo.children) || schema.allTags;\n}\nfunction openTags(doc, tree) {\n    let open = [];\n    for (let parent = findParentElement(tree); parent && !parent.type.isTop; parent = findParentElement(parent.parent)) {\n        let tagName = elementName(doc, parent);\n        if (tagName && parent.lastChild.name == \"CloseTag\")\n            break;\n        if (tagName && open.indexOf(tagName) < 0 && (tree.name == \"EndTag\" || tree.from >= parent.firstChild.to))\n            open.push(tagName);\n    }\n    return open;\n}\nconst identifier = /^[:\\-\\.\\w\\u00b7-\\uffff]*$/;\nfunction completeTag(state, schema, tree, from, to) {\n    let end = /\\s*>/.test(state.sliceDoc(to, to + 5)) ? \"\" : \">\";\n    let parent = findParentElement(tree, true);\n    return { from, to,\n        options: allowedChildren(state.doc, parent, schema).map(tagName => ({ label: tagName, type: \"type\" })).concat(openTags(state.doc, tree).map((tag, i) => ({ label: \"/\" + tag, apply: \"/\" + tag + end,\n            type: \"type\", boost: 99 - i }))),\n        validFor: /^\\/?[:\\-\\.\\w\\u00b7-\\uffff]*$/ };\n}\nfunction completeCloseTag(state, tree, from, to) {\n    let end = /\\s*>/.test(state.sliceDoc(to, to + 5)) ? \"\" : \">\";\n    return { from, to,\n        options: openTags(state.doc, tree).map((tag, i) => ({ label: tag, apply: tag + end, type: \"type\", boost: 99 - i })),\n        validFor: identifier };\n}\nfunction completeStartTag(state, schema, tree, pos) {\n    let options = [], level = 0;\n    for (let tagName of allowedChildren(state.doc, tree, schema))\n        options.push({ label: \"<\" + tagName, type: \"type\" });\n    for (let open of openTags(state.doc, tree))\n        options.push({ label: \"</\" + open + \">\", type: \"type\", boost: 99 - level++ });\n    return { from: pos, to: pos, options, validFor: /^<\\/?[:\\-\\.\\w\\u00b7-\\uffff]*$/ };\n}\nfunction completeAttrName(state, schema, tree, from, to) {\n    let elt = findParentElement(tree), info = elt ? schema.tags[elementName(state.doc, elt)] : null;\n    let localAttrs = info && info.attrs ? Object.keys(info.attrs) : [];\n    let names = info && info.globalAttrs === false ? localAttrs\n        : localAttrs.length ? localAttrs.concat(schema.globalAttrNames) : schema.globalAttrNames;\n    return { from, to,\n        options: names.map(attrName => ({ label: attrName, type: \"property\" })),\n        validFor: identifier };\n}\nfunction completeAttrValue(state, schema, tree, from, to) {\n    var _a;\n    let nameNode = (_a = tree.parent) === null || _a === void 0 ? void 0 : _a.getChild(\"AttributeName\");\n    let options = [], token = undefined;\n    if (nameNode) {\n        let attrName = state.sliceDoc(nameNode.from, nameNode.to);\n        let attrs = schema.globalAttrs[attrName];\n        if (!attrs) {\n            let elt = findParentElement(tree), info = elt ? schema.tags[elementName(state.doc, elt)] : null;\n            attrs = (info === null || info === void 0 ? void 0 : info.attrs) && info.attrs[attrName];\n        }\n        if (attrs) {\n            let base = state.sliceDoc(from, to).toLowerCase(), quoteStart = '\"', quoteEnd = '\"';\n            if (/^['\"]/.test(base)) {\n                token = base[0] == '\"' ? /^[^\"]*$/ : /^[^']*$/;\n                quoteStart = \"\";\n                quoteEnd = state.sliceDoc(to, to + 1) == base[0] ? \"\" : base[0];\n                base = base.slice(1);\n                from++;\n            }\n            else {\n                token = /^[^\\s<>='\"]*$/;\n            }\n            for (let value of attrs)\n                options.push({ label: value, apply: quoteStart + value + quoteEnd, type: \"constant\" });\n        }\n    }\n    return { from, to, options, validFor: token };\n}\nfunction htmlCompletionFor(schema, context) {\n    let { state, pos } = context, tree = syntaxTree(state).resolveInner(pos, -1), around = tree.resolve(pos);\n    for (let scan = pos, before; around == tree && (before = tree.childBefore(scan));) {\n        let last = before.lastChild;\n        if (!last || !last.type.isError || last.from < last.to)\n            break;\n        around = tree = before;\n        scan = last.from;\n    }\n    if (tree.name == \"TagName\") {\n        return tree.parent && /CloseTag$/.test(tree.parent.name) ? completeCloseTag(state, tree, tree.from, pos)\n            : completeTag(state, schema, tree, tree.from, pos);\n    }\n    else if (tree.name == \"StartTag\") {\n        return completeTag(state, schema, tree, pos, pos);\n    }\n    else if (tree.name == \"StartCloseTag\" || tree.name == \"IncompleteCloseTag\") {\n        return completeCloseTag(state, tree, pos, pos);\n    }\n    else if (tree.name == \"OpenTag\" || tree.name == \"SelfClosingTag\" || tree.name == \"AttributeName\") {\n        return completeAttrName(state, schema, tree, tree.name == \"AttributeName\" ? tree.from : pos, pos);\n    }\n    else if (tree.name == \"Is\" || tree.name == \"AttributeValue\" || tree.name == \"UnquotedAttributeValue\") {\n        return completeAttrValue(state, schema, tree, tree.name == \"Is\" ? pos : tree.from, pos);\n    }\n    else if (context.explicit && (around.name == \"Element\" || around.name == \"Text\" || around.name == \"Document\")) {\n        return completeStartTag(state, schema, tree, pos);\n    }\n    else {\n        return null;\n    }\n}\n/**\nHTML tag completion. Opens and closes tags and attributes in a\ncontext-aware way.\n*/\nfunction htmlCompletionSource(context) {\n    return htmlCompletionFor(Schema.default, context);\n}\n/**\nCreate a completion source for HTML extended with additional tags\nor attributes.\n*/\nfunction htmlCompletionSourceWith(config) {\n    let { extraTags, extraGlobalAttributes: extraAttrs } = config;\n    let schema = extraAttrs || extraTags ? new Schema(extraTags, extraAttrs) : Schema.default;\n    return (context) => htmlCompletionFor(schema, context);\n}\n\nconst jsonParser = /*@__PURE__*/javascriptLanguage.parser.configure({ top: \"SingleExpression\" });\nconst defaultNesting = [\n    { tag: \"script\",\n        attrs: attrs => attrs.type == \"text/typescript\" || attrs.lang == \"ts\",\n        parser: typescriptLanguage.parser },\n    { tag: \"script\",\n        attrs: attrs => attrs.type == \"text/babel\" || attrs.type == \"text/jsx\",\n        parser: jsxLanguage.parser },\n    { tag: \"script\",\n        attrs: attrs => attrs.type == \"text/typescript-jsx\",\n        parser: tsxLanguage.parser },\n    { tag: \"script\",\n        attrs(attrs) {\n            return /^(importmap|speculationrules|application\\/(.+\\+)?json)$/i.test(attrs.type);\n        },\n        parser: jsonParser },\n    { tag: \"script\",\n        attrs(attrs) {\n            return !attrs.type || /^(?:text|application)\\/(?:x-)?(?:java|ecma)script$|^module$|^$/i.test(attrs.type);\n        },\n        parser: javascriptLanguage.parser },\n    { tag: \"style\",\n        attrs(attrs) {\n            return (!attrs.lang || attrs.lang == \"css\") && (!attrs.type || /^(text\\/)?(x-)?(stylesheet|css)$/i.test(attrs.type));\n        },\n        parser: cssLanguage.parser }\n];\nconst defaultAttrs = /*@__PURE__*/[\n    { name: \"style\",\n        parser: /*@__PURE__*/cssLanguage.parser.configure({ top: \"Styles\" }) }\n].concat(/*@__PURE__*/eventAttributes.map(name => ({ name, parser: javascriptLanguage.parser })));\n/**\nA language provider based on the [Lezer HTML\nparser](https://github.com/lezer-parser/html), extended with the\nJavaScript and CSS parsers to parse the content of `<script>` and\n`<style>` tags.\n*/\nconst htmlPlain = /*@__PURE__*/LRLanguage.define({\n    name: \"html\",\n    parser: /*@__PURE__*/parser.configure({\n        props: [\n            /*@__PURE__*/indentNodeProp.add({\n                Element(context) {\n                    let after = /^(\\s*)(<\\/)?/.exec(context.textAfter);\n                    if (context.node.to <= context.pos + after[0].length)\n                        return context.continue();\n                    return context.lineIndent(context.node.from) + (after[2] ? 0 : context.unit);\n                },\n                \"OpenTag CloseTag SelfClosingTag\"(context) {\n                    return context.column(context.node.from) + context.unit;\n                },\n                Document(context) {\n                    if (context.pos + /\\s*/.exec(context.textAfter)[0].length < context.node.to)\n                        return context.continue();\n                    let endElt = null, close;\n                    for (let cur = context.node;;) {\n                        let last = cur.lastChild;\n                        if (!last || last.name != \"Element\" || last.to != cur.to)\n                            break;\n                        endElt = cur = last;\n                    }\n                    if (endElt && !((close = endElt.lastChild) && (close.name == \"CloseTag\" || close.name == \"SelfClosingTag\")))\n                        return context.lineIndent(endElt.from) + context.unit;\n                    return null;\n                }\n            }),\n            /*@__PURE__*/foldNodeProp.add({\n                Element(node) {\n                    let first = node.firstChild, last = node.lastChild;\n                    if (!first || first.name != \"OpenTag\")\n                        return null;\n                    return { from: first.to, to: last.name == \"CloseTag\" ? last.from : node.to };\n                }\n            }),\n            /*@__PURE__*/bracketMatchingHandle.add({\n                \"OpenTag CloseTag\": node => node.getChild(\"TagName\")\n            })\n        ]\n    }),\n    languageData: {\n        commentTokens: { block: { open: \"<!--\", close: \"-->\" } },\n        indentOnInput: /^\\s*<\\/\\w+\\W$/,\n        wordChars: \"-._\"\n    }\n});\n/**\nA language provider based on the [Lezer HTML\nparser](https://github.com/lezer-parser/html), extended with the\nJavaScript and CSS parsers to parse the content of `<script>` and\n`<style>` tags.\n*/\nconst htmlLanguage = /*@__PURE__*/htmlPlain.configure({\n    wrap: /*@__PURE__*/configureNesting(defaultNesting, defaultAttrs)\n});\n/**\nLanguage support for HTML, including\n[`htmlCompletion`](https://codemirror.net/6/docs/ref/#lang-html.htmlCompletion) and JavaScript and\nCSS support extensions.\n*/\nfunction html(config = {}) {\n    let dialect = \"\", wrap;\n    if (config.matchClosingTags === false)\n        dialect = \"noMatch\";\n    if (config.selfClosingTags === true)\n        dialect = (dialect ? dialect + \" \" : \"\") + \"selfClosing\";\n    if (config.nestedLanguages && config.nestedLanguages.length ||\n        config.nestedAttributes && config.nestedAttributes.length)\n        wrap = configureNesting((config.nestedLanguages || []).concat(defaultNesting), (config.nestedAttributes || []).concat(defaultAttrs));\n    let lang = wrap ? htmlPlain.configure({ wrap, dialect }) : dialect ? htmlLanguage.configure({ dialect }) : htmlLanguage;\n    return new LanguageSupport(lang, [\n        htmlLanguage.data.of({ autocomplete: htmlCompletionSourceWith(config) }),\n        config.autoCloseTags !== false ? autoCloseTags : [],\n        javascript().support,\n        css().support\n    ]);\n}\nconst selfClosers = /*@__PURE__*/new Set(/*@__PURE__*/\"area base br col command embed frame hr img input keygen link meta param source track wbr menuitem\".split(\" \"));\n/**\nExtension that will automatically insert close tags when a `>` or\n`/` is typed.\n*/\nconst autoCloseTags = /*@__PURE__*/EditorView.inputHandler.of((view, from, to, text, insertTransaction) => {\n    if (view.composing || view.state.readOnly || from != to || (text != \">\" && text != \"/\") ||\n        !htmlLanguage.isActiveAt(view.state, from, -1))\n        return false;\n    let base = insertTransaction(), { state } = base;\n    let closeTags = state.changeByRange(range => {\n        var _a, _b, _c;\n        let didType = state.doc.sliceString(range.from - 1, range.to) == text;\n        let { head } = range, after = syntaxTree(state).resolveInner(head, -1), name;\n        if (didType && text == \">\" && after.name == \"EndTag\") {\n            let tag = after.parent;\n            if (((_b = (_a = tag.parent) === null || _a === void 0 ? void 0 : _a.lastChild) === null || _b === void 0 ? void 0 : _b.name) != \"CloseTag\" &&\n                (name = elementName(state.doc, tag.parent, head)) &&\n                !selfClosers.has(name)) {\n                let to = head + (state.doc.sliceString(head, head + 1) === \">\" ? 1 : 0);\n                let insert = `</${name}>`;\n                return { range, changes: { from: head, to, insert } };\n            }\n        }\n        else if (didType && text == \"/\" && after.name == \"IncompleteCloseTag\") {\n            let tag = after.parent;\n            if (after.from == head - 2 && ((_c = tag.lastChild) === null || _c === void 0 ? void 0 : _c.name) != \"CloseTag\" &&\n                (name = elementName(state.doc, tag, head)) && !selfClosers.has(name)) {\n                let to = head + (state.doc.sliceString(head, head + 1) === \">\" ? 1 : 0);\n                let insert = `${name}>`;\n                return {\n                    range: EditorSelection.cursor(head + insert.length, -1),\n                    changes: { from: head, to, insert }\n                };\n            }\n        }\n        return { range };\n    });\n    if (closeTags.changes.empty)\n        return false;\n    view.dispatch([\n        base,\n        state.update(closeTags, {\n            userEvent: \"input.complete\",\n            scrollIntoView: true\n        })\n    ]);\n    return true;\n});\n\nexport { autoCloseTags, html, htmlCompletionSource, htmlCompletionSourceWith, htmlLanguage, htmlPlain };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,IAAM,aAAa;AAAnB,IACE,sBAAsB;AADxB,IAEE,YAAY;AAFd,IAGE,qBAAqB;AAHvB,IAIE,eAAe;AAJjB,IAKE,wBAAwB;AAL1B,IAME,SAAS;AANX,IAOE,oBAAoB;AAPtB,IAQE,WAAW;AARb,IASE,iBAAiB;AATnB,IAUE,gBAAgB;AAVlB,IAWE,mBAAmB;AAXrB,IAYE,sBAAsB;AAZxB,IAaE,gBAAgB;AAblB,IAcE,uBAAuB;AAdzB,IAeE,0BAA0B;AAf5B,IAgBE,kBAAkB;AAhBpB,IAiBE,qBAAqB;AAjBvB,IAkBE,mBAAmB;AAlBrB,IAmBE,UAAU;AAnBZ,IAoBE,UAAU;AApBZ,IAqBE,YAAY;AArBd,IAsBE,gBAAgB;AAtBlB,IAuBE,iBAAiB;AAvBnB,IAwBE,yBAAyB;AAxB3B,IAyBE,aAAa;AAzBf,IA0BE,YAAY;AA1Bd,IA2BE,eAAe;AA3BjB,IA4BE,UAAU;AA5BZ,IA6BE,WAAW;AA7Bb,IA8BE,kBAAkB;AA9BpB,IA+BE,sBAAsB;AAIxB,IAAM,cAAc;AAAA,EAClB,MAAM;AAAA,EAAM,MAAM;AAAA,EAAM,IAAI;AAAA,EAAM,KAAK;AAAA,EAAM,SAAS;AAAA,EACtD,OAAO;AAAA,EAAM,OAAO;AAAA,EAAM,IAAI;AAAA,EAAM,KAAK;AAAA,EAAM,OAAO;AAAA,EACtD,QAAQ;AAAA,EAAM,MAAM;AAAA,EAAM,MAAM;AAAA,EAAM,OAAO;AAAA,EAAM,QAAQ;AAAA,EAC3D,OAAO;AAAA,EAAM,KAAK;AAAA,EAAM,UAAU;AACpC;AAEA,IAAM,mBAAmB;AAAA,EACvB,IAAI;AAAA,EAAM,IAAI;AAAA,EAAM,UAAU;AAAA,EAAM,QAAQ;AAAA,EAAM,GAAG;AAAA,EACrD,IAAI;AAAA,EAAM,IAAI;AAAA,EAAM,OAAO;AAAA,EAAM,IAAI;AAAA,EAAM,OAAO;AAAA,EAClD,IAAI;AAAA,EAAM,IAAI;AAChB;AAEA,IAAM,cAAc;AAAA,EAClB,IAAI,EAAC,IAAI,MAAM,IAAI,KAAI;AAAA,EACvB,IAAI,EAAC,IAAI,MAAM,IAAI,KAAI;AAAA,EACvB,IAAI,EAAC,IAAI,KAAI;AAAA,EACb,QAAQ,EAAC,QAAQ,MAAM,UAAU,KAAI;AAAA,EACrC,UAAU,EAAC,UAAU,KAAI;AAAA,EACzB,GAAG;AAAA,IACD,SAAS;AAAA,IAAM,SAAS;AAAA,IAAM,OAAO;AAAA,IAAM,YAAY;AAAA,IAAM,KAAK;AAAA,IAClE,KAAK;AAAA,IAAM,IAAI;AAAA,IAAM,UAAU;AAAA,IAAM,QAAQ;AAAA,IAAM,MAAM;AAAA,IACzD,IAAI;AAAA,IAAM,IAAI;AAAA,IAAM,IAAI;AAAA,IAAM,IAAI;AAAA,IAAM,IAAI;AAAA,IAAM,IAAI;AAAA,IACtD,QAAQ;AAAA,IAAM,QAAQ;AAAA,IAAM,IAAI;AAAA,IAAM,MAAM;AAAA,IAAM,KAAK;AAAA,IAAM,IAAI;AAAA,IACjE,GAAG;AAAA,IAAM,KAAK;AAAA,IAAM,SAAS;AAAA,IAAM,OAAO;AAAA,IAAM,IAAI;AAAA,EACtD;AAAA,EACA,IAAI,EAAC,IAAI,MAAM,IAAI,KAAI;AAAA,EACvB,IAAI,EAAC,IAAI,MAAM,IAAI,KAAI;AAAA,EACvB,OAAO,EAAC,OAAO,MAAM,OAAO,KAAI;AAAA,EAChC,IAAI,EAAC,IAAI,MAAM,IAAI,KAAI;AAAA,EACvB,OAAO,EAAC,OAAO,KAAI;AAAA,EACnB,IAAI,EAAC,IAAI,MAAM,IAAI,KAAI;AAAA,EACvB,OAAO,EAAC,OAAO,MAAM,OAAO,KAAI;AAAA,EAChC,IAAI,EAAC,IAAI,KAAI;AACf;AAEA,SAAS,SAAS,IAAI;AACpB,SAAO,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM;AAChH;AAEA,SAAS,QAAQ,IAAI;AACnB,SAAO,MAAM,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM;AAClD;AAEA,IAAI,aAAa;AAAjB,IAAuB,cAAc;AAArC,IAA2C,YAAY;AACvD,SAAS,aAAa,OAAO,QAAQ;AACnC,MAAI,MAAM,MAAM,MAAM;AACtB,MAAI,aAAa,OAAO,eAAe,MAAO,QAAO;AACrD,MAAI,OAAO,MAAM,KAAK,MAAM;AAC5B,SAAO,QAAQ,IAAI,EAAG,QAAO,MAAM,KAAK,EAAE,MAAM;AAChD,MAAI,OAAO;AACX,aAAS;AACP,QAAI,CAAC,SAAS,IAAI,EAAG;AACrB,YAAQ,OAAO,aAAa,IAAI;AAChC,WAAO,MAAM,KAAK,EAAE,MAAM;AAAA,EAC5B;AAEA,gBAAc;AAAO,cAAY;AACjC,SAAO,aAAa,OAAO,KAAK,YAAY,IAAI,QAAQ,YAAY,QAAQ,OAAO,SAAY;AACjG;AAEA,IAAM,WAAW;AAAjB,IAAqB,cAAc;AAAnC,IAAuC,QAAQ;AAA/C,IAAmD,WAAW;AAA9D,IAAkE,OAAO;AAAzE,IAA6E,OAAO;AAEpF,SAAS,eAAe,MAAM,QAAQ;AACpC,OAAK,OAAO;AACZ,OAAK,SAAS;AAChB;AAEA,IAAM,gBAAgB,CAAC,UAAU,qBAAqB,gBAAgB,eAAe,gBAAgB;AAErG,IAAM,iBAAiB,IAAI,eAAe;AAAA,EACxC,OAAO;AAAA,EACP,MAAM,SAAS,MAAM,OAAO,OAAO;AACjC,WAAO,cAAc,QAAQ,IAAI,IAAI,KAAK,IAAI,eAAe,aAAa,OAAO,CAAC,KAAK,IAAI,OAAO,IAAI;AAAA,EACxG;AAAA,EACA,OAAO,SAAS,MAAM;AACpB,WAAO,QAAQ,WAAW,UAAU,QAAQ,SAAS;AAAA,EACvD;AAAA,EACA,MAAM,SAAS,MAAM,OAAO,OAAO;AACjC,QAAI,OAAO,KAAK,KAAK;AACrB,WAAO,QAAQ,YAAY,QAAQ,UAC/B,IAAI,eAAe,aAAa,OAAO,CAAC,KAAK,IAAI,OAAO,IAAI;AAAA,EAClE;AAAA,EACA,QAAQ;AACV,CAAC;AAED,IAAM,WAAW,IAAI,kBAAkB,CAAC,OAAO,UAAU;AACvD,MAAI,MAAM,QAAQ,UAAU;AAE1B,QAAI,MAAM,OAAO,KAAK,MAAM,QAAS,OAAM,YAAY,eAAe;AACtE;AAAA,EACF;AACA,QAAM,QAAQ;AACd,MAAI,QAAQ,MAAM,QAAQ;AAC1B,MAAI,MAAO,OAAM,QAAQ;AACzB,MAAI,OAAO,aAAa,OAAO,CAAC;AAChC,MAAI,SAAS,OAAW;AACxB,MAAI,CAAC,KAAM,QAAO,MAAM,YAAY,QAAQ,qBAAqB,QAAQ;AAEzE,MAAI,SAAS,MAAM,UAAU,MAAM,QAAQ,OAAO;AAClD,MAAI,OAAO;AACT,QAAI,QAAQ,OAAQ,QAAO,MAAM,YAAY,aAAa;AAC1D,QAAI,UAAU,iBAAiB,MAAM,EAAG,QAAO,MAAM,YAAY,iBAAiB,EAAE;AACpF,QAAI,MAAM,eAAe,eAAe,EAAG,QAAO,MAAM,YAAY,oBAAoB;AACxF,aAAS,KAAK,MAAM,SAAS,IAAI,KAAK,GAAG,OAAQ,KAAI,GAAG,QAAQ,KAAM;AACtE,UAAM,YAAY,uBAAuB;AAAA,EAC3C,OAAO;AACL,QAAI,QAAQ,SAAU,QAAO,MAAM,YAAY,cAAc;AAC7D,QAAI,QAAQ,QAAS,QAAO,MAAM,YAAY,aAAa;AAC3D,QAAI,QAAQ,WAAY,QAAO,MAAM,YAAY,gBAAgB;AACjE,QAAI,YAAY,eAAe,IAAI,EAAG,QAAO,MAAM,YAAY,mBAAmB;AAClF,QAAI,UAAU,YAAY,MAAM,KAAK,YAAY,MAAM,EAAE,IAAI,EAAG,OAAM,YAAY,iBAAiB,EAAE;AAAA,QAChG,OAAM,YAAY,QAAQ;AAAA,EACjC;AACF,GAAG,EAAC,YAAY,KAAI,CAAC;AAErB,IAAM,iBAAiB,IAAI,kBAAkB,WAAS;AACpD,WAAS,SAAS,GAAG,IAAI,KAAI,KAAK;AAChC,QAAI,MAAM,OAAO,GAAG;AAClB,UAAI,EAAG,OAAM,YAAY,gBAAgB;AACzC;AAAA,IACF;AACA,QAAI,MAAM,QAAQ,MAAM;AACtB;AAAA,IACF,WAAW,MAAM,QAAQ,eAAe,UAAU,GAAG;AACnD,UAAI,KAAK,EAAG,OAAM,YAAY,kBAAkB,EAAE;AAClD;AAAA,IACF,OAAO;AACL,eAAS;AAAA,IACX;AACA,UAAM,QAAQ;AAAA,EAChB;AACF,CAAC;AAED,SAAS,iBAAiB,SAAS;AACjC,SAAO,SAAS,UAAU,QAAQ;AAChC,QAAI,QAAQ,QAAQ,SAAS,QAAQ,QAAQ,OAAQ,QAAO;AAC9D,SAAO;AACT;AAEA,IAAM,SAAS,IAAI,kBAAkB,CAAC,OAAO,UAAU;AACrD,MAAI,MAAM,QAAQ,SAAS,MAAM,KAAK,CAAC,KAAK,aAAa;AACvD,QAAI,cAAc,MAAM,eAAe,mBAAmB,KAAK,iBAAiB,MAAM,OAAO;AAC7F,UAAM,YAAY,cAAc,oBAAoB,QAAQ,CAAC;AAAA,EAC/D,WAAW,MAAM,QAAQ,aAAa;AACpC,UAAM,YAAY,QAAQ,CAAC;AAAA,EAC7B;AACF,CAAC;AAED,SAAS,iBAAiB,KAAK,WAAW,UAAU;AAClD,MAAI,YAAY,IAAI,IAAI;AACxB,SAAO,IAAI,kBAAkB,WAAS;AAOpC,aAAS,QAAQ,GAAG,aAAa,GAAG,IAAI,KAAI,KAAK;AAC/C,UAAI,MAAM,OAAO,GAAG;AAClB,YAAI,EAAG,OAAM,YAAY,SAAS;AAClC;AAAA,MACF;AACA,UAAI,SAAS,KAAK,MAAM,QAAQ,YAC5B,SAAS,KAAK,MAAM,QAAQ,SAC5B,SAAS,KAAK,QAAQ,aAAa,MAAM,QAAQ,IAAI,WAAW,QAAQ,CAAC,GAAG;AAC9E;AACA;AAAA,MACF,YAAY,SAAS,KAAK,SAAS,cAAc,QAAQ,MAAM,IAAI,GAAG;AACpE;AAAA,MACF,WAAW,SAAS,aAAa,MAAM,QAAQ,aAAa;AAC1D,YAAI,IAAI;AACN,gBAAM,YAAY,WAAW,CAAC,UAAU;AAAA;AAExC,gBAAM,YAAY,UAAU,EAAE,aAAa,EAAE;AAC/C;AAAA,MACF,YAAY,MAAM,QAAQ,MAAiB,MAAM,QAAQ,OAAkB,GAAG;AAC5E,cAAM,YAAY,WAAW,CAAC;AAC9B;AAAA,MACF,OAAO;AACL,gBAAQ,aAAa;AAAA,MACvB;AACA,YAAM,QAAQ;AAAA,IAChB;AAAA,EACF,CAAC;AACH;AAEA,IAAM,eAAe,iBAAiB,UAAU,YAAY,mBAAmB;AAE/E,IAAM,cAAc,iBAAiB,SAAS,WAAW,kBAAkB;AAE3E,IAAM,iBAAiB,iBAAiB,YAAY,cAAc,qBAAqB;AAEvF,IAAM,mBAAmB,UAAU;AAAA,EACjC,gBAAgB,KAAK;AAAA,EACrB,mDAAmD,KAAK;AAAA,EACxD,SAAS,KAAK;AAAA,EACd,8BAA8B,CAAC,KAAK,SAAU,KAAK,OAAO;AAAA,EAC1D,eAAe,KAAK;AAAA,EACpB,yCAAyC,KAAK;AAAA,EAC9C,IAAI,KAAK;AAAA,EACT,sCAAsC,KAAK;AAAA,EAC3C,SAAS,KAAK;AAAA,EACd,gBAAgB,KAAK;AAAA,EACrB,aAAa,KAAK;AACpB,CAAC;AAGD,IAAM,SAAS,SAAS,YAAY;AAAA,EAClC,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,MAAM;AAAA,EACN,WAAW;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,WAAW;AAAA,IACT,CAAC,YAAY,KAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,UAAS,GAAE,4BAA2B,IAAG,IAAG,IAAG,IAAG,IAAG,UAAU;AAAA,IACxG,CAAC,YAAY,GAAE,0BAAyB,GAAE,YAAW,IAAG,IAAG,IAAG,IAAG,IAAG,SAAS;AAAA,IAC7E,CAAC,SAAS,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,UAAS,IAAG,sBAAqB,IAAG,IAAG,IAAG,IAAG,oBAAoB;AAAA,IACzG,CAAC,WAAW,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,OAAM,IAAG,IAAG,IAAG,IAAG,EAAE;AAAA,EACvE;AAAA,EACA,aAAa,CAAC,gBAAgB;AAAA,EAC9B,cAAc,CAAC,CAAC;AAAA,EAChB,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,YAAY,CAAC,cAAc,aAAa,gBAAgB,QAAQ,UAAU,gBAAgB,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,EAC1G,UAAU,EAAC,YAAW,CAAC,GAAE,EAAE,EAAC;AAAA,EAC5B,UAAU,EAAC,SAAS,GAAG,aAAa,IAAG;AAAA,EACvC,WAAW;AACb,CAAC;AAED,SAAS,SAAS,SAAS,OAAO;AAChC,MAAI,QAAQ,uBAAO,OAAO,IAAI;AAC9B,WAAS,OAAO,QAAQ,YAAY,SAAS,GAAG;AAC9C,QAAI,OAAO,IAAI,SAAS,aAAa,GAAG,QAAQ,IAAI,SAAS,cAAc,KAAK,IAAI,SAAS,sBAAsB;AACnH,QAAI,KAAM,OAAM,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,CAAC,IAC5C,CAAC,QAAQ,KAAK,MAAM,KAAK,MAAM,iBAAiB,MAAM,KAAK,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,IAAI,MAAM,KAAK,MAAM,MAAM,MAAM,EAAE;AAAA,EAC9H;AACA,SAAO;AACT;AAEA,SAAS,YAAY,SAAS,OAAO;AACnC,MAAI,cAAc,QAAQ,SAAS,OAAO;AAC1C,SAAO,cAAc,MAAM,KAAK,YAAY,MAAM,YAAY,EAAE,IAAI;AACtE;AAEA,SAAS,UAAU,MAAM,OAAOA,OAAM;AACpC,MAAI;AACJ,WAAS,OAAOA,OAAM;AACpB,QAAI,CAAC,IAAI,SAAS,IAAI,MAAM,UAAU,QAAQ,SAAS,KAAK,KAAK,OAAO,YAAY,KAAK,EAAE;AACzF,aAAO,EAAC,QAAQ,IAAI,OAAM;AAAA,EAC9B;AACA,SAAO;AACT;AAaA,SAAS,iBAAiBA,QAAO,CAAC,GAAG,aAAa,CAAC,GAAG;AACpD,MAAI,SAAS,CAAC,GAAG,QAAQ,CAAC,GAAG,WAAW,CAAC,GAAG,QAAQ,CAAC;AACrD,WAAS,OAAOA,OAAM;AACpB,QAAI,QAAQ,IAAI,OAAO,WAAW,SAAS,IAAI,OAAO,UAAU,QAAQ,IAAI,OAAO,aAAa,WAAW;AAC3G,UAAM,KAAK,GAAG;AAAA,EAChB;AACA,MAAI,QAAQ,WAAW,SAAS,uBAAO,OAAO,IAAI,IAAI;AACtD,WAAS,QAAQ,WAAY,EAAC,MAAM,KAAK,IAAI,MAAM,MAAM,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI;AAEpF,SAAO,WAAW,CAAC,MAAM,UAAU;AACjC,QAAI,KAAK,KAAK,KAAK;AACnB,QAAI,MAAM,WAAY,QAAO,UAAU,MAAM,OAAO,MAAM;AAC1D,QAAI,MAAM,UAAW,QAAO,UAAU,MAAM,OAAO,KAAK;AACxD,QAAI,MAAM,aAAc,QAAO,UAAU,MAAM,OAAO,QAAQ;AAE9D,QAAI,MAAM,WAAW,MAAM,QAAQ;AACjC,UAAI,IAAI,KAAK,MAAM,OAAO,EAAE,YAAY,UAAU,QAAQ,YAAY,MAAM,KAAK,GAAGC;AACpF,UAAI,QAAS,UAAS,OAAO,OAAO;AAClC,YAAI,IAAI,OAAO,YAAY,CAAC,IAAI,SAAS,IAAI,MAAMA,WAAUA,SAAQ,SAAS,MAAM,KAAK,EAAE,IAAI;AAC7F,cAAI,QAAQ,EAAE;AACd,cAAI,KAAK,MAAM,KAAK,MAAM,WAAW,MAAM,OAAO,EAAE;AACpD,cAAI,KAAK,KAAK;AACZ,mBAAO,EAAC,QAAQ,IAAI,QAAQ,SAAS,CAAC,EAAC,MAAM,KAAK,IAAI,GAAE,CAAC,EAAC;AAAA,QAC9D;AAAA,MACF;AAAA,IACF;AAEA,QAAI,SAAS,MAAM,WAAW;AAC5B,UAAI,IAAI,KAAK,MAAM;AACnB,UAAI,WAAW,EAAE,YAAY;AAC3B,YAAI,UAAU,MAAM,MAAM,KAAK,SAAS,MAAM,SAAS,EAAE,CAAC;AAC1D,YAAI,QAAS,UAAS,QAAQ,SAAS;AACrC,cAAI,KAAK,WAAW,KAAK,WAAW,YAAY,EAAE,QAAQ,KAAK,EAAG;AAClE,cAAI,QAAQ,EAAE;AACd,cAAI,MAAM,KAAK,MAAM,gBAAgB;AACnC,gBAAI,OAAO,MAAM,OAAO;AACxB,gBAAI,OAAO,MAAM,WAAW,KAAK,MAAM,MAAM,QAAQ,KAAK,UAAU,IAAI;AACxE,gBAAI,KAAK,KAAM,QAAO,EAAC,QAAQ,KAAK,QAAQ,SAAS,CAAC,EAAC,MAAM,GAAE,CAAC,EAAC;AAAA,UACnE,WAAW,MAAM,KAAK,MAAM,wBAAwB;AAClD,mBAAO,EAAC,QAAQ,KAAK,QAAQ,SAAS,CAAC,EAAC,MAAM,MAAM,MAAM,IAAI,MAAM,GAAE,CAAC,EAAC;AAAA,UAC1E;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT,CAAC;AACH;;;AC1VA,IAAM,UAAU,CAAC,UAAU,SAAS,QAAQ,SAAS;AACrD,IAAM,WAAW,CAAC,SAAS,SAAS,UAAU,UAAU,QAAQ;AAChE,IAAM,UAAU,CAAC,OAAO,QAAQ,OAAO,QAAQ;AAC/C,IAAM,OAAO,CAAC,qCAAqC,uBAAuB,YAAY;AACtF,IAAM,OAAO,CAAC,QAAQ,OAAO;AAC7B,IAAM,IAAI,CAAC;AACX,IAAM,OAAO;AAAA,EACT,GAAG;AAAA,IACC,OAAO;AAAA,MACH,MAAM;AAAA,MAAM,MAAM;AAAA,MAAM,MAAM;AAAA,MAC9B,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,UAAU;AAAA,IACd;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,EACN,SAAS;AAAA,EACT,MAAM;AAAA,IACF,OAAO;AAAA,MACH,KAAK;AAAA,MAAM,QAAQ;AAAA,MAAM,MAAM;AAAA,MAAM,QAAQ;AAAA,MAAM,MAAM;AAAA,MACzD,OAAO;AAAA,MAAM,UAAU;AAAA,MAAM,MAAM;AAAA,MACnC,OAAO,CAAC,WAAW,QAAQ,UAAU,MAAM;AAAA,IAC/C;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,EACT,OAAO;AAAA,EACP,OAAO;AAAA,IACH,OAAO;AAAA,MACH,KAAK;AAAA,MAAM,YAAY;AAAA,MACvB,aAAa,CAAC,aAAa,iBAAiB;AAAA,MAC5C,SAAS,CAAC,QAAQ,YAAY,MAAM;AAAA,MACpC,UAAU,CAAC,UAAU;AAAA,MACrB,MAAM,CAAC,MAAM;AAAA,MACb,UAAU,CAAC,UAAU;AAAA,IACzB;AAAA,EACJ;AAAA,EACA,GAAG;AAAA,EACH,MAAM,EAAE,OAAO,EAAE,MAAM,MAAM,QAAQ,QAAQ,EAAE;AAAA,EAC/C,KAAK;AAAA,EACL,KAAK;AAAA,EACL,YAAY,EAAE,OAAO,EAAE,MAAM,KAAK,EAAE;AAAA,EACpC,MAAM;AAAA,EACN,IAAI;AAAA,EACJ,QAAQ;AAAA,IACJ,OAAO;AAAA,MACH,MAAM;AAAA,MAAM,YAAY;AAAA,MAAM,MAAM;AAAA,MAAM,OAAO;AAAA,MACjD,WAAW,CAAC,WAAW;AAAA,MACvB,UAAU,CAAC,WAAW;AAAA,MACtB,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,gBAAgB,CAAC,YAAY;AAAA,MAC7B,YAAY;AAAA,MACZ,MAAM,CAAC,UAAU,SAAS,QAAQ;AAAA,IACtC;AAAA,EACJ;AAAA,EACA,QAAQ,EAAE,OAAO,EAAE,OAAO,MAAM,QAAQ,KAAK,EAAE;AAAA,EAC/C,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,MAAM;AAAA,EACN,KAAK,EAAE,OAAO,EAAE,MAAM,KAAK,EAAE;AAAA,EAC7B,UAAU,EAAE,OAAO,EAAE,MAAM,KAAK,EAAE;AAAA,EAClC,SAAS;AAAA,IACL,OAAO;AAAA,MACH,MAAM,CAAC,WAAW,YAAY,OAAO;AAAA,MACrC,OAAO;AAAA,MAAM,MAAM;AAAA,MAAM,YAAY;AAAA,MAAM,SAAS;AAAA,MAAM,OAAO;AAAA,MACjE,UAAU,CAAC,UAAU;AAAA,MACrB,SAAS,CAAC,SAAS;AAAA,IACvB;AAAA,EACJ;AAAA,EACA,MAAM,EAAE,OAAO,EAAE,OAAO,KAAK,EAAE;AAAA,EAC/B,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,EAAE,EAAE;AAAA,EACtE,UAAU,EAAE,OAAO,EAAE,MAAM,KAAK,EAAE;AAAA,EAClC,IAAI;AAAA,EACJ,KAAK,EAAE,OAAO,EAAE,MAAM,MAAM,UAAU,KAAK,EAAE;AAAA,EAC7C,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,EAAE;AAAA,EACrC,KAAK;AAAA,EACL,KAAK;AAAA,EACL,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,OAAO,EAAE,OAAO,EAAE,KAAK,MAAM,MAAM,MAAM,OAAO,MAAM,QAAQ,KAAK,EAAE;AAAA,EACrE,aAAa,EAAE,OAAO,EAAE,KAAK,KAAK,EAAE;AAAA,EACpC,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,UAAU,GAAG,MAAM,MAAM,MAAM,KAAK,EAAE;AAAA,EACtE,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,MAAM;AAAA,IACF,OAAO;AAAA,MACH,QAAQ;AAAA,MAAM,MAAM;AAAA,MACpB,kBAAkB;AAAA,MAClB,cAAc,CAAC,MAAM,KAAK;AAAA,MAC1B,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,YAAY,CAAC,YAAY;AAAA,MACzB,QAAQ;AAAA,IACZ;AAAA,EACJ;AAAA,EACA,IAAI;AAAA,EAAG,IAAI;AAAA,EAAG,IAAI;AAAA,EAAG,IAAI;AAAA,EAAG,IAAI;AAAA,EAAG,IAAI;AAAA,EACvC,MAAM;AAAA,IACF,UAAU,CAAC,SAAS,QAAQ,QAAQ,SAAS,QAAQ,UAAU,YAAY,SAAS;AAAA,EACxF;AAAA,EACA,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,IAAI;AAAA,EACJ,MAAM;AAAA,IACF,OAAO,EAAE,UAAU,KAAK;AAAA,EAC5B;AAAA,EACA,GAAG;AAAA,EACH,QAAQ;AAAA,IACJ,OAAO;AAAA,MACH,KAAK;AAAA,MAAM,QAAQ;AAAA,MAAM,MAAM;AAAA,MAAM,OAAO;AAAA,MAAM,QAAQ;AAAA,MAC1D,SAAS,CAAC,wBAAwB,qBAAqB,eAAe,eAAe;AAAA,MACrF,UAAU,CAAC,UAAU;AAAA,IACzB;AAAA,EACJ;AAAA,EACA,KAAK;AAAA,IACD,OAAO;AAAA,MACH,KAAK;AAAA,MAAM,KAAK;AAAA,MAAM,OAAO;AAAA,MAAM,QAAQ;AAAA,MAAM,OAAO;AAAA,MAAM,QAAQ;AAAA,MACtE,aAAa,CAAC,aAAa,iBAAiB;AAAA,IAChD;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH,OAAO;AAAA,MACH,KAAK;AAAA,MAAM,SAAS;AAAA,MAAM,MAAM;AAAA,MAAM,YAAY;AAAA,MAClD,QAAQ;AAAA,MAAM,MAAM;AAAA,MAAM,KAAK;AAAA,MAAM,WAAW;AAAA,MAAM,KAAK;AAAA,MAC3D,MAAM;AAAA,MAAM,SAAS;AAAA,MAAM,aAAa;AAAA,MAAM,MAAM;AAAA,MAAM,KAAK;AAAA,MAC/D,MAAM;AAAA,MAAM,OAAO;AAAA,MAAM,OAAO;AAAA,MAChC,QAAQ,CAAC,WAAW,WAAW,SAAS;AAAA,MACxC,cAAc,CAAC,MAAM,KAAK;AAAA,MAC1B,WAAW,CAAC,WAAW;AAAA,MACvB,SAAS,CAAC,SAAS;AAAA,MACnB,UAAU,CAAC,UAAU;AAAA,MACrB,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,gBAAgB,CAAC,YAAY;AAAA,MAC7B,YAAY;AAAA,MACZ,UAAU,CAAC,UAAU;AAAA,MACrB,UAAU,CAAC,UAAU;AAAA,MACrB,UAAU,CAAC,UAAU;AAAA,MACrB,MAAM;AAAA,QAAC;AAAA,QAAU;AAAA,QAAQ;AAAA,QAAU;AAAA,QAAO;AAAA,QAAO;AAAA,QAAS;AAAA,QAAY;AAAA,QAAY;AAAA,QAAQ;AAAA,QACtF;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAkB;AAAA,QAAU;AAAA,QAAS;AAAA,QAAS;AAAA,QAAY;AAAA,QAC1E;AAAA,QAAQ;AAAA,QAAU;AAAA,QAAS;AAAA,QAAS;AAAA,MAAQ;AAAA,IACpD;AAAA,EACJ;AAAA,EACA,KAAK,EAAE,OAAO,EAAE,MAAM,MAAM,UAAU,KAAK,EAAE;AAAA,EAC7C,KAAK;AAAA,EACL,QAAQ;AAAA,IACJ,OAAO;AAAA,MACH,WAAW;AAAA,MAAM,MAAM;AAAA,MAAM,MAAM;AAAA,MACnC,WAAW,CAAC,WAAW;AAAA,MACvB,UAAU,CAAC,UAAU;AAAA,MACrB,SAAS,CAAC,KAAK;AAAA,IACnB;AAAA,EACJ;AAAA,EACA,OAAO,EAAE,OAAO,EAAE,KAAK,MAAM,MAAM,KAAK,EAAE;AAAA,EAC1C,QAAQ;AAAA,EACR,IAAI,EAAE,OAAO,EAAE,OAAO,KAAK,EAAE;AAAA,EAC7B,MAAM;AAAA,IACF,OAAO;AAAA,MACH,MAAM;AAAA,MAAM,MAAM;AAAA,MAClB,UAAU;AAAA,MACV,OAAO;AAAA,MACP,OAAO,CAAC,OAAO,SAAS,eAAe,mBAAmB;AAAA,IAC9D;AAAA,EACJ;AAAA,EACA,KAAK,EAAE,OAAO,EAAE,MAAM,KAAK,EAAE;AAAA,EAC7B,MAAM;AAAA,EACN,MAAM,EAAE,OAAO,EAAE,OAAO,MAAM,MAAM,CAAC,QAAQ,WAAW,SAAS,EAAE,EAAE;AAAA,EACrE,MAAM;AAAA,IACF,OAAO;AAAA,MACH,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM,CAAC,YAAY,oBAAoB,UAAU,eAAe,aAAa,UAAU;AAAA,MACvF,cAAc,CAAC,oBAAoB,gBAAgB,iBAAiB,SAAS;AAAA,IACjF;AAAA,EACJ;AAAA,EACA,OAAO,EAAE,OAAO,EAAE,OAAO,MAAM,KAAK,MAAM,KAAK,MAAM,MAAM,MAAM,KAAK,MAAM,SAAS,KAAK,EAAE;AAAA,EAC5F,KAAK;AAAA,EACL,UAAU;AAAA,EACV,QAAQ;AAAA,IACJ,OAAO;AAAA,MACH,MAAM;AAAA,MAAM,MAAM;AAAA,MAAM,MAAM;AAAA,MAAM,QAAQ;AAAA,MAAM,MAAM;AAAA,MAAM,OAAO;AAAA,MAAM,QAAQ;AAAA,MACnF,eAAe,CAAC,eAAe;AAAA,IACnC;AAAA,EACJ;AAAA,EACA,IAAI;AAAA,IAAE,OAAO,EAAE,UAAU,CAAC,UAAU,GAAG,OAAO,MAAM,MAAM,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG,EAAE;AAAA,IAChF,UAAU,CAAC,MAAM,UAAU,YAAY,MAAM,IAAI;AAAA,EAAE;AAAA,EACvD,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,UAAU,GAAG,OAAO,KAAK,EAAE;AAAA,EAC3D,QAAQ,EAAE,OAAO,EAAE,UAAU,CAAC,UAAU,GAAG,OAAO,MAAM,UAAU,CAAC,UAAU,GAAG,OAAO,KAAK,EAAE;AAAA,EAC9F,QAAQ,EAAE,OAAO,EAAE,KAAK,MAAM,MAAM,MAAM,MAAM,KAAK,EAAE;AAAA,EACvD,GAAG;AAAA,EACH,OAAO,EAAE,OAAO,EAAE,MAAM,MAAM,OAAO,KAAK,EAAE;AAAA,EAC5C,KAAK;AAAA,EACL,UAAU,EAAE,OAAO,EAAE,OAAO,MAAM,KAAK,KAAK,EAAE;AAAA,EAC9C,GAAG,EAAE,OAAO,EAAE,MAAM,KAAK,EAAE;AAAA,EAC3B,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,IACJ,OAAO;AAAA,MACH,MAAM,CAAC,iBAAiB;AAAA,MACxB,KAAK;AAAA,MACL,OAAO,CAAC,OAAO;AAAA,MACf,OAAO,CAAC,OAAO;AAAA,MACf,SAAS;AAAA,IACb;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,EACT,QAAQ;AAAA,IACJ,OAAO;AAAA,MACH,MAAM;AAAA,MAAM,MAAM;AAAA,MAAM,MAAM;AAAA,MAC9B,WAAW,CAAC,WAAW;AAAA,MACvB,UAAU,CAAC,UAAU;AAAA,MACrB,UAAU,CAAC,UAAU;AAAA,IACzB;AAAA,EACJ;AAAA,EACA,MAAM,EAAE,OAAO,EAAE,MAAM,KAAK,EAAE;AAAA,EAC9B,OAAO;AAAA,EACP,QAAQ,EAAE,OAAO,EAAE,KAAK,MAAM,MAAM,MAAM,OAAO,KAAK,EAAE;AAAA,EACxD,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AAAA,IACH,OAAO;AAAA,MACH,MAAM,CAAC,UAAU;AAAA,MACjB,OAAO;AAAA,MACP,QAAQ;AAAA,IACZ;AAAA,EACJ;AAAA,EACA,KAAK;AAAA,EACL,SAAS;AAAA,EACT,KAAK;AAAA,EACL,OAAO;AAAA,EACP,OAAO;AAAA,EACP,IAAI,EAAE,OAAO,EAAE,SAAS,MAAM,SAAS,MAAM,SAAS,KAAK,EAAE;AAAA,EAC7D,UAAU;AAAA,EACV,UAAU;AAAA,IACN,OAAO;AAAA,MACH,SAAS;AAAA,MAAM,MAAM;AAAA,MAAM,WAAW;AAAA,MAAM,MAAM;AAAA,MAAM,aAAa;AAAA,MACrE,MAAM;AAAA,MAAM,MAAM;AAAA,MAClB,WAAW,CAAC,WAAW;AAAA,MACvB,UAAU,CAAC,UAAU;AAAA,MACrB,UAAU,CAAC,UAAU;AAAA,MACrB,UAAU,CAAC,UAAU;AAAA,MACrB,MAAM,CAAC,QAAQ,MAAM;AAAA,IACzB;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,EACP,IAAI,EAAE,OAAO,EAAE,SAAS,MAAM,SAAS,MAAM,SAAS,MAAM,OAAO,CAAC,OAAO,OAAO,YAAY,UAAU,EAAE,EAAE;AAAA,EAC5G,OAAO;AAAA,EACP,MAAM,EAAE,OAAO,EAAE,UAAU,KAAK,EAAE;AAAA,EAClC,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,OAAO;AAAA,IACH,OAAO;AAAA,MACH,KAAK;AAAA,MAAM,OAAO;AAAA,MAAM,SAAS;AAAA,MACjC,MAAM,CAAC,aAAa,YAAY,gBAAgB,YAAY,UAAU;AAAA,MACtE,SAAS;AAAA,IACb;AAAA,EACJ;AAAA,EACA,IAAI,EAAE,UAAU,CAAC,MAAM,UAAU,YAAY,MAAM,IAAI,EAAE;AAAA,EACzD,KAAK;AAAA,EACL,OAAO;AAAA,IACH,OAAO;AAAA,MACH,KAAK;AAAA,MAAM,QAAQ;AAAA,MAAM,OAAO;AAAA,MAAM,QAAQ;AAAA,MAC9C,aAAa,CAAC,aAAa,iBAAiB;AAAA,MAC5C,SAAS,CAAC,QAAQ,YAAY,MAAM;AAAA,MACpC,UAAU,CAAC,UAAU;AAAA,MACrB,YAAY,CAAC,OAAO;AAAA,MACpB,OAAO,CAAC,OAAO;AAAA,MACf,UAAU,CAAC,UAAU;AAAA,IACzB;AAAA,EACJ;AAAA,EACA,KAAK;AACT;AACA,IAAM,cAAc;AAAA,EAChB,WAAW;AAAA,EACX,OAAO;AAAA,EACP,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,KAAK,CAAC,OAAO,OAAO,MAAM;AAAA,EAC1B,WAAW,CAAC,QAAQ,SAAS,MAAM;AAAA,EACnC,UAAU,CAAC,QAAQ,QAAQ,QAAQ,WAAW,OAAO;AAAA,EACrD,QAAQ,CAAC,QAAQ;AAAA,EACjB,IAAI;AAAA,EACJ,OAAO,CAAC,OAAO;AAAA,EACf,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW,CAAC,WAAW;AAAA,EACvB,UAAU;AAAA,EACV,MAAM,CAAC,MAAM,MAAM,MAAM,SAAS,SAAS,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EACrG,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,OAAO;AAAA,EACP,UAAU;AAAA,EACV,OAAO;AAAA,EACP,WAAW,CAAC,OAAO,IAAI;AAAA,EACvB,KAAK,CAAC,cAAc,aAAa,UAAU,YAAY,QAAQ,WAAW,QAAQ,YAAY,cAAc,YAAY,QAAQ,UAAU,KAAK;AAAA,EAC/I,MAAmB,sPAAsP,MAAM,GAAG;AAAA,EAClR,yBAAyB;AAAA,EACzB,eAAe;AAAA,EACf,qBAAqB,CAAC,UAAU,QAAQ,QAAQ,MAAM;AAAA,EACtD,aAAa;AAAA,EACb,gBAAgB,CAAC,QAAQ,SAAS,SAAS,WAAW;AAAA,EACtD,iBAAiB;AAAA,EACjB,oBAAoB;AAAA,EACpB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,iBAAiB,CAAC,QAAQ,SAAS,WAAW;AAAA,EAC9C,eAAe;AAAA,EACf,gBAAgB,CAAC,QAAQ,SAAS,WAAW;AAAA,EAC7C,iBAAiB;AAAA,EACjB,eAAe;AAAA,EACf,gBAAgB,CAAC,QAAQ,SAAS,WAAW,UAAU;AAAA,EACvD,cAAc;AAAA,EACd,mBAAmB;AAAA,EACnB,cAAc;AAAA,EACd,aAAa,CAAC,OAAO,UAAU,WAAW;AAAA,EAC1C,kBAAkB;AAAA,EAClB,wBAAwB;AAAA,EACxB,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,gBAAgB,CAAC,QAAQ,SAAS,SAAS,WAAW;AAAA,EACtD,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,iBAAiB,CAAC,QAAQ,SAAS,WAAW;AAAA,EAC9C,gBAAgB;AAAA,EAChB,aAAa,CAAC,aAAa,cAAc,QAAQ,OAAO;AAAA,EACxD,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,kBAAkB;AACtB;AACA,IAAM,kBAAgC,0LAEY,MAAM,GAAG,EAAE,IAAI,OAAK,OAAO,CAAC;AAC9E,SAAS,KAAK;AACV,cAAY,CAAC,IAAI;AACrB,IAAM,SAAN,MAAa;AAAA,EACT,YAAY,WAAW,YAAY;AAC/B,SAAK,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,SAAS;AAC5D,SAAK,cAAc,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,WAAW,GAAG,UAAU;AAC3E,SAAK,UAAU,OAAO,KAAK,KAAK,IAAI;AACpC,SAAK,kBAAkB,OAAO,KAAK,KAAK,WAAW;AAAA,EACvD;AACJ;AACA,OAAO,UAAuB,IAAI;AAClC,SAAS,YAAY,KAAK,MAAM,MAAM,IAAI,QAAQ;AAC9C,MAAI,CAAC;AACD,WAAO;AACX,MAAI,MAAM,KAAK;AACf,MAAI,OAAO,OAAO,IAAI,SAAS,SAAS;AACxC,SAAO,OAAO,IAAI,YAAY,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,IAAI;AACvE;AACA,SAAS,kBAAkB,MAAM,OAAO,OAAO;AAC3C,SAAO,MAAM,OAAO,KAAK;AACrB,QAAI,KAAK,QAAQ,WAAW;AACxB,UAAI;AACA,eAAO;AAAA;AAEP,eAAO;AAAA,IACf;AACJ,SAAO;AACX;AACA,SAAS,gBAAgB,KAAK,MAAM,QAAQ;AACxC,MAAI,aAAa,OAAO,KAAK,YAAY,KAAK,kBAAkB,IAAI,CAAC,CAAC;AACtE,UAAQ,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,aAAa,OAAO;AACnG;AACA,SAAS,SAAS,KAAK,MAAM;AACzB,MAAI,OAAO,CAAC;AACZ,WAAS,SAAS,kBAAkB,IAAI,GAAG,UAAU,CAAC,OAAO,KAAK,OAAO,SAAS,kBAAkB,OAAO,MAAM,GAAG;AAChH,QAAI,UAAU,YAAY,KAAK,MAAM;AACrC,QAAI,WAAW,OAAO,UAAU,QAAQ;AACpC;AACJ,QAAI,WAAW,KAAK,QAAQ,OAAO,IAAI,MAAM,KAAK,QAAQ,YAAY,KAAK,QAAQ,OAAO,WAAW;AACjG,WAAK,KAAK,OAAO;AAAA,EACzB;AACA,SAAO;AACX;AACA,IAAM,aAAa;AACnB,SAAS,YAAY,OAAO,QAAQ,MAAM,MAAM,IAAI;AAChD,MAAI,MAAM,OAAO,KAAK,MAAM,SAAS,IAAI,KAAK,CAAC,CAAC,IAAI,KAAK;AACzD,MAAI,SAAS,kBAAkB,MAAM,IAAI;AACzC,SAAO;AAAA,IAAE;AAAA,IAAM;AAAA,IACX,SAAS,gBAAgB,MAAM,KAAK,QAAQ,MAAM,EAAE,IAAI,cAAY,EAAE,OAAO,SAAS,MAAM,OAAO,EAAE,EAAE,OAAO,SAAS,MAAM,KAAK,IAAI,EAAE,IAAI,CAAC,KAAK,OAAO;AAAA,MAAE,OAAO,MAAM;AAAA,MAAK,OAAO,MAAM,MAAM;AAAA,MAC5L,MAAM;AAAA,MAAQ,OAAO,KAAK;AAAA,IAAE,EAAE,CAAC;AAAA,IACnC,UAAU;AAAA,EAA+B;AACjD;AACA,SAAS,iBAAiB,OAAO,MAAM,MAAM,IAAI;AAC7C,MAAI,MAAM,OAAO,KAAK,MAAM,SAAS,IAAI,KAAK,CAAC,CAAC,IAAI,KAAK;AACzD,SAAO;AAAA,IAAE;AAAA,IAAM;AAAA,IACX,SAAS,SAAS,MAAM,KAAK,IAAI,EAAE,IAAI,CAAC,KAAK,OAAO,EAAE,OAAO,KAAK,OAAO,MAAM,KAAK,MAAM,QAAQ,OAAO,KAAK,EAAE,EAAE;AAAA,IAClH,UAAU;AAAA,EAAW;AAC7B;AACA,SAAS,iBAAiB,OAAO,QAAQ,MAAM,KAAK;AAChD,MAAI,UAAU,CAAC,GAAG,QAAQ;AAC1B,WAAS,WAAW,gBAAgB,MAAM,KAAK,MAAM,MAAM;AACvD,YAAQ,KAAK,EAAE,OAAO,MAAM,SAAS,MAAM,OAAO,CAAC;AACvD,WAAS,QAAQ,SAAS,MAAM,KAAK,IAAI;AACrC,YAAQ,KAAK,EAAE,OAAO,OAAO,OAAO,KAAK,MAAM,QAAQ,OAAO,KAAK,QAAQ,CAAC;AAChF,SAAO,EAAE,MAAM,KAAK,IAAI,KAAK,SAAS,UAAU,gCAAgC;AACpF;AACA,SAAS,iBAAiB,OAAO,QAAQ,MAAM,MAAM,IAAI;AACrD,MAAI,MAAM,kBAAkB,IAAI,GAAG,OAAO,MAAM,OAAO,KAAK,YAAY,MAAM,KAAK,GAAG,CAAC,IAAI;AAC3F,MAAI,aAAa,QAAQ,KAAK,QAAQ,OAAO,KAAK,KAAK,KAAK,IAAI,CAAC;AACjE,MAAI,QAAQ,QAAQ,KAAK,gBAAgB,QAAQ,aAC3C,WAAW,SAAS,WAAW,OAAO,OAAO,eAAe,IAAI,OAAO;AAC7E,SAAO;AAAA,IAAE;AAAA,IAAM;AAAA,IACX,SAAS,MAAM,IAAI,eAAa,EAAE,OAAO,UAAU,MAAM,WAAW,EAAE;AAAA,IACtE,UAAU;AAAA,EAAW;AAC7B;AACA,SAAS,kBAAkB,OAAO,QAAQ,MAAM,MAAM,IAAI;AACtD,MAAI;AACJ,MAAI,YAAY,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,eAAe;AAClG,MAAI,UAAU,CAAC,GAAG,QAAQ;AAC1B,MAAI,UAAU;AACV,QAAI,WAAW,MAAM,SAAS,SAAS,MAAM,SAAS,EAAE;AACxD,QAAI,QAAQ,OAAO,YAAY,QAAQ;AACvC,QAAI,CAAC,OAAO;AACR,UAAI,MAAM,kBAAkB,IAAI,GAAG,OAAO,MAAM,OAAO,KAAK,YAAY,MAAM,KAAK,GAAG,CAAC,IAAI;AAC3F,eAAS,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,UAAU,KAAK,MAAM,QAAQ;AAAA,IAC3F;AACA,QAAI,OAAO;AACP,UAAI,OAAO,MAAM,SAAS,MAAM,EAAE,EAAE,YAAY,GAAG,aAAa,KAAK,WAAW;AAChF,UAAI,QAAQ,KAAK,IAAI,GAAG;AACpB,gBAAQ,KAAK,CAAC,KAAK,MAAM,YAAY;AACrC,qBAAa;AACb,mBAAW,MAAM,SAAS,IAAI,KAAK,CAAC,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC;AAC9D,eAAO,KAAK,MAAM,CAAC;AACnB;AAAA,MACJ,OACK;AACD,gBAAQ;AAAA,MACZ;AACA,eAAS,SAAS;AACd,gBAAQ,KAAK,EAAE,OAAO,OAAO,OAAO,aAAa,QAAQ,UAAU,MAAM,WAAW,CAAC;AAAA,IAC7F;AAAA,EACJ;AACA,SAAO,EAAE,MAAM,IAAI,SAAS,UAAU,MAAM;AAChD;AACA,SAAS,kBAAkB,QAAQ,SAAS;AACxC,MAAI,EAAE,OAAO,IAAI,IAAI,SAAS,OAAO,WAAW,KAAK,EAAE,aAAa,KAAK,EAAE,GAAG,SAAS,KAAK,QAAQ,GAAG;AACvG,WAAS,OAAO,KAAK,QAAQ,UAAU,SAAS,SAAS,KAAK,YAAY,IAAI,MAAK;AAC/E,QAAI,OAAO,OAAO;AAClB,QAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,WAAW,KAAK,OAAO,KAAK;AAChD;AACJ,aAAS,OAAO;AAChB,WAAO,KAAK;AAAA,EAChB;AACA,MAAI,KAAK,QAAQ,WAAW;AACxB,WAAO,KAAK,UAAU,YAAY,KAAK,KAAK,OAAO,IAAI,IAAI,iBAAiB,OAAO,MAAM,KAAK,MAAM,GAAG,IACjG,YAAY,OAAO,QAAQ,MAAM,KAAK,MAAM,GAAG;AAAA,EACzD,WACS,KAAK,QAAQ,YAAY;AAC9B,WAAO,YAAY,OAAO,QAAQ,MAAM,KAAK,GAAG;AAAA,EACpD,WACS,KAAK,QAAQ,mBAAmB,KAAK,QAAQ,sBAAsB;AACxE,WAAO,iBAAiB,OAAO,MAAM,KAAK,GAAG;AAAA,EACjD,WACS,KAAK,QAAQ,aAAa,KAAK,QAAQ,oBAAoB,KAAK,QAAQ,iBAAiB;AAC9F,WAAO,iBAAiB,OAAO,QAAQ,MAAM,KAAK,QAAQ,kBAAkB,KAAK,OAAO,KAAK,GAAG;AAAA,EACpG,WACS,KAAK,QAAQ,QAAQ,KAAK,QAAQ,oBAAoB,KAAK,QAAQ,0BAA0B;AAClG,WAAO,kBAAkB,OAAO,QAAQ,MAAM,KAAK,QAAQ,OAAO,MAAM,KAAK,MAAM,GAAG;AAAA,EAC1F,WACS,QAAQ,aAAa,OAAO,QAAQ,aAAa,OAAO,QAAQ,UAAU,OAAO,QAAQ,aAAa;AAC3G,WAAO,iBAAiB,OAAO,QAAQ,MAAM,GAAG;AAAA,EACpD,OACK;AACD,WAAO;AAAA,EACX;AACJ;AAKA,SAAS,qBAAqB,SAAS;AACnC,SAAO,kBAAkB,OAAO,SAAS,OAAO;AACpD;AAKA,SAAS,yBAAyB,QAAQ;AACtC,MAAI,EAAE,WAAW,uBAAuB,WAAW,IAAI;AACvD,MAAI,SAAS,cAAc,YAAY,IAAI,OAAO,WAAW,UAAU,IAAI,OAAO;AAClF,SAAO,CAAC,YAAY,kBAAkB,QAAQ,OAAO;AACzD;AAEA,IAAM,aAA0B,mBAAmB,OAAO,UAAU,EAAE,KAAK,mBAAmB,CAAC;AAC/F,IAAM,iBAAiB;AAAA,EACnB;AAAA,IAAE,KAAK;AAAA,IACH,OAAO,WAAS,MAAM,QAAQ,qBAAqB,MAAM,QAAQ;AAAA,IACjE,QAAQ,mBAAmB;AAAA,EAAO;AAAA,EACtC;AAAA,IAAE,KAAK;AAAA,IACH,OAAO,WAAS,MAAM,QAAQ,gBAAgB,MAAM,QAAQ;AAAA,IAC5D,QAAQ,YAAY;AAAA,EAAO;AAAA,EAC/B;AAAA,IAAE,KAAK;AAAA,IACH,OAAO,WAAS,MAAM,QAAQ;AAAA,IAC9B,QAAQ,YAAY;AAAA,EAAO;AAAA,EAC/B;AAAA,IAAE,KAAK;AAAA,IACH,MAAM,OAAO;AACT,aAAO,2DAA2D,KAAK,MAAM,IAAI;AAAA,IACrF;AAAA,IACA,QAAQ;AAAA,EAAW;AAAA,EACvB;AAAA,IAAE,KAAK;AAAA,IACH,MAAM,OAAO;AACT,aAAO,CAAC,MAAM,QAAQ,kEAAkE,KAAK,MAAM,IAAI;AAAA,IAC3G;AAAA,IACA,QAAQ,mBAAmB;AAAA,EAAO;AAAA,EACtC;AAAA,IAAE,KAAK;AAAA,IACH,MAAM,OAAO;AACT,cAAQ,CAAC,MAAM,QAAQ,MAAM,QAAQ,WAAW,CAAC,MAAM,QAAQ,oCAAoC,KAAK,MAAM,IAAI;AAAA,IACtH;AAAA,IACA,QAAQ,YAAY;AAAA,EAAO;AACnC;AACA,IAAM,eAA4B;AAAA,EAC9B;AAAA,IAAE,MAAM;AAAA,IACJ,QAAqB,YAAY,OAAO,UAAU,EAAE,KAAK,SAAS,CAAC;AAAA,EAAE;AAC7E,EAAE,OAAoB,gBAAgB,IAAI,WAAS,EAAE,MAAM,QAAQ,mBAAmB,OAAO,EAAE,CAAC;AAOhG,IAAM,YAAyB,WAAW,OAAO;AAAA,EAC7C,MAAM;AAAA,EACN,QAAqB,OAAO,UAAU;AAAA,IAClC,OAAO;AAAA,MACU,eAAe,IAAI;AAAA,QAC5B,QAAQ,SAAS;AACb,cAAI,QAAQ,eAAe,KAAK,QAAQ,SAAS;AACjD,cAAI,QAAQ,KAAK,MAAM,QAAQ,MAAM,MAAM,CAAC,EAAE;AAC1C,mBAAO,QAAQ,SAAS;AAC5B,iBAAO,QAAQ,WAAW,QAAQ,KAAK,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,QAAQ;AAAA,QAC3E;AAAA,QACA,kCAAkC,SAAS;AACvC,iBAAO,QAAQ,OAAO,QAAQ,KAAK,IAAI,IAAI,QAAQ;AAAA,QACvD;AAAA,QACA,SAAS,SAAS;AACd,cAAI,QAAQ,MAAM,MAAM,KAAK,QAAQ,SAAS,EAAE,CAAC,EAAE,SAAS,QAAQ,KAAK;AACrE,mBAAO,QAAQ,SAAS;AAC5B,cAAI,SAAS,MAAM;AACnB,mBAAS,MAAM,QAAQ,UAAQ;AAC3B,gBAAI,OAAO,IAAI;AACf,gBAAI,CAAC,QAAQ,KAAK,QAAQ,aAAa,KAAK,MAAM,IAAI;AAClD;AACJ,qBAAS,MAAM;AAAA,UACnB;AACA,cAAI,UAAU,GAAG,QAAQ,OAAO,eAAe,MAAM,QAAQ,cAAc,MAAM,QAAQ;AACrF,mBAAO,QAAQ,WAAW,OAAO,IAAI,IAAI,QAAQ;AACrD,iBAAO;AAAA,QACX;AAAA,MACJ,CAAC;AAAA,MACY,aAAa,IAAI;AAAA,QAC1B,QAAQ,MAAM;AACV,cAAI,QAAQ,KAAK,YAAY,OAAO,KAAK;AACzC,cAAI,CAAC,SAAS,MAAM,QAAQ;AACxB,mBAAO;AACX,iBAAO,EAAE,MAAM,MAAM,IAAI,IAAI,KAAK,QAAQ,aAAa,KAAK,OAAO,KAAK,GAAG;AAAA,QAC/E;AAAA,MACJ,CAAC;AAAA,MACY,sBAAsB,IAAI;AAAA,QACnC,oBAAoB,UAAQ,KAAK,SAAS,SAAS;AAAA,MACvD,CAAC;AAAA,IACL;AAAA,EACJ,CAAC;AAAA,EACD,cAAc;AAAA,IACV,eAAe,EAAE,OAAO,EAAE,MAAM,QAAQ,OAAO,MAAM,EAAE;AAAA,IACvD,eAAe;AAAA,IACf,WAAW;AAAA,EACf;AACJ,CAAC;AAOD,IAAM,eAA4B,UAAU,UAAU;AAAA,EAClD,MAAmB,iBAAiB,gBAAgB,YAAY;AACpE,CAAC;AAMD,SAAS,KAAK,SAAS,CAAC,GAAG;AACvB,MAAI,UAAU,IAAI;AAClB,MAAI,OAAO,qBAAqB;AAC5B,cAAU;AACd,MAAI,OAAO,oBAAoB;AAC3B,eAAW,UAAU,UAAU,MAAM,MAAM;AAC/C,MAAI,OAAO,mBAAmB,OAAO,gBAAgB,UACjD,OAAO,oBAAoB,OAAO,iBAAiB;AACnD,WAAO,kBAAkB,OAAO,mBAAmB,CAAC,GAAG,OAAO,cAAc,IAAI,OAAO,oBAAoB,CAAC,GAAG,OAAO,YAAY,CAAC;AACvI,MAAI,OAAO,OAAO,UAAU,UAAU,EAAE,MAAM,QAAQ,CAAC,IAAI,UAAU,aAAa,UAAU,EAAE,QAAQ,CAAC,IAAI;AAC3G,SAAO,IAAI,gBAAgB,MAAM;AAAA,IAC7B,aAAa,KAAK,GAAG,EAAE,cAAc,yBAAyB,MAAM,EAAE,CAAC;AAAA,IACvE,OAAO,kBAAkB,QAAQ,gBAAgB,CAAC;AAAA,IAClD,WAAW,EAAE;AAAA,IACb,IAAI,EAAE;AAAA,EACV,CAAC;AACL;AACA,IAAMC,eAA2B,IAAI,IAAiB,qGAAqG,MAAM,GAAG,CAAC;AAKrK,IAAM,gBAA6B,WAAW,aAAa,GAAG,CAAC,MAAM,MAAM,IAAI,MAAM,sBAAsB;AACvG,MAAI,KAAK,aAAa,KAAK,MAAM,YAAY,QAAQ,MAAO,QAAQ,OAAO,QAAQ,OAC/E,CAAC,aAAa,WAAW,KAAK,OAAO,MAAM,EAAE;AAC7C,WAAO;AACX,MAAI,OAAO,kBAAkB,GAAG,EAAE,MAAM,IAAI;AAC5C,MAAI,YAAY,MAAM,cAAc,WAAS;AACzC,QAAI,IAAI,IAAI;AACZ,QAAI,UAAU,MAAM,IAAI,YAAY,MAAM,OAAO,GAAG,MAAM,EAAE,KAAK;AACjE,QAAI,EAAE,KAAK,IAAI,OAAO,QAAQ,WAAW,KAAK,EAAE,aAAa,MAAM,EAAE,GAAG;AACxE,QAAI,WAAW,QAAQ,OAAO,MAAM,QAAQ,UAAU;AAClD,UAAI,MAAM,MAAM;AAChB,YAAM,MAAM,KAAK,IAAI,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,eAC5H,OAAO,YAAY,MAAM,KAAK,IAAI,QAAQ,IAAI,MAC/C,CAACA,aAAY,IAAI,IAAI,GAAG;AACxB,YAAIC,MAAK,QAAQ,MAAM,IAAI,YAAY,MAAM,OAAO,CAAC,MAAM,MAAM,IAAI;AACrE,YAAI,SAAS,KAAK,IAAI;AACtB,eAAO,EAAE,OAAO,SAAS,EAAE,MAAM,MAAM,IAAAA,KAAI,OAAO,EAAE;AAAA,MACxD;AAAA,IACJ,WACS,WAAW,QAAQ,OAAO,MAAM,QAAQ,sBAAsB;AACnE,UAAI,MAAM,MAAM;AAChB,UAAI,MAAM,QAAQ,OAAO,OAAO,KAAK,IAAI,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,eAChG,OAAO,YAAY,MAAM,KAAK,KAAK,IAAI,MAAM,CAACD,aAAY,IAAI,IAAI,GAAG;AACtE,YAAIC,MAAK,QAAQ,MAAM,IAAI,YAAY,MAAM,OAAO,CAAC,MAAM,MAAM,IAAI;AACrE,YAAI,SAAS,GAAG,IAAI;AACpB,eAAO;AAAA,UACH,OAAO,gBAAgB,OAAO,OAAO,OAAO,QAAQ,EAAE;AAAA,UACtD,SAAS,EAAE,MAAM,MAAM,IAAAA,KAAI,OAAO;AAAA,QACtC;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,EAAE,MAAM;AAAA,EACnB,CAAC;AACD,MAAI,UAAU,QAAQ;AAClB,WAAO;AACX,OAAK,SAAS;AAAA,IACV;AAAA,IACA,MAAM,OAAO,WAAW;AAAA,MACpB,WAAW;AAAA,MACX,gBAAgB;AAAA,IACpB,CAAC;AAAA,EACL,CAAC;AACD,SAAO;AACX,CAAC;", "names": ["tags", "attrs", "selfClosers", "to"]}