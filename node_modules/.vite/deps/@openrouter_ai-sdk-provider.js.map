{"version": 3, "sources": ["../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider/src/errors/api-call-error.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider/src/errors/download-error.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider/src/errors/empty-response-body-error.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider/src/errors/invalid-argument-error.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider/src/errors/invalid-data-content-error.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider/src/errors/invalid-prompt-error.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider/src/errors/invalid-response-data-error.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider/src/errors/get-error-message.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider/src/errors/invalid-tool-arguments-error.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider/src/errors/json-parse-error.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider/src/errors/load-api-key-error.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider/src/errors/load-setting-error.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider/src/errors/no-content-generated-error.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider/src/errors/no-object-generated-error.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider/src/errors/no-such-tool-error.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider/src/errors/retry-error.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider/src/errors/too-many-embedding-values-for-call-error.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider/src/errors/tool-call-parse-error.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider/src/errors/type-validation-error.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider/src/errors/unsupported-functionality-error.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider/src/errors/unsupported-json-schema-error.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider-utils/src/combine-headers.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider-utils/src/convert-async-generator-to-readable-stream.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider-utils/src/download.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider-utils/src/extract-response-headers.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider-utils/src/generate-id.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider-utils/src/get-error-message.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider-utils/src/is-abort-error.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider-utils/src/load-api-key.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider-utils/src/load-setting.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider-utils/src/parse-json.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider-utils/src/validate-types.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider-utils/src/post-to-api.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider-utils/src/remove-undefined-entries.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider-utils/src/response-handler.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider-utils/src/uint8-utils.ts", "../../@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider-utils/src/without-trailing-slash.ts", "../../@openrouter/ai-sdk-provider/src/openrouter-facade.ts", "../../@openrouter/ai-sdk-provider/src/openrouter-chat-language-model.ts", "../../@openrouter/ai-sdk-provider/src/convert-to-openrouter-chat-messages.ts", "../../@openrouter/ai-sdk-provider/src/map-openrouter-chat-logprobs.ts", "../../@openrouter/ai-sdk-provider/src/map-openrouter-finish-reason.ts", "../../@openrouter/ai-sdk-provider/src/openrouter-error.ts", "../../@openrouter/ai-sdk-provider/src/openrouter-completion-language-model.ts", "../../@openrouter/ai-sdk-provider/src/convert-to-openrouter-completion-prompt.ts", "../../@openrouter/ai-sdk-provider/src/map-openrouter-completion-logprobs.ts", "../../@openrouter/ai-sdk-provider/src/openrouter-provider.ts"], "sourcesContent": ["export class APICallError extends Error {\n  readonly url: string;\n  readonly requestBodyValues: unknown;\n  readonly statusCode?: number;\n\n  readonly responseHeaders?: Record<string, string>;\n  readonly responseBody?: string;\n\n  readonly cause?: unknown;\n  readonly isRetryable: boolean;\n  readonly data?: unknown;\n\n  constructor({\n    message,\n    url,\n    requestBodyValues,\n    statusCode,\n    responseHeaders,\n    responseBody,\n    cause,\n    isRetryable = statusCode != null &&\n      (statusCode === 408 || // request timeout\n        statusCode === 409 || // conflict\n        statusCode === 429 || // too many requests\n        statusCode >= 500), // server error\n    data,\n  }: {\n    message: string;\n    url: string;\n    requestBodyValues: unknown;\n    statusCode?: number;\n    responseHeaders?: Record<string, string>;\n    responseBody?: string;\n    cause?: unknown;\n    isRetryable?: boolean;\n    data?: unknown;\n  }) {\n    super(message);\n\n    this.name = 'AI_APICallError';\n\n    this.url = url;\n    this.requestBodyValues = requestBodyValues;\n    this.statusCode = statusCode;\n    this.responseHeaders = responseHeaders;\n    this.responseBody = responseBody;\n    this.cause = cause;\n    this.isRetryable = isRetryable;\n    this.data = data;\n  }\n\n  static isAPICallError(error: unknown): error is APICallError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_APICallError' &&\n      typeof (error as APICallError).url === 'string' &&\n      typeof (error as APICallError).requestBodyValues === 'object' &&\n      ((error as APICallError).statusCode == null ||\n        typeof (error as APICallError).statusCode === 'number') &&\n      ((error as APICallError).responseHeaders == null ||\n        typeof (error as APICallError).responseHeaders === 'object') &&\n      ((error as APICallError).responseBody == null ||\n        typeof (error as APICallError).responseBody === 'string') &&\n      ((error as APICallError).cause == null ||\n        typeof (error as APICallError).cause === 'object') &&\n      typeof (error as APICallError).isRetryable === 'boolean' &&\n      ((error as APICallError).data == null ||\n        typeof (error as APICallError).data === 'object')\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      url: this.url,\n      requestBodyValues: this.requestBodyValues,\n      statusCode: this.statusCode,\n      responseHeaders: this.responseHeaders,\n      responseBody: this.responseBody,\n      cause: this.cause,\n      isRetryable: this.isRetryable,\n      data: this.data,\n    };\n  }\n}\n", "export class DownloadError extends Error {\n  readonly url: string;\n  readonly statusCode?: number;\n  readonly statusText?: string;\n  readonly cause?: unknown;\n\n  constructor({\n    url,\n    statusCode,\n    statusText,\n    cause,\n    message = cause == null\n      ? `Failed to download ${url}: ${statusCode} ${statusText}`\n      : `Failed to download ${url}: ${cause}`,\n  }: {\n    url: string;\n    statusCode?: number;\n    statusText?: string;\n    message?: string;\n    cause?: unknown;\n  }) {\n    super(message);\n\n    this.name = 'AI_DownloadError';\n\n    this.url = url;\n    this.statusCode = statusCode;\n    this.statusText = statusText;\n    this.cause = cause;\n  }\n\n  static isDownloadError(error: unknown): error is DownloadError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_DownloadError' &&\n      typeof (error as DownloadError).url === 'string' &&\n      ((error as DownloadError).statusCode == null ||\n        typeof (error as DownloadError).statusCode === 'number') &&\n      ((error as DownloadError).statusText == null ||\n        typeof (error as DownloadError).statusText === 'string')\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      url: this.url,\n      statusCode: this.statusCode,\n      statusText: this.statusText,\n      cause: this.cause,\n    };\n  }\n}\n", "export class EmptyResponseBodyError extends Error {\n  constructor({ message = 'Empty response body' }: { message?: string } = {}) {\n    super(message);\n\n    this.name = 'AI_EmptyResponseBodyError';\n  }\n\n  static isEmptyResponseBodyError(\n    error: unknown,\n  ): error is EmptyResponseBodyError {\n    return error instanceof Error && error.name === 'AI_EmptyResponseBodyError';\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n    };\n  }\n}\n", "export class InvalidArgumentError extends Error {\n  readonly parameter: string;\n  readonly value: unknown;\n\n  constructor({\n    parameter,\n    value,\n    message,\n  }: {\n    parameter: string;\n    value: unknown;\n    message: string;\n  }) {\n    super(`Invalid argument for parameter ${parameter}: ${message}`);\n\n    this.name = 'AI_InvalidArgumentError';\n\n    this.parameter = parameter;\n    this.value = value;\n  }\n\n  static isInvalidArgumentError(error: unknown): error is InvalidArgumentError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_InvalidArgumentError' &&\n      typeof (error as InvalidArgumentError).parameter === 'string' &&\n      typeof (error as InvalidArgumentError).value === 'string'\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n\n      parameter: this.parameter,\n      value: this.value,\n    };\n  }\n}\n", "export class InvalidDataContentError extends Error {\n  readonly content: unknown;\n  readonly cause?: unknown;\n\n  constructor({\n    content,\n    cause,\n    message = `Invalid data content. Expected a base64 string, Uint8Array, ArrayBuffer, or Buffer, but got ${typeof content}.`,\n  }: {\n    content: unknown;\n    cause?: unknown;\n    message?: string;\n  }) {\n    super(message);\n\n    this.name = 'AI_InvalidDataContentError';\n\n    this.cause = cause;\n    this.content = content;\n  }\n\n  static isInvalidDataContentError(\n    error: unknown,\n  ): error is InvalidDataContentError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_InvalidDataContentError' &&\n      (error as InvalidDataContentError).content != null\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n      cause: this.cause,\n      content: this.content,\n    };\n  }\n}\n", "export class InvalidPromptError extends Error {\n  readonly prompt: unknown;\n\n  constructor({ prompt, message }: { prompt: unknown; message: string }) {\n    super(`Invalid prompt: ${message}`);\n\n    this.name = 'AI_InvalidPromptError';\n\n    this.prompt = prompt;\n  }\n\n  static isInvalidPromptError(error: unknown): error is InvalidPromptError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_InvalidPromptError' &&\n      prompt != null\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n\n      prompt: this.prompt,\n    };\n  }\n}\n", "/**\nServer returned a response with invalid data content. This should be thrown by providers when they\ncannot parse the response from the API.\n */\nexport class InvalidResponseDataError extends Error {\n  readonly data: unknown;\n\n  constructor({\n    data,\n    message = `Invalid response data: ${JSON.stringify(data)}.`,\n  }: {\n    data: unknown;\n    message?: string;\n  }) {\n    super(message);\n\n    this.name = 'AI_InvalidResponseDataError';\n\n    this.data = data;\n  }\n\n  static isInvalidResponseDataError(\n    error: unknown,\n  ): error is InvalidResponseDataError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_InvalidResponseDataError' &&\n      (error as InvalidResponseDataError).data != null\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n\n      data: this.data,\n    };\n  }\n}\n", "export function getErrorMessage(error: unknown | undefined) {\n  if (error == null) {\n    return 'unknown error';\n  }\n\n  if (typeof error === 'string') {\n    return error;\n  }\n\n  if (error instanceof Error) {\n    return error.message;\n  }\n\n  return JSON.stringify(error);\n}\n", "import { getErrorMessage } from './get-error-message';\n\nexport class InvalidToolArgumentsError extends Error {\n  readonly toolName: string;\n  readonly toolArgs: string;\n  readonly cause: unknown;\n\n  constructor({\n    toolArgs,\n    toolName,\n    cause,\n    message = `Invalid arguments for tool ${toolName}: ${getErrorMessage(\n      cause,\n    )}`,\n  }: {\n    message?: string;\n    toolArgs: string;\n    toolName: string;\n    cause: unknown;\n  }) {\n    super(message);\n\n    this.name = 'AI_InvalidToolArgumentsError';\n\n    this.toolArgs = toolArgs;\n    this.toolName = toolName;\n    this.cause = cause;\n  }\n\n  static isInvalidToolArgumentsError(\n    error: unknown,\n  ): error is InvalidToolArgumentsError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_InvalidToolArgumentsError' &&\n      typeof (error as InvalidToolArgumentsError).toolName === 'string' &&\n      typeof (error as InvalidToolArgumentsError).toolArgs === 'string'\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      cause: this.cause,\n      stack: this.stack,\n\n      toolName: this.toolName,\n      toolArgs: this.toolArgs,\n    };\n  }\n}\n", "import { getErrorMessage } from './get-error-message';\n\nexport class JSONParseError extends Error {\n  // note: property order determines debugging output\n  readonly text: string;\n  readonly cause: unknown;\n\n  constructor({ text, cause }: { text: string; cause: unknown }) {\n    super(\n      `JSON parsing failed: ` +\n        `Text: ${text}.\\n` +\n        `Error message: ${getErrorMessage(cause)}`,\n    );\n\n    this.name = 'AI_JSONParseError';\n\n    this.cause = cause;\n    this.text = text;\n  }\n\n  static isJSONParseError(error: unknown): error is JSONParseError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_JSONParseError' &&\n      typeof (error as JSONParseError).text === 'string' &&\n      typeof (error as JSONParseError).cause === 'string'\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      cause: this.cause,\n      stack: this.stack,\n\n      valueText: this.text,\n    };\n  }\n}\n", "export class LoadAPIKeyError extends Error {\n  constructor({ message }: { message: string }) {\n    super(message);\n\n    this.name = 'AI_LoadAPIKeyError';\n  }\n\n  static isLoadAPIKeyError(error: unknown): error is LoadAPIKeyError {\n    return error instanceof Error && error.name === 'AI_LoadAPIKeyError';\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n    };\n  }\n}\n", "export class LoadSettingError extends Error {\n  constructor({ message }: { message: string }) {\n    super(message);\n\n    this.name = 'AI_LoadSettingError';\n  }\n\n  static isLoadSettingError(error: unknown): error is LoadSettingError {\n    return error instanceof Error && error.name === 'AI_LoadSettingError';\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n    };\n  }\n}\n", "/**\nThrown when the AI provider fails to generate any content.\n */\nexport class NoContentGeneratedError extends Error {\n  readonly cause: unknown;\n\n  constructor({\n    message = 'No content generated.',\n  }: { message?: string } = {}) {\n    super(message);\n\n    this.name = 'AI_NoContentGeneratedError';\n  }\n\n  static isNoContentGeneratedError(\n    error: unknown,\n  ): error is NoContentGeneratedError {\n    return (\n      error instanceof Error && error.name === 'AI_NoContentGeneratedError'\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      cause: this.cause,\n      message: this.message,\n      stack: this.stack,\n    };\n  }\n}\n", "/**\nThrown when the AI provider fails to generate a parsable object.\n */\nexport class NoObjectGeneratedError extends Error {\n  readonly cause: unknown;\n\n  constructor({ message = 'No object generated.' }: { message?: string } = {}) {\n    super(message);\n\n    this.name = 'AI_NoObjectGeneratedError';\n  }\n\n  static isNoObjectGeneratedError(\n    error: unknown,\n  ): error is NoObjectGeneratedError {\n    return error instanceof Error && error.name === 'AI_NoObjectGeneratedError';\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      cause: this.cause,\n      message: this.message,\n      stack: this.stack,\n    };\n  }\n}\n", "export class NoSuchToolError extends Error {\n  readonly toolName: string;\n  readonly availableTools: string[] | undefined;\n\n  constructor({\n    toolName,\n    availableTools = undefined,\n    message = `Model tried to call unavailable tool '${toolName}'. ${\n      availableTools === undefined\n        ? 'No tools are available.'\n        : `Available tools: ${availableTools.join(', ')}.`\n    }`,\n  }: {\n    toolName: string;\n    availableTools?: string[] | undefined;\n    message?: string;\n  }) {\n    super(message);\n\n    this.name = 'AI_NoSuchToolError';\n\n    this.toolName = toolName;\n    this.availableTools = availableTools;\n  }\n\n  static isNoSuchToolError(error: unknown): error is NoSuchToolError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_NoSuchToolError' &&\n      'toolName' in error &&\n      error.toolName != undefined &&\n      typeof error.name === 'string'\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n\n      toolName: this.toolName,\n      availableTools: this.availableTools,\n    };\n  }\n}\n", "export type RetryErrorReason =\n  | 'maxRetriesExceeded'\n  | 'errorNotRetryable'\n  | 'abort';\n\nexport class RetryError extends Error {\n  // note: property order determines debugging output\n  readonly reason: RetryErrorReason;\n  readonly lastError: unknown;\n  readonly errors: Array<unknown>;\n\n  constructor({\n    message,\n    reason,\n    errors,\n  }: {\n    message: string;\n    reason: RetryErrorReason;\n    errors: Array<unknown>;\n  }) {\n    super(message);\n\n    this.name = 'AI_RetryError';\n    this.reason = reason;\n    this.errors = errors;\n\n    // separate our last error to make debugging via log easier:\n    this.lastError = errors[errors.length - 1];\n  }\n\n  static isRetryError(error: unknown): error is RetryError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_RetryError' &&\n      typeof (error as RetryError).reason === 'string' &&\n      Array.isArray((error as RetryError).errors)\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      reason: this.reason,\n      lastError: this.lastError,\n      errors: this.errors,\n    };\n  }\n}\n", "export class TooManyEmbeddingValuesForCallError extends Error {\n  readonly provider: string;\n  readonly modelId: string;\n  readonly maxEmbeddingsPerCall: number;\n  readonly values: Array<unknown>;\n\n  constructor(options: {\n    provider: string;\n    modelId: string;\n    maxEmbeddingsPerCall: number;\n    values: Array<unknown>;\n  }) {\n    super(\n      `Too many values for a single embedding call. ` +\n        `The ${options.provider} model \"${options.modelId}\" can only embed up to ` +\n        `${options.maxEmbeddingsPerCall} values per call, but ${options.values.length} values were provided.`,\n    );\n\n    this.name = 'AI_TooManyEmbeddingValuesForCallError';\n\n    this.provider = options.provider;\n    this.modelId = options.modelId;\n    this.maxEmbeddingsPerCall = options.maxEmbeddingsPerCall;\n    this.values = options.values;\n  }\n\n  static isInvalidPromptError(\n    error: unknown,\n  ): error is TooManyEmbeddingValuesForCallError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_TooManyEmbeddingValuesForCallError' &&\n      'provider' in error &&\n      typeof error.provider === 'string' &&\n      'modelId' in error &&\n      typeof error.modelId === 'string' &&\n      'maxEmbeddingsPerCall' in error &&\n      typeof error.maxEmbeddingsPerCall === 'number' &&\n      'values' in error &&\n      Array.isArray(error.values)\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n\n      provider: this.provider,\n      modelId: this.modelId,\n      maxEmbeddingsPerCall: this.maxEmbeddingsPerCall,\n      values: this.values,\n    };\n  }\n}\n", "import { LanguageModelV1FunctionTool } from '../language-model/v1/language-model-v1-function-tool';\nimport { getErrorMessage } from './get-error-message';\n\nexport class ToolCallParseError extends Error {\n  readonly cause: unknown;\n  readonly text: string;\n  readonly tools: LanguageModelV1FunctionTool[];\n\n  constructor({\n    cause,\n    text,\n    tools,\n    message = `Failed to parse tool calls: ${getErrorMessage(cause)}`,\n  }: {\n    cause: unknown;\n    text: string;\n    tools: LanguageModelV1FunctionTool[];\n    message?: string;\n  }) {\n    super(message);\n\n    this.name = 'AI_ToolCallParseError';\n\n    this.cause = cause;\n    this.text = text;\n    this.tools = tools;\n  }\n\n  static isToolCallParseError(error: unknown): error is ToolCallParseError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_ToolCallParseError' &&\n      'cause' in error &&\n      error.cause != undefined &&\n      'text' in error &&\n      error.text != undefined &&\n      typeof error.text === 'string' &&\n      'tools' in error &&\n      error.tools != undefined\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n\n      cause: this.cause,\n      text: this.text,\n      tools: this.tools,\n    };\n  }\n}\n", "import { getErrorMessage } from './get-error-message';\n\nexport class TypeValidationError extends Error {\n  readonly value: unknown;\n  readonly cause: unknown;\n\n  constructor({ value, cause }: { value: unknown; cause: unknown }) {\n    super(\n      `Type validation failed: ` +\n        `Value: ${JSON.stringify(value)}.\\n` +\n        `Error message: ${getErrorMessage(cause)}`,\n    );\n\n    this.name = 'AI_TypeValidationError';\n\n    this.cause = cause;\n    this.value = value;\n  }\n\n  static isTypeValidationError(error: unknown): error is TypeValidationError {\n    return error instanceof Error && error.name === 'AI_TypeValidationError';\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      cause: this.cause,\n      stack: this.stack,\n\n      value: this.value,\n    };\n  }\n}\n", "export class UnsupportedFunctionalityError extends Error {\n  readonly functionality: string;\n\n  constructor({ functionality }: { functionality: string }) {\n    super(`'${functionality}' functionality not supported.`);\n\n    this.name = 'AI_UnsupportedFunctionalityError';\n\n    this.functionality = functionality;\n  }\n\n  static isUnsupportedFunctionalityError(\n    error: unknown,\n  ): error is UnsupportedFunctionalityError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_UnsupportedFunctionalityError' &&\n      typeof (error as UnsupportedFunctionalityError).functionality === 'string'\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n\n      functionality: this.functionality,\n    };\n  }\n}\n", "export class UnsupportedJSONSchemaError extends Error {\n  readonly reason: string;\n  readonly schema: unknown;\n\n  constructor({\n    schema,\n    reason,\n    message = `Unsupported JSON schema: ${reason}`,\n  }: {\n    schema: unknown;\n    reason: string;\n    message?: string;\n  }) {\n    super(message);\n\n    this.name = 'AI_UnsupportedJSONSchemaError';\n\n    this.reason = reason;\n    this.schema = schema;\n  }\n\n  static isUnsupportedJSONSchemaError(\n    error: unknown,\n  ): error is UnsupportedJSONSchemaError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_UnsupportedJSONSchemaError' &&\n      'reason' in error &&\n      error.reason != undefined &&\n      'schema' in error &&\n      error.schema !== undefined\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n\n      reason: this.reason,\n      schema: this.schema,\n    };\n  }\n}\n", "export function combineHeaders(\n  ...headers: Array<Record<string, string | undefined> | undefined>\n): Record<string, string | undefined> {\n  return headers.reduce(\n    (combinedHeaders, currentHeaders) => ({\n      ...combinedHeaders,\n      ...(currentHeaders ?? {}),\n    }),\n    {},\n  ) as Record<string, string | undefined>;\n}\n", "/**\n * Converts an AsyncGenerator to a ReadableStream.\n *\n * @template T - The type of elements produced by the AsyncGenerator.\n * @param {AsyncGenerator<T>} stream - The AsyncGenerator to convert.\n * @returns {ReadableStream<T>} - A ReadableStream that provides the same data as the AsyncGenerator.\n */\nexport function convertAsyncGeneratorToReadableStream<T>(\n  stream: AsyncGenerator<T>,\n): ReadableStream<T> {\n  return new ReadableStream<T>({\n    /**\n     * Called when the consumer wants to pull more data from the stream.\n     *\n     * @param {ReadableStreamDefaultController<T>} controller - The controller to enqueue data into the stream.\n     * @returns {Promise<void>}\n     */\n    async pull(controller) {\n      try {\n        const { value, done } = await stream.next();\n        if (done) {\n          controller.close();\n        } else {\n          controller.enqueue(value);\n        }\n      } catch (error) {\n        controller.error(error);\n      }\n    },\n    /**\n     * Called when the consumer cancels the stream.\n     */\n    cancel() {},\n  });\n}\n", "import { DownloadError } from '@ai-sdk/provider';\n\nexport async function download({\n  url,\n  fetchImplementation = fetch,\n}: {\n  url: URL;\n  fetchImplementation?: typeof fetch;\n}): Promise<{\n  data: Uint8Array;\n  mimeType: string | undefined;\n}> {\n  const urlText = url.toString();\n  try {\n    const response = await fetchImplementation(urlText);\n\n    if (!response.ok) {\n      throw new DownloadError({\n        url: urlText,\n        statusCode: response.status,\n        statusText: response.statusText,\n      });\n    }\n\n    return {\n      data: new Uint8Array(await response.arrayBuffer()),\n      mimeType: response.headers.get('content-type') ?? undefined,\n    };\n  } catch (error) {\n    if (DownloadError.isDownloadError(error)) {\n      throw error;\n    }\n\n    throw new DownloadError({ url: urlText, cause: error });\n  }\n}\n", "/**\nExtracts the headers from a response object and returns them as a key-value object.\n\n@param response - The response object to extract headers from.\n@returns The headers as a key-value object.\n*/\nexport function extractResponseHeaders(\n  response: Response,\n): Record<string, string> {\n  const headers: Record<string, string> = {};\n  response.headers.forEach((value, key) => {\n    headers[key] = value;\n  });\n  return headers;\n}\n", "import { customAlphabet } from 'nanoid/non-secure';\n\n/**\n * Generates a 7-character random string to use for IDs. Not secure.\n */\nexport const generateId = customAlphabet(\n  '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',\n  7,\n);\n", "export function getErrorMessage(error: unknown | undefined) {\n  if (error == null) {\n    return 'unknown error';\n  }\n\n  if (typeof error === 'string') {\n    return error;\n  }\n\n  if (error instanceof Error) {\n    return error.message;\n  }\n\n  return JSON.stringify(error);\n}\n", "export function isAbortError(error: unknown): error is Error {\n  return (\n    error instanceof Error &&\n    (error.name === 'AbortError' || error.name === 'TimeoutError')\n  );\n}\n", "import { LoadAPIKeyError } from '@ai-sdk/provider';\n\nexport function loadApiKey({\n  apiKey,\n  environmentVariableName,\n  apiKeyParameterName = 'apiKey',\n  description,\n}: {\n  apiKey: string | undefined;\n  environmentVariableName: string;\n  apiKeyParameterName?: string;\n  description: string;\n}): string {\n  if (typeof apiKey === 'string') {\n    return apiKey;\n  }\n\n  if (apiKey != null) {\n    throw new LoadAPIKeyError({\n      message: `${description} API key must be a string.`,\n    });\n  }\n\n  if (typeof process === 'undefined') {\n    throw new LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter. Environment variables is not supported in this environment.`,\n    });\n  }\n\n  apiKey = process.env[environmentVariableName];\n\n  if (apiKey == null) {\n    throw new LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter or the ${environmentVariableName} environment variable.`,\n    });\n  }\n\n  if (typeof apiKey !== 'string') {\n    throw new LoadAPIKeyError({\n      message: `${description} API key must be a string. The value of the ${environmentVariableName} environment variable is not a string.`,\n    });\n  }\n\n  return apiKey;\n}\n", "import { LoadSettingError } from '@ai-sdk/provider';\n\nexport function loadSetting({\n  settingValue,\n  environmentVariableName,\n  settingName,\n  description,\n}: {\n  settingValue: string | undefined;\n  environmentVariableName: string;\n  settingName: string;\n  description: string;\n}): string {\n  if (typeof settingValue === 'string') {\n    return settingValue;\n  }\n\n  if (settingValue != null) {\n    throw new LoadSettingError({\n      message: `${description} setting must be a string.`,\n    });\n  }\n\n  if (typeof process === 'undefined') {\n    throw new LoadSettingError({\n      message: `${description} setting is missing. Pass it using the '${settingName}' parameter. Environment variables is not supported in this environment.`,\n    });\n  }\n\n  settingValue = process.env[environmentVariableName];\n\n  if (settingValue == null) {\n    throw new LoadSettingError({\n      message: `${description} setting is missing. Pass it using the '${settingName}' parameter or the ${environmentVariableName} environment variable.`,\n    });\n  }\n\n  if (typeof settingValue !== 'string') {\n    throw new LoadSettingError({\n      message: `${description} setting must be a string. The value of the ${environmentVariableName} environment variable is not a string.`,\n    });\n  }\n\n  return settingValue;\n}\n", "import { JSONParseError, TypeValidationError } from '@ai-sdk/provider';\nimport SecureJSON from 'secure-json-parse';\nimport { ZodSchema } from 'zod';\nimport { safeValidateTypes, validateTypes } from './validate-types';\n\n/**\n * Parses a JSON string into an unknown object.\n *\n * @param text - The JSON string to parse.\n * @returns {unknown} - The parsed JSON object.\n */\nexport function parseJSON({ text }: { text: string }): unknown;\n/**\n * Parses a JSON string into a strongly-typed object using the provided schema.\n *\n * @template T - The type of the object to parse the JSON into.\n * @param {string} text - The JSON string to parse.\n * @param {Schema<T>} schema - The schema to use for parsing the JSON.\n * @returns {T} - The parsed object.\n */\nexport function parseJSON<T>({\n  text,\n  schema,\n}: {\n  text: string;\n  schema: ZodSchema<T>;\n}): T;\nexport function parseJSON<T>({\n  text,\n  schema,\n}: {\n  text: string;\n  schema?: ZodSchema<T>;\n}): T {\n  try {\n    const value = SecureJSON.parse(text);\n\n    if (schema == null) {\n      return value;\n    }\n\n    return validateTypes({ value, schema });\n  } catch (error) {\n    if (\n      JSONParseError.isJSONParseError(error) ||\n      TypeValidationError.isTypeValidationError(error)\n    ) {\n      throw error;\n    }\n\n    throw new JSONParseError({ text, cause: error });\n  }\n}\n\nexport type ParseResult<T> =\n  | { success: true; value: T }\n  | { success: false; error: JSONParseError | TypeValidationError };\n\n/**\n * Safely parses a JSON string and returns the result as an object of type `unknown`.\n *\n * @param text - The JSON string to parse.\n * @returns {object} Either an object with `success: true` and the parsed data, or an object with `success: false` and the error that occurred.\n */\nexport function safeParseJSON({ text }: { text: string }): ParseResult<unknown>;\n/**\n * Safely parses a JSON string into a strongly-typed object, using a provided schema to validate the object.\n *\n * @template T - The type of the object to parse the JSON into.\n * @param {string} text - The JSON string to parse.\n * @param {Schema<T>} schema - The schema to use for parsing the JSON.\n * @returns An object with either a `success` flag and the parsed and typed data, or a `success` flag and an error object.\n */\nexport function safeParseJSON<T>({\n  text,\n  schema,\n}: {\n  text: string;\n  schema: ZodSchema<T>;\n}): ParseResult<T>;\nexport function safeParseJSON<T>({\n  text,\n  schema,\n}: {\n  text: string;\n  schema?: ZodSchema<T>;\n}):\n  | { success: true; value: T }\n  | { success: false; error: JSONParseError | TypeValidationError } {\n  try {\n    const value = SecureJSON.parse(text);\n\n    if (schema == null) {\n      return {\n        success: true,\n        value: value as T,\n      };\n    }\n\n    return safeValidateTypes({ value, schema });\n  } catch (error) {\n    return {\n      success: false,\n      error: JSONParseError.isJSONParseError(error)\n        ? error\n        : new JSONParseError({ text, cause: error }),\n    };\n  }\n}\n\nexport function isParsableJson(input: string): boolean {\n  try {\n    SecureJSON.parse(input);\n    return true;\n  } catch {\n    return false;\n  }\n}\n\n/**\n@deprecated Use `isParsableJson` instead.  \n */\nexport const isParseableJson = isParsableJson;\n", "import { TypeValidationError } from '@ai-sdk/provider';\nimport { ZodSchema } from 'zod';\n\n/**\n * Validates the types of an unknown object using a schema and\n * return a strongly-typed object.\n *\n * @template T - The type of the object to validate.\n * @param {string} options.value - The object to validate.\n * @param {Schema<T>} options.schema - The schema to use for validating the JSON.\n * @returns {T} - The typed object.\n */\nexport function validateTypes<T>({\n  value,\n  schema,\n}: {\n  value: unknown;\n  schema: ZodSchema<T>;\n}): T {\n  try {\n    return schema.parse(value);\n  } catch (error) {\n    throw new TypeValidationError({ value, cause: error });\n  }\n}\n\n/**\n * Safely validates the types of an unknown object using a schema and\n * return a strongly-typed object.\n *\n * @template T - The type of the object to validate.\n * @param {string} options.value - The JSON object to validate.\n * @param {Schema<T>} options.schema - The schema to use for validating the JSON.\n * @returns An object with either a `success` flag and the parsed and typed data, or a `success` flag and an error object.\n */\nexport function safeValidateTypes<T>({\n  value,\n  schema,\n}: {\n  value: unknown;\n  schema: ZodSchema<T>;\n}):\n  | { success: true; value: T }\n  | { success: false; error: TypeValidationError } {\n  try {\n    const validationResult = schema.safeParse(value);\n\n    if (validationResult.success) {\n      return {\n        success: true,\n        value: validationResult.data,\n      };\n    }\n\n    return {\n      success: false,\n      error: new TypeValidationError({\n        value,\n        cause: validationResult.error,\n      }),\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: TypeValidationError.isTypeValidationError(error)\n        ? error\n        : new TypeValidationError({ value, cause: error }),\n    };\n  }\n}\n", "import { APICallError } from '@ai-sdk/provider';\nimport { extractResponseHeaders } from './extract-response-headers';\nimport { isAbortError } from './is-abort-error';\nimport { ResponseHandler } from './response-handler';\nimport { removeUndefinedEntries } from './remove-undefined-entries';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => fetch;\n\nexport const postJsonToApi = async <T>({\n  url,\n  headers,\n  body,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch,\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  body: unknown;\n  failedResponseHandler: ResponseHandler<APICallError>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: ReturnType<typeof getOriginalFetch>;\n}) =>\n  postToApi({\n    url,\n    headers: {\n      'Content-Type': 'application/json',\n      ...headers,\n    },\n    body: {\n      content: JSON.stringify(body),\n      values: body,\n    },\n    failedResponseHandler,\n    successfulResponseHandler,\n    abortSignal,\n    fetch,\n  });\n\nexport const postToApi = async <T>({\n  url,\n  headers = {},\n  body,\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch(),\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  body: {\n    content: string | FormData | Uint8Array;\n    values: unknown;\n  };\n  failedResponseHandler: ResponseHandler<Error>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: ReturnType<typeof getOriginalFetch>;\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: 'POST',\n      headers: removeUndefinedEntries(headers),\n      body: body.content,\n      signal: abortSignal,\n    });\n\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!response.ok) {\n      let errorInformation: {\n        value: Error;\n        responseHeaders?: Record<string, string> | undefined;\n      };\n\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: body.values,\n        });\n      } catch (error) {\n        if (isAbortError(error) || APICallError.isAPICallError(error)) {\n          throw error;\n        }\n\n        throw new APICallError({\n          message: 'Failed to process error response',\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: body.values,\n        });\n      }\n\n      throw errorInformation.value;\n    }\n\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: body.values,\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || APICallError.isAPICallError(error)) {\n          throw error;\n        }\n      }\n\n      throw new APICallError({\n        message: 'Failed to process successful response',\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: body.values,\n      });\n    }\n  } catch (error) {\n    if (isAbortError(error)) {\n      throw error;\n    }\n\n    // unwrap original error when fetch failed (for easier debugging):\n    if (error instanceof TypeError && error.message === 'fetch failed') {\n      const cause = (error as any).cause;\n\n      if (cause != null) {\n        // Failed to connect to server:\n        throw new APICallError({\n          message: `Cannot connect to API: ${cause.message}`,\n          cause,\n          url,\n          requestBodyValues: body.values,\n          isRetryable: true, // retry when network error\n        });\n      }\n    }\n\n    throw error;\n  }\n};\n", "export function removeUndefinedEntries<T>(\n  record: Record<string, T | undefined>,\n): Record<string, T> {\n  return Object.fromEntries(\n    Object.entries(record).filter(([_key, value]) => value != null),\n  ) as Record<string, T>;\n}\n", "import { APICallError, EmptyResponseBodyError } from '@ai-sdk/provider';\nimport {\n  EventSourceParserStream,\n  ParsedEvent,\n} from 'eventsource-parser/stream';\nimport { ZodSchema } from 'zod';\nimport { extractResponseHeaders } from './extract-response-headers';\nimport { ParseResult, parseJSON, safeParseJSON } from './parse-json';\n\nexport type ResponseHandler<RETURN_TYPE> = (options: {\n  url: string;\n  requestBodyValues: unknown;\n  response: Response;\n}) => PromiseLike<{\n  value: RETURN_TYPE;\n  responseHeaders?: Record<string, string>;\n}>;\n\nexport const createJsonErrorResponseHandler =\n  <T>({\n    errorSchema,\n    errorToMessage,\n    isRetryable,\n  }: {\n    errorSchema: ZodSchema<T>;\n    errorToMessage: (error: T) => string;\n    isRetryable?: (response: Response, error?: T) => boolean;\n  }): ResponseHandler<APICallError> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseBody = await response.text();\n    const responseHeaders = extractResponseHeaders(response);\n\n    // Some providers return an empty response body for some errors:\n    if (responseBody.trim() === '') {\n      return {\n        responseHeaders,\n        value: new APICallError({\n          message: response.statusText,\n          url,\n          requestBodyValues,\n          statusCode: response.status,\n          responseHeaders,\n          responseBody,\n          isRetryable: isRetryable?.(response),\n        }),\n      };\n    }\n\n    // resilient parsing in case the response is not JSON or does not match the schema:\n    try {\n      const parsedError = parseJSON({\n        text: responseBody,\n        schema: errorSchema,\n      });\n\n      return {\n        responseHeaders,\n        value: new APICallError({\n          message: errorToMessage(parsedError),\n          url,\n          requestBodyValues,\n          statusCode: response.status,\n          responseHeaders,\n          responseBody,\n          data: parsedError,\n          isRetryable: isRetryable?.(response, parsedError),\n        }),\n      };\n    } catch (parseError) {\n      return {\n        responseHeaders,\n        value: new APICallError({\n          message: response.statusText,\n          url,\n          requestBodyValues,\n          statusCode: response.status,\n          responseHeaders,\n          responseBody,\n          isRetryable: isRetryable?.(response),\n        }),\n      };\n    }\n  };\n\nexport const createEventSourceResponseHandler =\n  <T>(\n    chunkSchema: ZodSchema<T>,\n  ): ResponseHandler<ReadableStream<ParseResult<T>>> =>\n  async ({ response }: { response: Response }) => {\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (response.body == null) {\n      throw new EmptyResponseBodyError({});\n    }\n\n    return {\n      responseHeaders,\n      value: response.body\n        .pipeThrough(new TextDecoderStream())\n        .pipeThrough(new EventSourceParserStream())\n        .pipeThrough(\n          new TransformStream<ParsedEvent, ParseResult<T>>({\n            transform({ data }, controller) {\n              // ignore the 'DONE' event that e.g. OpenAI sends:\n              if (data === '[DONE]') {\n                return;\n              }\n\n              controller.enqueue(\n                safeParseJSON({\n                  text: data,\n                  schema: chunkSchema,\n                }),\n              );\n            },\n          }),\n        ),\n    };\n  };\n\nexport const createJsonStreamResponseHandler =\n  <T>(\n    chunkSchema: ZodSchema<T>,\n  ): ResponseHandler<ReadableStream<ParseResult<T>>> =>\n  async ({ response }: { response: Response }) => {\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (response.body == null) {\n      throw new EmptyResponseBodyError({});\n    }\n\n    let buffer = '';\n\n    return {\n      responseHeaders,\n      value: response.body.pipeThrough(new TextDecoderStream()).pipeThrough(\n        new TransformStream<string, ParseResult<T>>({\n          transform(chunkText, controller) {\n            if (chunkText.endsWith('\\n')) {\n              controller.enqueue(\n                safeParseJSON({\n                  text: buffer + chunkText,\n                  schema: chunkSchema,\n                }),\n              );\n              buffer = '';\n            } else {\n              buffer += chunkText;\n            }\n          },\n        }),\n      ),\n    };\n  };\n\nexport const createJsonResponseHandler =\n  <T>(responseSchema: ZodSchema<T>): ResponseHandler<T> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseBody = await response.text();\n\n    const parsedResult = safeParseJSON({\n      text: responseBody,\n      schema: responseSchema,\n    });\n\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!parsedResult.success) {\n      throw new APICallError({\n        message: 'Invalid JSON response',\n        cause: parsedResult.error,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        url,\n        requestBodyValues,\n      });\n    }\n\n    return {\n      responseHeaders,\n      value: parsedResult.value,\n    };\n  };\n", "export function convertBase64ToUint8Array(base64String: string) {\n  const base64Url = base64String.replace(/-/g, '+').replace(/_/g, '/');\n  const latin1string = globalThis.atob(base64Url);\n  return Uint8Array.from(latin1string, byte => byte.codePointAt(0)!);\n}\n\nexport function convertUint8ArrayToBase64(array: Uint8Array): string {\n  let latin1string = '';\n\n  // Note: regular for loop to support older JavaScript versions that\n  // do not support for..of on Uint8Array\n  for (let i = 0; i < array.length; i++) {\n    latin1string += String.fromCodePoint(array[i]);\n  }\n\n  return globalThis.btoa(latin1string);\n}\n", "export function withoutTrailingSlash(url: string | undefined) {\n  return url?.replace(/\\/$/, '');\n}\n", "import { loadApiKey, withoutTrailingSlash } from \"@ai-sdk/provider-utils\";\nimport { OpenRouterChatLanguageModel } from \"./openrouter-chat-language-model\";\nimport type {\n  OpenRouterChatModelId,\n  OpenRouterChatSettings,\n} from \"./openrouter-chat-settings\";\nimport { OpenRouterCompletionLanguageModel } from \"./openrouter-completion-language-model\";\nimport type {\n  OpenRouterCompletionModelId,\n  OpenRouterCompletionSettings,\n} from \"./openrouter-completion-settings\";\nimport type { OpenRouterProviderSettings } from \"./openrouter-provider\";\n\n/**\n@deprecated Use `createOpenRouter` instead.\n */\nexport class OpenRouter {\n  /**\nUse a different URL prefix for API calls, e.g. to use proxy servers.\nThe default prefix is `https://openrouter.ai/api/v1`.\n   */\n  readonly baseURL: string;\n\n  /**\nAPI key that is being send using the `Authorization` header.\nIt defaults to the `OPENROUTER_API_KEY` environment variable.\n */\n  readonly apiKey?: string;\n\n  /**\nCustom headers to include in the requests.\n   */\n  readonly headers?: Record<string, string>;\n\n  /**\n   * Creates a new OpenRouter provider instance.\n   */\n  constructor(options: OpenRouterProviderSettings = {}) {\n    this.baseURL =\n      withoutTrailingSlash(options.baseURL ?? options.baseUrl) ??\n      \"https://openrouter.ai/api/v1\";\n    this.apiKey = options.apiKey;\n    this.headers = options.headers;\n  }\n\n  private get baseConfig() {\n    return {\n      baseURL: this.baseURL,\n      headers: () => ({\n        Authorization: `Bearer ${loadApiKey({\n          apiKey: this.apiKey,\n          environmentVariableName: \"OPENROUTER_API_KEY\",\n          description: \"OpenRouter\",\n        })}`,\n        ...this.headers,\n      }),\n    };\n  }\n\n  chat(modelId: OpenRouterChatModelId, settings: OpenRouterChatSettings = {}) {\n    return new OpenRouterChatLanguageModel(modelId, settings, {\n      provider: \"openrouter.chat\",\n      ...this.baseConfig,\n      compatibility: \"strict\",\n      url: ({ path }) => `${this.baseURL}${path}`,\n    });\n  }\n\n  completion(\n    modelId: OpenRouterCompletionModelId,\n    settings: OpenRouterCompletionSettings = {}\n  ) {\n    return new OpenRouterCompletionLanguageModel(modelId, settings, {\n      provider: \"openrouter.completion\",\n      ...this.baseConfig,\n      compatibility: \"strict\",\n      url: ({ path }) => `${this.baseURL}${path}`,\n    });\n  }\n}\n", "import {\n  InvalidResponseDataError,\n  type LanguageModelV1,\n  type LanguageModelV1FinishReason,\n  type LanguageModelV1LogProbs,\n  type LanguageModelV1StreamPart,\n  UnsupportedFunctionalityError,\n} from \"@ai-sdk/provider\";\nimport type { ParseResult } from \"@ai-sdk/provider-utils\";\nimport {\n  combineHeaders,\n  createEventSourceResponseHandler,\n  createJsonResponseHandler,\n  generateId,\n  isParsableJson,\n  postJsonToApi,\n} from \"@ai-sdk/provider-utils\";\nimport { z } from \"zod\";\nimport { convertToOpenRouterChatMessages } from \"./convert-to-openrouter-chat-messages\";\nimport { mapOpenRouterChatLogProbsOutput } from \"./map-openrouter-chat-logprobs\";\nimport { mapOpenRouterFinishReason } from \"./map-openrouter-finish-reason\";\nimport type {\n  OpenRouterChatModelId,\n  OpenRouterChatSettings,\n} from \"./openrouter-chat-settings\";\nimport {\n  openAIErrorDataSchema,\n  openrouterFailedResponseHandler,\n} from \"./openrouter-error\";\n\ntype OpenRouterChatConfig = {\n  provider: string;\n  compatibility: \"strict\" | \"compatible\";\n  headers: () => Record<string, string | undefined>;\n  url: (options: { modelId: string; path: string }) => string;\n  fetch?: typeof fetch;\n  extraBody?: Record<string, unknown>;\n};\n\nexport class OpenRouterChatLanguageModel implements LanguageModelV1 {\n  readonly specificationVersion = \"v1\";\n  readonly defaultObjectGenerationMode = \"tool\";\n\n  readonly modelId: OpenRouterChatModelId;\n  readonly settings: OpenRouterChatSettings;\n\n  private readonly config: OpenRouterChatConfig;\n\n  constructor(\n    modelId: OpenRouterChatModelId,\n    settings: OpenRouterChatSettings,\n    config: OpenRouterChatConfig\n  ) {\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  private getArgs({\n    mode,\n    prompt,\n    maxTokens,\n    temperature,\n    topP,\n    frequencyPenalty,\n    presencePenalty,\n    seed,\n  }: Parameters<LanguageModelV1[\"doGenerate\"]>[0]) {\n    const type = mode.type;\n\n    const baseArgs = {\n      // model id:\n      model: this.modelId,\n\n      // model specific settings:\n      logit_bias: this.settings.logitBias,\n      logprobs:\n        this.settings.logprobs === true ||\n        typeof this.settings.logprobs === \"number\"\n          ? true\n          : undefined,\n      top_logprobs:\n        typeof this.settings.logprobs === \"number\"\n          ? this.settings.logprobs\n          : typeof this.settings.logprobs === \"boolean\"\n          ? this.settings.logprobs\n            ? 0\n            : undefined\n          : undefined,\n      user: this.settings.user,\n      parallel_tool_calls: this.settings.parallelToolCalls,\n\n      // standardized settings:\n      max_tokens: maxTokens,\n      temperature,\n      top_p: topP,\n      frequency_penalty: frequencyPenalty,\n      presence_penalty: presencePenalty,\n      seed,\n\n      // messages:\n      messages: convertToOpenRouterChatMessages(prompt),\n\n      // extra body:\n      ...this.config.extraBody,\n    };\n\n    switch (type) {\n      case \"regular\": {\n        return { ...baseArgs, ...prepareToolsAndToolChoice(mode) };\n      }\n\n      case \"object-json\": {\n        return {\n          ...baseArgs,\n          response_format: { type: \"json_object\" },\n        };\n      }\n\n      case \"object-tool\": {\n        return {\n          ...baseArgs,\n          tool_choice: { type: \"function\", function: { name: mode.tool.name } },\n          tools: [\n            {\n              type: \"function\",\n              function: {\n                name: mode.tool.name,\n                description: mode.tool.description,\n                parameters: mode.tool.parameters,\n              },\n            },\n          ],\n        };\n      }\n\n      case \"object-grammar\": {\n        throw new UnsupportedFunctionalityError({\n          functionality: \"object-grammar mode\",\n        });\n      }\n\n      default: {\n        const _exhaustiveCheck: never = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  async doGenerate(\n    options: Parameters<LanguageModelV1[\"doGenerate\"]>[0]\n  ): Promise<Awaited<ReturnType<LanguageModelV1[\"doGenerate\"]>>> {\n    const args = this.getArgs(options);\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: this.config.url({\n        path: \"/chat/completions\",\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body: args,\n      failedResponseHandler: openrouterFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        openAIChatResponseSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { messages: rawPrompt, ...rawSettings } = args;\n    const choice = response.choices[0];\n\n    if (choice == null) {\n      throw new Error(\"No choice in response\");\n    }\n\n    return {\n      text: choice.message.content ?? undefined,\n      toolCalls: choice.message.tool_calls?.map((toolCall) => ({\n        toolCallType: \"function\",\n        toolCallId: toolCall.id ?? generateId(),\n        toolName: toolCall.function.name,\n        args: toolCall.function.arguments!,\n      })),\n      finishReason: mapOpenRouterFinishReason(choice.finish_reason),\n      usage: {\n        promptTokens: response.usage.prompt_tokens,\n        completionTokens: response.usage.completion_tokens,\n      },\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings: [],\n      logprobs: mapOpenRouterChatLogProbsOutput(choice.logprobs),\n    };\n  }\n\n  async doStream(\n    options: Parameters<LanguageModelV1[\"doStream\"]>[0]\n  ): Promise<Awaited<ReturnType<LanguageModelV1[\"doStream\"]>>> {\n    const args = this.getArgs(options);\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: this.config.url({\n        path: \"/chat/completions\",\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body: {\n        ...args,\n        stream: true,\n\n        // only include stream_options when in strict compatibility mode:\n        stream_options:\n          this.config.compatibility === \"strict\"\n            ? { include_usage: true }\n            : undefined,\n      },\n      failedResponseHandler: openrouterFailedResponseHandler,\n      successfulResponseHandler: createEventSourceResponseHandler(\n        openrouterChatChunkSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { messages: rawPrompt, ...rawSettings } = args;\n\n    const toolCalls: Array<{\n      id: string;\n      type: \"function\";\n      function: {\n        name: string;\n        arguments: string;\n      };\n    }> = [];\n\n    let finishReason: LanguageModelV1FinishReason = \"other\";\n    let usage: { promptTokens: number; completionTokens: number } = {\n      promptTokens: Number.NaN,\n      completionTokens: Number.NaN,\n    };\n    let logprobs: LanguageModelV1LogProbs;\n\n    return {\n      stream: response.pipeThrough(\n        new TransformStream<\n          ParseResult<z.infer<typeof openrouterChatChunkSchema>>,\n          LanguageModelV1StreamPart\n        >({\n          transform(chunk, controller) {\n            // handle failed chunk parsing / validation:\n            if (!chunk.success) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: chunk.error });\n              return;\n            }\n\n            const value = chunk.value;\n\n            // handle error chunks:\n            if (\"error\" in value) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: value.error });\n              return;\n            }\n\n            if (value.usage != null) {\n              usage = {\n                promptTokens: value.usage.prompt_tokens,\n                completionTokens: value.usage.completion_tokens,\n              };\n            }\n\n            const choice = value.choices[0];\n\n            if (choice?.finish_reason != null) {\n              finishReason = mapOpenRouterFinishReason(choice.finish_reason);\n            }\n\n            if (choice?.delta == null) {\n              return;\n            }\n\n            const delta = choice.delta;\n\n            if (delta.content != null) {\n              controller.enqueue({\n                type: \"text-delta\",\n                textDelta: delta.content,\n              });\n            }\n\n            const mappedLogprobs = mapOpenRouterChatLogProbsOutput(\n              choice?.logprobs\n            );\n            if (mappedLogprobs?.length) {\n              if (logprobs === undefined) logprobs = [];\n              logprobs.push(...mappedLogprobs);\n            }\n\n            if (delta.tool_calls != null) {\n              for (const toolCallDelta of delta.tool_calls) {\n                const index = toolCallDelta.index;\n\n                // Tool call start. OpenRouter returns all information except the arguments in the first chunk.\n                if (toolCalls[index] == null) {\n                  if (toolCallDelta.type !== \"function\") {\n                    throw new InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'function' type.`,\n                    });\n                  }\n\n                  if (toolCallDelta.id == null) {\n                    throw new InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'id' to be a string.`,\n                    });\n                  }\n\n                  if (toolCallDelta.function?.name == null) {\n                    throw new InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'function.name' to be a string.`,\n                    });\n                  }\n\n                  toolCalls[index] = {\n                    id: toolCallDelta.id,\n                    type: \"function\",\n                    function: {\n                      name: toolCallDelta.function.name,\n                      arguments: toolCallDelta.function.arguments ?? \"\",\n                    },\n                  };\n\n                  const toolCall = toolCalls[index];\n\n                  if (toolCall == null) {\n                    throw new Error(\"Tool call is missing\");\n                  }\n\n                  // check if tool call is complete (some providers send the full tool call in one chunk)\n                  if (\n                    toolCall.function?.name != null &&\n                    toolCall.function?.arguments != null &&\n                    isParsableJson(toolCall.function.arguments)\n                  ) {\n                    // send delta\n                    controller.enqueue({\n                      type: \"tool-call-delta\",\n                      toolCallType: \"function\",\n                      toolCallId: toolCall.id,\n                      toolName: toolCall.function.name,\n                      argsTextDelta: toolCall.function.arguments,\n                    });\n\n                    // send tool call\n                    controller.enqueue({\n                      type: \"tool-call\",\n                      toolCallType: \"function\",\n                      toolCallId: toolCall.id ?? generateId(),\n                      toolName: toolCall.function.name,\n                      args: toolCall.function.arguments,\n                    });\n                  }\n\n                  continue;\n                }\n\n                // existing tool call, merge\n                const toolCall = toolCalls[index];\n\n                if (toolCall == null) {\n                  throw new Error(\"Tool call is missing\");\n                }\n\n                if (toolCallDelta.function?.arguments != null) {\n                  toolCall.function!.arguments +=\n                    toolCallDelta.function?.arguments ?? \"\";\n                }\n\n                // send delta\n                controller.enqueue({\n                  type: \"tool-call-delta\",\n                  toolCallType: \"function\",\n                  toolCallId: toolCall.id,\n                  toolName: toolCall.function.name,\n                  argsTextDelta: toolCallDelta.function.arguments ?? \"\",\n                });\n\n                // check if tool call is complete\n                if (\n                  toolCall.function?.name != null &&\n                  toolCall.function?.arguments != null &&\n                  isParsableJson(toolCall.function.arguments)\n                ) {\n                  controller.enqueue({\n                    type: \"tool-call\",\n                    toolCallType: \"function\",\n                    toolCallId: toolCall.id ?? generateId(),\n                    toolName: toolCall.function.name,\n                    args: toolCall.function.arguments,\n                  });\n                }\n              }\n            }\n          },\n\n          flush(controller) {\n            controller.enqueue({\n              type: \"finish\",\n              finishReason,\n              logprobs,\n              usage,\n            });\n          },\n        })\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings: [],\n    };\n  }\n}\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst openAIChatResponseSchema = z.object({\n  choices: z.array(\n    z.object({\n      message: z.object({\n        role: z.literal(\"assistant\"),\n        content: z.string().nullable().optional(),\n        tool_calls: z\n          .array(\n            z.object({\n              id: z.string().optional().nullable(),\n              type: z.literal(\"function\"),\n              function: z.object({\n                name: z.string(),\n                arguments: z.string(),\n              }),\n            })\n          )\n          .optional(),\n      }),\n      index: z.number(),\n      logprobs: z\n        .object({\n          content: z\n            .array(\n              z.object({\n                token: z.string(),\n                logprob: z.number(),\n                top_logprobs: z.array(\n                  z.object({\n                    token: z.string(),\n                    logprob: z.number(),\n                  })\n                ),\n              })\n            )\n            .nullable(),\n        })\n        .nullable()\n        .optional(),\n      finish_reason: z.string().optional().nullable(),\n    })\n  ),\n  usage: z.object({\n    prompt_tokens: z.number(),\n    completion_tokens: z.number(),\n  }),\n});\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst openrouterChatChunkSchema = z.union([\n  z.object({\n    choices: z.array(\n      z.object({\n        delta: z\n          .object({\n            role: z.enum([\"assistant\"]).optional(),\n            content: z.string().nullish(),\n            tool_calls: z\n              .array(\n                z.object({\n                  index: z.number(),\n                  id: z.string().nullish(),\n                  type: z.literal(\"function\").optional(),\n                  function: z.object({\n                    name: z.string().nullish(),\n                    arguments: z.string().nullish(),\n                  }),\n                })\n              )\n              .nullish(),\n          })\n          .nullish(),\n        logprobs: z\n          .object({\n            content: z\n              .array(\n                z.object({\n                  token: z.string(),\n                  logprob: z.number(),\n                  top_logprobs: z.array(\n                    z.object({\n                      token: z.string(),\n                      logprob: z.number(),\n                    })\n                  ),\n                })\n              )\n              .nullable(),\n          })\n          .nullish(),\n        finish_reason: z.string().nullable().optional(),\n        index: z.number(),\n      })\n    ),\n    usage: z\n      .object({\n        prompt_tokens: z.number(),\n        completion_tokens: z.number(),\n      })\n      .nullish(),\n  }),\n  openAIErrorDataSchema,\n]);\n\nfunction prepareToolsAndToolChoice(\n  mode: Parameters<LanguageModelV1[\"doGenerate\"]>[0][\"mode\"] & {\n    type: \"regular\";\n  }\n) {\n  // when the tools array is empty, change it to undefined to prevent errors:\n  const tools = mode.tools?.length ? mode.tools : undefined;\n\n  if (tools == null) {\n    return { tools: undefined, tool_choice: undefined };\n  }\n\n  const mappedTools = tools.map((tool) => ({\n    type: \"function\",\n    function: {\n      name: tool.name,\n      description: tool.description,\n      parameters: tool.parameters,\n    },\n  }));\n\n  const toolChoice = mode.toolChoice;\n\n  if (toolChoice == null) {\n    return { tools: mappedTools, tool_choice: undefined };\n  }\n\n  const type = toolChoice.type;\n\n  switch (type) {\n    case \"auto\":\n    case \"none\":\n    case \"required\":\n      return { tools: mappedTools, tool_choice: type };\n    case \"tool\":\n      return {\n        tools: mappedTools,\n        tool_choice: {\n          type: \"function\",\n          function: {\n            name: toolChoice.toolName,\n          },\n        },\n      };\n    default: {\n      const _exhaustiveCheck: never = type;\n      throw new Error(`Unsupported tool choice type: ${_exhaustiveCheck}`);\n    }\n  }\n}\n", "import type { LanguageModelV1Prompt } from \"@ai-sdk/provider\";\nimport { convertUint8ArrayToBase64 } from \"@ai-sdk/provider-utils\";\nimport type { OpenRouterChatPrompt } from \"./openrouter-chat-prompt\";\n\nexport function convertToOpenRouterChatMessages(\n  prompt: LanguageModelV1Prompt\n): OpenRouterChatPrompt {\n  const messages: OpenRouterChatPrompt = [];\n\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case \"system\": {\n        messages.push({ role: \"system\", content });\n        break;\n      }\n\n      case \"user\": {\n        if (content.length === 1 && content[0]?.type === \"text\") {\n          messages.push({ role: \"user\", content: content[0].text });\n          break;\n        }\n\n        messages.push({\n          role: \"user\",\n          content: content.map((part) => {\n            switch (part.type) {\n              case \"text\": {\n                return { type: \"text\", text: part.text };\n              }\n              case \"image\": {\n                return {\n                  type: \"image_url\",\n                  image_url: {\n                    url:\n                      part.image instanceof URL\n                        ? part.image.toString()\n                        : `data:${\n                            part.mimeType ?? \"image/jpeg\"\n                          };base64,${convertUint8ArrayToBase64(part.image)}`,\n                  },\n                };\n              }\n            }\n          }),\n        });\n\n        break;\n      }\n\n      case \"assistant\": {\n        let text = \"\";\n        const toolCalls: Array<{\n          id: string;\n          type: \"function\";\n          function: { name: string; arguments: string };\n        }> = [];\n\n        for (const part of content) {\n          switch (part.type) {\n            case \"text\": {\n              text += part.text;\n              break;\n            }\n            case \"tool-call\": {\n              toolCalls.push({\n                id: part.toolCallId,\n                type: \"function\",\n                function: {\n                  name: part.toolName,\n                  arguments: JSON.stringify(part.args),\n                },\n              });\n              break;\n            }\n            default: {\n              const _exhaustiveCheck: never = part;\n              throw new Error(`Unsupported part: ${_exhaustiveCheck}`);\n            }\n          }\n        }\n\n        messages.push({\n          role: \"assistant\",\n          content: text,\n          tool_calls: toolCalls.length > 0 ? toolCalls : undefined,\n        });\n\n        break;\n      }\n\n      case \"tool\": {\n        for (const toolResponse of content) {\n          messages.push({\n            role: \"tool\",\n            tool_call_id: toolResponse.toolCallId,\n            content: JSON.stringify(toolResponse.result),\n          });\n        }\n        break;\n      }\n\n      default: {\n        const _exhaustiveCheck: never = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  return messages;\n}\n", "import type { LanguageModelV1LogProbs } from \"@ai-sdk/provider\";\n\ntype OpenRouterChatLogProbs = {\n  content:\n    | {\n        token: string;\n        logprob: number;\n        top_logprobs:\n          | {\n              token: string;\n              logprob: number;\n            }[]\n          | null;\n      }[]\n    | null;\n};\n\nexport function mapOpenRouterChatLogProbsOutput(\n  logprobs: OpenRouterChatLogProbs | null | undefined\n): LanguageModelV1LogProbs | undefined {\n  return (\n    logprobs?.content?.map(({ token, logprob, top_logprobs }) => ({\n      token,\n      logprob,\n      topLogprobs: top_logprobs\n        ? top_logprobs.map(({ token, logprob }) => ({\n            token,\n            logprob,\n          }))\n        : [],\n    })) ?? undefined\n  );\n}\n", "import type { LanguageModelV1FinishReason } from \"@ai-sdk/provider\";\n\nexport function mapOpenRouterFinishReason(\n  finishReason: string | null | undefined\n): LanguageModelV1FinishReason {\n  switch (finishReason) {\n    case \"stop\":\n      return \"stop\";\n    case \"length\":\n      return \"length\";\n    case \"content_filter\":\n      return \"content-filter\";\n    case \"function_call\":\n    case \"tool_calls\":\n      return \"tool-calls\";\n    default:\n      return \"unknown\";\n  }\n}\n", "import { z } from \"zod\";\nimport { createJsonErrorResponseHandler } from \"@ai-sdk/provider-utils\";\n\nexport const openAIErrorDataSchema = z.object({\n  error: z.object({\n    message: z.string(),\n    type: z.string(),\n    param: z.any().nullable(),\n    code: z.string().nullable(),\n  }),\n});\n\nexport type OpenRouterErrorData = z.infer<typeof openAIErrorDataSchema>;\n\nexport const openrouterFailedResponseHandler = createJsonErrorResponseHandler({\n  errorSchema: openAIErrorDataSchema,\n  errorToMessage: (data) => data.error.message,\n});\n", "import {\n  UnsupportedFunctionalityError,\n  type LanguageModelV1,\n  type LanguageModelV1FinishReason,\n  type LanguageModelV1LogProbs,\n  type LanguageModelV1StreamPart,\n} from \"@ai-sdk/provider\";\nimport type { ParseResult } from \"@ai-sdk/provider-utils\";\nimport {\n  combineHeaders,\n  createEventSourceResponseHandler,\n  createJsonResponseHandler,\n  postJsonToApi,\n} from \"@ai-sdk/provider-utils\";\nimport { z } from \"zod\";\nimport { convertToOpenRouterCompletionPrompt } from \"./convert-to-openrouter-completion-prompt\";\nimport { mapOpenRouterCompletionLogProbs } from \"./map-openrouter-completion-logprobs\";\nimport { mapOpenRouterFinishReason } from \"./map-openrouter-finish-reason\";\nimport type {\n  OpenRouterCompletionModelId,\n  OpenRouterCompletionSettings,\n} from \"./openrouter-completion-settings\";\nimport {\n  openAIErrorDataSchema,\n  openrouterFailedResponseHandler,\n} from \"./openrouter-error\";\n\ntype OpenRouterCompletionConfig = {\n  provider: string;\n  compatibility: \"strict\" | \"compatible\";\n  headers: () => Record<string, string | undefined>;\n  url: (options: { modelId: string; path: string }) => string;\n  fetch?: typeof fetch;\n  extraBody?: Record<string, unknown>;\n};\n\nexport class OpenRouterCompletionLanguageModel implements LanguageModelV1 {\n  readonly specificationVersion = \"v1\";\n  readonly defaultObjectGenerationMode = undefined;\n\n  readonly modelId: OpenRouterCompletionModelId;\n  readonly settings: OpenRouterCompletionSettings;\n\n  private readonly config: OpenRouterCompletionConfig;\n\n  constructor(\n    modelId: OpenRouterCompletionModelId,\n    settings: OpenRouterCompletionSettings,\n    config: OpenRouterCompletionConfig\n  ) {\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  private getArgs({\n    mode,\n    inputFormat,\n    prompt,\n    maxTokens,\n    temperature,\n    topP,\n    frequencyPenalty,\n    presencePenalty,\n    seed,\n  }: Parameters<LanguageModelV1[\"doGenerate\"]>[0]) {\n    const type = mode.type;\n\n    const { prompt: completionPrompt, stopSequences } =\n      convertToOpenRouterCompletionPrompt({ prompt, inputFormat });\n\n    const baseArgs = {\n      // model id:\n      model: this.modelId,\n\n      // model specific settings:\n      echo: this.settings.echo,\n      logit_bias: this.settings.logitBias,\n      logprobs:\n        typeof this.settings.logprobs === \"number\"\n          ? this.settings.logprobs\n          : typeof this.settings.logprobs === \"boolean\"\n          ? this.settings.logprobs\n            ? 0\n            : undefined\n          : undefined,\n      suffix: this.settings.suffix,\n      user: this.settings.user,\n\n      // standardized settings:\n      max_tokens: maxTokens,\n      temperature,\n      top_p: topP,\n      frequency_penalty: frequencyPenalty,\n      presence_penalty: presencePenalty,\n      seed,\n\n      // prompt:\n      prompt: completionPrompt,\n\n      // stop sequences:\n      stop: stopSequences,\n\n      // extra body:\n      ...this.config.extraBody,\n    };\n\n    switch (type) {\n      case \"regular\": {\n        if (mode.tools?.length) {\n          throw new UnsupportedFunctionalityError({\n            functionality: \"tools\",\n          });\n        }\n\n        if (mode.toolChoice) {\n          throw new UnsupportedFunctionalityError({\n            functionality: \"toolChoice\",\n          });\n        }\n\n        return baseArgs;\n      }\n\n      case \"object-json\": {\n        throw new UnsupportedFunctionalityError({\n          functionality: \"object-json mode\",\n        });\n      }\n\n      case \"object-tool\": {\n        throw new UnsupportedFunctionalityError({\n          functionality: \"object-tool mode\",\n        });\n      }\n\n      case \"object-grammar\": {\n        throw new UnsupportedFunctionalityError({\n          functionality: \"object-grammar mode\",\n        });\n      }\n\n      default: {\n        const _exhaustiveCheck: never = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  async doGenerate(\n    options: Parameters<LanguageModelV1[\"doGenerate\"]>[0]\n  ): Promise<Awaited<ReturnType<LanguageModelV1[\"doGenerate\"]>>> {\n    const args = this.getArgs(options);\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: this.config.url({\n        path: \"/completions\",\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body: args,\n      failedResponseHandler: openrouterFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        openAICompletionResponseSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { prompt: rawPrompt, ...rawSettings } = args;\n    const choice = response.choices[0];\n\n    if (!choice) {\n      throw new Error(\"No choice in OpenRouter completion response\");\n    }\n\n    return {\n      text: choice.text,\n      usage: {\n        promptTokens: response.usage.prompt_tokens,\n        completionTokens: response.usage.completion_tokens,\n      },\n      finishReason: mapOpenRouterFinishReason(choice.finish_reason),\n      logprobs: mapOpenRouterCompletionLogProbs(choice.logprobs),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings: [],\n    };\n  }\n\n  async doStream(\n    options: Parameters<LanguageModelV1[\"doStream\"]>[0]\n  ): Promise<Awaited<ReturnType<LanguageModelV1[\"doStream\"]>>> {\n    const args = this.getArgs(options);\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: this.config.url({\n        path: \"/completions\",\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body: {\n        ...this.getArgs(options),\n        stream: true,\n\n        // only include stream_options when in strict compatibility mode:\n        stream_options:\n          this.config.compatibility === \"strict\"\n            ? { include_usage: true }\n            : undefined,\n      },\n      failedResponseHandler: openrouterFailedResponseHandler,\n      successfulResponseHandler: createEventSourceResponseHandler(\n        openrouterCompletionChunkSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { prompt: rawPrompt, ...rawSettings } = args;\n\n    let finishReason: LanguageModelV1FinishReason = \"other\";\n    let usage: { promptTokens: number; completionTokens: number } = {\n      promptTokens: Number.NaN,\n      completionTokens: Number.NaN,\n    };\n    let logprobs: LanguageModelV1LogProbs;\n\n    return {\n      stream: response.pipeThrough(\n        new TransformStream<\n          ParseResult<z.infer<typeof openrouterCompletionChunkSchema>>,\n          LanguageModelV1StreamPart\n        >({\n          transform(chunk, controller) {\n            // handle failed chunk parsing / validation:\n            if (!chunk.success) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: chunk.error });\n              return;\n            }\n\n            const value = chunk.value;\n\n            // handle error chunks:\n            if (\"error\" in value) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: value.error });\n              return;\n            }\n\n            if (value.usage != null) {\n              usage = {\n                promptTokens: value.usage.prompt_tokens,\n                completionTokens: value.usage.completion_tokens,\n              };\n            }\n\n            const choice = value.choices[0];\n\n            if (choice?.finish_reason != null) {\n              finishReason = mapOpenRouterFinishReason(choice.finish_reason);\n            }\n\n            if (choice?.text != null) {\n              controller.enqueue({\n                type: \"text-delta\",\n                textDelta: choice.text,\n              });\n            }\n\n            const mappedLogprobs = mapOpenRouterCompletionLogProbs(\n              choice?.logprobs\n            );\n            if (mappedLogprobs?.length) {\n              if (logprobs === undefined) logprobs = [];\n              logprobs.push(...mappedLogprobs);\n            }\n          },\n\n          flush(controller) {\n            controller.enqueue({\n              type: \"finish\",\n              finishReason,\n              logprobs,\n              usage,\n            });\n          },\n        })\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings: [],\n    };\n  }\n}\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst openAICompletionResponseSchema = z.object({\n  choices: z.array(\n    z.object({\n      text: z.string(),\n      finish_reason: z.string(),\n      logprobs: z\n        .object({\n          tokens: z.array(z.string()),\n          token_logprobs: z.array(z.number()),\n          top_logprobs: z.array(z.record(z.string(), z.number())).nullable(),\n        })\n        .nullable()\n        .optional(),\n    })\n  ),\n  usage: z.object({\n    prompt_tokens: z.number(),\n    completion_tokens: z.number(),\n  }),\n});\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst openrouterCompletionChunkSchema = z.union([\n  z.object({\n    choices: z.array(\n      z.object({\n        text: z.string(),\n        finish_reason: z.string().nullish(),\n        index: z.number(),\n        logprobs: z\n          .object({\n            tokens: z.array(z.string()),\n            token_logprobs: z.array(z.number()),\n            top_logprobs: z.array(z.record(z.string(), z.number())).nullable(),\n          })\n          .nullable()\n          .optional(),\n      })\n    ),\n    usage: z\n      .object({\n        prompt_tokens: z.number(),\n        completion_tokens: z.number(),\n      })\n      .optional()\n      .nullable(),\n  }),\n  openAIErrorDataSchema,\n]);\n", "import {\n  InvalidPromptError,\n  type LanguageModelV1Prompt,\n  UnsupportedFunctionalityError,\n} from \"@ai-sdk/provider\";\n\nexport function convertToOpenRouterCompletionPrompt({\n  prompt,\n  inputFormat,\n  user = \"user\",\n  assistant = \"assistant\",\n}: {\n  prompt: LanguageModelV1Prompt;\n  inputFormat: \"prompt\" | \"messages\";\n  user?: string;\n  assistant?: string;\n}): {\n  prompt: string;\n  stopSequences?: string[];\n} {\n  // When the user supplied a prompt input, we don't transform it:\n  if (\n    inputFormat === \"prompt\" &&\n    prompt.length === 1 &&\n    prompt[0] &&\n    prompt[0].role === \"user\" &&\n    prompt[0].content.length === 1 &&\n    prompt[0].content[0] &&\n    prompt[0].content[0].type === \"text\"\n  ) {\n    return { prompt: prompt[0].content[0].text };\n  }\n\n  // otherwise transform to a chat message format:\n  let text = \"\";\n\n  // if first message is a system message, add it to the text:\n  if (prompt[0] && prompt[0].role === \"system\") {\n    text += `${prompt[0].content}\\n\\n`;\n    prompt = prompt.slice(1);\n  }\n\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case \"system\": {\n        throw new InvalidPromptError({\n          message: \"Unexpected system message in prompt: ${content}\",\n          prompt,\n        });\n      }\n\n      case \"user\": {\n        const userMessage = content\n          .map((part) => {\n            switch (part.type) {\n              case \"text\": {\n                return part.text;\n              }\n              case \"image\": {\n                throw new UnsupportedFunctionalityError({\n                  functionality: \"images\",\n                });\n              }\n            }\n          })\n          .join(\"\");\n\n        text += `${user}:\\n${userMessage}\\n\\n`;\n        break;\n      }\n\n      case \"assistant\": {\n        const assistantMessage = content\n          .map((part) => {\n            switch (part.type) {\n              case \"text\": {\n                return part.text;\n              }\n              case \"tool-call\": {\n                throw new UnsupportedFunctionalityError({\n                  functionality: \"tool-call messages\",\n                });\n              }\n            }\n          })\n          .join(\"\");\n\n        text += `${assistant}:\\n${assistantMessage}\\n\\n`;\n        break;\n      }\n\n      case \"tool\": {\n        throw new UnsupportedFunctionalityError({\n          functionality: \"tool messages\",\n        });\n      }\n\n      default: {\n        const _exhaustiveCheck: never = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  // Assistant message prefix:\n  text += `${assistant}:\\n`;\n\n  return {\n    prompt: text,\n    stopSequences: [`\\n${user}:`],\n  };\n}\n", "type OpenRouterCompletionLogProps = {\n  tokens: string[];\n  token_logprobs: number[];\n  top_logprobs: Record<string, number>[] | null;\n};\n\nexport function mapOpenRouterCompletionLogProbs(\n  logprobs: OpenRouterCompletionLogProps | null | undefined\n) {\n  return logprobs?.tokens.map((token, index) => ({\n    token,\n    logprob: logprobs.token_logprobs[index] ?? 0,\n    topLogprobs: logprobs.top_logprobs\n      ? Object.entries(logprobs.top_logprobs[index] ?? {}).map(\n          ([token, logprob]) => ({\n            token,\n            logprob,\n          })\n        )\n      : [],\n  }));\n}\n", "import { loadApi<PERSON><PERSON>, withoutTrailingSlash } from \"@ai-sdk/provider-utils\";\nimport { OpenRouterChatLanguageModel } from \"./openrouter-chat-language-model\";\nimport type {\n  OpenRouterChatModelId,\n  OpenRouterChatSettings,\n} from \"./openrouter-chat-settings\";\nimport { OpenRouterCompletionLanguageModel } from \"./openrouter-completion-language-model\";\nimport type {\n  OpenRouterCompletionModelId,\n  OpenRouterCompletionSettings,\n} from \"./openrouter-completion-settings\";\n\nexport interface OpenRouterProvider {\n  (\n    modelId: \"openai/gpt-3.5-turbo-instruct\",\n    settings?: OpenRouterCompletionSettings\n  ): OpenRouterCompletionLanguageModel;\n  (\n    modelId: OpenRouterChatModelId,\n    settings?: OpenRouterChatSettings\n  ): OpenRouterChatLanguageModel;\n\n  languageModel(\n    modelId: \"openai/gpt-3.5-turbo-instruct\",\n    settings?: OpenRouterCompletionSettings\n  ): OpenRouterCompletionLanguageModel;\n  languageModel(\n    modelId: OpenRouterChatModelId,\n    settings?: OpenRouterChatSettings\n  ): OpenRouterChatLanguageModel;\n\n  /**\nCreates an OpenRouter chat model for text generation.\n   */\n  chat(\n    modelId: OpenRouterChatModelId,\n    settings?: OpenRouterChatSettings\n  ): OpenRouterChatLanguageModel;\n\n  /**\nCreates an OpenRouter completion model for text generation.\n   */\n  completion(\n    modelId: OpenRouterCompletionModelId,\n    settings?: OpenRouterCompletionSettings\n  ): OpenRouterCompletionLanguageModel;\n}\n\nexport interface OpenRouterProviderSettings {\n  /**\nBase URL for the OpenRouter API calls.\n     */\n  baseURL?: string;\n\n  /**\n@deprecated Use `baseURL` instead.\n     */\n  baseUrl?: string;\n\n  /**\nAPI key for authenticating requests.\n     */\n  apiKey?: string;\n\n  /**\nCustom headers to include in the requests.\n     */\n  headers?: Record<string, string>;\n\n  /**\nOpenRouter compatibility mode. Should be set to `strict` when using the OpenRouter API,\nand `compatible` when using 3rd party providers. In `compatible` mode, newer\ninformation such as streamOptions are not being sent. Defaults to 'compatible'.\n   */\n  compatibility?: \"strict\" | \"compatible\";\n\n  /**\nCustom fetch implementation. You can use it as a middleware to intercept requests,\nor to provide a custom fetch implementation for e.g. testing.\n    */\n  fetch?: typeof fetch;\n\n  /**\nA JSON object to send as the request body to access OpenRouter features & upstream provider features.\n  */\n  extraBody?: Record<string, unknown>;\n}\n\n/**\nCreate an OpenRouter provider instance.\n */\nexport function createOpenRouter(\n  options: OpenRouterProviderSettings = {}\n): OpenRouterProvider {\n  const baseURL =\n    withoutTrailingSlash(options.baseURL ?? options.baseUrl) ??\n    \"https://openrouter.ai/api/v1\";\n\n  // we default to compatible, because strict breaks providers like Groq:\n  const compatibility = options.compatibility ?? \"compatible\";\n\n  const getHeaders = () => ({\n    Authorization: `Bearer ${loadApiKey({\n      apiKey: options.apiKey,\n      environmentVariableName: \"OPENROUTER_API_KEY\",\n      description: \"OpenRouter\",\n    })}`,\n    ...options.headers,\n  });\n\n  const createChatModel = (\n    modelId: OpenRouterChatModelId,\n    settings: OpenRouterChatSettings = {}\n  ) =>\n    new OpenRouterChatLanguageModel(modelId, settings, {\n      provider: \"openrouter.chat\",\n      url: ({ path }) => `${baseURL}${path}`,\n      headers: getHeaders,\n      compatibility,\n      fetch: options.fetch,\n      extraBody: options.extraBody,\n    });\n\n  const createCompletionModel = (\n    modelId: OpenRouterCompletionModelId,\n    settings: OpenRouterCompletionSettings = {}\n  ) =>\n    new OpenRouterCompletionLanguageModel(modelId, settings, {\n      provider: \"openrouter.completion\",\n      url: ({ path }) => `${baseURL}${path}`,\n      headers: getHeaders,\n      compatibility,\n      fetch: options.fetch,\n      extraBody: options.extraBody,\n    });\n\n  const createLanguageModel = (\n    modelId: OpenRouterChatModelId | OpenRouterCompletionModelId,\n    settings?: OpenRouterChatSettings | OpenRouterCompletionSettings\n  ) => {\n    if (new.target) {\n      throw new Error(\n        \"The OpenRouter model function cannot be called with the new keyword.\"\n      );\n    }\n\n    if (modelId === \"openai/gpt-3.5-turbo-instruct\") {\n      return createCompletionModel(\n        modelId,\n        settings as OpenRouterCompletionSettings\n      );\n    }\n\n    return createChatModel(modelId, settings as OpenRouterChatSettings);\n  };\n\n  const provider = function (\n    modelId: OpenRouterChatModelId | OpenRouterCompletionModelId,\n    settings?: OpenRouterChatSettings | OpenRouterCompletionSettings\n  ) {\n    return createLanguageModel(modelId, settings);\n  };\n\n  provider.languageModel = createLanguageModel;\n  provider.chat = createChatModel;\n  provider.completion = createCompletionModel;\n\n  return provider as OpenRouterProvider;\n}\n\n/**\nDefault OpenRouter provider instance. It uses 'strict' compatibility mode.\n */\nexport const openrouter = createOpenRouter({\n  compatibility: \"strict\", // strict for OpenRouter API\n});\n"], "mappings": ";;;;;;;;;;;;;AAAO,IAAM,eAAN,cAA2B,MAAM;EAYtC,YAAY;IACV;IACA;IACA;IACA;IACA;IACA;IACA;IACA,cAAc,cAAc,SACzB,eAAe;IACd,eAAe;IACf,eAAe;IACf,cAAc;;IAClB;EACF,GAUG;AACD,UAAM,OAAO;AAEb,SAAK,OAAO;AAEZ,SAAK,MAAM;AACX,SAAK,oBAAoB;AACzB,SAAK,aAAa;AAClB,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,QAAQ;AACb,SAAK,cAAc;AACnB,SAAK,OAAO;EACd;EAEA,OAAO,eAAe,OAAuC;AAC3D,WACE,iBAAiB,SACjB,MAAM,SAAS,qBACf,OAAQ,MAAuB,QAAQ,YACvC,OAAQ,MAAuB,sBAAsB,aACnD,MAAuB,cAAc,QACrC,OAAQ,MAAuB,eAAe,cAC9C,MAAuB,mBAAmB,QAC1C,OAAQ,MAAuB,oBAAoB,cACnD,MAAuB,gBAAgB,QACvC,OAAQ,MAAuB,iBAAiB,cAChD,MAAuB,SAAS,QAChC,OAAQ,MAAuB,UAAU,aAC3C,OAAQ,MAAuB,gBAAgB,cAC7C,MAAuB,QAAQ,QAC/B,OAAQ,MAAuB,SAAS;EAE9C;EAEA,SAAS;AACP,WAAO;MACL,MAAM,KAAK;MACX,SAAS,KAAK;MACd,KAAK,KAAK;MACV,mBAAmB,KAAK;MACxB,YAAY,KAAK;MACjB,iBAAiB,KAAK;MACtB,cAAc,KAAK;MACnB,OAAO,KAAK;MACZ,aAAa,KAAK;MAClB,MAAM,KAAK;IACb;EACF;AACF;AErFO,IAAM,yBAAN,cAAqC,MAAM;EAChD,YAAY,EAAE,UAAU,sBAAsB,IAA0B,CAAC,GAAG;AAC1E,UAAM,OAAO;AAEb,SAAK,OAAO;EACd;EAEA,OAAO,yBACL,OACiC;AACjC,WAAO,iBAAiB,SAAS,MAAM,SAAS;EAClD;EAEA,SAAS;AACP,WAAO;MACL,MAAM,KAAK;MACX,SAAS,KAAK;MACd,OAAO,KAAK;IACd;EACF;AACF;AGpBO,IAAM,qBAAN,cAAiC,MAAM;EAG5C,YAAY,EAAE,QAAAA,SAAQ,QAAQ,GAAyC;AACrE,UAAM,mBAAmB,OAAO,EAAE;AAElC,SAAK,OAAO;AAEZ,SAAK,SAASA;EAChB;EAEA,OAAO,qBAAqB,OAA6C;AACvE,WACE,iBAAiB,SACjB,MAAM,SAAS,2BACf,UAAU;EAEd;EAEA,SAAS;AACP,WAAO;MACL,MAAM,KAAK;MACX,SAAS,KAAK;MACd,OAAO,KAAK;MAEZ,QAAQ,KAAK;IACf;EACF;AACF;ACxBO,IAAM,2BAAN,cAAuC,MAAM;EAGlD,YAAY;IACV;IACA,UAAU,0BAA0B,KAAK,UAAU,IAAI,CAAC;EAC1D,GAGG;AACD,UAAM,OAAO;AAEb,SAAK,OAAO;AAEZ,SAAK,OAAO;EACd;EAEA,OAAO,2BACL,OACmC;AACnC,WACE,iBAAiB,SACjB,MAAM,SAAS,iCACd,MAAmC,QAAQ;EAEhD;EAEA,SAAS;AACP,WAAO;MACL,MAAM,KAAK;MACX,SAAS,KAAK;MACd,OAAO,KAAK;MAEZ,MAAM,KAAK;IACb;EACF;AACF;ACxCO,SAAS,gBAAgB,OAA4B;AAC1D,MAAI,SAAS,MAAM;AACjB,WAAO;EACT;AAEA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;EACT;AAEA,MAAI,iBAAiB,OAAO;AAC1B,WAAO,MAAM;EACf;AAEA,SAAO,KAAK,UAAU,KAAK;AAC7B;AEZO,IAAM,iBAAN,cAA6B,MAAM;EAKxC,YAAY,EAAE,MAAM,MAAM,GAAqC;AAC7D;MACE,8BACW,IAAI;iBACK,gBAAgB,KAAK,CAAC;IAC5C;AAEA,SAAK,OAAO;AAEZ,SAAK,QAAQ;AACb,SAAK,OAAO;EACd;EAEA,OAAO,iBAAiB,OAAyC;AAC/D,WACE,iBAAiB,SACjB,MAAM,SAAS,uBACf,OAAQ,MAAyB,SAAS,YAC1C,OAAQ,MAAyB,UAAU;EAE/C;EAEA,SAAS;AACP,WAAO;MACL,MAAM,KAAK;MACX,SAAS,KAAK;MACd,OAAO,KAAK;MACZ,OAAO,KAAK;MAEZ,WAAW,KAAK;IAClB;EACF;AACF;ACvCO,IAAM,kBAAN,cAA8B,MAAM;EACzC,YAAY,EAAE,QAAQ,GAAwB;AAC5C,UAAM,OAAO;AAEb,SAAK,OAAO;EACd;EAEA,OAAO,kBAAkB,OAA0C;AACjE,WAAO,iBAAiB,SAAS,MAAM,SAAS;EAClD;EAEA,SAAS;AACP,WAAO;MACL,MAAM,KAAK;MACX,SAAS,KAAK;IAChB;EACF;AACF;AQfO,IAAM,sBAAN,cAAkC,MAAM;EAI7C,YAAY,EAAE,OAAO,MAAM,GAAuC;AAChE;MACE,kCACY,KAAK,UAAU,KAAK,CAAC;iBACb,gBAAgB,KAAK,CAAC;IAC5C;AAEA,SAAK,OAAO;AAEZ,SAAK,QAAQ;AACb,SAAK,QAAQ;EACf;EAEA,OAAO,sBAAsB,OAA8C;AACzE,WAAO,iBAAiB,SAAS,MAAM,SAAS;EAClD;EAEA,SAAS;AACP,WAAO;MACL,MAAM,KAAK;MACX,SAAS,KAAK;MACd,OAAO,KAAK;MACZ,OAAO,KAAK;MAEZ,OAAO,KAAK;IACd;EACF;AACF;ACjCO,IAAM,gCAAN,cAA4C,MAAM;EAGvD,YAAY,EAAE,cAAc,GAA8B;AACxD,UAAM,IAAI,aAAa,gCAAgC;AAEvD,SAAK,OAAO;AAEZ,SAAK,gBAAgB;EACvB;EAEA,OAAO,gCACL,OACwC;AACxC,WACE,iBAAiB,SACjB,MAAM,SAAS,sCACf,OAAQ,MAAwC,kBAAkB;EAEtE;EAEA,SAAS;AACP,WAAO;MACL,MAAM,KAAK;MACX,SAAS,KAAK;MACd,OAAO,KAAK;MAEZ,eAAe,KAAK;IACtB;EACF;AACF;;;AW7BA,+BAAuB;ATDhB,SAAS,kBACX,SACiC;AACpC,SAAO,QAAQ;IACb,CAAC,iBAAiB,oBAAoB;MACpC,GAAG;MACH,GAAI,kBAAA,OAAA,iBAAkB,CAAC;IACzB;IACA,CAAC;EACH;AACF;AGJO,SAAS,uBACd,UACwB;AACxB,QAAM,UAAkC,CAAC;AACzC,WAAS,QAAQ,QAAQ,CAAC,OAAO,QAAQ;AACvC,YAAQ,GAAG,IAAI;EACjB,CAAC;AACD,SAAO;AACT;ACTO,IAAM,aAAa;EACxB;EACA;AACF;AERO,SAAS,aAAa,OAAgC;AAC3D,SACE,iBAAiB,UAChB,MAAM,SAAS,gBAAgB,MAAM,SAAS;AAEnD;ACHO,SAAS,WAAW;EACzB;EACA;EACA,sBAAsB;EACtB;AACF,GAKW;AACT,MAAI,OAAO,WAAW,UAAU;AAC9B,WAAO;EACT;AAEA,MAAI,UAAU,MAAM;AAClB,UAAM,IAAI,gBAAgB;MACxB,SAAS,GAAG,WAAW;IACzB,CAAC;EACH;AAEA,MAAI,OAAO,YAAY,aAAa;AAClC,UAAM,IAAI,gBAAgB;MACxB,SAAS,GAAG,WAAW,2CAA2C,mBAAmB;IACvF,CAAC;EACH;AAEA,WAAS,QAAQ,IAAI,uBAAuB;AAE5C,MAAI,UAAU,MAAM;AAClB,UAAM,IAAI,gBAAgB;MACxB,SAAS,GAAG,WAAW,2CAA2C,mBAAmB,sBAAsB,uBAAuB;IACpI,CAAC;EACH;AAEA,MAAI,OAAO,WAAW,UAAU;AAC9B,UAAM,IAAI,gBAAgB;MACxB,SAAS,GAAG,WAAW,+CAA+C,uBAAuB;IAC/F,CAAC;EACH;AAEA,SAAO;AACT;AGhCO,SAAS,cAAiB;EAC/B;EACA;AACF,GAGM;AACJ,MAAI;AACF,WAAO,OAAO,MAAM,KAAK;EAC3B,SAAS,OAAO;AACd,UAAM,IAAI,oBAAoB,EAAE,OAAO,OAAO,MAAM,CAAC;EACvD;AACF;AAWO,SAAS,kBAAqB;EACnC;EACA;AACF,GAKmD;AACjD,MAAI;AACF,UAAM,mBAAmB,OAAO,UAAU,KAAK;AAE/C,QAAI,iBAAiB,SAAS;AAC5B,aAAO;QACL,SAAS;QACT,OAAO,iBAAiB;MAC1B;IACF;AAEA,WAAO;MACL,SAAS;MACT,OAAO,IAAI,oBAAoB;QAC7B;QACA,OAAO,iBAAiB;MAC1B,CAAC;IACH;EACF,SAAS,OAAO;AACd,WAAO;MACL,SAAS;MACT,OAAO,oBAAoB,sBAAsB,KAAK,IAClD,QACA,IAAI,oBAAoB,EAAE,OAAO,OAAO,MAAM,CAAC;IACrD;EACF;AACF;AD1CO,SAAS,UAAa;EAC3B;EACA;AACF,GAGM;AACJ,MAAI;AACF,UAAM,QAAQ,yBAAAC,QAAW,MAAM,IAAI;AAEnC,QAAI,UAAU,MAAM;AAClB,aAAO;IACT;AAEA,WAAO,cAAc,EAAE,OAAO,OAAO,CAAC;EACxC,SAAS,OAAO;AACd,QACE,eAAe,iBAAiB,KAAK,KACrCC,oBAAoB,sBAAsB,KAAK,GAC/C;AACA,YAAM;IACR;AAEA,UAAM,IAAI,eAAe,EAAE,MAAM,OAAO,MAAM,CAAC;EACjD;AACF;AA4BO,SAAS,cAAiB;EAC/B;EACA;AACF,GAKoE;AAClE,MAAI;AACF,UAAM,QAAQ,yBAAAD,QAAW,MAAM,IAAI;AAEnC,QAAI,UAAU,MAAM;AAClB,aAAO;QACL,SAAS;QACT;MACF;IACF;AAEA,WAAO,kBAAkB,EAAE,OAAO,OAAO,CAAC;EAC5C,SAAS,OAAO;AACd,WAAO;MACL,SAAS;MACT,OAAO,eAAe,iBAAiB,KAAK,IACxC,QACA,IAAI,eAAe,EAAE,MAAM,OAAO,MAAM,CAAC;IAC/C;EACF;AACF;AAEO,SAAS,eAAe,OAAwB;AACrD,MAAI;AACF,6BAAAA,QAAW,MAAM,KAAK;AACtB,WAAO;EACT,SAAQ,GAAA;AACN,WAAO;EACT;AACF;AGrHO,SAAS,uBACd,QACmB;AACnB,SAAO,OAAO;IACZ,OAAO,QAAQ,MAAM,EAAE,OAAO,CAAC,CAAC,MAAM,KAAK,MAAM,SAAS,IAAI;EAChE;AACF;ADCA,IAAM,mBAAmB,MAAM;AAExB,IAAM,gBAAgB,OAAU;EACrC;EACA;EACA;EACA;EACA;EACA;EACA,OAAAE;AACF,MASE,UAAU;EACR;EACA,SAAS;IACP,gBAAgB;IAChB,GAAG;EACL;EACA,MAAM;IACJ,SAAS,KAAK,UAAU,IAAI;IAC5B,QAAQ;EACV;EACA;EACA;EACA;EACA,OAAAA;AACF,CAAC;AAEI,IAAM,YAAY,OAAU;EACjC;EACA,UAAU,CAAC;EACX;EACA;EACA;EACA;EACA,OAAAA,SAAQ,iBAAiB;AAC3B,MAWM;AACJ,MAAI;AACF,UAAM,WAAW,MAAMA,OAAM,KAAK;MAChC,QAAQ;MACR,SAAS,uBAAuB,OAAO;MACvC,MAAM,KAAK;MACX,QAAQ;IACV,CAAC;AAED,UAAM,kBAAkB,uBAAuB,QAAQ;AAEvD,QAAI,CAAC,SAAS,IAAI;AAChB,UAAI;AAKJ,UAAI;AACF,2BAAmB,MAAM,sBAAsB;UAC7C;UACA;UACA,mBAAmB,KAAK;QAC1B,CAAC;MACH,SAAS,OAAO;AACd,YAAI,aAAa,KAAK,KAAK,aAAa,eAAe,KAAK,GAAG;AAC7D,gBAAM;QACR;AAEA,cAAM,IAAI,aAAa;UACrB,SAAS;UACT,OAAO;UACP,YAAY,SAAS;UACrB;UACA;UACA,mBAAmB,KAAK;QAC1B,CAAC;MACH;AAEA,YAAM,iBAAiB;IACzB;AAEA,QAAI;AACF,aAAO,MAAM,0BAA0B;QACrC;QACA;QACA,mBAAmB,KAAK;MAC1B,CAAC;IACH,SAAS,OAAO;AACd,UAAI,iBAAiB,OAAO;AAC1B,YAAI,aAAa,KAAK,KAAK,aAAa,eAAe,KAAK,GAAG;AAC7D,gBAAM;QACR;MACF;AAEA,YAAM,IAAI,aAAa;QACrB,SAAS;QACT,OAAO;QACP,YAAY,SAAS;QACrB;QACA;QACA,mBAAmB,KAAK;MAC1B,CAAC;IACH;EACF,SAAS,OAAO;AACd,QAAI,aAAa,KAAK,GAAG;AACvB,YAAM;IACR;AAGA,QAAI,iBAAiB,aAAa,MAAM,YAAY,gBAAgB;AAClE,YAAM,QAAS,MAAc;AAE7B,UAAI,SAAS,MAAM;AAEjB,cAAM,IAAI,aAAa;UACrB,SAAS,0BAA0B,MAAM,OAAO;UAChD;UACA;UACA,mBAAmB,KAAK;UACxB,aAAa;;QACf,CAAC;MACH;IACF;AAEA,UAAM;EACR;AACF;AEjIO,IAAM,iCACX,CAAI;EACF;EACA;EACA;AACF,MAKA,OAAO,EAAE,UAAU,KAAK,kBAAkB,MAAM;AAC9C,QAAM,eAAe,MAAM,SAAS,KAAK;AACzC,QAAM,kBAAkB,uBAAuB,QAAQ;AAGvD,MAAI,aAAa,KAAK,MAAM,IAAI;AAC9B,WAAO;MACL;MACA,OAAO,IAAIC,aAAa;QACtB,SAAS,SAAS;QAClB;QACA;QACA,YAAY,SAAS;QACrB;QACA;QACA,aAAa,eAAA,OAAA,SAAA,YAAc,QAAA;MAC7B,CAAC;IACH;EACF;AAGA,MAAI;AACF,UAAM,cAAc,UAAU;MAC5B,MAAM;MACN,QAAQ;IACV,CAAC;AAED,WAAO;MACL;MACA,OAAO,IAAIA,aAAa;QACtB,SAAS,eAAe,WAAW;QACnC;QACA;QACA,YAAY,SAAS;QACrB;QACA;QACA,MAAM;QACN,aAAa,eAAA,OAAA,SAAA,YAAc,UAAU,WAAA;MACvC,CAAC;IACH;EACF,SAAS,YAAY;AACnB,WAAO;MACL;MACA,OAAO,IAAIA,aAAa;QACtB,SAAS,SAAS;QAClB;QACA;QACA,YAAY,SAAS;QACrB;QACA;QACA,aAAa,eAAA,OAAA,SAAA,YAAc,QAAA;MAC7B,CAAC;IACH;EACF;AACF;AAEK,IAAM,mCACX,CACE,gBAEF,OAAO,EAAE,SAAS,MAA8B;AAC9C,QAAM,kBAAkB,uBAAuB,QAAQ;AAEvD,MAAI,SAAS,QAAQ,MAAM;AACzB,UAAM,IAAI,uBAAuB,CAAC,CAAC;EACrC;AAEA,SAAO;IACL;IACA,OAAO,SAAS,KACb,YAAY,IAAI,kBAAkB,CAAC,EACnC,YAAY,IAAI,wBAAwB,CAAC,EACzC;MACC,IAAI,gBAA6C;QAC/C,UAAU,EAAE,KAAK,GAAG,YAAY;AAE9B,cAAI,SAAS,UAAU;AACrB;UACF;AAEA,qBAAW;YACT,cAAc;cACZ,MAAM;cACN,QAAQ;YACV,CAAC;UACH;QACF;MACF,CAAC;IACH;EACJ;AACF;AAqCK,IAAM,4BACX,CAAI,mBACJ,OAAO,EAAE,UAAU,KAAK,kBAAkB,MAAM;AAC9C,QAAM,eAAe,MAAM,SAAS,KAAK;AAEzC,QAAM,eAAe,cAAc;IACjC,MAAM;IACN,QAAQ;EACV,CAAC;AAED,QAAM,kBAAkB,uBAAuB,QAAQ;AAEvD,MAAI,CAAC,aAAa,SAAS;AACzB,UAAM,IAAIC,aAAa;MACrB,SAAS;MACT,OAAO,aAAa;MACpB,YAAY,SAAS;MACrB;MACA;MACA;MACA;IACF,CAAC;EACH;AAEA,SAAO;IACL;IACA,OAAO,aAAa;EACtB;AACF;ACjLK,SAAS,0BAA0B,OAA2B;AACnE,MAAI,eAAe;AAInB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,oBAAgB,OAAO,cAAc,MAAM,CAAC,CAAC;EAC/C;AAEA,SAAO,WAAW,KAAK,YAAY;AACrC;AChBO,SAAS,qBAAqB,KAAyB;AAC5D,SAAO,OAAA,OAAA,SAAA,IAAK,QAAQ,OAAO,EAAA;AAC7B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AGEO,SAAS,gCACdC,SACsB;AANxB,MAAA;AAOE,QAAM,WAAiC,CAAC;AAExC,aAAW,EAAE,MAAM,QAAQ,KAAKA,SAAQ;AACtC,YAAQ,MAAM;MACZ,KAAK,UAAU;AACb,iBAAS,KAAK,EAAE,MAAM,UAAU,QAAQ,CAAC;AACzC;MACF;MAEA,KAAK,QAAQ;AACX,YAAI,QAAQ,WAAW,OAAK,KAAA,QAAQ,CAAC,MAAT,OAAA,SAAA,GAAY,UAAS,QAAQ;AACvD,mBAAS,KAAK,EAAE,MAAM,QAAQ,SAAS,QAAQ,CAAC,EAAE,KAAK,CAAC;AACxD;QACF;AAEA,iBAAS,KAAK;UACZ,MAAM;UACN,SAAS,QAAQ,IAAI,CAAC,SAAS;AAxBzC,gBAAAC;AAyBY,oBAAQ,KAAK,MAAM;cACjB,KAAK,QAAQ;AACX,uBAAO,EAAE,MAAM,QAAQ,MAAM,KAAK,KAAK;cACzC;cACA,KAAK,SAAS;AACZ,uBAAO;kBACL,MAAM;kBACN,WAAW;oBACT,KACE,KAAK,iBAAiB,MAClB,KAAK,MAAM,SAAS,IACpB,SACEA,MAAA,KAAK,aAAL,OAAAA,MAAiB,YACnB,WAAW,0BAA0B,KAAK,KAAK,CAAC;kBACxD;gBACF;cACF;YACF;UACF,CAAC;QACH,CAAC;AAED;MACF;MAEA,KAAK,aAAa;AAChB,YAAI,OAAO;AACX,cAAM,YAID,CAAC;AAEN,mBAAW,QAAQ,SAAS;AAC1B,kBAAQ,KAAK,MAAM;YACjB,KAAK,QAAQ;AACX,sBAAQ,KAAK;AACb;YACF;YACA,KAAK,aAAa;AAChB,wBAAU,KAAK;gBACb,IAAI,KAAK;gBACT,MAAM;gBACN,UAAU;kBACR,MAAM,KAAK;kBACX,WAAW,KAAK,UAAU,KAAK,IAAI;gBACrC;cACF,CAAC;AACD;YACF;YACA,SAAS;AACP,oBAAM,mBAA0B;AAChC,oBAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;YACzD;UACF;QACF;AAEA,iBAAS,KAAK;UACZ,MAAM;UACN,SAAS;UACT,YAAY,UAAU,SAAS,IAAI,YAAY;QACjD,CAAC;AAED;MACF;MAEA,KAAK,QAAQ;AACX,mBAAW,gBAAgB,SAAS;AAClC,mBAAS,KAAK;YACZ,MAAM;YACN,cAAc,aAAa;YAC3B,SAAS,KAAK,UAAU,aAAa,MAAM;UAC7C,CAAC;QACH;AACA;MACF;MAEA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;MACzD;IACF;EACF;AAEA,SAAO;AACT;AC5FO,SAAS,gCACd,UACqC;AAnBvC,MAAA,IAAA;AAoBE,UACE,MAAA,KAAA,YAAA,OAAA,SAAA,SAAU,YAAV,OAAA,SAAA,GAAmB,IAAI,CAAC,EAAE,OAAO,SAAS,aAAa,OAAO;IAC5D;IACA;IACA,aAAa,eACT,aAAa,IAAI,CAAC,EAAE,OAAAC,QAAO,SAAAC,SAAQ,OAAO;MACxC,OAAAD;MACA,SAAAC;IACF,EAAE,IACF,CAAC;EACP,EAAA,MATA,OAAA,KASO;AAEX;AC9BO,SAAS,0BACd,cAC6B;AAC7B,UAAQ,cAAc;IACpB,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;IACL,KAAK;AACH,aAAO;IACT;AACE,aAAO;EACX;AACF;ACfO,IAAM,wBAAwB,iBAAE,OAAO;EAC5C,OAAO,iBAAE,OAAO;IACd,SAAS,iBAAE,OAAO;IAClB,MAAM,iBAAE,OAAO;IACf,OAAO,iBAAE,IAAI,EAAE,SAAS;IACxB,MAAM,iBAAE,OAAO,EAAE,SAAS;EAC5B,CAAC;AACH,CAAC;AAIM,IAAM,kCAAkC,+BAA+B;EAC5E,aAAa;EACb,gBAAgB,CAAC,SAAS,KAAK,MAAM;AACvC,CAAC;AJsBM,IAAM,8BAAN,MAA6D;EASlE,YACE,SACA,UACA,QACA;AAZF,SAAS,uBAAuB;AAChC,SAAS,8BAA8B;AAYrC,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,SAAS;EAChB;EAEA,IAAI,WAAmB;AACrB,WAAO,KAAK,OAAO;EACrB;EAEQ,QAAQ;IACd;IACA,QAAAH;IACA;IACA;IACA;IACA;IACA;IACA;EACF,GAAiD;AAC/C,UAAM,OAAO,KAAK;AAElB,UAAM,WAAW,eAAA;;MAEf,OAAO,KAAK;;MAGZ,YAAY,KAAK,SAAS;MAC1B,UACE,KAAK,SAAS,aAAa,QAC3B,OAAO,KAAK,SAAS,aAAa,WAC9B,OACA;MACN,cACE,OAAO,KAAK,SAAS,aAAa,WAC9B,KAAK,SAAS,WACd,OAAO,KAAK,SAAS,aAAa,YAClC,KAAK,SAAS,WACZ,IACA,SACF;MACN,MAAM,KAAK,SAAS;MACpB,qBAAqB,KAAK,SAAS;;MAGnC,YAAY;MACZ;MACA,OAAO;MACP,mBAAmB;MACnB,kBAAkB;MAClB;;MAGA,UAAU,gCAAgCA,OAAM;IAAA,GAG7C,KAAK,OAAO,SAAA;AAGjB,YAAQ,MAAM;MACZ,KAAK,WAAW;AACd,eAAO,eAAA,eAAA,CAAA,GAAK,QAAA,GAAa,0BAA0B,IAAI,CAAA;MACzD;MAEA,KAAK,eAAe;AAClB,eAAO,cAAA,eAAA,CAAA,GACF,QAAA,GADE;UAEL,iBAAiB,EAAE,MAAM,cAAc;QACzC,CAAA;MACF;MAEA,KAAK,eAAe;AAClB,eAAO,cAAA,eAAA,CAAA,GACF,QAAA,GADE;UAEL,aAAa,EAAE,MAAM,YAAY,UAAU,EAAE,MAAM,KAAK,KAAK,KAAK,EAAE;UACpE,OAAO;YACL;cACE,MAAM;cACN,UAAU;gBACR,MAAM,KAAK,KAAK;gBAChB,aAAa,KAAK,KAAK;gBACvB,YAAY,KAAK,KAAK;cACxB;YACF;UACF;QACF,CAAA;MACF;MAEA,KAAK,kBAAkB;AACrB,cAAM,IAAI,8BAA8B;UACtC,eAAe;QACjB,CAAC;MACH;MAEA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;MACzD;IACF;EACF;EAEA,MAAM,WACJ,SAC6D;AA3JjE,QAAA,IAAA;AA4JI,UAAM,OAAO,KAAK,QAAQ,OAAO;AAEjC,UAAM,EAAE,iBAAiB,OAAO,SAAS,IAAI,MAAM,cAAc;MAC/D,KAAK,KAAK,OAAO,IAAI;QACnB,MAAM;QACN,SAAS,KAAK;MAChB,CAAC;MACD,SAAS,eAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;MAC9D,MAAM;MACN,uBAAuB;MACvB,2BAA2B;QACzB;MACF;MACA,aAAa,QAAQ;MACrB,OAAO,KAAK,OAAO;IACrB,CAAC;AAED,UAAgD,KAAA,MAAxC,EAAA,UAAU,UA7KtB,IA6KoD,IAAhB,cAAA,UAAgB,IAAhB,CAAxB,UAAA,CAAA;AACR,UAAM,SAAS,SAAS,QAAQ,CAAC;AAEjC,QAAI,UAAU,MAAM;AAClB,YAAM,IAAI,MAAM,uBAAuB;IACzC;AAEA,WAAO;MACL,OAAM,KAAA,OAAO,QAAQ,YAAf,OAAA,KAA0B;MAChC,YAAW,KAAA,OAAO,QAAQ,eAAf,OAAA,SAAA,GAA2B,IAAI,CAAC,aAAU;AAtL3D,YAAAC;AAsL+D,eAAA;UACvD,cAAc;UACd,aAAYA,MAAA,SAAS,OAAT,OAAAA,MAAe,WAAW;UACtC,UAAU,SAAS,SAAS;UAC5B,MAAM,SAAS,SAAS;QAC1B;MAAA,CAAA;MACA,cAAc,0BAA0B,OAAO,aAAa;MAC5D,OAAO;QACL,cAAc,SAAS,MAAM;QAC7B,kBAAkB,SAAS,MAAM;MACnC;MACA,SAAS,EAAE,WAAW,YAAY;MAClC,aAAa,EAAE,SAAS,gBAAgB;MACxC,UAAU,CAAC;MACX,UAAU,gCAAgC,OAAO,QAAQ;IAC3D;EACF;EAEA,MAAM,SACJ,SAC2D;AAC3D,UAAM,OAAO,KAAK,QAAQ,OAAO;AAEjC,UAAM,EAAE,iBAAiB,OAAO,SAAS,IAAI,MAAM,cAAc;MAC/D,KAAK,KAAK,OAAO,IAAI;QACnB,MAAM;QACN,SAAS,KAAK;MAChB,CAAC;MACD,SAAS,eAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;MAC9D,MAAM,cAAA,eAAA,CAAA,GACD,IAAA,GADC;QAEJ,QAAQ;;QAGR,gBACE,KAAK,OAAO,kBAAkB,WAC1B,EAAE,eAAe,KAAK,IACtB;MACR,CAAA;MACA,uBAAuB;MACvB,2BAA2B;QACzB;MACF;MACA,aAAa,QAAQ;MACrB,OAAO,KAAK,OAAO;IACrB,CAAC;AAED,UAAgD,KAAA,MAAxC,EAAA,UAAU,UArOtB,IAqOoD,IAAhB,cAAA,UAAgB,IAAhB,CAAxB,UAAA,CAAA;AAER,UAAM,YAOD,CAAC;AAEN,QAAI,eAA4C;AAChD,QAAI,QAA4D;MAC9D,cAAc,OAAO;MACrB,kBAAkB,OAAO;IAC3B;AACA,QAAI;AAEJ,WAAO;MACL,QAAQ,SAAS;QACf,IAAI,gBAGF;UACA,UAAU,OAAO,YAAY;AA7PvC,gBAAAA,KAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA;AA+PY,gBAAI,CAAC,MAAM,SAAS;AAClB,6BAAe;AACf,yBAAW,QAAQ,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,CAAC;AACxD;YACF;AAEA,kBAAM,QAAQ,MAAM;AAGpB,gBAAI,WAAW,OAAO;AACpB,6BAAe;AACf,yBAAW,QAAQ,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,CAAC;AACxD;YACF;AAEA,gBAAI,MAAM,SAAS,MAAM;AACvB,sBAAQ;gBACN,cAAc,MAAM,MAAM;gBAC1B,kBAAkB,MAAM,MAAM;cAChC;YACF;AAEA,kBAAM,SAAS,MAAM,QAAQ,CAAC;AAE9B,iBAAI,UAAA,OAAA,SAAA,OAAQ,kBAAiB,MAAM;AACjC,6BAAe,0BAA0B,OAAO,aAAa;YAC/D;AAEA,iBAAI,UAAA,OAAA,SAAA,OAAQ,UAAS,MAAM;AACzB;YACF;AAEA,kBAAM,QAAQ,OAAO;AAErB,gBAAI,MAAM,WAAW,MAAM;AACzB,yBAAW,QAAQ;gBACjB,MAAM;gBACN,WAAW,MAAM;cACnB,CAAC;YACH;AAEA,kBAAM,iBAAiB;cACrB,UAAA,OAAA,SAAA,OAAQ;YACV;AACA,gBAAI,kBAAA,OAAA,SAAA,eAAgB,QAAQ;AAC1B,kBAAI,aAAa,OAAW,YAAW,CAAC;AACxC,uBAAS,KAAK,GAAG,cAAc;YACjC;AAEA,gBAAI,MAAM,cAAc,MAAM;AAC5B,yBAAW,iBAAiB,MAAM,YAAY;AAC5C,sBAAM,QAAQ,cAAc;AAG5B,oBAAI,UAAU,KAAK,KAAK,MAAM;AAC5B,sBAAI,cAAc,SAAS,YAAY;AACrC,0BAAM,IAAI,yBAAyB;sBACjC,MAAM;sBACN,SAAS;oBACX,CAAC;kBACH;AAEA,sBAAI,cAAc,MAAM,MAAM;AAC5B,0BAAM,IAAI,yBAAyB;sBACjC,MAAM;sBACN,SAAS;oBACX,CAAC;kBACH;AAEA,wBAAIA,MAAA,cAAc,aAAd,OAAA,SAAAA,IAAwB,SAAQ,MAAM;AACxC,0BAAM,IAAI,yBAAyB;sBACjC,MAAM;sBACN,SAAS;oBACX,CAAC;kBACH;AAEA,4BAAU,KAAK,IAAI;oBACjB,IAAI,cAAc;oBAClB,MAAM;oBACN,UAAU;sBACR,MAAM,cAAc,SAAS;sBAC7B,YAAW,KAAA,cAAc,SAAS,cAAvB,OAAA,KAAoC;oBACjD;kBACF;AAEA,wBAAMG,YAAW,UAAU,KAAK;AAEhC,sBAAIA,aAAY,MAAM;AACpB,0BAAM,IAAI,MAAM,sBAAsB;kBACxC;AAGA,wBACE,KAAAA,UAAS,aAAT,OAAA,SAAA,GAAmB,SAAQ,UAC3B,KAAAA,UAAS,aAAT,OAAA,SAAA,GAAmB,cAAa,QAChC,eAAeA,UAAS,SAAS,SAAS,GAC1C;AAEA,+BAAW,QAAQ;sBACjB,MAAM;sBACN,cAAc;sBACd,YAAYA,UAAS;sBACrB,UAAUA,UAAS,SAAS;sBAC5B,eAAeA,UAAS,SAAS;oBACnC,CAAC;AAGD,+BAAW,QAAQ;sBACjB,MAAM;sBACN,cAAc;sBACd,aAAY,KAAAA,UAAS,OAAT,OAAA,KAAe,WAAW;sBACtC,UAAUA,UAAS,SAAS;sBAC5B,MAAMA,UAAS,SAAS;oBAC1B,CAAC;kBACH;AAEA;gBACF;AAGA,sBAAM,WAAW,UAAU,KAAK;AAEhC,oBAAI,YAAY,MAAM;AACpB,wBAAM,IAAI,MAAM,sBAAsB;gBACxC;AAEA,sBAAI,KAAA,cAAc,aAAd,OAAA,SAAA,GAAwB,cAAa,MAAM;AAC7C,2BAAS,SAAU,cACjB,MAAA,KAAA,cAAc,aAAd,OAAA,SAAA,GAAwB,cAAxB,OAAA,KAAqC;gBACzC;AAGA,2BAAW,QAAQ;kBACjB,MAAM;kBACN,cAAc;kBACd,YAAY,SAAS;kBACrB,UAAU,SAAS,SAAS;kBAC5B,gBAAe,KAAA,cAAc,SAAS,cAAvB,OAAA,KAAoC;gBACrD,CAAC;AAGD,sBACE,KAAA,SAAS,aAAT,OAAA,SAAA,GAAmB,SAAQ,UAC3B,KAAA,SAAS,aAAT,OAAA,SAAA,GAAmB,cAAa,QAChC,eAAe,SAAS,SAAS,SAAS,GAC1C;AACA,6BAAW,QAAQ;oBACjB,MAAM;oBACN,cAAc;oBACd,aAAY,KAAA,SAAS,OAAT,OAAA,KAAe,WAAW;oBACtC,UAAU,SAAS,SAAS;oBAC5B,MAAM,SAAS,SAAS;kBAC1B,CAAC;gBACH;cACF;YACF;UACF;UAEA,MAAM,YAAY;AAChB,uBAAW,QAAQ;cACjB,MAAM;cACN;cACA;cACA;YACF,CAAC;UACH;QACF,CAAC;MACH;MACA,SAAS,EAAE,WAAW,YAAY;MAClC,aAAa,EAAE,SAAS,gBAAgB;MACxC,UAAU,CAAC;IACb;EACF;AACF;AAIA,IAAM,2BAA2BC,iBAAE,OAAO;EACxC,SAASA,iBAAE;IACTA,iBAAE,OAAO;MACP,SAASA,iBAAE,OAAO;QAChB,MAAMA,iBAAE,QAAQ,WAAW;QAC3B,SAASA,iBAAE,OAAO,EAAE,SAAS,EAAE,SAAS;QACxC,YAAYA,iBACT;UACCA,iBAAE,OAAO;YACP,IAAIA,iBAAE,OAAO,EAAE,SAAS,EAAE,SAAS;YACnC,MAAMA,iBAAE,QAAQ,UAAU;YAC1B,UAAUA,iBAAE,OAAO;cACjB,MAAMA,iBAAE,OAAO;cACf,WAAWA,iBAAE,OAAO;YACtB,CAAC;UACH,CAAC;QACH,EACC,SAAS;MACd,CAAC;MACD,OAAOA,iBAAE,OAAO;MAChB,UAAUA,iBACP,OAAO;QACN,SAASA,iBACN;UACCA,iBAAE,OAAO;YACP,OAAOA,iBAAE,OAAO;YAChB,SAASA,iBAAE,OAAO;YAClB,cAAcA,iBAAE;cACdA,iBAAE,OAAO;gBACP,OAAOA,iBAAE,OAAO;gBAChB,SAASA,iBAAE,OAAO;cACpB,CAAC;YACH;UACF,CAAC;QACH,EACC,SAAS;MACd,CAAC,EACA,SAAS,EACT,SAAS;MACZ,eAAeA,iBAAE,OAAO,EAAE,SAAS,EAAE,SAAS;IAChD,CAAC;EACH;EACA,OAAOA,iBAAE,OAAO;IACd,eAAeA,iBAAE,OAAO;IACxB,mBAAmBA,iBAAE,OAAO;EAC9B,CAAC;AACH,CAAC;AAID,IAAM,4BAA4BA,iBAAE,MAAM;EACxCA,iBAAE,OAAO;IACP,SAASA,iBAAE;MACTA,iBAAE,OAAO;QACP,OAAOA,iBACJ,OAAO;UACN,MAAMA,iBAAE,KAAK,CAAC,WAAW,CAAC,EAAE,SAAS;UACrC,SAASA,iBAAE,OAAO,EAAE,QAAQ;UAC5B,YAAYA,iBACT;YACCA,iBAAE,OAAO;cACP,OAAOA,iBAAE,OAAO;cAChB,IAAIA,iBAAE,OAAO,EAAE,QAAQ;cACvB,MAAMA,iBAAE,QAAQ,UAAU,EAAE,SAAS;cACrC,UAAUA,iBAAE,OAAO;gBACjB,MAAMA,iBAAE,OAAO,EAAE,QAAQ;gBACzB,WAAWA,iBAAE,OAAO,EAAE,QAAQ;cAChC,CAAC;YACH,CAAC;UACH,EACC,QAAQ;QACb,CAAC,EACA,QAAQ;QACX,UAAUA,iBACP,OAAO;UACN,SAASA,iBACN;YACCA,iBAAE,OAAO;cACP,OAAOA,iBAAE,OAAO;cAChB,SAASA,iBAAE,OAAO;cAClB,cAAcA,iBAAE;gBACdA,iBAAE,OAAO;kBACP,OAAOA,iBAAE,OAAO;kBAChB,SAASA,iBAAE,OAAO;gBACpB,CAAC;cACH;YACF,CAAC;UACH,EACC,SAAS;QACd,CAAC,EACA,QAAQ;QACX,eAAeA,iBAAE,OAAO,EAAE,SAAS,EAAE,SAAS;QAC9C,OAAOA,iBAAE,OAAO;MAClB,CAAC;IACH;IACA,OAAOA,iBACJ,OAAO;MACN,eAAeA,iBAAE,OAAO;MACxB,mBAAmBA,iBAAE,OAAO;IAC9B,CAAC,EACA,QAAQ;EACb,CAAC;EACD;AACF,CAAC;AAED,SAAS,0BACP,MAGA;AA7hBF,MAAA;AA+hBE,QAAM,UAAQ,KAAA,KAAK,UAAL,OAAA,SAAA,GAAY,UAAS,KAAK,QAAQ;AAEhD,MAAI,SAAS,MAAM;AACjB,WAAO,EAAE,OAAO,QAAW,aAAa,OAAU;EACpD;AAEA,QAAM,cAAc,MAAM,IAAI,CAAC,UAAU;IACvC,MAAM;IACN,UAAU;MACR,MAAM,KAAK;MACX,aAAa,KAAK;MAClB,YAAY,KAAK;IACnB;EACF,EAAE;AAEF,QAAM,aAAa,KAAK;AAExB,MAAI,cAAc,MAAM;AACtB,WAAO,EAAE,OAAO,aAAa,aAAa,OAAU;EACtD;AAEA,QAAM,OAAO,WAAW;AAExB,UAAQ,MAAM;IACZ,KAAK;IACL,KAAK;IACL,KAAK;AACH,aAAO,EAAE,OAAO,aAAa,aAAa,KAAK;IACjD,KAAK;AACH,aAAO;QACL,OAAO;QACP,aAAa;UACX,MAAM;UACN,UAAU;YACR,MAAM,WAAW;UACnB;QACF;MACF;IACF,SAAS;AACP,YAAM,mBAA0B;AAChC,YAAM,IAAI,MAAM,iCAAiC,gBAAgB,EAAE;IACrE;EACF;AACF;AMpkBO,SAAS,oCAAoC;EAClD,QAAAL;EACA;EACA,OAAO;EACP,YAAY;AACd,GAQE;AAEA,MACE,gBAAgB,YAChBA,QAAO,WAAW,KAClBA,QAAO,CAAC,KACRA,QAAO,CAAC,EAAE,SAAS,UACnBA,QAAO,CAAC,EAAE,QAAQ,WAAW,KAC7BA,QAAO,CAAC,EAAE,QAAQ,CAAC,KACnBA,QAAO,CAAC,EAAE,QAAQ,CAAC,EAAE,SAAS,QAC9B;AACA,WAAO,EAAE,QAAQA,QAAO,CAAC,EAAE,QAAQ,CAAC,EAAE,KAAK;EAC7C;AAGA,MAAI,OAAO;AAGX,MAAIA,QAAO,CAAC,KAAKA,QAAO,CAAC,EAAE,SAAS,UAAU;AAC5C,YAAQ,GAAGA,QAAO,CAAC,EAAE,OAAO;;;AAC5B,IAAAA,UAASA,QAAO,MAAM,CAAC;EACzB;AAEA,aAAW,EAAE,MAAM,QAAQ,KAAKA,SAAQ;AACtC,YAAQ,MAAM;MACZ,KAAK,UAAU;AACb,cAAM,IAAI,mBAAmB;UAC3B,SAAS;UACT,QAAAA;QACF,CAAC;MACH;MAEA,KAAK,QAAQ;AACX,cAAM,cAAc,QACjB,IAAI,CAAC,SAAS;AACb,kBAAQ,KAAK,MAAM;YACjB,KAAK,QAAQ;AACX,qBAAO,KAAK;YACd;YACA,KAAK,SAAS;AACZ,oBAAM,IAAIM,8BAA8B;gBACtC,eAAe;cACjB,CAAC;YACH;UACF;QACF,CAAC,EACA,KAAK,EAAE;AAEV,gBAAQ,GAAG,IAAI;EAAM,WAAW;;;AAChC;MACF;MAEA,KAAK,aAAa;AAChB,cAAM,mBAAmB,QACtB,IAAI,CAAC,SAAS;AACb,kBAAQ,KAAK,MAAM;YACjB,KAAK,QAAQ;AACX,qBAAO,KAAK;YACd;YACA,KAAK,aAAa;AAChB,oBAAM,IAAIA,8BAA8B;gBACtC,eAAe;cACjB,CAAC;YACH;UACF;QACF,CAAC,EACA,KAAK,EAAE;AAEV,gBAAQ,GAAG,SAAS;EAAM,gBAAgB;;;AAC1C;MACF;MAEA,KAAK,QAAQ;AACX,cAAM,IAAIA,8BAA8B;UACtC,eAAe;QACjB,CAAC;MACH;MAEA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;MACzD;IACF;EACF;AAGA,UAAQ,GAAG,SAAS;;AAEpB,SAAO;IACL,QAAQ;IACR,eAAe,CAAC;EAAK,IAAI,GAAG;EAC9B;AACF;ACzGO,SAAS,gCACd,UACA;AACA,SAAO,YAAA,OAAA,SAAA,SAAU,OAAO,IAAI,CAAC,OAAO,UAAO;AAT7C,QAAA,IAAA;AASiD,WAAA;MAC7C;MACA,UAAS,KAAA,SAAS,eAAe,KAAK,MAA7B,OAAA,KAAkC;MAC3C,aAAa,SAAS,eAClB,OAAO,SAAQ,KAAA,SAAS,aAAa,KAAK,MAA3B,OAAA,KAAgC,CAAC,CAAC,EAAE;QACjD,CAAC,CAACJ,QAAO,OAAO,OAAO;UACrB,OAAAA;UACA;QACF;MACF,IACA,CAAC;IACP;EAAA,CAAA;AACF;AFeO,IAAM,oCAAN,MAAmE;EASxE,YACE,SACA,UACA,QACA;AAZF,SAAS,uBAAuB;AAChC,SAAS,8BAA8B;AAYrC,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,SAAS;EAChB;EAEA,IAAI,WAAmB;AACrB,WAAO,KAAK,OAAO;EACrB;EAEQ,QAAQ;IACd;IACA;IACA,QAAAF;IACA;IACA;IACA;IACA;IACA;IACA;EACF,GAAiD;AArEnD,QAAA;AAsEI,UAAM,OAAO,KAAK;AAElB,UAAM,EAAE,QAAQ,kBAAkB,cAAc,IAC9C,oCAAoC,EAAE,QAAAA,SAAQ,YAAY,CAAC;AAE7D,UAAM,WAAW,eAAA;;MAEf,OAAO,KAAK;;MAGZ,MAAM,KAAK,SAAS;MACpB,YAAY,KAAK,SAAS;MAC1B,UACE,OAAO,KAAK,SAAS,aAAa,WAC9B,KAAK,SAAS,WACd,OAAO,KAAK,SAAS,aAAa,YAClC,KAAK,SAAS,WACZ,IACA,SACF;MACN,QAAQ,KAAK,SAAS;MACtB,MAAM,KAAK,SAAS;;MAGpB,YAAY;MACZ;MACA,OAAO;MACP,mBAAmB;MACnB,kBAAkB;MAClB;;MAGA,QAAQ;;MAGR,MAAM;IAAA,GAGH,KAAK,OAAO,SAAA;AAGjB,YAAQ,MAAM;MACZ,KAAK,WAAW;AACd,aAAI,KAAA,KAAK,UAAL,OAAA,SAAA,GAAY,QAAQ;AACtB,gBAAM,IAAIM,8BAA8B;YACtC,eAAe;UACjB,CAAC;QACH;AAEA,YAAI,KAAK,YAAY;AACnB,gBAAM,IAAIA,8BAA8B;YACtC,eAAe;UACjB,CAAC;QACH;AAEA,eAAO;MACT;MAEA,KAAK,eAAe;AAClB,cAAM,IAAIA,8BAA8B;UACtC,eAAe;QACjB,CAAC;MACH;MAEA,KAAK,eAAe;AAClB,cAAM,IAAIA,8BAA8B;UACtC,eAAe;QACjB,CAAC;MACH;MAEA,KAAK,kBAAkB;AACrB,cAAM,IAAIA,8BAA8B;UACtC,eAAe;QACjB,CAAC;MACH;MAEA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;MACzD;IACF;EACF;EAEA,MAAM,WACJ,SAC6D;AAC7D,UAAM,OAAO,KAAK,QAAQ,OAAO;AAEjC,UAAM,EAAE,iBAAiB,OAAO,SAAS,IAAI,MAAMC,cAAc;MAC/D,KAAK,KAAK,OAAO,IAAI;QACnB,MAAM;QACN,SAAS,KAAK;MAChB,CAAC;MACD,SAASC,eAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;MAC9D,MAAM;MACN,uBAAuB;MACvB,2BAA2BC;QACzB;MACF;MACA,aAAa,QAAQ;MACrB,OAAO,KAAK,OAAO;IACrB,CAAC;AAED,UAA8C,KAAA,MAAtC,EAAA,QAAQ,UA7KpB,IA6KkD,IAAhB,cAAA,UAAgB,IAAhB,CAAtB,QAAA,CAAA;AACR,UAAM,SAAS,SAAS,QAAQ,CAAC;AAEjC,QAAI,CAAC,QAAQ;AACX,YAAM,IAAI,MAAM,6CAA6C;IAC/D;AAEA,WAAO;MACL,MAAM,OAAO;MACb,OAAO;QACL,cAAc,SAAS,MAAM;QAC7B,kBAAkB,SAAS,MAAM;MACnC;MACA,cAAc,0BAA0B,OAAO,aAAa;MAC5D,UAAU,gCAAgC,OAAO,QAAQ;MACzD,SAAS,EAAE,WAAW,YAAY;MAClC,aAAa,EAAE,SAAS,gBAAgB;MACxC,UAAU,CAAC;IACb;EACF;EAEA,MAAM,SACJ,SAC2D;AAC3D,UAAM,OAAO,KAAK,QAAQ,OAAO;AAEjC,UAAM,EAAE,iBAAiB,OAAO,SAAS,IAAI,MAAMF,cAAc;MAC/D,KAAK,KAAK,OAAO,IAAI;QACnB,MAAM;QACN,SAAS,KAAK;MAChB,CAAC;MACD,SAASC,eAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;MAC9D,MAAM,cAAA,eAAA,CAAA,GACD,KAAK,QAAQ,OAAO,CAAA,GADnB;QAEJ,QAAQ;;QAGR,gBACE,KAAK,OAAO,kBAAkB,WAC1B,EAAE,eAAe,KAAK,IACtB;MACR,CAAA;MACA,uBAAuB;MACvB,2BAA2BE;QACzB;MACF;MACA,aAAa,QAAQ;MACrB,OAAO,KAAK,OAAO;IACrB,CAAC;AAED,UAA8C,KAAA,MAAtC,EAAA,QAAQ,UA/NpB,IA+NkD,IAAhB,cAAA,UAAgB,IAAhB,CAAtB,QAAA,CAAA;AAER,QAAI,eAA4C;AAChD,QAAI,QAA4D;MAC9D,cAAc,OAAO;MACrB,kBAAkB,OAAO;IAC3B;AACA,QAAI;AAEJ,WAAO;MACL,QAAQ,SAAS;QACf,IAAI,gBAGF;UACA,UAAU,OAAO,YAAY;AAE3B,gBAAI,CAAC,MAAM,SAAS;AAClB,6BAAe;AACf,yBAAW,QAAQ,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,CAAC;AACxD;YACF;AAEA,kBAAM,QAAQ,MAAM;AAGpB,gBAAI,WAAW,OAAO;AACpB,6BAAe;AACf,yBAAW,QAAQ,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,CAAC;AACxD;YACF;AAEA,gBAAI,MAAM,SAAS,MAAM;AACvB,sBAAQ;gBACN,cAAc,MAAM,MAAM;gBAC1B,kBAAkB,MAAM,MAAM;cAChC;YACF;AAEA,kBAAM,SAAS,MAAM,QAAQ,CAAC;AAE9B,iBAAI,UAAA,OAAA,SAAA,OAAQ,kBAAiB,MAAM;AACjC,6BAAe,0BAA0B,OAAO,aAAa;YAC/D;AAEA,iBAAI,UAAA,OAAA,SAAA,OAAQ,SAAQ,MAAM;AACxB,yBAAW,QAAQ;gBACjB,MAAM;gBACN,WAAW,OAAO;cACpB,CAAC;YACH;AAEA,kBAAM,iBAAiB;cACrB,UAAA,OAAA,SAAA,OAAQ;YACV;AACA,gBAAI,kBAAA,OAAA,SAAA,eAAgB,QAAQ;AAC1B,kBAAI,aAAa,OAAW,YAAW,CAAC;AACxC,uBAAS,KAAK,GAAG,cAAc;YACjC;UACF;UAEA,MAAM,YAAY;AAChB,uBAAW,QAAQ;cACjB,MAAM;cACN;cACA;cACA;YACF,CAAC;UACH;QACF,CAAC;MACH;MACA,SAAS,EAAE,WAAW,YAAY;MAClC,aAAa,EAAE,SAAS,gBAAgB;MACxC,UAAU,CAAC;IACb;EACF;AACF;AAIA,IAAM,iCAAiCL,iBAAE,OAAO;EAC9C,SAASA,iBAAE;IACTA,iBAAE,OAAO;MACP,MAAMA,iBAAE,OAAO;MACf,eAAeA,iBAAE,OAAO;MACxB,UAAUA,iBACP,OAAO;QACN,QAAQA,iBAAE,MAAMA,iBAAE,OAAO,CAAC;QAC1B,gBAAgBA,iBAAE,MAAMA,iBAAE,OAAO,CAAC;QAClC,cAAcA,iBAAE,MAAMA,iBAAE,OAAOA,iBAAE,OAAO,GAAGA,iBAAE,OAAO,CAAC,CAAC,EAAE,SAAS;MACnE,CAAC,EACA,SAAS,EACT,SAAS;IACd,CAAC;EACH;EACA,OAAOA,iBAAE,OAAO;IACd,eAAeA,iBAAE,OAAO;IACxB,mBAAmBA,iBAAE,OAAO;EAC9B,CAAC;AACH,CAAC;AAID,IAAM,kCAAkCA,iBAAE,MAAM;EAC9CA,iBAAE,OAAO;IACP,SAASA,iBAAE;MACTA,iBAAE,OAAO;QACP,MAAMA,iBAAE,OAAO;QACf,eAAeA,iBAAE,OAAO,EAAE,QAAQ;QAClC,OAAOA,iBAAE,OAAO;QAChB,UAAUA,iBACP,OAAO;UACN,QAAQA,iBAAE,MAAMA,iBAAE,OAAO,CAAC;UAC1B,gBAAgBA,iBAAE,MAAMA,iBAAE,OAAO,CAAC;UAClC,cAAcA,iBAAE,MAAMA,iBAAE,OAAOA,iBAAE,OAAO,GAAGA,iBAAE,OAAO,CAAC,CAAC,EAAE,SAAS;QACnE,CAAC,EACA,SAAS,EACT,SAAS;MACd,CAAC;IACH;IACA,OAAOA,iBACJ,OAAO;MACN,eAAeA,iBAAE,OAAO;MACxB,mBAAmBA,iBAAE,OAAO;IAC9B,CAAC,EACA,SAAS,EACT,SAAS;EACd,CAAC;EACD;AACF,CAAC;ANhVM,IAAM,aAAN,MAAiB;;;;EAqBtB,YAAY,UAAsC,CAAC,GAAG;AArCxD,QAAA,IAAA;AAsCI,SAAK,WACH,KAAA,sBAAqB,KAAA,QAAQ,YAAR,OAAA,KAAmB,QAAQ,OAAO,MAAvD,OAAA,KACA;AACF,SAAK,SAAS,QAAQ;AACtB,SAAK,UAAU,QAAQ;EACzB;EAEA,IAAY,aAAa;AACvB,WAAO;MACL,SAAS,KAAK;MACd,SAAS,MAAO,eAAA;QACd,eAAe,UAAU,WAAW;UAClC,QAAQ,KAAK;UACb,yBAAyB;UACzB,aAAa;QACf,CAAC,CAAC;MAAA,GACC,KAAK,OAAA;IAEZ;EACF;EAEA,KAAK,SAAgC,WAAmC,CAAC,GAAG;AAC1E,WAAO,IAAI,4BAA4B,SAAS,UAAU,cAAA,eAAA;MACxD,UAAU;IAAA,GACP,KAAK,UAAA,GAFgD;MAGxD,eAAe;MACf,KAAK,CAAC,EAAE,KAAK,MAAM,GAAG,KAAK,OAAO,GAAG,IAAI;IAC3C,CAAA,CAAC;EACH;EAEA,WACE,SACA,WAAyC,CAAC,GAC1C;AACA,WAAO,IAAI,kCAAkC,SAAS,UAAU,cAAA,eAAA;MAC9D,UAAU;IAAA,GACP,KAAK,UAAA,GAFsD;MAG9D,eAAe;MACf,KAAK,CAAC,EAAE,KAAK,MAAM,GAAG,KAAK,OAAO,GAAG,IAAI;IAC3C,CAAA,CAAC;EACH;AACF;ASYO,SAAS,iBACd,UAAsC,CAAC,GACnB;AA7FtB,MAAA,IAAA,IAAA;AA8FE,QAAM,WACJ,KAAAM,sBAAqB,KAAA,QAAQ,YAAR,OAAA,KAAmB,QAAQ,OAAO,MAAvD,OAAA,KACA;AAGF,QAAM,iBAAgB,KAAA,QAAQ,kBAAR,OAAA,KAAyB;AAE/C,QAAM,aAAa,MAAO,eAAA;IACxB,eAAe,UAAUC,WAAW;MAClC,QAAQ,QAAQ;MAChB,yBAAyB;MACzB,aAAa;IACf,CAAC,CAAC;EAAA,GACC,QAAQ,OAAA;AAGb,QAAM,kBAAkB,CACtB,SACA,WAAmC,CAAC,MAEpC,IAAI,4BAA4B,SAAS,UAAU;IACjD,UAAU;IACV,KAAK,CAAC,EAAE,KAAK,MAAM,GAAG,OAAO,GAAG,IAAI;IACpC,SAAS;IACT;IACA,OAAO,QAAQ;IACf,WAAW,QAAQ;EACrB,CAAC;AAEH,QAAM,wBAAwB,CAC5B,SACA,WAAyC,CAAC,MAE1C,IAAI,kCAAkC,SAAS,UAAU;IACvD,UAAU;IACV,KAAK,CAAC,EAAE,KAAK,MAAM,GAAG,OAAO,GAAG,IAAI;IACpC,SAAS;IACT;IACA,OAAO,QAAQ;IACf,WAAW,QAAQ;EACrB,CAAC;AAEH,QAAM,sBAAsB,CAC1B,SACA,aACG;AACH,QAAI,YAAY;AACd,YAAM,IAAI;QACR;MACF;IACF;AAEA,QAAI,YAAY,iCAAiC;AAC/C,aAAO;QACL;QACA;MACF;IACF;AAEA,WAAO,gBAAgB,SAAS,QAAkC;EACpE;AAEA,QAAM,WAAW,SACf,SACA,UACA;AACA,WAAO,oBAAoB,SAAS,QAAQ;EAC9C;AAEA,WAAS,gBAAgB;AACzB,WAAS,OAAO;AAChB,WAAS,aAAa;AAEtB,SAAO;AACT;AAKO,IAAM,aAAa,iBAAiB;EACzC,eAAe;;AACjB,CAAC;", "names": ["prompt", "SecureJSON", "TypeValidationError", "fetch", "APICallError", "APICallError", "prompt", "_a", "token", "logprob", "toolCall", "z", "UnsupportedFunctionalityError", "postJsonToApi", "combineHeaders", "createJsonResponseHandler", "createEventSourceResponseHandler", "withoutTrailingSlash", "loadApiKey"]}