{"version": 3, "sources": ["../../@marijn/find-cluster-break/src/index.js", "../../@codemirror/state/dist/index.js"], "sourcesContent": ["// These are filled with ranges (rangeFrom[i] up to but not including\n// rangeTo[i]) of code points that count as extending characters.\nlet rangeFrom = [], rangeTo = []\n\n;(() => {\n  // Compressed representation of the Grapheme_Cluster_Break=Extend\n  // information from\n  // http://www.unicode.org/Public/16.0.0/ucd/auxiliary/GraphemeBreakProperty.txt.\n  // Each pair of elements represents a range, as an offet from the\n  // previous range and a length. Numbers are in base-36, with the empty\n  // string being a shorthand for 1.\n  let numbers = \"lc,34,7n,7,7b,19,,,,2,,2,,,20,b,1c,l,g,,2t,7,2,6,2,2,,4,z,,u,r,2j,b,1m,9,9,,o,4,,9,,3,,5,17,3,3b,f,,w,1j,,,,4,8,4,,3,7,a,2,t,,1m,,,,2,4,8,,9,,a,2,q,,2,2,1l,,4,2,4,2,2,3,3,,u,2,3,,b,2,1l,,4,5,,2,4,,k,2,m,6,,,1m,,,2,,4,8,,7,3,a,2,u,,1n,,,,c,,9,,14,,3,,1l,3,5,3,,4,7,2,b,2,t,,1m,,2,,2,,3,,5,2,7,2,b,2,s,2,1l,2,,,2,4,8,,9,,a,2,t,,20,,4,,2,3,,,8,,29,,2,7,c,8,2q,,2,9,b,6,22,2,r,,,,,,1j,e,,5,,2,5,b,,10,9,,2u,4,,6,,2,2,2,p,2,4,3,g,4,d,,2,2,6,,f,,jj,3,qa,3,t,3,t,2,u,2,1s,2,,7,8,,2,b,9,,19,3,3b,2,y,,3a,3,4,2,9,,6,3,63,2,2,,1m,,,7,,,,,2,8,6,a,2,,1c,h,1r,4,1c,7,,,5,,14,9,c,2,w,4,2,2,,3,1k,,,2,3,,,3,1m,8,2,2,48,3,,d,,7,4,,6,,3,2,5i,1m,,5,ek,,5f,x,2da,3,3x,,2o,w,fe,6,2x,2,n9w,4,,a,w,2,28,2,7k,,3,,4,,p,2,5,,47,2,q,i,d,,12,8,p,b,1a,3,1c,,2,4,2,2,13,,1v,6,2,2,2,2,c,,8,,1b,,1f,,,3,2,2,5,2,,,16,2,8,,6m,,2,,4,,fn4,,kh,g,g,g,a6,2,gt,,6a,,45,5,1ae,3,,2,5,4,14,3,4,,4l,2,fx,4,ar,2,49,b,4w,,1i,f,1k,3,1d,4,2,2,1x,3,10,5,,8,1q,,c,2,1g,9,a,4,2,,2n,3,2,,,2,6,,4g,,3,8,l,2,1l,2,,,,,m,,e,7,3,5,5f,8,2,3,,,n,,29,,2,6,,,2,,,2,,2,6j,,2,4,6,2,,2,r,2,2d,8,2,,,2,2y,,,,2,6,,,2t,3,2,4,,5,77,9,,2,6t,,a,2,,,4,,40,4,2,2,4,,w,a,14,6,2,4,8,,9,6,2,3,1a,d,,2,ba,7,,6,,,2a,m,2,7,,2,,2,3e,6,3,,,2,,7,,,20,2,3,,,,9n,2,f0b,5,1n,7,t4,,1r,4,29,,f5k,2,43q,,,3,4,5,8,8,2,7,u,4,44,3,1iz,1j,4,1e,8,,e,,m,5,,f,11s,7,,h,2,7,,2,,5,79,7,c5,4,15s,7,31,7,240,5,gx7k,2o,3k,6o\".split(\",\").map(s => s ? parseInt(s, 36) : 1)\n  for (let i = 0, n = 0; i < numbers.length; i++)\n    (i % 2 ? rangeTo : rangeFrom).push(n = n + numbers[i])\n})()\n\nexport function isExtendingChar(code) {\n  if (code < 768) return false\n  for (let from = 0, to = rangeFrom.length;;) {\n    let mid = (from + to) >> 1\n    if (code < rangeFrom[mid]) to = mid\n    else if (code >= rangeTo[mid]) from = mid + 1\n    else return true\n    if (from == to) return false\n  }\n}\n\nfunction isRegionalIndicator(code) {\n  return code >= 0x1F1E6 && code <= 0x1F1FF\n}\n\nfunction check(code) {\n  for (let i = 0; i < rangeFrom.length; i++) {\n    if (rangeTo[i] > code) return rangeFrom[i] <= code\n  }\n  return false\n}\n\nconst ZWJ = 0x200d\n\nexport function findClusterBreak(str, pos, forward = true, includeExtending = true) {\n  return (forward ? nextClusterBreak : prevClusterBreak)(str, pos, includeExtending)\n}\n\nfunction nextClusterBreak(str, pos, includeExtending) {\n  if (pos == str.length) return pos\n  // If pos is in the middle of a surrogate pair, move to its start\n  if (pos && surrogateLow(str.charCodeAt(pos)) && surrogateHigh(str.charCodeAt(pos - 1))) pos--\n  let prev = codePointAt(str, pos)\n  pos += codePointSize(prev)\n  while (pos < str.length) {\n    let next = codePointAt(str, pos)\n    if (prev == ZWJ || next == ZWJ || includeExtending && isExtendingChar(next)) {\n      pos += codePointSize(next)\n      prev = next\n    } else if (isRegionalIndicator(next)) {\n      let countBefore = 0, i = pos - 2\n      while (i >= 0 && isRegionalIndicator(codePointAt(str, i))) { countBefore++; i -= 2 }\n      if (countBefore % 2 == 0) break\n      else pos += 2\n    } else {\n      break\n    }\n  }\n  return pos\n}\n\nfunction prevClusterBreak(str, pos, includeExtending) {\n  while (pos > 0) {\n    let found = nextClusterBreak(str, pos - 2, includeExtending)\n    if (found < pos) return found\n    pos--\n  }\n  return 0\n}\n\nfunction codePointAt(str, pos) {\n  let code0 = str.charCodeAt(pos)\n  if (!surrogateHigh(code0) || pos + 1 == str.length) return code0\n  let code1 = str.charCodeAt(pos + 1)\n  if (!surrogateLow(code1)) return code0\n  return ((code0 - 0xd800) << 10) + (code1 - 0xdc00) + 0x10000\n}\n\nfunction surrogateLow(ch) { return ch >= 0xDC00 && ch < 0xE000 }\nfunction surrogateHigh(ch) { return ch >= 0xD800 && ch < 0xDC00 }\nfunction codePointSize(code) { return code < 0x10000 ? 1 : 2 }\n", "import { find<PERSON>lusterBreak as findClusterBreak$1 } from '@marijn/find-cluster-break';\n\n/**\nThe data structure for documents. @nonabstract\n*/\nclass Text {\n    /**\n    Get the line description around the given position.\n    */\n    lineAt(pos) {\n        if (pos < 0 || pos > this.length)\n            throw new RangeError(`Invalid position ${pos} in document of length ${this.length}`);\n        return this.lineInner(pos, false, 1, 0);\n    }\n    /**\n    Get the description for the given (1-based) line number.\n    */\n    line(n) {\n        if (n < 1 || n > this.lines)\n            throw new RangeError(`Invalid line number ${n} in ${this.lines}-line document`);\n        return this.lineInner(n, true, 1, 0);\n    }\n    /**\n    Replace a range of the text with the given content.\n    */\n    replace(from, to, text) {\n        [from, to] = clip(this, from, to);\n        let parts = [];\n        this.decompose(0, from, parts, 2 /* Open.To */);\n        if (text.length)\n            text.decompose(0, text.length, parts, 1 /* Open.From */ | 2 /* Open.To */);\n        this.decompose(to, this.length, parts, 1 /* Open.From */);\n        return TextNode.from(parts, this.length - (to - from) + text.length);\n    }\n    /**\n    Append another document to this one.\n    */\n    append(other) {\n        return this.replace(this.length, this.length, other);\n    }\n    /**\n    Retrieve the text between the given points.\n    */\n    slice(from, to = this.length) {\n        [from, to] = clip(this, from, to);\n        let parts = [];\n        this.decompose(from, to, parts, 0);\n        return TextNode.from(parts, to - from);\n    }\n    /**\n    Test whether this text is equal to another instance.\n    */\n    eq(other) {\n        if (other == this)\n            return true;\n        if (other.length != this.length || other.lines != this.lines)\n            return false;\n        let start = this.scanIdentical(other, 1), end = this.length - this.scanIdentical(other, -1);\n        let a = new RawTextCursor(this), b = new RawTextCursor(other);\n        for (let skip = start, pos = start;;) {\n            a.next(skip);\n            b.next(skip);\n            skip = 0;\n            if (a.lineBreak != b.lineBreak || a.done != b.done || a.value != b.value)\n                return false;\n            pos += a.value.length;\n            if (a.done || pos >= end)\n                return true;\n        }\n    }\n    /**\n    Iterate over the text. When `dir` is `-1`, iteration happens\n    from end to start. This will return lines and the breaks between\n    them as separate strings.\n    */\n    iter(dir = 1) { return new RawTextCursor(this, dir); }\n    /**\n    Iterate over a range of the text. When `from` > `to`, the\n    iterator will run in reverse.\n    */\n    iterRange(from, to = this.length) { return new PartialTextCursor(this, from, to); }\n    /**\n    Return a cursor that iterates over the given range of lines,\n    _without_ returning the line breaks between, and yielding empty\n    strings for empty lines.\n    \n    When `from` and `to` are given, they should be 1-based line numbers.\n    */\n    iterLines(from, to) {\n        let inner;\n        if (from == null) {\n            inner = this.iter();\n        }\n        else {\n            if (to == null)\n                to = this.lines + 1;\n            let start = this.line(from).from;\n            inner = this.iterRange(start, Math.max(start, to == this.lines + 1 ? this.length : to <= 1 ? 0 : this.line(to - 1).to));\n        }\n        return new LineCursor(inner);\n    }\n    /**\n    Return the document as a string, using newline characters to\n    separate lines.\n    */\n    toString() { return this.sliceString(0); }\n    /**\n    Convert the document to an array of lines (which can be\n    deserialized again via [`Text.of`](https://codemirror.net/6/docs/ref/#state.Text^of)).\n    */\n    toJSON() {\n        let lines = [];\n        this.flatten(lines);\n        return lines;\n    }\n    /**\n    @internal\n    */\n    constructor() { }\n    /**\n    Create a `Text` instance for the given array of lines.\n    */\n    static of(text) {\n        if (text.length == 0)\n            throw new RangeError(\"A document must have at least one line\");\n        if (text.length == 1 && !text[0])\n            return Text.empty;\n        return text.length <= 32 /* Tree.Branch */ ? new TextLeaf(text) : TextNode.from(TextLeaf.split(text, []));\n    }\n}\n// Leaves store an array of line strings. There are always line breaks\n// between these strings. Leaves are limited in size and have to be\n// contained in TextNode instances for bigger documents.\nclass TextLeaf extends Text {\n    constructor(text, length = textLength(text)) {\n        super();\n        this.text = text;\n        this.length = length;\n    }\n    get lines() { return this.text.length; }\n    get children() { return null; }\n    lineInner(target, isLine, line, offset) {\n        for (let i = 0;; i++) {\n            let string = this.text[i], end = offset + string.length;\n            if ((isLine ? line : end) >= target)\n                return new Line(offset, end, line, string);\n            offset = end + 1;\n            line++;\n        }\n    }\n    decompose(from, to, target, open) {\n        let text = from <= 0 && to >= this.length ? this\n            : new TextLeaf(sliceText(this.text, from, to), Math.min(to, this.length) - Math.max(0, from));\n        if (open & 1 /* Open.From */) {\n            let prev = target.pop();\n            let joined = appendText(text.text, prev.text.slice(), 0, text.length);\n            if (joined.length <= 32 /* Tree.Branch */) {\n                target.push(new TextLeaf(joined, prev.length + text.length));\n            }\n            else {\n                let mid = joined.length >> 1;\n                target.push(new TextLeaf(joined.slice(0, mid)), new TextLeaf(joined.slice(mid)));\n            }\n        }\n        else {\n            target.push(text);\n        }\n    }\n    replace(from, to, text) {\n        if (!(text instanceof TextLeaf))\n            return super.replace(from, to, text);\n        [from, to] = clip(this, from, to);\n        let lines = appendText(this.text, appendText(text.text, sliceText(this.text, 0, from)), to);\n        let newLen = this.length + text.length - (to - from);\n        if (lines.length <= 32 /* Tree.Branch */)\n            return new TextLeaf(lines, newLen);\n        return TextNode.from(TextLeaf.split(lines, []), newLen);\n    }\n    sliceString(from, to = this.length, lineSep = \"\\n\") {\n        [from, to] = clip(this, from, to);\n        let result = \"\";\n        for (let pos = 0, i = 0; pos <= to && i < this.text.length; i++) {\n            let line = this.text[i], end = pos + line.length;\n            if (pos > from && i)\n                result += lineSep;\n            if (from < end && to > pos)\n                result += line.slice(Math.max(0, from - pos), to - pos);\n            pos = end + 1;\n        }\n        return result;\n    }\n    flatten(target) {\n        for (let line of this.text)\n            target.push(line);\n    }\n    scanIdentical() { return 0; }\n    static split(text, target) {\n        let part = [], len = -1;\n        for (let line of text) {\n            part.push(line);\n            len += line.length + 1;\n            if (part.length == 32 /* Tree.Branch */) {\n                target.push(new TextLeaf(part, len));\n                part = [];\n                len = -1;\n            }\n        }\n        if (len > -1)\n            target.push(new TextLeaf(part, len));\n        return target;\n    }\n}\n// Nodes provide the tree structure of the `Text` type. They store a\n// number of other nodes or leaves, taking care to balance themselves\n// on changes. There are implied line breaks _between_ the children of\n// a node (but not before the first or after the last child).\nclass TextNode extends Text {\n    constructor(children, length) {\n        super();\n        this.children = children;\n        this.length = length;\n        this.lines = 0;\n        for (let child of children)\n            this.lines += child.lines;\n    }\n    lineInner(target, isLine, line, offset) {\n        for (let i = 0;; i++) {\n            let child = this.children[i], end = offset + child.length, endLine = line + child.lines - 1;\n            if ((isLine ? endLine : end) >= target)\n                return child.lineInner(target, isLine, line, offset);\n            offset = end + 1;\n            line = endLine + 1;\n        }\n    }\n    decompose(from, to, target, open) {\n        for (let i = 0, pos = 0; pos <= to && i < this.children.length; i++) {\n            let child = this.children[i], end = pos + child.length;\n            if (from <= end && to >= pos) {\n                let childOpen = open & ((pos <= from ? 1 /* Open.From */ : 0) | (end >= to ? 2 /* Open.To */ : 0));\n                if (pos >= from && end <= to && !childOpen)\n                    target.push(child);\n                else\n                    child.decompose(from - pos, to - pos, target, childOpen);\n            }\n            pos = end + 1;\n        }\n    }\n    replace(from, to, text) {\n        [from, to] = clip(this, from, to);\n        if (text.lines < this.lines)\n            for (let i = 0, pos = 0; i < this.children.length; i++) {\n                let child = this.children[i], end = pos + child.length;\n                // Fast path: if the change only affects one child and the\n                // child's size remains in the acceptable range, only update\n                // that child\n                if (from >= pos && to <= end) {\n                    let updated = child.replace(from - pos, to - pos, text);\n                    let totalLines = this.lines - child.lines + updated.lines;\n                    if (updated.lines < (totalLines >> (5 /* Tree.BranchShift */ - 1)) &&\n                        updated.lines > (totalLines >> (5 /* Tree.BranchShift */ + 1))) {\n                        let copy = this.children.slice();\n                        copy[i] = updated;\n                        return new TextNode(copy, this.length - (to - from) + text.length);\n                    }\n                    return super.replace(pos, end, updated);\n                }\n                pos = end + 1;\n            }\n        return super.replace(from, to, text);\n    }\n    sliceString(from, to = this.length, lineSep = \"\\n\") {\n        [from, to] = clip(this, from, to);\n        let result = \"\";\n        for (let i = 0, pos = 0; i < this.children.length && pos <= to; i++) {\n            let child = this.children[i], end = pos + child.length;\n            if (pos > from && i)\n                result += lineSep;\n            if (from < end && to > pos)\n                result += child.sliceString(from - pos, to - pos, lineSep);\n            pos = end + 1;\n        }\n        return result;\n    }\n    flatten(target) {\n        for (let child of this.children)\n            child.flatten(target);\n    }\n    scanIdentical(other, dir) {\n        if (!(other instanceof TextNode))\n            return 0;\n        let length = 0;\n        let [iA, iB, eA, eB] = dir > 0 ? [0, 0, this.children.length, other.children.length]\n            : [this.children.length - 1, other.children.length - 1, -1, -1];\n        for (;; iA += dir, iB += dir) {\n            if (iA == eA || iB == eB)\n                return length;\n            let chA = this.children[iA], chB = other.children[iB];\n            if (chA != chB)\n                return length + chA.scanIdentical(chB, dir);\n            length += chA.length + 1;\n        }\n    }\n    static from(children, length = children.reduce((l, ch) => l + ch.length + 1, -1)) {\n        let lines = 0;\n        for (let ch of children)\n            lines += ch.lines;\n        if (lines < 32 /* Tree.Branch */) {\n            let flat = [];\n            for (let ch of children)\n                ch.flatten(flat);\n            return new TextLeaf(flat, length);\n        }\n        let chunk = Math.max(32 /* Tree.Branch */, lines >> 5 /* Tree.BranchShift */), maxChunk = chunk << 1, minChunk = chunk >> 1;\n        let chunked = [], currentLines = 0, currentLen = -1, currentChunk = [];\n        function add(child) {\n            let last;\n            if (child.lines > maxChunk && child instanceof TextNode) {\n                for (let node of child.children)\n                    add(node);\n            }\n            else if (child.lines > minChunk && (currentLines > minChunk || !currentLines)) {\n                flush();\n                chunked.push(child);\n            }\n            else if (child instanceof TextLeaf && currentLines &&\n                (last = currentChunk[currentChunk.length - 1]) instanceof TextLeaf &&\n                child.lines + last.lines <= 32 /* Tree.Branch */) {\n                currentLines += child.lines;\n                currentLen += child.length + 1;\n                currentChunk[currentChunk.length - 1] = new TextLeaf(last.text.concat(child.text), last.length + 1 + child.length);\n            }\n            else {\n                if (currentLines + child.lines > chunk)\n                    flush();\n                currentLines += child.lines;\n                currentLen += child.length + 1;\n                currentChunk.push(child);\n            }\n        }\n        function flush() {\n            if (currentLines == 0)\n                return;\n            chunked.push(currentChunk.length == 1 ? currentChunk[0] : TextNode.from(currentChunk, currentLen));\n            currentLen = -1;\n            currentLines = currentChunk.length = 0;\n        }\n        for (let child of children)\n            add(child);\n        flush();\n        return chunked.length == 1 ? chunked[0] : new TextNode(chunked, length);\n    }\n}\nText.empty = /*@__PURE__*/new TextLeaf([\"\"], 0);\nfunction textLength(text) {\n    let length = -1;\n    for (let line of text)\n        length += line.length + 1;\n    return length;\n}\nfunction appendText(text, target, from = 0, to = 1e9) {\n    for (let pos = 0, i = 0, first = true; i < text.length && pos <= to; i++) {\n        let line = text[i], end = pos + line.length;\n        if (end >= from) {\n            if (end > to)\n                line = line.slice(0, to - pos);\n            if (pos < from)\n                line = line.slice(from - pos);\n            if (first) {\n                target[target.length - 1] += line;\n                first = false;\n            }\n            else\n                target.push(line);\n        }\n        pos = end + 1;\n    }\n    return target;\n}\nfunction sliceText(text, from, to) {\n    return appendText(text, [\"\"], from, to);\n}\nclass RawTextCursor {\n    constructor(text, dir = 1) {\n        this.dir = dir;\n        this.done = false;\n        this.lineBreak = false;\n        this.value = \"\";\n        this.nodes = [text];\n        this.offsets = [dir > 0 ? 1 : (text instanceof TextLeaf ? text.text.length : text.children.length) << 1];\n    }\n    nextInner(skip, dir) {\n        this.done = this.lineBreak = false;\n        for (;;) {\n            let last = this.nodes.length - 1;\n            let top = this.nodes[last], offsetValue = this.offsets[last], offset = offsetValue >> 1;\n            let size = top instanceof TextLeaf ? top.text.length : top.children.length;\n            if (offset == (dir > 0 ? size : 0)) {\n                if (last == 0) {\n                    this.done = true;\n                    this.value = \"\";\n                    return this;\n                }\n                if (dir > 0)\n                    this.offsets[last - 1]++;\n                this.nodes.pop();\n                this.offsets.pop();\n            }\n            else if ((offsetValue & 1) == (dir > 0 ? 0 : 1)) {\n                this.offsets[last] += dir;\n                if (skip == 0) {\n                    this.lineBreak = true;\n                    this.value = \"\\n\";\n                    return this;\n                }\n                skip--;\n            }\n            else if (top instanceof TextLeaf) {\n                // Move to the next string\n                let next = top.text[offset + (dir < 0 ? -1 : 0)];\n                this.offsets[last] += dir;\n                if (next.length > Math.max(0, skip)) {\n                    this.value = skip == 0 ? next : dir > 0 ? next.slice(skip) : next.slice(0, next.length - skip);\n                    return this;\n                }\n                skip -= next.length;\n            }\n            else {\n                let next = top.children[offset + (dir < 0 ? -1 : 0)];\n                if (skip > next.length) {\n                    skip -= next.length;\n                    this.offsets[last] += dir;\n                }\n                else {\n                    if (dir < 0)\n                        this.offsets[last]--;\n                    this.nodes.push(next);\n                    this.offsets.push(dir > 0 ? 1 : (next instanceof TextLeaf ? next.text.length : next.children.length) << 1);\n                }\n            }\n        }\n    }\n    next(skip = 0) {\n        if (skip < 0) {\n            this.nextInner(-skip, (-this.dir));\n            skip = this.value.length;\n        }\n        return this.nextInner(skip, this.dir);\n    }\n}\nclass PartialTextCursor {\n    constructor(text, start, end) {\n        this.value = \"\";\n        this.done = false;\n        this.cursor = new RawTextCursor(text, start > end ? -1 : 1);\n        this.pos = start > end ? text.length : 0;\n        this.from = Math.min(start, end);\n        this.to = Math.max(start, end);\n    }\n    nextInner(skip, dir) {\n        if (dir < 0 ? this.pos <= this.from : this.pos >= this.to) {\n            this.value = \"\";\n            this.done = true;\n            return this;\n        }\n        skip += Math.max(0, dir < 0 ? this.pos - this.to : this.from - this.pos);\n        let limit = dir < 0 ? this.pos - this.from : this.to - this.pos;\n        if (skip > limit)\n            skip = limit;\n        limit -= skip;\n        let { value } = this.cursor.next(skip);\n        this.pos += (value.length + skip) * dir;\n        this.value = value.length <= limit ? value : dir < 0 ? value.slice(value.length - limit) : value.slice(0, limit);\n        this.done = !this.value;\n        return this;\n    }\n    next(skip = 0) {\n        if (skip < 0)\n            skip = Math.max(skip, this.from - this.pos);\n        else if (skip > 0)\n            skip = Math.min(skip, this.to - this.pos);\n        return this.nextInner(skip, this.cursor.dir);\n    }\n    get lineBreak() { return this.cursor.lineBreak && this.value != \"\"; }\n}\nclass LineCursor {\n    constructor(inner) {\n        this.inner = inner;\n        this.afterBreak = true;\n        this.value = \"\";\n        this.done = false;\n    }\n    next(skip = 0) {\n        let { done, lineBreak, value } = this.inner.next(skip);\n        if (done && this.afterBreak) {\n            this.value = \"\";\n            this.afterBreak = false;\n        }\n        else if (done) {\n            this.done = true;\n            this.value = \"\";\n        }\n        else if (lineBreak) {\n            if (this.afterBreak) {\n                this.value = \"\";\n            }\n            else {\n                this.afterBreak = true;\n                this.next();\n            }\n        }\n        else {\n            this.value = value;\n            this.afterBreak = false;\n        }\n        return this;\n    }\n    get lineBreak() { return false; }\n}\nif (typeof Symbol != \"undefined\") {\n    Text.prototype[Symbol.iterator] = function () { return this.iter(); };\n    RawTextCursor.prototype[Symbol.iterator] = PartialTextCursor.prototype[Symbol.iterator] =\n        LineCursor.prototype[Symbol.iterator] = function () { return this; };\n}\n/**\nThis type describes a line in the document. It is created\non-demand when lines are [queried](https://codemirror.net/6/docs/ref/#state.Text.lineAt).\n*/\nclass Line {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The position of the start of the line.\n    */\n    from, \n    /**\n    The position at the end of the line (_before_ the line break,\n    or at the end of document for the last line).\n    */\n    to, \n    /**\n    This line's line number (1-based).\n    */\n    number, \n    /**\n    The line's content.\n    */\n    text) {\n        this.from = from;\n        this.to = to;\n        this.number = number;\n        this.text = text;\n    }\n    /**\n    The length of the line (not including any line break after it).\n    */\n    get length() { return this.to - this.from; }\n}\nfunction clip(text, from, to) {\n    from = Math.max(0, Math.min(text.length, from));\n    return [from, Math.max(from, Math.min(text.length, to))];\n}\n\n/**\nReturns a next grapheme cluster break _after_ (not equal to)\n`pos`, if `forward` is true, or before otherwise. Returns `pos`\nitself if no further cluster break is available in the string.\nMoves across surrogate pairs, extending characters (when\n`includeExtending` is true), characters joined with zero-width\njoiners, and flag emoji.\n*/\nfunction findClusterBreak(str, pos, forward = true, includeExtending = true) {\n    return findClusterBreak$1(str, pos, forward, includeExtending);\n}\nfunction surrogateLow(ch) { return ch >= 0xDC00 && ch < 0xE000; }\nfunction surrogateHigh(ch) { return ch >= 0xD800 && ch < 0xDC00; }\n/**\nFind the code point at the given position in a string (like the\n[`codePointAt`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/codePointAt)\nstring method).\n*/\nfunction codePointAt(str, pos) {\n    let code0 = str.charCodeAt(pos);\n    if (!surrogateHigh(code0) || pos + 1 == str.length)\n        return code0;\n    let code1 = str.charCodeAt(pos + 1);\n    if (!surrogateLow(code1))\n        return code0;\n    return ((code0 - 0xd800) << 10) + (code1 - 0xdc00) + 0x10000;\n}\n/**\nGiven a Unicode codepoint, return the JavaScript string that\nrespresents it (like\n[`String.fromCodePoint`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/fromCodePoint)).\n*/\nfunction fromCodePoint(code) {\n    if (code <= 0xffff)\n        return String.fromCharCode(code);\n    code -= 0x10000;\n    return String.fromCharCode((code >> 10) + 0xd800, (code & 1023) + 0xdc00);\n}\n/**\nThe amount of positions a character takes up in a JavaScript string.\n*/\nfunction codePointSize(code) { return code < 0x10000 ? 1 : 2; }\n\nconst DefaultSplit = /\\r\\n?|\\n/;\n/**\nDistinguishes different ways in which positions can be mapped.\n*/\nvar MapMode = /*@__PURE__*/(function (MapMode) {\n    /**\n    Map a position to a valid new position, even when its context\n    was deleted.\n    */\n    MapMode[MapMode[\"Simple\"] = 0] = \"Simple\";\n    /**\n    Return null if deletion happens across the position.\n    */\n    MapMode[MapMode[\"TrackDel\"] = 1] = \"TrackDel\";\n    /**\n    Return null if the character _before_ the position is deleted.\n    */\n    MapMode[MapMode[\"TrackBefore\"] = 2] = \"TrackBefore\";\n    /**\n    Return null if the character _after_ the position is deleted.\n    */\n    MapMode[MapMode[\"TrackAfter\"] = 3] = \"TrackAfter\";\nreturn MapMode})(MapMode || (MapMode = {}));\n/**\nA change description is a variant of [change set](https://codemirror.net/6/docs/ref/#state.ChangeSet)\nthat doesn't store the inserted text. As such, it can't be\napplied, but is cheaper to store and manipulate.\n*/\nclass ChangeDesc {\n    // Sections are encoded as pairs of integers. The first is the\n    // length in the current document, and the second is -1 for\n    // unaffected sections, and the length of the replacement content\n    // otherwise. So an insertion would be (0, n>0), a deletion (n>0,\n    // 0), and a replacement two positive numbers.\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    @internal\n    */\n    sections) {\n        this.sections = sections;\n    }\n    /**\n    The length of the document before the change.\n    */\n    get length() {\n        let result = 0;\n        for (let i = 0; i < this.sections.length; i += 2)\n            result += this.sections[i];\n        return result;\n    }\n    /**\n    The length of the document after the change.\n    */\n    get newLength() {\n        let result = 0;\n        for (let i = 0; i < this.sections.length; i += 2) {\n            let ins = this.sections[i + 1];\n            result += ins < 0 ? this.sections[i] : ins;\n        }\n        return result;\n    }\n    /**\n    False when there are actual changes in this set.\n    */\n    get empty() { return this.sections.length == 0 || this.sections.length == 2 && this.sections[1] < 0; }\n    /**\n    Iterate over the unchanged parts left by these changes. `posA`\n    provides the position of the range in the old document, `posB`\n    the new position in the changed document.\n    */\n    iterGaps(f) {\n        for (let i = 0, posA = 0, posB = 0; i < this.sections.length;) {\n            let len = this.sections[i++], ins = this.sections[i++];\n            if (ins < 0) {\n                f(posA, posB, len);\n                posB += len;\n            }\n            else {\n                posB += ins;\n            }\n            posA += len;\n        }\n    }\n    /**\n    Iterate over the ranges changed by these changes. (See\n    [`ChangeSet.iterChanges`](https://codemirror.net/6/docs/ref/#state.ChangeSet.iterChanges) for a\n    variant that also provides you with the inserted text.)\n    `fromA`/`toA` provides the extent of the change in the starting\n    document, `fromB`/`toB` the extent of the replacement in the\n    changed document.\n    \n    When `individual` is true, adjacent changes (which are kept\n    separate for [position mapping](https://codemirror.net/6/docs/ref/#state.ChangeDesc.mapPos)) are\n    reported separately.\n    */\n    iterChangedRanges(f, individual = false) {\n        iterChanges(this, f, individual);\n    }\n    /**\n    Get a description of the inverted form of these changes.\n    */\n    get invertedDesc() {\n        let sections = [];\n        for (let i = 0; i < this.sections.length;) {\n            let len = this.sections[i++], ins = this.sections[i++];\n            if (ins < 0)\n                sections.push(len, ins);\n            else\n                sections.push(ins, len);\n        }\n        return new ChangeDesc(sections);\n    }\n    /**\n    Compute the combined effect of applying another set of changes\n    after this one. The length of the document after this set should\n    match the length before `other`.\n    */\n    composeDesc(other) { return this.empty ? other : other.empty ? this : composeSets(this, other); }\n    /**\n    Map this description, which should start with the same document\n    as `other`, over another set of changes, so that it can be\n    applied after it. When `before` is true, map as if the changes\n    in `this` happened before the ones in `other`.\n    */\n    mapDesc(other, before = false) { return other.empty ? this : mapSet(this, other, before); }\n    mapPos(pos, assoc = -1, mode = MapMode.Simple) {\n        let posA = 0, posB = 0;\n        for (let i = 0; i < this.sections.length;) {\n            let len = this.sections[i++], ins = this.sections[i++], endA = posA + len;\n            if (ins < 0) {\n                if (endA > pos)\n                    return posB + (pos - posA);\n                posB += len;\n            }\n            else {\n                if (mode != MapMode.Simple && endA >= pos &&\n                    (mode == MapMode.TrackDel && posA < pos && endA > pos ||\n                        mode == MapMode.TrackBefore && posA < pos ||\n                        mode == MapMode.TrackAfter && endA > pos))\n                    return null;\n                if (endA > pos || endA == pos && assoc < 0 && !len)\n                    return pos == posA || assoc < 0 ? posB : posB + ins;\n                posB += ins;\n            }\n            posA = endA;\n        }\n        if (pos > posA)\n            throw new RangeError(`Position ${pos} is out of range for changeset of length ${posA}`);\n        return posB;\n    }\n    /**\n    Check whether these changes touch a given range. When one of the\n    changes entirely covers the range, the string `\"cover\"` is\n    returned.\n    */\n    touchesRange(from, to = from) {\n        for (let i = 0, pos = 0; i < this.sections.length && pos <= to;) {\n            let len = this.sections[i++], ins = this.sections[i++], end = pos + len;\n            if (ins >= 0 && pos <= to && end >= from)\n                return pos < from && end > to ? \"cover\" : true;\n            pos = end;\n        }\n        return false;\n    }\n    /**\n    @internal\n    */\n    toString() {\n        let result = \"\";\n        for (let i = 0; i < this.sections.length;) {\n            let len = this.sections[i++], ins = this.sections[i++];\n            result += (result ? \" \" : \"\") + len + (ins >= 0 ? \":\" + ins : \"\");\n        }\n        return result;\n    }\n    /**\n    Serialize this change desc to a JSON-representable value.\n    */\n    toJSON() { return this.sections; }\n    /**\n    Create a change desc from its JSON representation (as produced\n    by [`toJSON`](https://codemirror.net/6/docs/ref/#state.ChangeDesc.toJSON).\n    */\n    static fromJSON(json) {\n        if (!Array.isArray(json) || json.length % 2 || json.some(a => typeof a != \"number\"))\n            throw new RangeError(\"Invalid JSON representation of ChangeDesc\");\n        return new ChangeDesc(json);\n    }\n    /**\n    @internal\n    */\n    static create(sections) { return new ChangeDesc(sections); }\n}\n/**\nA change set represents a group of modifications to a document. It\nstores the document length, and can only be applied to documents\nwith exactly that length.\n*/\nclass ChangeSet extends ChangeDesc {\n    constructor(sections, \n    /**\n    @internal\n    */\n    inserted) {\n        super(sections);\n        this.inserted = inserted;\n    }\n    /**\n    Apply the changes to a document, returning the modified\n    document.\n    */\n    apply(doc) {\n        if (this.length != doc.length)\n            throw new RangeError(\"Applying change set to a document with the wrong length\");\n        iterChanges(this, (fromA, toA, fromB, _toB, text) => doc = doc.replace(fromB, fromB + (toA - fromA), text), false);\n        return doc;\n    }\n    mapDesc(other, before = false) { return mapSet(this, other, before, true); }\n    /**\n    Given the document as it existed _before_ the changes, return a\n    change set that represents the inverse of this set, which could\n    be used to go from the document created by the changes back to\n    the document as it existed before the changes.\n    */\n    invert(doc) {\n        let sections = this.sections.slice(), inserted = [];\n        for (let i = 0, pos = 0; i < sections.length; i += 2) {\n            let len = sections[i], ins = sections[i + 1];\n            if (ins >= 0) {\n                sections[i] = ins;\n                sections[i + 1] = len;\n                let index = i >> 1;\n                while (inserted.length < index)\n                    inserted.push(Text.empty);\n                inserted.push(len ? doc.slice(pos, pos + len) : Text.empty);\n            }\n            pos += len;\n        }\n        return new ChangeSet(sections, inserted);\n    }\n    /**\n    Combine two subsequent change sets into a single set. `other`\n    must start in the document produced by `this`. If `this` goes\n    `docA` → `docB` and `other` represents `docB` → `docC`, the\n    returned value will represent the change `docA` → `docC`.\n    */\n    compose(other) { return this.empty ? other : other.empty ? this : composeSets(this, other, true); }\n    /**\n    Given another change set starting in the same document, maps this\n    change set over the other, producing a new change set that can be\n    applied to the document produced by applying `other`. When\n    `before` is `true`, order changes as if `this` comes before\n    `other`, otherwise (the default) treat `other` as coming first.\n    \n    Given two changes `A` and `B`, `A.compose(B.map(A))` and\n    `B.compose(A.map(B, true))` will produce the same document. This\n    provides a basic form of [operational\n    transformation](https://en.wikipedia.org/wiki/Operational_transformation),\n    and can be used for collaborative editing.\n    */\n    map(other, before = false) { return other.empty ? this : mapSet(this, other, before, true); }\n    /**\n    Iterate over the changed ranges in the document, calling `f` for\n    each, with the range in the original document (`fromA`-`toA`)\n    and the range that replaces it in the new document\n    (`fromB`-`toB`).\n    \n    When `individual` is true, adjacent changes are reported\n    separately.\n    */\n    iterChanges(f, individual = false) {\n        iterChanges(this, f, individual);\n    }\n    /**\n    Get a [change description](https://codemirror.net/6/docs/ref/#state.ChangeDesc) for this change\n    set.\n    */\n    get desc() { return ChangeDesc.create(this.sections); }\n    /**\n    @internal\n    */\n    filter(ranges) {\n        let resultSections = [], resultInserted = [], filteredSections = [];\n        let iter = new SectionIter(this);\n        done: for (let i = 0, pos = 0;;) {\n            let next = i == ranges.length ? 1e9 : ranges[i++];\n            while (pos < next || pos == next && iter.len == 0) {\n                if (iter.done)\n                    break done;\n                let len = Math.min(iter.len, next - pos);\n                addSection(filteredSections, len, -1);\n                let ins = iter.ins == -1 ? -1 : iter.off == 0 ? iter.ins : 0;\n                addSection(resultSections, len, ins);\n                if (ins > 0)\n                    addInsert(resultInserted, resultSections, iter.text);\n                iter.forward(len);\n                pos += len;\n            }\n            let end = ranges[i++];\n            while (pos < end) {\n                if (iter.done)\n                    break done;\n                let len = Math.min(iter.len, end - pos);\n                addSection(resultSections, len, -1);\n                addSection(filteredSections, len, iter.ins == -1 ? -1 : iter.off == 0 ? iter.ins : 0);\n                iter.forward(len);\n                pos += len;\n            }\n        }\n        return { changes: new ChangeSet(resultSections, resultInserted),\n            filtered: ChangeDesc.create(filteredSections) };\n    }\n    /**\n    Serialize this change set to a JSON-representable value.\n    */\n    toJSON() {\n        let parts = [];\n        for (let i = 0; i < this.sections.length; i += 2) {\n            let len = this.sections[i], ins = this.sections[i + 1];\n            if (ins < 0)\n                parts.push(len);\n            else if (ins == 0)\n                parts.push([len]);\n            else\n                parts.push([len].concat(this.inserted[i >> 1].toJSON()));\n        }\n        return parts;\n    }\n    /**\n    Create a change set for the given changes, for a document of the\n    given length, using `lineSep` as line separator.\n    */\n    static of(changes, length, lineSep) {\n        let sections = [], inserted = [], pos = 0;\n        let total = null;\n        function flush(force = false) {\n            if (!force && !sections.length)\n                return;\n            if (pos < length)\n                addSection(sections, length - pos, -1);\n            let set = new ChangeSet(sections, inserted);\n            total = total ? total.compose(set.map(total)) : set;\n            sections = [];\n            inserted = [];\n            pos = 0;\n        }\n        function process(spec) {\n            if (Array.isArray(spec)) {\n                for (let sub of spec)\n                    process(sub);\n            }\n            else if (spec instanceof ChangeSet) {\n                if (spec.length != length)\n                    throw new RangeError(`Mismatched change set length (got ${spec.length}, expected ${length})`);\n                flush();\n                total = total ? total.compose(spec.map(total)) : spec;\n            }\n            else {\n                let { from, to = from, insert } = spec;\n                if (from > to || from < 0 || to > length)\n                    throw new RangeError(`Invalid change range ${from} to ${to} (in doc of length ${length})`);\n                let insText = !insert ? Text.empty : typeof insert == \"string\" ? Text.of(insert.split(lineSep || DefaultSplit)) : insert;\n                let insLen = insText.length;\n                if (from == to && insLen == 0)\n                    return;\n                if (from < pos)\n                    flush();\n                if (from > pos)\n                    addSection(sections, from - pos, -1);\n                addSection(sections, to - from, insLen);\n                addInsert(inserted, sections, insText);\n                pos = to;\n            }\n        }\n        process(changes);\n        flush(!total);\n        return total;\n    }\n    /**\n    Create an empty changeset of the given length.\n    */\n    static empty(length) {\n        return new ChangeSet(length ? [length, -1] : [], []);\n    }\n    /**\n    Create a changeset from its JSON representation (as produced by\n    [`toJSON`](https://codemirror.net/6/docs/ref/#state.ChangeSet.toJSON).\n    */\n    static fromJSON(json) {\n        if (!Array.isArray(json))\n            throw new RangeError(\"Invalid JSON representation of ChangeSet\");\n        let sections = [], inserted = [];\n        for (let i = 0; i < json.length; i++) {\n            let part = json[i];\n            if (typeof part == \"number\") {\n                sections.push(part, -1);\n            }\n            else if (!Array.isArray(part) || typeof part[0] != \"number\" || part.some((e, i) => i && typeof e != \"string\")) {\n                throw new RangeError(\"Invalid JSON representation of ChangeSet\");\n            }\n            else if (part.length == 1) {\n                sections.push(part[0], 0);\n            }\n            else {\n                while (inserted.length < i)\n                    inserted.push(Text.empty);\n                inserted[i] = Text.of(part.slice(1));\n                sections.push(part[0], inserted[i].length);\n            }\n        }\n        return new ChangeSet(sections, inserted);\n    }\n    /**\n    @internal\n    */\n    static createSet(sections, inserted) {\n        return new ChangeSet(sections, inserted);\n    }\n}\nfunction addSection(sections, len, ins, forceJoin = false) {\n    if (len == 0 && ins <= 0)\n        return;\n    let last = sections.length - 2;\n    if (last >= 0 && ins <= 0 && ins == sections[last + 1])\n        sections[last] += len;\n    else if (last >= 0 && len == 0 && sections[last] == 0)\n        sections[last + 1] += ins;\n    else if (forceJoin) {\n        sections[last] += len;\n        sections[last + 1] += ins;\n    }\n    else\n        sections.push(len, ins);\n}\nfunction addInsert(values, sections, value) {\n    if (value.length == 0)\n        return;\n    let index = (sections.length - 2) >> 1;\n    if (index < values.length) {\n        values[values.length - 1] = values[values.length - 1].append(value);\n    }\n    else {\n        while (values.length < index)\n            values.push(Text.empty);\n        values.push(value);\n    }\n}\nfunction iterChanges(desc, f, individual) {\n    let inserted = desc.inserted;\n    for (let posA = 0, posB = 0, i = 0; i < desc.sections.length;) {\n        let len = desc.sections[i++], ins = desc.sections[i++];\n        if (ins < 0) {\n            posA += len;\n            posB += len;\n        }\n        else {\n            let endA = posA, endB = posB, text = Text.empty;\n            for (;;) {\n                endA += len;\n                endB += ins;\n                if (ins && inserted)\n                    text = text.append(inserted[(i - 2) >> 1]);\n                if (individual || i == desc.sections.length || desc.sections[i + 1] < 0)\n                    break;\n                len = desc.sections[i++];\n                ins = desc.sections[i++];\n            }\n            f(posA, endA, posB, endB, text);\n            posA = endA;\n            posB = endB;\n        }\n    }\n}\nfunction mapSet(setA, setB, before, mkSet = false) {\n    // Produce a copy of setA that applies to the document after setB\n    // has been applied (assuming both start at the same document).\n    let sections = [], insert = mkSet ? [] : null;\n    let a = new SectionIter(setA), b = new SectionIter(setB);\n    // Iterate over both sets in parallel. inserted tracks, for changes\n    // in A that have to be processed piece-by-piece, whether their\n    // content has been inserted already, and refers to the section\n    // index.\n    for (let inserted = -1;;) {\n        if (a.done && b.len || b.done && a.len) {\n            throw new Error(\"Mismatched change set lengths\");\n        }\n        else if (a.ins == -1 && b.ins == -1) {\n            // Move across ranges skipped by both sets.\n            let len = Math.min(a.len, b.len);\n            addSection(sections, len, -1);\n            a.forward(len);\n            b.forward(len);\n        }\n        else if (b.ins >= 0 && (a.ins < 0 || inserted == a.i || a.off == 0 && (b.len < a.len || b.len == a.len && !before))) {\n            // If there's a change in B that comes before the next change in\n            // A (ordered by start pos, then len, then before flag), skip\n            // that (and process any changes in A it covers).\n            let len = b.len;\n            addSection(sections, b.ins, -1);\n            while (len) {\n                let piece = Math.min(a.len, len);\n                if (a.ins >= 0 && inserted < a.i && a.len <= piece) {\n                    addSection(sections, 0, a.ins);\n                    if (insert)\n                        addInsert(insert, sections, a.text);\n                    inserted = a.i;\n                }\n                a.forward(piece);\n                len -= piece;\n            }\n            b.next();\n        }\n        else if (a.ins >= 0) {\n            // Process the part of a change in A up to the start of the next\n            // non-deletion change in B (if overlapping).\n            let len = 0, left = a.len;\n            while (left) {\n                if (b.ins == -1) {\n                    let piece = Math.min(left, b.len);\n                    len += piece;\n                    left -= piece;\n                    b.forward(piece);\n                }\n                else if (b.ins == 0 && b.len < left) {\n                    left -= b.len;\n                    b.next();\n                }\n                else {\n                    break;\n                }\n            }\n            addSection(sections, len, inserted < a.i ? a.ins : 0);\n            if (insert && inserted < a.i)\n                addInsert(insert, sections, a.text);\n            inserted = a.i;\n            a.forward(a.len - left);\n        }\n        else if (a.done && b.done) {\n            return insert ? ChangeSet.createSet(sections, insert) : ChangeDesc.create(sections);\n        }\n        else {\n            throw new Error(\"Mismatched change set lengths\");\n        }\n    }\n}\nfunction composeSets(setA, setB, mkSet = false) {\n    let sections = [];\n    let insert = mkSet ? [] : null;\n    let a = new SectionIter(setA), b = new SectionIter(setB);\n    for (let open = false;;) {\n        if (a.done && b.done) {\n            return insert ? ChangeSet.createSet(sections, insert) : ChangeDesc.create(sections);\n        }\n        else if (a.ins == 0) { // Deletion in A\n            addSection(sections, a.len, 0, open);\n            a.next();\n        }\n        else if (b.len == 0 && !b.done) { // Insertion in B\n            addSection(sections, 0, b.ins, open);\n            if (insert)\n                addInsert(insert, sections, b.text);\n            b.next();\n        }\n        else if (a.done || b.done) {\n            throw new Error(\"Mismatched change set lengths\");\n        }\n        else {\n            let len = Math.min(a.len2, b.len), sectionLen = sections.length;\n            if (a.ins == -1) {\n                let insB = b.ins == -1 ? -1 : b.off ? 0 : b.ins;\n                addSection(sections, len, insB, open);\n                if (insert && insB)\n                    addInsert(insert, sections, b.text);\n            }\n            else if (b.ins == -1) {\n                addSection(sections, a.off ? 0 : a.len, len, open);\n                if (insert)\n                    addInsert(insert, sections, a.textBit(len));\n            }\n            else {\n                addSection(sections, a.off ? 0 : a.len, b.off ? 0 : b.ins, open);\n                if (insert && !b.off)\n                    addInsert(insert, sections, b.text);\n            }\n            open = (a.ins > len || b.ins >= 0 && b.len > len) && (open || sections.length > sectionLen);\n            a.forward2(len);\n            b.forward(len);\n        }\n    }\n}\nclass SectionIter {\n    constructor(set) {\n        this.set = set;\n        this.i = 0;\n        this.next();\n    }\n    next() {\n        let { sections } = this.set;\n        if (this.i < sections.length) {\n            this.len = sections[this.i++];\n            this.ins = sections[this.i++];\n        }\n        else {\n            this.len = 0;\n            this.ins = -2;\n        }\n        this.off = 0;\n    }\n    get done() { return this.ins == -2; }\n    get len2() { return this.ins < 0 ? this.len : this.ins; }\n    get text() {\n        let { inserted } = this.set, index = (this.i - 2) >> 1;\n        return index >= inserted.length ? Text.empty : inserted[index];\n    }\n    textBit(len) {\n        let { inserted } = this.set, index = (this.i - 2) >> 1;\n        return index >= inserted.length && !len ? Text.empty\n            : inserted[index].slice(this.off, len == null ? undefined : this.off + len);\n    }\n    forward(len) {\n        if (len == this.len)\n            this.next();\n        else {\n            this.len -= len;\n            this.off += len;\n        }\n    }\n    forward2(len) {\n        if (this.ins == -1)\n            this.forward(len);\n        else if (len == this.ins)\n            this.next();\n        else {\n            this.ins -= len;\n            this.off += len;\n        }\n    }\n}\n\n/**\nA single selection range. When\n[`allowMultipleSelections`](https://codemirror.net/6/docs/ref/#state.EditorState^allowMultipleSelections)\nis enabled, a [selection](https://codemirror.net/6/docs/ref/#state.EditorSelection) may hold\nmultiple ranges. By default, selections hold exactly one range.\n*/\nclass SelectionRange {\n    constructor(\n    /**\n    The lower boundary of the range.\n    */\n    from, \n    /**\n    The upper boundary of the range.\n    */\n    to, flags) {\n        this.from = from;\n        this.to = to;\n        this.flags = flags;\n    }\n    /**\n    The anchor of the range—the side that doesn't move when you\n    extend it.\n    */\n    get anchor() { return this.flags & 32 /* RangeFlag.Inverted */ ? this.to : this.from; }\n    /**\n    The head of the range, which is moved when the range is\n    [extended](https://codemirror.net/6/docs/ref/#state.SelectionRange.extend).\n    */\n    get head() { return this.flags & 32 /* RangeFlag.Inverted */ ? this.from : this.to; }\n    /**\n    True when `anchor` and `head` are at the same position.\n    */\n    get empty() { return this.from == this.to; }\n    /**\n    If this is a cursor that is explicitly associated with the\n    character on one of its sides, this returns the side. -1 means\n    the character before its position, 1 the character after, and 0\n    means no association.\n    */\n    get assoc() { return this.flags & 8 /* RangeFlag.AssocBefore */ ? -1 : this.flags & 16 /* RangeFlag.AssocAfter */ ? 1 : 0; }\n    /**\n    The bidirectional text level associated with this cursor, if\n    any.\n    */\n    get bidiLevel() {\n        let level = this.flags & 7 /* RangeFlag.BidiLevelMask */;\n        return level == 7 ? null : level;\n    }\n    /**\n    The goal column (stored vertical offset) associated with a\n    cursor. This is used to preserve the vertical position when\n    [moving](https://codemirror.net/6/docs/ref/#view.EditorView.moveVertically) across\n    lines of different length.\n    */\n    get goalColumn() {\n        let value = this.flags >> 6 /* RangeFlag.GoalColumnOffset */;\n        return value == 16777215 /* RangeFlag.NoGoalColumn */ ? undefined : value;\n    }\n    /**\n    Map this range through a change, producing a valid range in the\n    updated document.\n    */\n    map(change, assoc = -1) {\n        let from, to;\n        if (this.empty) {\n            from = to = change.mapPos(this.from, assoc);\n        }\n        else {\n            from = change.mapPos(this.from, 1);\n            to = change.mapPos(this.to, -1);\n        }\n        return from == this.from && to == this.to ? this : new SelectionRange(from, to, this.flags);\n    }\n    /**\n    Extend this range to cover at least `from` to `to`.\n    */\n    extend(from, to = from) {\n        if (from <= this.anchor && to >= this.anchor)\n            return EditorSelection.range(from, to);\n        let head = Math.abs(from - this.anchor) > Math.abs(to - this.anchor) ? from : to;\n        return EditorSelection.range(this.anchor, head);\n    }\n    /**\n    Compare this range to another range.\n    */\n    eq(other, includeAssoc = false) {\n        return this.anchor == other.anchor && this.head == other.head &&\n            (!includeAssoc || !this.empty || this.assoc == other.assoc);\n    }\n    /**\n    Return a JSON-serializable object representing the range.\n    */\n    toJSON() { return { anchor: this.anchor, head: this.head }; }\n    /**\n    Convert a JSON representation of a range to a `SelectionRange`\n    instance.\n    */\n    static fromJSON(json) {\n        if (!json || typeof json.anchor != \"number\" || typeof json.head != \"number\")\n            throw new RangeError(\"Invalid JSON representation for SelectionRange\");\n        return EditorSelection.range(json.anchor, json.head);\n    }\n    /**\n    @internal\n    */\n    static create(from, to, flags) {\n        return new SelectionRange(from, to, flags);\n    }\n}\n/**\nAn editor selection holds one or more selection ranges.\n*/\nclass EditorSelection {\n    constructor(\n    /**\n    The ranges in the selection, sorted by position. Ranges cannot\n    overlap (but they may touch, if they aren't empty).\n    */\n    ranges, \n    /**\n    The index of the _main_ range in the selection (which is\n    usually the range that was added last).\n    */\n    mainIndex) {\n        this.ranges = ranges;\n        this.mainIndex = mainIndex;\n    }\n    /**\n    Map a selection through a change. Used to adjust the selection\n    position for changes.\n    */\n    map(change, assoc = -1) {\n        if (change.empty)\n            return this;\n        return EditorSelection.create(this.ranges.map(r => r.map(change, assoc)), this.mainIndex);\n    }\n    /**\n    Compare this selection to another selection. By default, ranges\n    are compared only by position. When `includeAssoc` is true,\n    cursor ranges must also have the same\n    [`assoc`](https://codemirror.net/6/docs/ref/#state.SelectionRange.assoc) value.\n    */\n    eq(other, includeAssoc = false) {\n        if (this.ranges.length != other.ranges.length ||\n            this.mainIndex != other.mainIndex)\n            return false;\n        for (let i = 0; i < this.ranges.length; i++)\n            if (!this.ranges[i].eq(other.ranges[i], includeAssoc))\n                return false;\n        return true;\n    }\n    /**\n    Get the primary selection range. Usually, you should make sure\n    your code applies to _all_ ranges, by using methods like\n    [`changeByRange`](https://codemirror.net/6/docs/ref/#state.EditorState.changeByRange).\n    */\n    get main() { return this.ranges[this.mainIndex]; }\n    /**\n    Make sure the selection only has one range. Returns a selection\n    holding only the main range from this selection.\n    */\n    asSingle() {\n        return this.ranges.length == 1 ? this : new EditorSelection([this.main], 0);\n    }\n    /**\n    Extend this selection with an extra range.\n    */\n    addRange(range, main = true) {\n        return EditorSelection.create([range].concat(this.ranges), main ? 0 : this.mainIndex + 1);\n    }\n    /**\n    Replace a given range with another range, and then normalize the\n    selection to merge and sort ranges if necessary.\n    */\n    replaceRange(range, which = this.mainIndex) {\n        let ranges = this.ranges.slice();\n        ranges[which] = range;\n        return EditorSelection.create(ranges, this.mainIndex);\n    }\n    /**\n    Convert this selection to an object that can be serialized to\n    JSON.\n    */\n    toJSON() {\n        return { ranges: this.ranges.map(r => r.toJSON()), main: this.mainIndex };\n    }\n    /**\n    Create a selection from a JSON representation.\n    */\n    static fromJSON(json) {\n        if (!json || !Array.isArray(json.ranges) || typeof json.main != \"number\" || json.main >= json.ranges.length)\n            throw new RangeError(\"Invalid JSON representation for EditorSelection\");\n        return new EditorSelection(json.ranges.map((r) => SelectionRange.fromJSON(r)), json.main);\n    }\n    /**\n    Create a selection holding a single range.\n    */\n    static single(anchor, head = anchor) {\n        return new EditorSelection([EditorSelection.range(anchor, head)], 0);\n    }\n    /**\n    Sort and merge the given set of ranges, creating a valid\n    selection.\n    */\n    static create(ranges, mainIndex = 0) {\n        if (ranges.length == 0)\n            throw new RangeError(\"A selection needs at least one range\");\n        for (let pos = 0, i = 0; i < ranges.length; i++) {\n            let range = ranges[i];\n            if (range.empty ? range.from <= pos : range.from < pos)\n                return EditorSelection.normalized(ranges.slice(), mainIndex);\n            pos = range.to;\n        }\n        return new EditorSelection(ranges, mainIndex);\n    }\n    /**\n    Create a cursor selection range at the given position. You can\n    safely ignore the optional arguments in most situations.\n    */\n    static cursor(pos, assoc = 0, bidiLevel, goalColumn) {\n        return SelectionRange.create(pos, pos, (assoc == 0 ? 0 : assoc < 0 ? 8 /* RangeFlag.AssocBefore */ : 16 /* RangeFlag.AssocAfter */) |\n            (bidiLevel == null ? 7 : Math.min(6, bidiLevel)) |\n            ((goalColumn !== null && goalColumn !== void 0 ? goalColumn : 16777215 /* RangeFlag.NoGoalColumn */) << 6 /* RangeFlag.GoalColumnOffset */));\n    }\n    /**\n    Create a selection range.\n    */\n    static range(anchor, head, goalColumn, bidiLevel) {\n        let flags = ((goalColumn !== null && goalColumn !== void 0 ? goalColumn : 16777215 /* RangeFlag.NoGoalColumn */) << 6 /* RangeFlag.GoalColumnOffset */) |\n            (bidiLevel == null ? 7 : Math.min(6, bidiLevel));\n        return head < anchor ? SelectionRange.create(head, anchor, 32 /* RangeFlag.Inverted */ | 16 /* RangeFlag.AssocAfter */ | flags)\n            : SelectionRange.create(anchor, head, (head > anchor ? 8 /* RangeFlag.AssocBefore */ : 0) | flags);\n    }\n    /**\n    @internal\n    */\n    static normalized(ranges, mainIndex = 0) {\n        let main = ranges[mainIndex];\n        ranges.sort((a, b) => a.from - b.from);\n        mainIndex = ranges.indexOf(main);\n        for (let i = 1; i < ranges.length; i++) {\n            let range = ranges[i], prev = ranges[i - 1];\n            if (range.empty ? range.from <= prev.to : range.from < prev.to) {\n                let from = prev.from, to = Math.max(range.to, prev.to);\n                if (i <= mainIndex)\n                    mainIndex--;\n                ranges.splice(--i, 2, range.anchor > range.head ? EditorSelection.range(to, from) : EditorSelection.range(from, to));\n            }\n        }\n        return new EditorSelection(ranges, mainIndex);\n    }\n}\nfunction checkSelection(selection, docLength) {\n    for (let range of selection.ranges)\n        if (range.to > docLength)\n            throw new RangeError(\"Selection points outside of document\");\n}\n\nlet nextID = 0;\n/**\nA facet is a labeled value that is associated with an editor\nstate. It takes inputs from any number of extensions, and combines\nthose into a single output value.\n\nExamples of uses of facets are the [tab\nsize](https://codemirror.net/6/docs/ref/#state.EditorState^tabSize), [editor\nattributes](https://codemirror.net/6/docs/ref/#view.EditorView^editorAttributes), and [update\nlisteners](https://codemirror.net/6/docs/ref/#view.EditorView^updateListener).\n\nNote that `Facet` instances can be used anywhere where\n[`FacetReader`](https://codemirror.net/6/docs/ref/#state.FacetReader) is expected.\n*/\nclass Facet {\n    constructor(\n    /**\n    @internal\n    */\n    combine, \n    /**\n    @internal\n    */\n    compareInput, \n    /**\n    @internal\n    */\n    compare, isStatic, enables) {\n        this.combine = combine;\n        this.compareInput = compareInput;\n        this.compare = compare;\n        this.isStatic = isStatic;\n        /**\n        @internal\n        */\n        this.id = nextID++;\n        this.default = combine([]);\n        this.extensions = typeof enables == \"function\" ? enables(this) : enables;\n    }\n    /**\n    Returns a facet reader for this facet, which can be used to\n    [read](https://codemirror.net/6/docs/ref/#state.EditorState.facet) it but not to define values for it.\n    */\n    get reader() { return this; }\n    /**\n    Define a new facet.\n    */\n    static define(config = {}) {\n        return new Facet(config.combine || ((a) => a), config.compareInput || ((a, b) => a === b), config.compare || (!config.combine ? sameArray : (a, b) => a === b), !!config.static, config.enables);\n    }\n    /**\n    Returns an extension that adds the given value to this facet.\n    */\n    of(value) {\n        return new FacetProvider([], this, 0 /* Provider.Static */, value);\n    }\n    /**\n    Create an extension that computes a value for the facet from a\n    state. You must take care to declare the parts of the state that\n    this value depends on, since your function is only called again\n    for a new state when one of those parts changed.\n    \n    In cases where your value depends only on a single field, you'll\n    want to use the [`from`](https://codemirror.net/6/docs/ref/#state.Facet.from) method instead.\n    */\n    compute(deps, get) {\n        if (this.isStatic)\n            throw new Error(\"Can't compute a static facet\");\n        return new FacetProvider(deps, this, 1 /* Provider.Single */, get);\n    }\n    /**\n    Create an extension that computes zero or more values for this\n    facet from a state.\n    */\n    computeN(deps, get) {\n        if (this.isStatic)\n            throw new Error(\"Can't compute a static facet\");\n        return new FacetProvider(deps, this, 2 /* Provider.Multi */, get);\n    }\n    from(field, get) {\n        if (!get)\n            get = x => x;\n        return this.compute([field], state => get(state.field(field)));\n    }\n}\nfunction sameArray(a, b) {\n    return a == b || a.length == b.length && a.every((e, i) => e === b[i]);\n}\nclass FacetProvider {\n    constructor(dependencies, facet, type, value) {\n        this.dependencies = dependencies;\n        this.facet = facet;\n        this.type = type;\n        this.value = value;\n        this.id = nextID++;\n    }\n    dynamicSlot(addresses) {\n        var _a;\n        let getter = this.value;\n        let compare = this.facet.compareInput;\n        let id = this.id, idx = addresses[id] >> 1, multi = this.type == 2 /* Provider.Multi */;\n        let depDoc = false, depSel = false, depAddrs = [];\n        for (let dep of this.dependencies) {\n            if (dep == \"doc\")\n                depDoc = true;\n            else if (dep == \"selection\")\n                depSel = true;\n            else if ((((_a = addresses[dep.id]) !== null && _a !== void 0 ? _a : 1) & 1) == 0)\n                depAddrs.push(addresses[dep.id]);\n        }\n        return {\n            create(state) {\n                state.values[idx] = getter(state);\n                return 1 /* SlotStatus.Changed */;\n            },\n            update(state, tr) {\n                if ((depDoc && tr.docChanged) || (depSel && (tr.docChanged || tr.selection)) || ensureAll(state, depAddrs)) {\n                    let newVal = getter(state);\n                    if (multi ? !compareArray(newVal, state.values[idx], compare) : !compare(newVal, state.values[idx])) {\n                        state.values[idx] = newVal;\n                        return 1 /* SlotStatus.Changed */;\n                    }\n                }\n                return 0;\n            },\n            reconfigure: (state, oldState) => {\n                let newVal, oldAddr = oldState.config.address[id];\n                if (oldAddr != null) {\n                    let oldVal = getAddr(oldState, oldAddr);\n                    if (this.dependencies.every(dep => {\n                        return dep instanceof Facet ? oldState.facet(dep) === state.facet(dep) :\n                            dep instanceof StateField ? oldState.field(dep, false) == state.field(dep, false) : true;\n                    }) || (multi ? compareArray(newVal = getter(state), oldVal, compare) : compare(newVal = getter(state), oldVal))) {\n                        state.values[idx] = oldVal;\n                        return 0;\n                    }\n                }\n                else {\n                    newVal = getter(state);\n                }\n                state.values[idx] = newVal;\n                return 1 /* SlotStatus.Changed */;\n            }\n        };\n    }\n}\nfunction compareArray(a, b, compare) {\n    if (a.length != b.length)\n        return false;\n    for (let i = 0; i < a.length; i++)\n        if (!compare(a[i], b[i]))\n            return false;\n    return true;\n}\nfunction ensureAll(state, addrs) {\n    let changed = false;\n    for (let addr of addrs)\n        if (ensureAddr(state, addr) & 1 /* SlotStatus.Changed */)\n            changed = true;\n    return changed;\n}\nfunction dynamicFacetSlot(addresses, facet, providers) {\n    let providerAddrs = providers.map(p => addresses[p.id]);\n    let providerTypes = providers.map(p => p.type);\n    let dynamic = providerAddrs.filter(p => !(p & 1));\n    let idx = addresses[facet.id] >> 1;\n    function get(state) {\n        let values = [];\n        for (let i = 0; i < providerAddrs.length; i++) {\n            let value = getAddr(state, providerAddrs[i]);\n            if (providerTypes[i] == 2 /* Provider.Multi */)\n                for (let val of value)\n                    values.push(val);\n            else\n                values.push(value);\n        }\n        return facet.combine(values);\n    }\n    return {\n        create(state) {\n            for (let addr of providerAddrs)\n                ensureAddr(state, addr);\n            state.values[idx] = get(state);\n            return 1 /* SlotStatus.Changed */;\n        },\n        update(state, tr) {\n            if (!ensureAll(state, dynamic))\n                return 0;\n            let value = get(state);\n            if (facet.compare(value, state.values[idx]))\n                return 0;\n            state.values[idx] = value;\n            return 1 /* SlotStatus.Changed */;\n        },\n        reconfigure(state, oldState) {\n            let depChanged = ensureAll(state, providerAddrs);\n            let oldProviders = oldState.config.facets[facet.id], oldValue = oldState.facet(facet);\n            if (oldProviders && !depChanged && sameArray(providers, oldProviders)) {\n                state.values[idx] = oldValue;\n                return 0;\n            }\n            let value = get(state);\n            if (facet.compare(value, oldValue)) {\n                state.values[idx] = oldValue;\n                return 0;\n            }\n            state.values[idx] = value;\n            return 1 /* SlotStatus.Changed */;\n        }\n    };\n}\nconst initField = /*@__PURE__*/Facet.define({ static: true });\n/**\nFields can store additional information in an editor state, and\nkeep it in sync with the rest of the state.\n*/\nclass StateField {\n    constructor(\n    /**\n    @internal\n    */\n    id, createF, updateF, compareF, \n    /**\n    @internal\n    */\n    spec) {\n        this.id = id;\n        this.createF = createF;\n        this.updateF = updateF;\n        this.compareF = compareF;\n        this.spec = spec;\n        /**\n        @internal\n        */\n        this.provides = undefined;\n    }\n    /**\n    Define a state field.\n    */\n    static define(config) {\n        let field = new StateField(nextID++, config.create, config.update, config.compare || ((a, b) => a === b), config);\n        if (config.provide)\n            field.provides = config.provide(field);\n        return field;\n    }\n    create(state) {\n        let init = state.facet(initField).find(i => i.field == this);\n        return ((init === null || init === void 0 ? void 0 : init.create) || this.createF)(state);\n    }\n    /**\n    @internal\n    */\n    slot(addresses) {\n        let idx = addresses[this.id] >> 1;\n        return {\n            create: (state) => {\n                state.values[idx] = this.create(state);\n                return 1 /* SlotStatus.Changed */;\n            },\n            update: (state, tr) => {\n                let oldVal = state.values[idx];\n                let value = this.updateF(oldVal, tr);\n                if (this.compareF(oldVal, value))\n                    return 0;\n                state.values[idx] = value;\n                return 1 /* SlotStatus.Changed */;\n            },\n            reconfigure: (state, oldState) => {\n                let init = state.facet(initField), oldInit = oldState.facet(initField), reInit;\n                if ((reInit = init.find(i => i.field == this)) && reInit != oldInit.find(i => i.field == this)) {\n                    state.values[idx] = reInit.create(state);\n                    return 1 /* SlotStatus.Changed */;\n                }\n                if (oldState.config.address[this.id] != null) {\n                    state.values[idx] = oldState.field(this);\n                    return 0;\n                }\n                state.values[idx] = this.create(state);\n                return 1 /* SlotStatus.Changed */;\n            }\n        };\n    }\n    /**\n    Returns an extension that enables this field and overrides the\n    way it is initialized. Can be useful when you need to provide a\n    non-default starting value for the field.\n    */\n    init(create) {\n        return [this, initField.of({ field: this, create })];\n    }\n    /**\n    State field instances can be used as\n    [`Extension`](https://codemirror.net/6/docs/ref/#state.Extension) values to enable the field in a\n    given state.\n    */\n    get extension() { return this; }\n}\nconst Prec_ = { lowest: 4, low: 3, default: 2, high: 1, highest: 0 };\nfunction prec(value) {\n    return (ext) => new PrecExtension(ext, value);\n}\n/**\nBy default extensions are registered in the order they are found\nin the flattened form of nested array that was provided.\nIndividual extension values can be assigned a precedence to\noverride this. Extensions that do not have a precedence set get\nthe precedence of the nearest parent with a precedence, or\n[`default`](https://codemirror.net/6/docs/ref/#state.Prec.default) if there is no such parent. The\nfinal ordering of extensions is determined by first sorting by\nprecedence and then by order within each precedence.\n*/\nconst Prec = {\n    /**\n    The highest precedence level, for extensions that should end up\n    near the start of the precedence ordering.\n    */\n    highest: /*@__PURE__*/prec(Prec_.highest),\n    /**\n    A higher-than-default precedence, for extensions that should\n    come before those with default precedence.\n    */\n    high: /*@__PURE__*/prec(Prec_.high),\n    /**\n    The default precedence, which is also used for extensions\n    without an explicit precedence.\n    */\n    default: /*@__PURE__*/prec(Prec_.default),\n    /**\n    A lower-than-default precedence.\n    */\n    low: /*@__PURE__*/prec(Prec_.low),\n    /**\n    The lowest precedence level. Meant for things that should end up\n    near the end of the extension order.\n    */\n    lowest: /*@__PURE__*/prec(Prec_.lowest)\n};\nclass PrecExtension {\n    constructor(inner, prec) {\n        this.inner = inner;\n        this.prec = prec;\n    }\n}\n/**\nExtension compartments can be used to make a configuration\ndynamic. By [wrapping](https://codemirror.net/6/docs/ref/#state.Compartment.of) part of your\nconfiguration in a compartment, you can later\n[replace](https://codemirror.net/6/docs/ref/#state.Compartment.reconfigure) that part through a\ntransaction.\n*/\nclass Compartment {\n    /**\n    Create an instance of this compartment to add to your [state\n    configuration](https://codemirror.net/6/docs/ref/#state.EditorStateConfig.extensions).\n    */\n    of(ext) { return new CompartmentInstance(this, ext); }\n    /**\n    Create an [effect](https://codemirror.net/6/docs/ref/#state.TransactionSpec.effects) that\n    reconfigures this compartment.\n    */\n    reconfigure(content) {\n        return Compartment.reconfigure.of({ compartment: this, extension: content });\n    }\n    /**\n    Get the current content of the compartment in the state, or\n    `undefined` if it isn't present.\n    */\n    get(state) {\n        return state.config.compartments.get(this);\n    }\n}\nclass CompartmentInstance {\n    constructor(compartment, inner) {\n        this.compartment = compartment;\n        this.inner = inner;\n    }\n}\nclass Configuration {\n    constructor(base, compartments, dynamicSlots, address, staticValues, facets) {\n        this.base = base;\n        this.compartments = compartments;\n        this.dynamicSlots = dynamicSlots;\n        this.address = address;\n        this.staticValues = staticValues;\n        this.facets = facets;\n        this.statusTemplate = [];\n        while (this.statusTemplate.length < dynamicSlots.length)\n            this.statusTemplate.push(0 /* SlotStatus.Unresolved */);\n    }\n    staticFacet(facet) {\n        let addr = this.address[facet.id];\n        return addr == null ? facet.default : this.staticValues[addr >> 1];\n    }\n    static resolve(base, compartments, oldState) {\n        let fields = [];\n        let facets = Object.create(null);\n        let newCompartments = new Map();\n        for (let ext of flatten(base, compartments, newCompartments)) {\n            if (ext instanceof StateField)\n                fields.push(ext);\n            else\n                (facets[ext.facet.id] || (facets[ext.facet.id] = [])).push(ext);\n        }\n        let address = Object.create(null);\n        let staticValues = [];\n        let dynamicSlots = [];\n        for (let field of fields) {\n            address[field.id] = dynamicSlots.length << 1;\n            dynamicSlots.push(a => field.slot(a));\n        }\n        let oldFacets = oldState === null || oldState === void 0 ? void 0 : oldState.config.facets;\n        for (let id in facets) {\n            let providers = facets[id], facet = providers[0].facet;\n            let oldProviders = oldFacets && oldFacets[id] || [];\n            if (providers.every(p => p.type == 0 /* Provider.Static */)) {\n                address[facet.id] = (staticValues.length << 1) | 1;\n                if (sameArray(oldProviders, providers)) {\n                    staticValues.push(oldState.facet(facet));\n                }\n                else {\n                    let value = facet.combine(providers.map(p => p.value));\n                    staticValues.push(oldState && facet.compare(value, oldState.facet(facet)) ? oldState.facet(facet) : value);\n                }\n            }\n            else {\n                for (let p of providers) {\n                    if (p.type == 0 /* Provider.Static */) {\n                        address[p.id] = (staticValues.length << 1) | 1;\n                        staticValues.push(p.value);\n                    }\n                    else {\n                        address[p.id] = dynamicSlots.length << 1;\n                        dynamicSlots.push(a => p.dynamicSlot(a));\n                    }\n                }\n                address[facet.id] = dynamicSlots.length << 1;\n                dynamicSlots.push(a => dynamicFacetSlot(a, facet, providers));\n            }\n        }\n        let dynamic = dynamicSlots.map(f => f(address));\n        return new Configuration(base, newCompartments, dynamic, address, staticValues, facets);\n    }\n}\nfunction flatten(extension, compartments, newCompartments) {\n    let result = [[], [], [], [], []];\n    let seen = new Map();\n    function inner(ext, prec) {\n        let known = seen.get(ext);\n        if (known != null) {\n            if (known <= prec)\n                return;\n            let found = result[known].indexOf(ext);\n            if (found > -1)\n                result[known].splice(found, 1);\n            if (ext instanceof CompartmentInstance)\n                newCompartments.delete(ext.compartment);\n        }\n        seen.set(ext, prec);\n        if (Array.isArray(ext)) {\n            for (let e of ext)\n                inner(e, prec);\n        }\n        else if (ext instanceof CompartmentInstance) {\n            if (newCompartments.has(ext.compartment))\n                throw new RangeError(`Duplicate use of compartment in extensions`);\n            let content = compartments.get(ext.compartment) || ext.inner;\n            newCompartments.set(ext.compartment, content);\n            inner(content, prec);\n        }\n        else if (ext instanceof PrecExtension) {\n            inner(ext.inner, ext.prec);\n        }\n        else if (ext instanceof StateField) {\n            result[prec].push(ext);\n            if (ext.provides)\n                inner(ext.provides, prec);\n        }\n        else if (ext instanceof FacetProvider) {\n            result[prec].push(ext);\n            if (ext.facet.extensions)\n                inner(ext.facet.extensions, Prec_.default);\n        }\n        else {\n            let content = ext.extension;\n            if (!content)\n                throw new Error(`Unrecognized extension value in extension set (${ext}). This sometimes happens because multiple instances of @codemirror/state are loaded, breaking instanceof checks.`);\n            inner(content, prec);\n        }\n    }\n    inner(extension, Prec_.default);\n    return result.reduce((a, b) => a.concat(b));\n}\nfunction ensureAddr(state, addr) {\n    if (addr & 1)\n        return 2 /* SlotStatus.Computed */;\n    let idx = addr >> 1;\n    let status = state.status[idx];\n    if (status == 4 /* SlotStatus.Computing */)\n        throw new Error(\"Cyclic dependency between fields and/or facets\");\n    if (status & 2 /* SlotStatus.Computed */)\n        return status;\n    state.status[idx] = 4 /* SlotStatus.Computing */;\n    let changed = state.computeSlot(state, state.config.dynamicSlots[idx]);\n    return state.status[idx] = 2 /* SlotStatus.Computed */ | changed;\n}\nfunction getAddr(state, addr) {\n    return addr & 1 ? state.config.staticValues[addr >> 1] : state.values[addr >> 1];\n}\n\nconst languageData = /*@__PURE__*/Facet.define();\nconst allowMultipleSelections = /*@__PURE__*/Facet.define({\n    combine: values => values.some(v => v),\n    static: true\n});\nconst lineSeparator = /*@__PURE__*/Facet.define({\n    combine: values => values.length ? values[0] : undefined,\n    static: true\n});\nconst changeFilter = /*@__PURE__*/Facet.define();\nconst transactionFilter = /*@__PURE__*/Facet.define();\nconst transactionExtender = /*@__PURE__*/Facet.define();\nconst readOnly = /*@__PURE__*/Facet.define({\n    combine: values => values.length ? values[0] : false\n});\n\n/**\nAnnotations are tagged values that are used to add metadata to\ntransactions in an extensible way. They should be used to model\nthings that effect the entire transaction (such as its [time\nstamp](https://codemirror.net/6/docs/ref/#state.Transaction^time) or information about its\n[origin](https://codemirror.net/6/docs/ref/#state.Transaction^userEvent)). For effects that happen\n_alongside_ the other changes made by the transaction, [state\neffects](https://codemirror.net/6/docs/ref/#state.StateEffect) are more appropriate.\n*/\nclass Annotation {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The annotation type.\n    */\n    type, \n    /**\n    The value of this annotation.\n    */\n    value) {\n        this.type = type;\n        this.value = value;\n    }\n    /**\n    Define a new type of annotation.\n    */\n    static define() { return new AnnotationType(); }\n}\n/**\nMarker that identifies a type of [annotation](https://codemirror.net/6/docs/ref/#state.Annotation).\n*/\nclass AnnotationType {\n    /**\n    Create an instance of this annotation.\n    */\n    of(value) { return new Annotation(this, value); }\n}\n/**\nRepresentation of a type of state effect. Defined with\n[`StateEffect.define`](https://codemirror.net/6/docs/ref/#state.StateEffect^define).\n*/\nclass StateEffectType {\n    /**\n    @internal\n    */\n    constructor(\n    // The `any` types in these function types are there to work\n    // around TypeScript issue #37631, where the type guard on\n    // `StateEffect.is` mysteriously stops working when these properly\n    // have type `Value`.\n    /**\n    @internal\n    */\n    map) {\n        this.map = map;\n    }\n    /**\n    Create a [state effect](https://codemirror.net/6/docs/ref/#state.StateEffect) instance of this\n    type.\n    */\n    of(value) { return new StateEffect(this, value); }\n}\n/**\nState effects can be used to represent additional effects\nassociated with a [transaction](https://codemirror.net/6/docs/ref/#state.Transaction.effects). They\nare often useful to model changes to custom [state\nfields](https://codemirror.net/6/docs/ref/#state.StateField), when those changes aren't implicit in\ndocument or selection changes.\n*/\nclass StateEffect {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    @internal\n    */\n    type, \n    /**\n    The value of this effect.\n    */\n    value) {\n        this.type = type;\n        this.value = value;\n    }\n    /**\n    Map this effect through a position mapping. Will return\n    `undefined` when that ends up deleting the effect.\n    */\n    map(mapping) {\n        let mapped = this.type.map(this.value, mapping);\n        return mapped === undefined ? undefined : mapped == this.value ? this : new StateEffect(this.type, mapped);\n    }\n    /**\n    Tells you whether this effect object is of a given\n    [type](https://codemirror.net/6/docs/ref/#state.StateEffectType).\n    */\n    is(type) { return this.type == type; }\n    /**\n    Define a new effect type. The type parameter indicates the type\n    of values that his effect holds. It should be a type that\n    doesn't include `undefined`, since that is used in\n    [mapping](https://codemirror.net/6/docs/ref/#state.StateEffect.map) to indicate that an effect is\n    removed.\n    */\n    static define(spec = {}) {\n        return new StateEffectType(spec.map || (v => v));\n    }\n    /**\n    Map an array of effects through a change set.\n    */\n    static mapEffects(effects, mapping) {\n        if (!effects.length)\n            return effects;\n        let result = [];\n        for (let effect of effects) {\n            let mapped = effect.map(mapping);\n            if (mapped)\n                result.push(mapped);\n        }\n        return result;\n    }\n}\n/**\nThis effect can be used to reconfigure the root extensions of\nthe editor. Doing this will discard any extensions\n[appended](https://codemirror.net/6/docs/ref/#state.StateEffect^appendConfig), but does not reset\nthe content of [reconfigured](https://codemirror.net/6/docs/ref/#state.Compartment.reconfigure)\ncompartments.\n*/\nStateEffect.reconfigure = /*@__PURE__*/StateEffect.define();\n/**\nAppend extensions to the top-level configuration of the editor.\n*/\nStateEffect.appendConfig = /*@__PURE__*/StateEffect.define();\n/**\nChanges to the editor state are grouped into transactions.\nTypically, a user action creates a single transaction, which may\ncontain any number of document changes, may change the selection,\nor have other effects. Create a transaction by calling\n[`EditorState.update`](https://codemirror.net/6/docs/ref/#state.EditorState.update), or immediately\ndispatch one by calling\n[`EditorView.dispatch`](https://codemirror.net/6/docs/ref/#view.EditorView.dispatch).\n*/\nclass Transaction {\n    constructor(\n    /**\n    The state from which the transaction starts.\n    */\n    startState, \n    /**\n    The document changes made by this transaction.\n    */\n    changes, \n    /**\n    The selection set by this transaction, or undefined if it\n    doesn't explicitly set a selection.\n    */\n    selection, \n    /**\n    The effects added to the transaction.\n    */\n    effects, \n    /**\n    @internal\n    */\n    annotations, \n    /**\n    Whether the selection should be scrolled into view after this\n    transaction is dispatched.\n    */\n    scrollIntoView) {\n        this.startState = startState;\n        this.changes = changes;\n        this.selection = selection;\n        this.effects = effects;\n        this.annotations = annotations;\n        this.scrollIntoView = scrollIntoView;\n        /**\n        @internal\n        */\n        this._doc = null;\n        /**\n        @internal\n        */\n        this._state = null;\n        if (selection)\n            checkSelection(selection, changes.newLength);\n        if (!annotations.some((a) => a.type == Transaction.time))\n            this.annotations = annotations.concat(Transaction.time.of(Date.now()));\n    }\n    /**\n    @internal\n    */\n    static create(startState, changes, selection, effects, annotations, scrollIntoView) {\n        return new Transaction(startState, changes, selection, effects, annotations, scrollIntoView);\n    }\n    /**\n    The new document produced by the transaction. Contrary to\n    [`.state`](https://codemirror.net/6/docs/ref/#state.Transaction.state)`.doc`, accessing this won't\n    force the entire new state to be computed right away, so it is\n    recommended that [transaction\n    filters](https://codemirror.net/6/docs/ref/#state.EditorState^transactionFilter) use this getter\n    when they need to look at the new document.\n    */\n    get newDoc() {\n        return this._doc || (this._doc = this.changes.apply(this.startState.doc));\n    }\n    /**\n    The new selection produced by the transaction. If\n    [`this.selection`](https://codemirror.net/6/docs/ref/#state.Transaction.selection) is undefined,\n    this will [map](https://codemirror.net/6/docs/ref/#state.EditorSelection.map) the start state's\n    current selection through the changes made by the transaction.\n    */\n    get newSelection() {\n        return this.selection || this.startState.selection.map(this.changes);\n    }\n    /**\n    The new state created by the transaction. Computed on demand\n    (but retained for subsequent access), so it is recommended not to\n    access it in [transaction\n    filters](https://codemirror.net/6/docs/ref/#state.EditorState^transactionFilter) when possible.\n    */\n    get state() {\n        if (!this._state)\n            this.startState.applyTransaction(this);\n        return this._state;\n    }\n    /**\n    Get the value of the given annotation type, if any.\n    */\n    annotation(type) {\n        for (let ann of this.annotations)\n            if (ann.type == type)\n                return ann.value;\n        return undefined;\n    }\n    /**\n    Indicates whether the transaction changed the document.\n    */\n    get docChanged() { return !this.changes.empty; }\n    /**\n    Indicates whether this transaction reconfigures the state\n    (through a [configuration compartment](https://codemirror.net/6/docs/ref/#state.Compartment) or\n    with a top-level configuration\n    [effect](https://codemirror.net/6/docs/ref/#state.StateEffect^reconfigure).\n    */\n    get reconfigured() { return this.startState.config != this.state.config; }\n    /**\n    Returns true if the transaction has a [user\n    event](https://codemirror.net/6/docs/ref/#state.Transaction^userEvent) annotation that is equal to\n    or more specific than `event`. For example, if the transaction\n    has `\"select.pointer\"` as user event, `\"select\"` and\n    `\"select.pointer\"` will match it.\n    */\n    isUserEvent(event) {\n        let e = this.annotation(Transaction.userEvent);\n        return !!(e && (e == event || e.length > event.length && e.slice(0, event.length) == event && e[event.length] == \".\"));\n    }\n}\n/**\nAnnotation used to store transaction timestamps. Automatically\nadded to every transaction, holding `Date.now()`.\n*/\nTransaction.time = /*@__PURE__*/Annotation.define();\n/**\nAnnotation used to associate a transaction with a user interface\nevent. Holds a string identifying the event, using a\ndot-separated format to support attaching more specific\ninformation. The events used by the core libraries are:\n\n - `\"input\"` when content is entered\n   - `\"input.type\"` for typed input\n     - `\"input.type.compose\"` for composition\n   - `\"input.paste\"` for pasted input\n   - `\"input.drop\"` when adding content with drag-and-drop\n   - `\"input.complete\"` when autocompleting\n - `\"delete\"` when the user deletes content\n   - `\"delete.selection\"` when deleting the selection\n   - `\"delete.forward\"` when deleting forward from the selection\n   - `\"delete.backward\"` when deleting backward from the selection\n   - `\"delete.cut\"` when cutting to the clipboard\n - `\"move\"` when content is moved\n   - `\"move.drop\"` when content is moved within the editor through drag-and-drop\n - `\"select\"` when explicitly changing the selection\n   - `\"select.pointer\"` when selecting with a mouse or other pointing device\n - `\"undo\"` and `\"redo\"` for history actions\n\nUse [`isUserEvent`](https://codemirror.net/6/docs/ref/#state.Transaction.isUserEvent) to check\nwhether the annotation matches a given event.\n*/\nTransaction.userEvent = /*@__PURE__*/Annotation.define();\n/**\nAnnotation indicating whether a transaction should be added to\nthe undo history or not.\n*/\nTransaction.addToHistory = /*@__PURE__*/Annotation.define();\n/**\nAnnotation indicating (when present and true) that a transaction\nrepresents a change made by some other actor, not the user. This\nis used, for example, to tag other people's changes in\ncollaborative editing.\n*/\nTransaction.remote = /*@__PURE__*/Annotation.define();\nfunction joinRanges(a, b) {\n    let result = [];\n    for (let iA = 0, iB = 0;;) {\n        let from, to;\n        if (iA < a.length && (iB == b.length || b[iB] >= a[iA])) {\n            from = a[iA++];\n            to = a[iA++];\n        }\n        else if (iB < b.length) {\n            from = b[iB++];\n            to = b[iB++];\n        }\n        else\n            return result;\n        if (!result.length || result[result.length - 1] < from)\n            result.push(from, to);\n        else if (result[result.length - 1] < to)\n            result[result.length - 1] = to;\n    }\n}\nfunction mergeTransaction(a, b, sequential) {\n    var _a;\n    let mapForA, mapForB, changes;\n    if (sequential) {\n        mapForA = b.changes;\n        mapForB = ChangeSet.empty(b.changes.length);\n        changes = a.changes.compose(b.changes);\n    }\n    else {\n        mapForA = b.changes.map(a.changes);\n        mapForB = a.changes.mapDesc(b.changes, true);\n        changes = a.changes.compose(mapForA);\n    }\n    return {\n        changes,\n        selection: b.selection ? b.selection.map(mapForB) : (_a = a.selection) === null || _a === void 0 ? void 0 : _a.map(mapForA),\n        effects: StateEffect.mapEffects(a.effects, mapForA).concat(StateEffect.mapEffects(b.effects, mapForB)),\n        annotations: a.annotations.length ? a.annotations.concat(b.annotations) : b.annotations,\n        scrollIntoView: a.scrollIntoView || b.scrollIntoView\n    };\n}\nfunction resolveTransactionInner(state, spec, docSize) {\n    let sel = spec.selection, annotations = asArray(spec.annotations);\n    if (spec.userEvent)\n        annotations = annotations.concat(Transaction.userEvent.of(spec.userEvent));\n    return {\n        changes: spec.changes instanceof ChangeSet ? spec.changes\n            : ChangeSet.of(spec.changes || [], docSize, state.facet(lineSeparator)),\n        selection: sel && (sel instanceof EditorSelection ? sel : EditorSelection.single(sel.anchor, sel.head)),\n        effects: asArray(spec.effects),\n        annotations,\n        scrollIntoView: !!spec.scrollIntoView\n    };\n}\nfunction resolveTransaction(state, specs, filter) {\n    let s = resolveTransactionInner(state, specs.length ? specs[0] : {}, state.doc.length);\n    if (specs.length && specs[0].filter === false)\n        filter = false;\n    for (let i = 1; i < specs.length; i++) {\n        if (specs[i].filter === false)\n            filter = false;\n        let seq = !!specs[i].sequential;\n        s = mergeTransaction(s, resolveTransactionInner(state, specs[i], seq ? s.changes.newLength : state.doc.length), seq);\n    }\n    let tr = Transaction.create(state, s.changes, s.selection, s.effects, s.annotations, s.scrollIntoView);\n    return extendTransaction(filter ? filterTransaction(tr) : tr);\n}\n// Finish a transaction by applying filters if necessary.\nfunction filterTransaction(tr) {\n    let state = tr.startState;\n    // Change filters\n    let result = true;\n    for (let filter of state.facet(changeFilter)) {\n        let value = filter(tr);\n        if (value === false) {\n            result = false;\n            break;\n        }\n        if (Array.isArray(value))\n            result = result === true ? value : joinRanges(result, value);\n    }\n    if (result !== true) {\n        let changes, back;\n        if (result === false) {\n            back = tr.changes.invertedDesc;\n            changes = ChangeSet.empty(state.doc.length);\n        }\n        else {\n            let filtered = tr.changes.filter(result);\n            changes = filtered.changes;\n            back = filtered.filtered.mapDesc(filtered.changes).invertedDesc;\n        }\n        tr = Transaction.create(state, changes, tr.selection && tr.selection.map(back), StateEffect.mapEffects(tr.effects, back), tr.annotations, tr.scrollIntoView);\n    }\n    // Transaction filters\n    let filters = state.facet(transactionFilter);\n    for (let i = filters.length - 1; i >= 0; i--) {\n        let filtered = filters[i](tr);\n        if (filtered instanceof Transaction)\n            tr = filtered;\n        else if (Array.isArray(filtered) && filtered.length == 1 && filtered[0] instanceof Transaction)\n            tr = filtered[0];\n        else\n            tr = resolveTransaction(state, asArray(filtered), false);\n    }\n    return tr;\n}\nfunction extendTransaction(tr) {\n    let state = tr.startState, extenders = state.facet(transactionExtender), spec = tr;\n    for (let i = extenders.length - 1; i >= 0; i--) {\n        let extension = extenders[i](tr);\n        if (extension && Object.keys(extension).length)\n            spec = mergeTransaction(spec, resolveTransactionInner(state, extension, tr.changes.newLength), true);\n    }\n    return spec == tr ? tr : Transaction.create(state, tr.changes, tr.selection, spec.effects, spec.annotations, spec.scrollIntoView);\n}\nconst none = [];\nfunction asArray(value) {\n    return value == null ? none : Array.isArray(value) ? value : [value];\n}\n\n/**\nThe categories produced by a [character\ncategorizer](https://codemirror.net/6/docs/ref/#state.EditorState.charCategorizer). These are used\ndo things like selecting by word.\n*/\nvar CharCategory = /*@__PURE__*/(function (CharCategory) {\n    /**\n    Word characters.\n    */\n    CharCategory[CharCategory[\"Word\"] = 0] = \"Word\";\n    /**\n    Whitespace.\n    */\n    CharCategory[CharCategory[\"Space\"] = 1] = \"Space\";\n    /**\n    Anything else.\n    */\n    CharCategory[CharCategory[\"Other\"] = 2] = \"Other\";\nreturn CharCategory})(CharCategory || (CharCategory = {}));\nconst nonASCIISingleCaseWordChar = /[\\u00df\\u0587\\u0590-\\u05f4\\u0600-\\u06ff\\u3040-\\u309f\\u30a0-\\u30ff\\u3400-\\u4db5\\u4e00-\\u9fcc\\uac00-\\ud7af]/;\nlet wordChar;\ntry {\n    wordChar = /*@__PURE__*/new RegExp(\"[\\\\p{Alphabetic}\\\\p{Number}_]\", \"u\");\n}\ncatch (_) { }\nfunction hasWordChar(str) {\n    if (wordChar)\n        return wordChar.test(str);\n    for (let i = 0; i < str.length; i++) {\n        let ch = str[i];\n        if (/\\w/.test(ch) || ch > \"\\x80\" && (ch.toUpperCase() != ch.toLowerCase() || nonASCIISingleCaseWordChar.test(ch)))\n            return true;\n    }\n    return false;\n}\nfunction makeCategorizer(wordChars) {\n    return (char) => {\n        if (!/\\S/.test(char))\n            return CharCategory.Space;\n        if (hasWordChar(char))\n            return CharCategory.Word;\n        for (let i = 0; i < wordChars.length; i++)\n            if (char.indexOf(wordChars[i]) > -1)\n                return CharCategory.Word;\n        return CharCategory.Other;\n    };\n}\n\n/**\nThe editor state class is a persistent (immutable) data structure.\nTo update a state, you [create](https://codemirror.net/6/docs/ref/#state.EditorState.update) a\n[transaction](https://codemirror.net/6/docs/ref/#state.Transaction), which produces a _new_ state\ninstance, without modifying the original object.\n\nAs such, _never_ mutate properties of a state directly. That'll\njust break things.\n*/\nclass EditorState {\n    constructor(\n    /**\n    @internal\n    */\n    config, \n    /**\n    The current document.\n    */\n    doc, \n    /**\n    The current selection.\n    */\n    selection, \n    /**\n    @internal\n    */\n    values, computeSlot, tr) {\n        this.config = config;\n        this.doc = doc;\n        this.selection = selection;\n        this.values = values;\n        this.status = config.statusTemplate.slice();\n        this.computeSlot = computeSlot;\n        // Fill in the computed state immediately, so that further queries\n        // for it made during the update return this state\n        if (tr)\n            tr._state = this;\n        for (let i = 0; i < this.config.dynamicSlots.length; i++)\n            ensureAddr(this, i << 1);\n        this.computeSlot = null;\n    }\n    field(field, require = true) {\n        let addr = this.config.address[field.id];\n        if (addr == null) {\n            if (require)\n                throw new RangeError(\"Field is not present in this state\");\n            return undefined;\n        }\n        ensureAddr(this, addr);\n        return getAddr(this, addr);\n    }\n    /**\n    Create a [transaction](https://codemirror.net/6/docs/ref/#state.Transaction) that updates this\n    state. Any number of [transaction specs](https://codemirror.net/6/docs/ref/#state.TransactionSpec)\n    can be passed. Unless\n    [`sequential`](https://codemirror.net/6/docs/ref/#state.TransactionSpec.sequential) is set, the\n    [changes](https://codemirror.net/6/docs/ref/#state.TransactionSpec.changes) (if any) of each spec\n    are assumed to start in the _current_ document (not the document\n    produced by previous specs), and its\n    [selection](https://codemirror.net/6/docs/ref/#state.TransactionSpec.selection) and\n    [effects](https://codemirror.net/6/docs/ref/#state.TransactionSpec.effects) are assumed to refer\n    to the document created by its _own_ changes. The resulting\n    transaction contains the combined effect of all the different\n    specs. For [selection](https://codemirror.net/6/docs/ref/#state.TransactionSpec.selection), later\n    specs take precedence over earlier ones.\n    */\n    update(...specs) {\n        return resolveTransaction(this, specs, true);\n    }\n    /**\n    @internal\n    */\n    applyTransaction(tr) {\n        let conf = this.config, { base, compartments } = conf;\n        for (let effect of tr.effects) {\n            if (effect.is(Compartment.reconfigure)) {\n                if (conf) {\n                    compartments = new Map;\n                    conf.compartments.forEach((val, key) => compartments.set(key, val));\n                    conf = null;\n                }\n                compartments.set(effect.value.compartment, effect.value.extension);\n            }\n            else if (effect.is(StateEffect.reconfigure)) {\n                conf = null;\n                base = effect.value;\n            }\n            else if (effect.is(StateEffect.appendConfig)) {\n                conf = null;\n                base = asArray(base).concat(effect.value);\n            }\n        }\n        let startValues;\n        if (!conf) {\n            conf = Configuration.resolve(base, compartments, this);\n            let intermediateState = new EditorState(conf, this.doc, this.selection, conf.dynamicSlots.map(() => null), (state, slot) => slot.reconfigure(state, this), null);\n            startValues = intermediateState.values;\n        }\n        else {\n            startValues = tr.startState.values.slice();\n        }\n        let selection = tr.startState.facet(allowMultipleSelections) ? tr.newSelection : tr.newSelection.asSingle();\n        new EditorState(conf, tr.newDoc, selection, startValues, (state, slot) => slot.update(state, tr), tr);\n    }\n    /**\n    Create a [transaction spec](https://codemirror.net/6/docs/ref/#state.TransactionSpec) that\n    replaces every selection range with the given content.\n    */\n    replaceSelection(text) {\n        if (typeof text == \"string\")\n            text = this.toText(text);\n        return this.changeByRange(range => ({ changes: { from: range.from, to: range.to, insert: text },\n            range: EditorSelection.cursor(range.from + text.length) }));\n    }\n    /**\n    Create a set of changes and a new selection by running the given\n    function for each range in the active selection. The function\n    can return an optional set of changes (in the coordinate space\n    of the start document), plus an updated range (in the coordinate\n    space of the document produced by the call's own changes). This\n    method will merge all the changes and ranges into a single\n    changeset and selection, and return it as a [transaction\n    spec](https://codemirror.net/6/docs/ref/#state.TransactionSpec), which can be passed to\n    [`update`](https://codemirror.net/6/docs/ref/#state.EditorState.update).\n    */\n    changeByRange(f) {\n        let sel = this.selection;\n        let result1 = f(sel.ranges[0]);\n        let changes = this.changes(result1.changes), ranges = [result1.range];\n        let effects = asArray(result1.effects);\n        for (let i = 1; i < sel.ranges.length; i++) {\n            let result = f(sel.ranges[i]);\n            let newChanges = this.changes(result.changes), newMapped = newChanges.map(changes);\n            for (let j = 0; j < i; j++)\n                ranges[j] = ranges[j].map(newMapped);\n            let mapBy = changes.mapDesc(newChanges, true);\n            ranges.push(result.range.map(mapBy));\n            changes = changes.compose(newMapped);\n            effects = StateEffect.mapEffects(effects, newMapped).concat(StateEffect.mapEffects(asArray(result.effects), mapBy));\n        }\n        return {\n            changes,\n            selection: EditorSelection.create(ranges, sel.mainIndex),\n            effects\n        };\n    }\n    /**\n    Create a [change set](https://codemirror.net/6/docs/ref/#state.ChangeSet) from the given change\n    description, taking the state's document length and line\n    separator into account.\n    */\n    changes(spec = []) {\n        if (spec instanceof ChangeSet)\n            return spec;\n        return ChangeSet.of(spec, this.doc.length, this.facet(EditorState.lineSeparator));\n    }\n    /**\n    Using the state's [line\n    separator](https://codemirror.net/6/docs/ref/#state.EditorState^lineSeparator), create a\n    [`Text`](https://codemirror.net/6/docs/ref/#state.Text) instance from the given string.\n    */\n    toText(string) {\n        return Text.of(string.split(this.facet(EditorState.lineSeparator) || DefaultSplit));\n    }\n    /**\n    Return the given range of the document as a string.\n    */\n    sliceDoc(from = 0, to = this.doc.length) {\n        return this.doc.sliceString(from, to, this.lineBreak);\n    }\n    /**\n    Get the value of a state [facet](https://codemirror.net/6/docs/ref/#state.Facet).\n    */\n    facet(facet) {\n        let addr = this.config.address[facet.id];\n        if (addr == null)\n            return facet.default;\n        ensureAddr(this, addr);\n        return getAddr(this, addr);\n    }\n    /**\n    Convert this state to a JSON-serializable object. When custom\n    fields should be serialized, you can pass them in as an object\n    mapping property names (in the resulting object, which should\n    not use `doc` or `selection`) to fields.\n    */\n    toJSON(fields) {\n        let result = {\n            doc: this.sliceDoc(),\n            selection: this.selection.toJSON()\n        };\n        if (fields)\n            for (let prop in fields) {\n                let value = fields[prop];\n                if (value instanceof StateField && this.config.address[value.id] != null)\n                    result[prop] = value.spec.toJSON(this.field(fields[prop]), this);\n            }\n        return result;\n    }\n    /**\n    Deserialize a state from its JSON representation. When custom\n    fields should be deserialized, pass the same object you passed\n    to [`toJSON`](https://codemirror.net/6/docs/ref/#state.EditorState.toJSON) when serializing as\n    third argument.\n    */\n    static fromJSON(json, config = {}, fields) {\n        if (!json || typeof json.doc != \"string\")\n            throw new RangeError(\"Invalid JSON representation for EditorState\");\n        let fieldInit = [];\n        if (fields)\n            for (let prop in fields) {\n                if (Object.prototype.hasOwnProperty.call(json, prop)) {\n                    let field = fields[prop], value = json[prop];\n                    fieldInit.push(field.init(state => field.spec.fromJSON(value, state)));\n                }\n            }\n        return EditorState.create({\n            doc: json.doc,\n            selection: EditorSelection.fromJSON(json.selection),\n            extensions: config.extensions ? fieldInit.concat([config.extensions]) : fieldInit\n        });\n    }\n    /**\n    Create a new state. You'll usually only need this when\n    initializing an editor—updated states are created by applying\n    transactions.\n    */\n    static create(config = {}) {\n        let configuration = Configuration.resolve(config.extensions || [], new Map);\n        let doc = config.doc instanceof Text ? config.doc\n            : Text.of((config.doc || \"\").split(configuration.staticFacet(EditorState.lineSeparator) || DefaultSplit));\n        let selection = !config.selection ? EditorSelection.single(0)\n            : config.selection instanceof EditorSelection ? config.selection\n                : EditorSelection.single(config.selection.anchor, config.selection.head);\n        checkSelection(selection, doc.length);\n        if (!configuration.staticFacet(allowMultipleSelections))\n            selection = selection.asSingle();\n        return new EditorState(configuration, doc, selection, configuration.dynamicSlots.map(() => null), (state, slot) => slot.create(state), null);\n    }\n    /**\n    The size (in columns) of a tab in the document, determined by\n    the [`tabSize`](https://codemirror.net/6/docs/ref/#state.EditorState^tabSize) facet.\n    */\n    get tabSize() { return this.facet(EditorState.tabSize); }\n    /**\n    Get the proper [line-break](https://codemirror.net/6/docs/ref/#state.EditorState^lineSeparator)\n    string for this state.\n    */\n    get lineBreak() { return this.facet(EditorState.lineSeparator) || \"\\n\"; }\n    /**\n    Returns true when the editor is\n    [configured](https://codemirror.net/6/docs/ref/#state.EditorState^readOnly) to be read-only.\n    */\n    get readOnly() { return this.facet(readOnly); }\n    /**\n    Look up a translation for the given phrase (via the\n    [`phrases`](https://codemirror.net/6/docs/ref/#state.EditorState^phrases) facet), or return the\n    original string if no translation is found.\n    \n    If additional arguments are passed, they will be inserted in\n    place of markers like `$1` (for the first value) and `$2`, etc.\n    A single `$` is equivalent to `$1`, and `$$` will produce a\n    literal dollar sign.\n    */\n    phrase(phrase, ...insert) {\n        for (let map of this.facet(EditorState.phrases))\n            if (Object.prototype.hasOwnProperty.call(map, phrase)) {\n                phrase = map[phrase];\n                break;\n            }\n        if (insert.length)\n            phrase = phrase.replace(/\\$(\\$|\\d*)/g, (m, i) => {\n                if (i == \"$\")\n                    return \"$\";\n                let n = +(i || 1);\n                return !n || n > insert.length ? m : insert[n - 1];\n            });\n        return phrase;\n    }\n    /**\n    Find the values for a given language data field, provided by the\n    the [`languageData`](https://codemirror.net/6/docs/ref/#state.EditorState^languageData) facet.\n    \n    Examples of language data fields are...\n    \n    - [`\"commentTokens\"`](https://codemirror.net/6/docs/ref/#commands.CommentTokens) for specifying\n      comment syntax.\n    - [`\"autocomplete\"`](https://codemirror.net/6/docs/ref/#autocomplete.autocompletion^config.override)\n      for providing language-specific completion sources.\n    - [`\"wordChars\"`](https://codemirror.net/6/docs/ref/#state.EditorState.charCategorizer) for adding\n      characters that should be considered part of words in this\n      language.\n    - [`\"closeBrackets\"`](https://codemirror.net/6/docs/ref/#autocomplete.CloseBracketConfig) controls\n      bracket closing behavior.\n    */\n    languageDataAt(name, pos, side = -1) {\n        let values = [];\n        for (let provider of this.facet(languageData)) {\n            for (let result of provider(this, pos, side)) {\n                if (Object.prototype.hasOwnProperty.call(result, name))\n                    values.push(result[name]);\n            }\n        }\n        return values;\n    }\n    /**\n    Return a function that can categorize strings (expected to\n    represent a single [grapheme cluster](https://codemirror.net/6/docs/ref/#state.findClusterBreak))\n    into one of:\n    \n     - Word (contains an alphanumeric character or a character\n       explicitly listed in the local language's `\"wordChars\"`\n       language data, which should be a string)\n     - Space (contains only whitespace)\n     - Other (anything else)\n    */\n    charCategorizer(at) {\n        return makeCategorizer(this.languageDataAt(\"wordChars\", at).join(\"\"));\n    }\n    /**\n    Find the word at the given position, meaning the range\n    containing all [word](https://codemirror.net/6/docs/ref/#state.CharCategory.Word) characters\n    around it. If no word characters are adjacent to the position,\n    this returns null.\n    */\n    wordAt(pos) {\n        let { text, from, length } = this.doc.lineAt(pos);\n        let cat = this.charCategorizer(pos);\n        let start = pos - from, end = pos - from;\n        while (start > 0) {\n            let prev = findClusterBreak(text, start, false);\n            if (cat(text.slice(prev, start)) != CharCategory.Word)\n                break;\n            start = prev;\n        }\n        while (end < length) {\n            let next = findClusterBreak(text, end);\n            if (cat(text.slice(end, next)) != CharCategory.Word)\n                break;\n            end = next;\n        }\n        return start == end ? null : EditorSelection.range(start + from, end + from);\n    }\n}\n/**\nA facet that, when enabled, causes the editor to allow multiple\nranges to be selected. Be careful though, because by default the\neditor relies on the native DOM selection, which cannot handle\nmultiple selections. An extension like\n[`drawSelection`](https://codemirror.net/6/docs/ref/#view.drawSelection) can be used to make\nsecondary selections visible to the user.\n*/\nEditorState.allowMultipleSelections = allowMultipleSelections;\n/**\nConfigures the tab size to use in this state. The first\n(highest-precedence) value of the facet is used. If no value is\ngiven, this defaults to 4.\n*/\nEditorState.tabSize = /*@__PURE__*/Facet.define({\n    combine: values => values.length ? values[0] : 4\n});\n/**\nThe line separator to use. By default, any of `\"\\n\"`, `\"\\r\\n\"`\nand `\"\\r\"` is treated as a separator when splitting lines, and\nlines are joined with `\"\\n\"`.\n\nWhen you configure a value here, only that precise separator\nwill be used, allowing you to round-trip documents through the\neditor without normalizing line separators.\n*/\nEditorState.lineSeparator = lineSeparator;\n/**\nThis facet controls the value of the\n[`readOnly`](https://codemirror.net/6/docs/ref/#state.EditorState.readOnly) getter, which is\nconsulted by commands and extensions that implement editing\nfunctionality to determine whether they should apply. It\ndefaults to false, but when its highest-precedence value is\n`true`, such functionality disables itself.\n\nNot to be confused with\n[`EditorView.editable`](https://codemirror.net/6/docs/ref/#view.EditorView^editable), which\ncontrols whether the editor's DOM is set to be editable (and\nthus focusable).\n*/\nEditorState.readOnly = readOnly;\n/**\nRegisters translation phrases. The\n[`phrase`](https://codemirror.net/6/docs/ref/#state.EditorState.phrase) method will look through\nall objects registered with this facet to find translations for\nits argument.\n*/\nEditorState.phrases = /*@__PURE__*/Facet.define({\n    compare(a, b) {\n        let kA = Object.keys(a), kB = Object.keys(b);\n        return kA.length == kB.length && kA.every(k => a[k] == b[k]);\n    }\n});\n/**\nA facet used to register [language\ndata](https://codemirror.net/6/docs/ref/#state.EditorState.languageDataAt) providers.\n*/\nEditorState.languageData = languageData;\n/**\nFacet used to register change filters, which are called for each\ntransaction (unless explicitly\n[disabled](https://codemirror.net/6/docs/ref/#state.TransactionSpec.filter)), and can suppress\npart of the transaction's changes.\n\nSuch a function can return `true` to indicate that it doesn't\nwant to do anything, `false` to completely stop the changes in\nthe transaction, or a set of ranges in which changes should be\nsuppressed. Such ranges are represented as an array of numbers,\nwith each pair of two numbers indicating the start and end of a\nrange. So for example `[10, 20, 100, 110]` suppresses changes\nbetween 10 and 20, and between 100 and 110.\n*/\nEditorState.changeFilter = changeFilter;\n/**\nFacet used to register a hook that gets a chance to update or\nreplace transaction specs before they are applied. This will\nonly be applied for transactions that don't have\n[`filter`](https://codemirror.net/6/docs/ref/#state.TransactionSpec.filter) set to `false`. You\ncan either return a single transaction spec (possibly the input\ntransaction), or an array of specs (which will be combined in\nthe same way as the arguments to\n[`EditorState.update`](https://codemirror.net/6/docs/ref/#state.EditorState.update)).\n\nWhen possible, it is recommended to avoid accessing\n[`Transaction.state`](https://codemirror.net/6/docs/ref/#state.Transaction.state) in a filter,\nsince it will force creation of a state that will then be\ndiscarded again, if the transaction is actually filtered.\n\n(This functionality should be used with care. Indiscriminately\nmodifying transaction is likely to break something or degrade\nthe user experience.)\n*/\nEditorState.transactionFilter = transactionFilter;\n/**\nThis is a more limited form of\n[`transactionFilter`](https://codemirror.net/6/docs/ref/#state.EditorState^transactionFilter),\nwhich can only add\n[annotations](https://codemirror.net/6/docs/ref/#state.TransactionSpec.annotations) and\n[effects](https://codemirror.net/6/docs/ref/#state.TransactionSpec.effects). _But_, this type\nof filter runs even if the transaction has disabled regular\n[filtering](https://codemirror.net/6/docs/ref/#state.TransactionSpec.filter), making it suitable\nfor effects that don't need to touch the changes or selection,\nbut do want to process every transaction.\n\nExtenders run _after_ filters, when both are present.\n*/\nEditorState.transactionExtender = transactionExtender;\nCompartment.reconfigure = /*@__PURE__*/StateEffect.define();\n\n/**\nUtility function for combining behaviors to fill in a config\nobject from an array of provided configs. `defaults` should hold\ndefault values for all optional fields in `Config`.\n\nThe function will, by default, error\nwhen a field gets two values that aren't `===`-equal, but you can\nprovide combine functions per field to do something else.\n*/\nfunction combineConfig(configs, defaults, // Should hold only the optional properties of Config, but I haven't managed to express that\ncombine = {}) {\n    let result = {};\n    for (let config of configs)\n        for (let key of Object.keys(config)) {\n            let value = config[key], current = result[key];\n            if (current === undefined)\n                result[key] = value;\n            else if (current === value || value === undefined) ; // No conflict\n            else if (Object.hasOwnProperty.call(combine, key))\n                result[key] = combine[key](current, value);\n            else\n                throw new Error(\"Config merge conflict for field \" + key);\n        }\n    for (let key in defaults)\n        if (result[key] === undefined)\n            result[key] = defaults[key];\n    return result;\n}\n\n/**\nEach range is associated with a value, which must inherit from\nthis class.\n*/\nclass RangeValue {\n    /**\n    Compare this value with another value. Used when comparing\n    rangesets. The default implementation compares by identity.\n    Unless you are only creating a fixed number of unique instances\n    of your value type, it is a good idea to implement this\n    properly.\n    */\n    eq(other) { return this == other; }\n    /**\n    Create a [range](https://codemirror.net/6/docs/ref/#state.Range) with this value.\n    */\n    range(from, to = from) { return Range.create(from, to, this); }\n}\nRangeValue.prototype.startSide = RangeValue.prototype.endSide = 0;\nRangeValue.prototype.point = false;\nRangeValue.prototype.mapMode = MapMode.TrackDel;\n/**\nA range associates a value with a range of positions.\n*/\nclass Range {\n    constructor(\n    /**\n    The range's start position.\n    */\n    from, \n    /**\n    Its end position.\n    */\n    to, \n    /**\n    The value associated with this range.\n    */\n    value) {\n        this.from = from;\n        this.to = to;\n        this.value = value;\n    }\n    /**\n    @internal\n    */\n    static create(from, to, value) {\n        return new Range(from, to, value);\n    }\n}\nfunction cmpRange(a, b) {\n    return a.from - b.from || a.value.startSide - b.value.startSide;\n}\nclass Chunk {\n    constructor(from, to, value, \n    // Chunks are marked with the largest point that occurs\n    // in them (or -1 for no points), so that scans that are\n    // only interested in points (such as the\n    // heightmap-related logic) can skip range-only chunks.\n    maxPoint) {\n        this.from = from;\n        this.to = to;\n        this.value = value;\n        this.maxPoint = maxPoint;\n    }\n    get length() { return this.to[this.to.length - 1]; }\n    // Find the index of the given position and side. Use the ranges'\n    // `from` pos when `end == false`, `to` when `end == true`.\n    findIndex(pos, side, end, startAt = 0) {\n        let arr = end ? this.to : this.from;\n        for (let lo = startAt, hi = arr.length;;) {\n            if (lo == hi)\n                return lo;\n            let mid = (lo + hi) >> 1;\n            let diff = arr[mid] - pos || (end ? this.value[mid].endSide : this.value[mid].startSide) - side;\n            if (mid == lo)\n                return diff >= 0 ? lo : hi;\n            if (diff >= 0)\n                hi = mid;\n            else\n                lo = mid + 1;\n        }\n    }\n    between(offset, from, to, f) {\n        for (let i = this.findIndex(from, -********** /* C.Far */, true), e = this.findIndex(to, ********** /* C.Far */, false, i); i < e; i++)\n            if (f(this.from[i] + offset, this.to[i] + offset, this.value[i]) === false)\n                return false;\n    }\n    map(offset, changes) {\n        let value = [], from = [], to = [], newPos = -1, maxPoint = -1;\n        for (let i = 0; i < this.value.length; i++) {\n            let val = this.value[i], curFrom = this.from[i] + offset, curTo = this.to[i] + offset, newFrom, newTo;\n            if (curFrom == curTo) {\n                let mapped = changes.mapPos(curFrom, val.startSide, val.mapMode);\n                if (mapped == null)\n                    continue;\n                newFrom = newTo = mapped;\n                if (val.startSide != val.endSide) {\n                    newTo = changes.mapPos(curFrom, val.endSide);\n                    if (newTo < newFrom)\n                        continue;\n                }\n            }\n            else {\n                newFrom = changes.mapPos(curFrom, val.startSide);\n                newTo = changes.mapPos(curTo, val.endSide);\n                if (newFrom > newTo || newFrom == newTo && val.startSide > 0 && val.endSide <= 0)\n                    continue;\n            }\n            if ((newTo - newFrom || val.endSide - val.startSide) < 0)\n                continue;\n            if (newPos < 0)\n                newPos = newFrom;\n            if (val.point)\n                maxPoint = Math.max(maxPoint, newTo - newFrom);\n            value.push(val);\n            from.push(newFrom - newPos);\n            to.push(newTo - newPos);\n        }\n        return { mapped: value.length ? new Chunk(from, to, value, maxPoint) : null, pos: newPos };\n    }\n}\n/**\nA range set stores a collection of [ranges](https://codemirror.net/6/docs/ref/#state.Range) in a\nway that makes them efficient to [map](https://codemirror.net/6/docs/ref/#state.RangeSet.map) and\n[update](https://codemirror.net/6/docs/ref/#state.RangeSet.update). This is an immutable data\nstructure.\n*/\nclass RangeSet {\n    constructor(\n    /**\n    @internal\n    */\n    chunkPos, \n    /**\n    @internal\n    */\n    chunk, \n    /**\n    @internal\n    */\n    nextLayer, \n    /**\n    @internal\n    */\n    maxPoint) {\n        this.chunkPos = chunkPos;\n        this.chunk = chunk;\n        this.nextLayer = nextLayer;\n        this.maxPoint = maxPoint;\n    }\n    /**\n    @internal\n    */\n    static create(chunkPos, chunk, nextLayer, maxPoint) {\n        return new RangeSet(chunkPos, chunk, nextLayer, maxPoint);\n    }\n    /**\n    @internal\n    */\n    get length() {\n        let last = this.chunk.length - 1;\n        return last < 0 ? 0 : Math.max(this.chunkEnd(last), this.nextLayer.length);\n    }\n    /**\n    The number of ranges in the set.\n    */\n    get size() {\n        if (this.isEmpty)\n            return 0;\n        let size = this.nextLayer.size;\n        for (let chunk of this.chunk)\n            size += chunk.value.length;\n        return size;\n    }\n    /**\n    @internal\n    */\n    chunkEnd(index) {\n        return this.chunkPos[index] + this.chunk[index].length;\n    }\n    /**\n    Update the range set, optionally adding new ranges or filtering\n    out existing ones.\n    \n    (Note: The type parameter is just there as a kludge to work\n    around TypeScript variance issues that prevented `RangeSet<X>`\n    from being a subtype of `RangeSet<Y>` when `X` is a subtype of\n    `Y`.)\n    */\n    update(updateSpec) {\n        let { add = [], sort = false, filterFrom = 0, filterTo = this.length } = updateSpec;\n        let filter = updateSpec.filter;\n        if (add.length == 0 && !filter)\n            return this;\n        if (sort)\n            add = add.slice().sort(cmpRange);\n        if (this.isEmpty)\n            return add.length ? RangeSet.of(add) : this;\n        let cur = new LayerCursor(this, null, -1).goto(0), i = 0, spill = [];\n        let builder = new RangeSetBuilder();\n        while (cur.value || i < add.length) {\n            if (i < add.length && (cur.from - add[i].from || cur.startSide - add[i].value.startSide) >= 0) {\n                let range = add[i++];\n                if (!builder.addInner(range.from, range.to, range.value))\n                    spill.push(range);\n            }\n            else if (cur.rangeIndex == 1 && cur.chunkIndex < this.chunk.length &&\n                (i == add.length || this.chunkEnd(cur.chunkIndex) < add[i].from) &&\n                (!filter || filterFrom > this.chunkEnd(cur.chunkIndex) || filterTo < this.chunkPos[cur.chunkIndex]) &&\n                builder.addChunk(this.chunkPos[cur.chunkIndex], this.chunk[cur.chunkIndex])) {\n                cur.nextChunk();\n            }\n            else {\n                if (!filter || filterFrom > cur.to || filterTo < cur.from || filter(cur.from, cur.to, cur.value)) {\n                    if (!builder.addInner(cur.from, cur.to, cur.value))\n                        spill.push(Range.create(cur.from, cur.to, cur.value));\n                }\n                cur.next();\n            }\n        }\n        return builder.finishInner(this.nextLayer.isEmpty && !spill.length ? RangeSet.empty\n            : this.nextLayer.update({ add: spill, filter, filterFrom, filterTo }));\n    }\n    /**\n    Map this range set through a set of changes, return the new set.\n    */\n    map(changes) {\n        if (changes.empty || this.isEmpty)\n            return this;\n        let chunks = [], chunkPos = [], maxPoint = -1;\n        for (let i = 0; i < this.chunk.length; i++) {\n            let start = this.chunkPos[i], chunk = this.chunk[i];\n            let touch = changes.touchesRange(start, start + chunk.length);\n            if (touch === false) {\n                maxPoint = Math.max(maxPoint, chunk.maxPoint);\n                chunks.push(chunk);\n                chunkPos.push(changes.mapPos(start));\n            }\n            else if (touch === true) {\n                let { mapped, pos } = chunk.map(start, changes);\n                if (mapped) {\n                    maxPoint = Math.max(maxPoint, mapped.maxPoint);\n                    chunks.push(mapped);\n                    chunkPos.push(pos);\n                }\n            }\n        }\n        let next = this.nextLayer.map(changes);\n        return chunks.length == 0 ? next : new RangeSet(chunkPos, chunks, next || RangeSet.empty, maxPoint);\n    }\n    /**\n    Iterate over the ranges that touch the region `from` to `to`,\n    calling `f` for each. There is no guarantee that the ranges will\n    be reported in any specific order. When the callback returns\n    `false`, iteration stops.\n    */\n    between(from, to, f) {\n        if (this.isEmpty)\n            return;\n        for (let i = 0; i < this.chunk.length; i++) {\n            let start = this.chunkPos[i], chunk = this.chunk[i];\n            if (to >= start && from <= start + chunk.length &&\n                chunk.between(start, from - start, to - start, f) === false)\n                return;\n        }\n        this.nextLayer.between(from, to, f);\n    }\n    /**\n    Iterate over the ranges in this set, in order, including all\n    ranges that end at or after `from`.\n    */\n    iter(from = 0) {\n        return HeapCursor.from([this]).goto(from);\n    }\n    /**\n    @internal\n    */\n    get isEmpty() { return this.nextLayer == this; }\n    /**\n    Iterate over the ranges in a collection of sets, in order,\n    starting from `from`.\n    */\n    static iter(sets, from = 0) {\n        return HeapCursor.from(sets).goto(from);\n    }\n    /**\n    Iterate over two groups of sets, calling methods on `comparator`\n    to notify it of possible differences.\n    */\n    static compare(oldSets, newSets, \n    /**\n    This indicates how the underlying data changed between these\n    ranges, and is needed to synchronize the iteration.\n    */\n    textDiff, comparator, \n    /**\n    Can be used to ignore all non-point ranges, and points below\n    the given size. When -1, all ranges are compared.\n    */\n    minPointSize = -1) {\n        let a = oldSets.filter(set => set.maxPoint > 0 || !set.isEmpty && set.maxPoint >= minPointSize);\n        let b = newSets.filter(set => set.maxPoint > 0 || !set.isEmpty && set.maxPoint >= minPointSize);\n        let sharedChunks = findSharedChunks(a, b, textDiff);\n        let sideA = new SpanCursor(a, sharedChunks, minPointSize);\n        let sideB = new SpanCursor(b, sharedChunks, minPointSize);\n        textDiff.iterGaps((fromA, fromB, length) => compare(sideA, fromA, sideB, fromB, length, comparator));\n        if (textDiff.empty && textDiff.length == 0)\n            compare(sideA, 0, sideB, 0, 0, comparator);\n    }\n    /**\n    Compare the contents of two groups of range sets, returning true\n    if they are equivalent in the given range.\n    */\n    static eq(oldSets, newSets, from = 0, to) {\n        if (to == null)\n            to = ********** /* C.Far */ - 1;\n        let a = oldSets.filter(set => !set.isEmpty && newSets.indexOf(set) < 0);\n        let b = newSets.filter(set => !set.isEmpty && oldSets.indexOf(set) < 0);\n        if (a.length != b.length)\n            return false;\n        if (!a.length)\n            return true;\n        let sharedChunks = findSharedChunks(a, b);\n        let sideA = new SpanCursor(a, sharedChunks, 0).goto(from), sideB = new SpanCursor(b, sharedChunks, 0).goto(from);\n        for (;;) {\n            if (sideA.to != sideB.to ||\n                !sameValues(sideA.active, sideB.active) ||\n                sideA.point && (!sideB.point || !sideA.point.eq(sideB.point)))\n                return false;\n            if (sideA.to > to)\n                return true;\n            sideA.next();\n            sideB.next();\n        }\n    }\n    /**\n    Iterate over a group of range sets at the same time, notifying\n    the iterator about the ranges covering every given piece of\n    content. Returns the open count (see\n    [`SpanIterator.span`](https://codemirror.net/6/docs/ref/#state.SpanIterator.span)) at the end\n    of the iteration.\n    */\n    static spans(sets, from, to, iterator, \n    /**\n    When given and greater than -1, only points of at least this\n    size are taken into account.\n    */\n    minPointSize = -1) {\n        let cursor = new SpanCursor(sets, null, minPointSize).goto(from), pos = from;\n        let openRanges = cursor.openStart;\n        for (;;) {\n            let curTo = Math.min(cursor.to, to);\n            if (cursor.point) {\n                let active = cursor.activeForPoint(cursor.to);\n                let openCount = cursor.pointFrom < from ? active.length + 1\n                    : cursor.point.startSide < 0 ? active.length\n                        : Math.min(active.length, openRanges);\n                iterator.point(pos, curTo, cursor.point, active, openCount, cursor.pointRank);\n                openRanges = Math.min(cursor.openEnd(curTo), active.length);\n            }\n            else if (curTo > pos) {\n                iterator.span(pos, curTo, cursor.active, openRanges);\n                openRanges = cursor.openEnd(curTo);\n            }\n            if (cursor.to > to)\n                return openRanges + (cursor.point && cursor.to > to ? 1 : 0);\n            pos = cursor.to;\n            cursor.next();\n        }\n    }\n    /**\n    Create a range set for the given range or array of ranges. By\n    default, this expects the ranges to be _sorted_ (by start\n    position and, if two start at the same position,\n    `value.startSide`). You can pass `true` as second argument to\n    cause the method to sort them.\n    */\n    static of(ranges, sort = false) {\n        let build = new RangeSetBuilder();\n        for (let range of ranges instanceof Range ? [ranges] : sort ? lazySort(ranges) : ranges)\n            build.add(range.from, range.to, range.value);\n        return build.finish();\n    }\n    /**\n    Join an array of range sets into a single set.\n    */\n    static join(sets) {\n        if (!sets.length)\n            return RangeSet.empty;\n        let result = sets[sets.length - 1];\n        for (let i = sets.length - 2; i >= 0; i--) {\n            for (let layer = sets[i]; layer != RangeSet.empty; layer = layer.nextLayer)\n                result = new RangeSet(layer.chunkPos, layer.chunk, result, Math.max(layer.maxPoint, result.maxPoint));\n        }\n        return result;\n    }\n}\n/**\nThe empty set of ranges.\n*/\nRangeSet.empty = /*@__PURE__*/new RangeSet([], [], null, -1);\nfunction lazySort(ranges) {\n    if (ranges.length > 1)\n        for (let prev = ranges[0], i = 1; i < ranges.length; i++) {\n            let cur = ranges[i];\n            if (cmpRange(prev, cur) > 0)\n                return ranges.slice().sort(cmpRange);\n            prev = cur;\n        }\n    return ranges;\n}\nRangeSet.empty.nextLayer = RangeSet.empty;\n/**\nA range set builder is a data structure that helps build up a\n[range set](https://codemirror.net/6/docs/ref/#state.RangeSet) directly, without first allocating\nan array of [`Range`](https://codemirror.net/6/docs/ref/#state.Range) objects.\n*/\nclass RangeSetBuilder {\n    finishChunk(newArrays) {\n        this.chunks.push(new Chunk(this.from, this.to, this.value, this.maxPoint));\n        this.chunkPos.push(this.chunkStart);\n        this.chunkStart = -1;\n        this.setMaxPoint = Math.max(this.setMaxPoint, this.maxPoint);\n        this.maxPoint = -1;\n        if (newArrays) {\n            this.from = [];\n            this.to = [];\n            this.value = [];\n        }\n    }\n    /**\n    Create an empty builder.\n    */\n    constructor() {\n        this.chunks = [];\n        this.chunkPos = [];\n        this.chunkStart = -1;\n        this.last = null;\n        this.lastFrom = -********** /* C.Far */;\n        this.lastTo = -********** /* C.Far */;\n        this.from = [];\n        this.to = [];\n        this.value = [];\n        this.maxPoint = -1;\n        this.setMaxPoint = -1;\n        this.nextLayer = null;\n    }\n    /**\n    Add a range. Ranges should be added in sorted (by `from` and\n    `value.startSide`) order.\n    */\n    add(from, to, value) {\n        if (!this.addInner(from, to, value))\n            (this.nextLayer || (this.nextLayer = new RangeSetBuilder)).add(from, to, value);\n    }\n    /**\n    @internal\n    */\n    addInner(from, to, value) {\n        let diff = from - this.lastTo || value.startSide - this.last.endSide;\n        if (diff <= 0 && (from - this.lastFrom || value.startSide - this.last.startSide) < 0)\n            throw new Error(\"Ranges must be added sorted by `from` position and `startSide`\");\n        if (diff < 0)\n            return false;\n        if (this.from.length == 250 /* C.ChunkSize */)\n            this.finishChunk(true);\n        if (this.chunkStart < 0)\n            this.chunkStart = from;\n        this.from.push(from - this.chunkStart);\n        this.to.push(to - this.chunkStart);\n        this.last = value;\n        this.lastFrom = from;\n        this.lastTo = to;\n        this.value.push(value);\n        if (value.point)\n            this.maxPoint = Math.max(this.maxPoint, to - from);\n        return true;\n    }\n    /**\n    @internal\n    */\n    addChunk(from, chunk) {\n        if ((from - this.lastTo || chunk.value[0].startSide - this.last.endSide) < 0)\n            return false;\n        if (this.from.length)\n            this.finishChunk(true);\n        this.setMaxPoint = Math.max(this.setMaxPoint, chunk.maxPoint);\n        this.chunks.push(chunk);\n        this.chunkPos.push(from);\n        let last = chunk.value.length - 1;\n        this.last = chunk.value[last];\n        this.lastFrom = chunk.from[last] + from;\n        this.lastTo = chunk.to[last] + from;\n        return true;\n    }\n    /**\n    Finish the range set. Returns the new set. The builder can't be\n    used anymore after this has been called.\n    */\n    finish() { return this.finishInner(RangeSet.empty); }\n    /**\n    @internal\n    */\n    finishInner(next) {\n        if (this.from.length)\n            this.finishChunk(false);\n        if (this.chunks.length == 0)\n            return next;\n        let result = RangeSet.create(this.chunkPos, this.chunks, this.nextLayer ? this.nextLayer.finishInner(next) : next, this.setMaxPoint);\n        this.from = null; // Make sure further `add` calls produce errors\n        return result;\n    }\n}\nfunction findSharedChunks(a, b, textDiff) {\n    let inA = new Map();\n    for (let set of a)\n        for (let i = 0; i < set.chunk.length; i++)\n            if (set.chunk[i].maxPoint <= 0)\n                inA.set(set.chunk[i], set.chunkPos[i]);\n    let shared = new Set();\n    for (let set of b)\n        for (let i = 0; i < set.chunk.length; i++) {\n            let known = inA.get(set.chunk[i]);\n            if (known != null && (textDiff ? textDiff.mapPos(known) : known) == set.chunkPos[i] &&\n                !(textDiff === null || textDiff === void 0 ? void 0 : textDiff.touchesRange(known, known + set.chunk[i].length)))\n                shared.add(set.chunk[i]);\n        }\n    return shared;\n}\nclass LayerCursor {\n    constructor(layer, skip, minPoint, rank = 0) {\n        this.layer = layer;\n        this.skip = skip;\n        this.minPoint = minPoint;\n        this.rank = rank;\n    }\n    get startSide() { return this.value ? this.value.startSide : 0; }\n    get endSide() { return this.value ? this.value.endSide : 0; }\n    goto(pos, side = -********** /* C.Far */) {\n        this.chunkIndex = this.rangeIndex = 0;\n        this.gotoInner(pos, side, false);\n        return this;\n    }\n    gotoInner(pos, side, forward) {\n        while (this.chunkIndex < this.layer.chunk.length) {\n            let next = this.layer.chunk[this.chunkIndex];\n            if (!(this.skip && this.skip.has(next) ||\n                this.layer.chunkEnd(this.chunkIndex) < pos ||\n                next.maxPoint < this.minPoint))\n                break;\n            this.chunkIndex++;\n            forward = false;\n        }\n        if (this.chunkIndex < this.layer.chunk.length) {\n            let rangeIndex = this.layer.chunk[this.chunkIndex].findIndex(pos - this.layer.chunkPos[this.chunkIndex], side, true);\n            if (!forward || this.rangeIndex < rangeIndex)\n                this.setRangeIndex(rangeIndex);\n        }\n        this.next();\n    }\n    forward(pos, side) {\n        if ((this.to - pos || this.endSide - side) < 0)\n            this.gotoInner(pos, side, true);\n    }\n    next() {\n        for (;;) {\n            if (this.chunkIndex == this.layer.chunk.length) {\n                this.from = this.to = ********** /* C.Far */;\n                this.value = null;\n                break;\n            }\n            else {\n                let chunkPos = this.layer.chunkPos[this.chunkIndex], chunk = this.layer.chunk[this.chunkIndex];\n                let from = chunkPos + chunk.from[this.rangeIndex];\n                this.from = from;\n                this.to = chunkPos + chunk.to[this.rangeIndex];\n                this.value = chunk.value[this.rangeIndex];\n                this.setRangeIndex(this.rangeIndex + 1);\n                if (this.minPoint < 0 || this.value.point && this.to - this.from >= this.minPoint)\n                    break;\n            }\n        }\n    }\n    setRangeIndex(index) {\n        if (index == this.layer.chunk[this.chunkIndex].value.length) {\n            this.chunkIndex++;\n            if (this.skip) {\n                while (this.chunkIndex < this.layer.chunk.length && this.skip.has(this.layer.chunk[this.chunkIndex]))\n                    this.chunkIndex++;\n            }\n            this.rangeIndex = 0;\n        }\n        else {\n            this.rangeIndex = index;\n        }\n    }\n    nextChunk() {\n        this.chunkIndex++;\n        this.rangeIndex = 0;\n        this.next();\n    }\n    compare(other) {\n        return this.from - other.from || this.startSide - other.startSide || this.rank - other.rank ||\n            this.to - other.to || this.endSide - other.endSide;\n    }\n}\nclass HeapCursor {\n    constructor(heap) {\n        this.heap = heap;\n    }\n    static from(sets, skip = null, minPoint = -1) {\n        let heap = [];\n        for (let i = 0; i < sets.length; i++) {\n            for (let cur = sets[i]; !cur.isEmpty; cur = cur.nextLayer) {\n                if (cur.maxPoint >= minPoint)\n                    heap.push(new LayerCursor(cur, skip, minPoint, i));\n            }\n        }\n        return heap.length == 1 ? heap[0] : new HeapCursor(heap);\n    }\n    get startSide() { return this.value ? this.value.startSide : 0; }\n    goto(pos, side = -********** /* C.Far */) {\n        for (let cur of this.heap)\n            cur.goto(pos, side);\n        for (let i = this.heap.length >> 1; i >= 0; i--)\n            heapBubble(this.heap, i);\n        this.next();\n        return this;\n    }\n    forward(pos, side) {\n        for (let cur of this.heap)\n            cur.forward(pos, side);\n        for (let i = this.heap.length >> 1; i >= 0; i--)\n            heapBubble(this.heap, i);\n        if ((this.to - pos || this.value.endSide - side) < 0)\n            this.next();\n    }\n    next() {\n        if (this.heap.length == 0) {\n            this.from = this.to = ********** /* C.Far */;\n            this.value = null;\n            this.rank = -1;\n        }\n        else {\n            let top = this.heap[0];\n            this.from = top.from;\n            this.to = top.to;\n            this.value = top.value;\n            this.rank = top.rank;\n            if (top.value)\n                top.next();\n            heapBubble(this.heap, 0);\n        }\n    }\n}\nfunction heapBubble(heap, index) {\n    for (let cur = heap[index];;) {\n        let childIndex = (index << 1) + 1;\n        if (childIndex >= heap.length)\n            break;\n        let child = heap[childIndex];\n        if (childIndex + 1 < heap.length && child.compare(heap[childIndex + 1]) >= 0) {\n            child = heap[childIndex + 1];\n            childIndex++;\n        }\n        if (cur.compare(child) < 0)\n            break;\n        heap[childIndex] = cur;\n        heap[index] = child;\n        index = childIndex;\n    }\n}\nclass SpanCursor {\n    constructor(sets, skip, minPoint) {\n        this.minPoint = minPoint;\n        this.active = [];\n        this.activeTo = [];\n        this.activeRank = [];\n        this.minActive = -1;\n        // A currently active point range, if any\n        this.point = null;\n        this.pointFrom = 0;\n        this.pointRank = 0;\n        this.to = -********** /* C.Far */;\n        this.endSide = 0;\n        // The amount of open active ranges at the start of the iterator.\n        // Not including points.\n        this.openStart = -1;\n        this.cursor = HeapCursor.from(sets, skip, minPoint);\n    }\n    goto(pos, side = -********** /* C.Far */) {\n        this.cursor.goto(pos, side);\n        this.active.length = this.activeTo.length = this.activeRank.length = 0;\n        this.minActive = -1;\n        this.to = pos;\n        this.endSide = side;\n        this.openStart = -1;\n        this.next();\n        return this;\n    }\n    forward(pos, side) {\n        while (this.minActive > -1 && (this.activeTo[this.minActive] - pos || this.active[this.minActive].endSide - side) < 0)\n            this.removeActive(this.minActive);\n        this.cursor.forward(pos, side);\n    }\n    removeActive(index) {\n        remove(this.active, index);\n        remove(this.activeTo, index);\n        remove(this.activeRank, index);\n        this.minActive = findMinIndex(this.active, this.activeTo);\n    }\n    addActive(trackOpen) {\n        let i = 0, { value, to, rank } = this.cursor;\n        // Organize active marks by rank first, then by size\n        while (i < this.activeRank.length && (rank - this.activeRank[i] || to - this.activeTo[i]) > 0)\n            i++;\n        insert(this.active, i, value);\n        insert(this.activeTo, i, to);\n        insert(this.activeRank, i, rank);\n        if (trackOpen)\n            insert(trackOpen, i, this.cursor.from);\n        this.minActive = findMinIndex(this.active, this.activeTo);\n    }\n    // After calling this, if `this.point` != null, the next range is a\n    // point. Otherwise, it's a regular range, covered by `this.active`.\n    next() {\n        let from = this.to, wasPoint = this.point;\n        this.point = null;\n        let trackOpen = this.openStart < 0 ? [] : null;\n        for (;;) {\n            let a = this.minActive;\n            if (a > -1 && (this.activeTo[a] - this.cursor.from || this.active[a].endSide - this.cursor.startSide) < 0) {\n                if (this.activeTo[a] > from) {\n                    this.to = this.activeTo[a];\n                    this.endSide = this.active[a].endSide;\n                    break;\n                }\n                this.removeActive(a);\n                if (trackOpen)\n                    remove(trackOpen, a);\n            }\n            else if (!this.cursor.value) {\n                this.to = this.endSide = ********** /* C.Far */;\n                break;\n            }\n            else if (this.cursor.from > from) {\n                this.to = this.cursor.from;\n                this.endSide = this.cursor.startSide;\n                break;\n            }\n            else {\n                let nextVal = this.cursor.value;\n                if (!nextVal.point) { // Opening a range\n                    this.addActive(trackOpen);\n                    this.cursor.next();\n                }\n                else if (wasPoint && this.cursor.to == this.to && this.cursor.from < this.cursor.to) {\n                    // Ignore any non-empty points that end precisely at the end of the prev point\n                    this.cursor.next();\n                }\n                else { // New point\n                    this.point = nextVal;\n                    this.pointFrom = this.cursor.from;\n                    this.pointRank = this.cursor.rank;\n                    this.to = this.cursor.to;\n                    this.endSide = nextVal.endSide;\n                    this.cursor.next();\n                    this.forward(this.to, this.endSide);\n                    break;\n                }\n            }\n        }\n        if (trackOpen) {\n            this.openStart = 0;\n            for (let i = trackOpen.length - 1; i >= 0 && trackOpen[i] < from; i--)\n                this.openStart++;\n        }\n    }\n    activeForPoint(to) {\n        if (!this.active.length)\n            return this.active;\n        let active = [];\n        for (let i = this.active.length - 1; i >= 0; i--) {\n            if (this.activeRank[i] < this.pointRank)\n                break;\n            if (this.activeTo[i] > to || this.activeTo[i] == to && this.active[i].endSide >= this.point.endSide)\n                active.push(this.active[i]);\n        }\n        return active.reverse();\n    }\n    openEnd(to) {\n        let open = 0;\n        for (let i = this.activeTo.length - 1; i >= 0 && this.activeTo[i] > to; i--)\n            open++;\n        return open;\n    }\n}\nfunction compare(a, startA, b, startB, length, comparator) {\n    a.goto(startA);\n    b.goto(startB);\n    let endB = startB + length;\n    let pos = startB, dPos = startB - startA;\n    for (;;) {\n        let dEnd = (a.to + dPos) - b.to, diff = dEnd || a.endSide - b.endSide;\n        let end = diff < 0 ? a.to + dPos : b.to, clipEnd = Math.min(end, endB);\n        if (a.point || b.point) {\n            if (!(a.point && b.point && (a.point == b.point || a.point.eq(b.point)) &&\n                sameValues(a.activeForPoint(a.to), b.activeForPoint(b.to))))\n                comparator.comparePoint(pos, clipEnd, a.point, b.point);\n        }\n        else {\n            if (clipEnd > pos && !sameValues(a.active, b.active))\n                comparator.compareRange(pos, clipEnd, a.active, b.active);\n        }\n        if (end > endB)\n            break;\n        if ((dEnd || a.openEnd != b.openEnd) && comparator.boundChange)\n            comparator.boundChange(end);\n        pos = end;\n        if (diff <= 0)\n            a.next();\n        if (diff >= 0)\n            b.next();\n    }\n}\nfunction sameValues(a, b) {\n    if (a.length != b.length)\n        return false;\n    for (let i = 0; i < a.length; i++)\n        if (a[i] != b[i] && !a[i].eq(b[i]))\n            return false;\n    return true;\n}\nfunction remove(array, index) {\n    for (let i = index, e = array.length - 1; i < e; i++)\n        array[i] = array[i + 1];\n    array.pop();\n}\nfunction insert(array, index, value) {\n    for (let i = array.length - 1; i >= index; i--)\n        array[i + 1] = array[i];\n    array[index] = value;\n}\nfunction findMinIndex(value, array) {\n    let found = -1, foundPos = ********** /* C.Far */;\n    for (let i = 0; i < array.length; i++)\n        if ((array[i] - foundPos || value[i].endSide - value[found].endSide) < 0) {\n            found = i;\n            foundPos = array[i];\n        }\n    return found;\n}\n\n/**\nCount the column position at the given offset into the string,\ntaking extending characters and tab size into account.\n*/\nfunction countColumn(string, tabSize, to = string.length) {\n    let n = 0;\n    for (let i = 0; i < to && i < string.length;) {\n        if (string.charCodeAt(i) == 9) {\n            n += tabSize - (n % tabSize);\n            i++;\n        }\n        else {\n            n++;\n            i = findClusterBreak(string, i);\n        }\n    }\n    return n;\n}\n/**\nFind the offset that corresponds to the given column position in a\nstring, taking extending characters and tab size into account. By\ndefault, the string length is returned when it is too short to\nreach the column. Pass `strict` true to make it return -1 in that\nsituation.\n*/\nfunction findColumn(string, col, tabSize, strict) {\n    for (let i = 0, n = 0;;) {\n        if (n >= col)\n            return i;\n        if (i == string.length)\n            break;\n        n += string.charCodeAt(i) == 9 ? tabSize - (n % tabSize) : 1;\n        i = findClusterBreak(string, i);\n    }\n    return strict === true ? -1 : string.length;\n}\n\nexport { Annotation, AnnotationType, ChangeDesc, ChangeSet, CharCategory, Compartment, EditorSelection, EditorState, Facet, Line, MapMode, Prec, Range, RangeSet, RangeSetBuilder, RangeValue, SelectionRange, StateEffect, StateEffectType, StateField, Text, Transaction, codePointAt, codePointSize, combineConfig, countColumn, findClusterBreak, findColumn, fromCodePoint };\n"], "mappings": ";AAEA,IAAI,YAAY,CAAC;AAAjB,IAAoB,UAAU,CAAC;AAAA,CAE7B,MAAM;AAON,MAAI,UAAU,izCAAizC,MAAM,GAAG,EAAE,IAAI,OAAK,IAAI,SAAS,GAAG,EAAE,IAAI,CAAC;AAC12C,WAAS,IAAI,GAAG,IAAI,GAAG,IAAI,QAAQ,QAAQ;AACzC,KAAC,IAAI,IAAI,UAAU,WAAW,KAAK,IAAI,IAAI,QAAQ,CAAC,CAAC;AACzD,GAAG;AAEI,SAAS,gBAAgB,MAAM;AACpC,MAAI,OAAO,IAAK,QAAO;AACvB,WAAS,OAAO,GAAG,KAAK,UAAU,YAAU;AAC1C,QAAI,MAAO,OAAO,MAAO;AACzB,QAAI,OAAO,UAAU,GAAG,EAAG,MAAK;AAAA,aACvB,QAAQ,QAAQ,GAAG,EAAG,QAAO,MAAM;AAAA,QACvC,QAAO;AACZ,QAAI,QAAQ,GAAI,QAAO;AAAA,EACzB;AACF;AAEA,SAAS,oBAAoB,MAAM;AACjC,SAAO,QAAQ,UAAW,QAAQ;AACpC;AASA,IAAM,MAAM;AAEL,SAAS,iBAAiB,KAAK,KAAK,UAAU,MAAM,mBAAmB,MAAM;AAClF,UAAQ,UAAU,mBAAmB,kBAAkB,KAAK,KAAK,gBAAgB;AACnF;AAEA,SAAS,iBAAiB,KAAK,KAAK,kBAAkB;AACpD,MAAI,OAAO,IAAI,OAAQ,QAAO;AAE9B,MAAI,OAAO,aAAa,IAAI,WAAW,GAAG,CAAC,KAAK,cAAc,IAAI,WAAW,MAAM,CAAC,CAAC,EAAG;AACxF,MAAI,OAAO,YAAY,KAAK,GAAG;AAC/B,SAAO,cAAc,IAAI;AACzB,SAAO,MAAM,IAAI,QAAQ;AACvB,QAAI,OAAO,YAAY,KAAK,GAAG;AAC/B,QAAI,QAAQ,OAAO,QAAQ,OAAO,oBAAoB,gBAAgB,IAAI,GAAG;AAC3E,aAAO,cAAc,IAAI;AACzB,aAAO;AAAA,IACT,WAAW,oBAAoB,IAAI,GAAG;AACpC,UAAI,cAAc,GAAG,IAAI,MAAM;AAC/B,aAAO,KAAK,KAAK,oBAAoB,YAAY,KAAK,CAAC,CAAC,GAAG;AAAE;AAAe,aAAK;AAAA,MAAE;AACnF,UAAI,cAAc,KAAK,EAAG;AAAA,UACrB,QAAO;AAAA,IACd,OAAO;AACL;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,iBAAiB,KAAK,KAAK,kBAAkB;AACpD,SAAO,MAAM,GAAG;AACd,QAAI,QAAQ,iBAAiB,KAAK,MAAM,GAAG,gBAAgB;AAC3D,QAAI,QAAQ,IAAK,QAAO;AACxB;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,YAAY,KAAK,KAAK;AAC7B,MAAI,QAAQ,IAAI,WAAW,GAAG;AAC9B,MAAI,CAAC,cAAc,KAAK,KAAK,MAAM,KAAK,IAAI,OAAQ,QAAO;AAC3D,MAAI,QAAQ,IAAI,WAAW,MAAM,CAAC;AAClC,MAAI,CAAC,aAAa,KAAK,EAAG,QAAO;AACjC,UAAS,QAAQ,SAAW,OAAO,QAAQ,SAAU;AACvD;AAEA,SAAS,aAAa,IAAI;AAAE,SAAO,MAAM,SAAU,KAAK;AAAO;AAC/D,SAAS,cAAc,IAAI;AAAE,SAAO,MAAM,SAAU,KAAK;AAAO;AAChE,SAAS,cAAc,MAAM;AAAE,SAAO,OAAO,QAAU,IAAI;AAAE;;;ACjF7D,IAAM,OAAN,MAAM,MAAK;AAAA;AAAA;AAAA;AAAA,EAIP,OAAO,KAAK;AACR,QAAI,MAAM,KAAK,MAAM,KAAK;AACtB,YAAM,IAAI,WAAW,oBAAoB,GAAG,0BAA0B,KAAK,MAAM,EAAE;AACvF,WAAO,KAAK,UAAU,KAAK,OAAO,GAAG,CAAC;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAIA,KAAK,GAAG;AACJ,QAAI,IAAI,KAAK,IAAI,KAAK;AAClB,YAAM,IAAI,WAAW,uBAAuB,CAAC,OAAO,KAAK,KAAK,gBAAgB;AAClF,WAAO,KAAK,UAAU,GAAG,MAAM,GAAG,CAAC;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,MAAM,IAAI,MAAM;AACpB,KAAC,MAAM,EAAE,IAAI,KAAK,MAAM,MAAM,EAAE;AAChC,QAAI,QAAQ,CAAC;AACb,SAAK;AAAA,MAAU;AAAA,MAAG;AAAA,MAAM;AAAA,MAAO;AAAA;AAAA,IAAe;AAC9C,QAAI,KAAK;AACL,WAAK;AAAA,QAAU;AAAA,QAAG,KAAK;AAAA,QAAQ;AAAA,QAAO,IAAoB;AAAA;AAAA,MAAe;AAC7E,SAAK;AAAA,MAAU;AAAA,MAAI,KAAK;AAAA,MAAQ;AAAA,MAAO;AAAA;AAAA,IAAiB;AACxD,WAAO,SAAS,KAAK,OAAO,KAAK,UAAU,KAAK,QAAQ,KAAK,MAAM;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,OAAO;AACV,WAAO,KAAK,QAAQ,KAAK,QAAQ,KAAK,QAAQ,KAAK;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,MAAM,KAAK,KAAK,QAAQ;AAC1B,KAAC,MAAM,EAAE,IAAI,KAAK,MAAM,MAAM,EAAE;AAChC,QAAI,QAAQ,CAAC;AACb,SAAK,UAAU,MAAM,IAAI,OAAO,CAAC;AACjC,WAAO,SAAS,KAAK,OAAO,KAAK,IAAI;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA,EAIA,GAAG,OAAO;AACN,QAAI,SAAS;AACT,aAAO;AACX,QAAI,MAAM,UAAU,KAAK,UAAU,MAAM,SAAS,KAAK;AACnD,aAAO;AACX,QAAI,QAAQ,KAAK,cAAc,OAAO,CAAC,GAAG,MAAM,KAAK,SAAS,KAAK,cAAc,OAAO,EAAE;AAC1F,QAAI,IAAI,IAAI,cAAc,IAAI,GAAG,IAAI,IAAI,cAAc,KAAK;AAC5D,aAAS,OAAO,OAAO,MAAM,WAAS;AAClC,QAAE,KAAK,IAAI;AACX,QAAE,KAAK,IAAI;AACX,aAAO;AACP,UAAI,EAAE,aAAa,EAAE,aAAa,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE;AAC/D,eAAO;AACX,aAAO,EAAE,MAAM;AACf,UAAI,EAAE,QAAQ,OAAO;AACjB,eAAO;AAAA,IACf;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,KAAK,MAAM,GAAG;AAAE,WAAO,IAAI,cAAc,MAAM,GAAG;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrD,UAAU,MAAM,KAAK,KAAK,QAAQ;AAAE,WAAO,IAAI,kBAAkB,MAAM,MAAM,EAAE;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQlF,UAAU,MAAM,IAAI;AAChB,QAAI;AACJ,QAAI,QAAQ,MAAM;AACd,cAAQ,KAAK,KAAK;AAAA,IACtB,OACK;AACD,UAAI,MAAM;AACN,aAAK,KAAK,QAAQ;AACtB,UAAI,QAAQ,KAAK,KAAK,IAAI,EAAE;AAC5B,cAAQ,KAAK,UAAU,OAAO,KAAK,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,KAAK,SAAS,MAAM,IAAI,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE,EAAE,CAAC;AAAA,IAC1H;AACA,WAAO,IAAI,WAAW,KAAK;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AAAE,WAAO,KAAK,YAAY,CAAC;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzC,SAAS;AACL,QAAI,QAAQ,CAAC;AACb,SAAK,QAAQ,KAAK;AAClB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AAAA,EAAE;AAAA;AAAA;AAAA;AAAA,EAIhB,OAAO,GAAG,MAAM;AACZ,QAAI,KAAK,UAAU;AACf,YAAM,IAAI,WAAW,wCAAwC;AACjE,QAAI,KAAK,UAAU,KAAK,CAAC,KAAK,CAAC;AAC3B,aAAO,MAAK;AAChB,WAAO,KAAK,UAAU,KAAuB,IAAI,SAAS,IAAI,IAAI,SAAS,KAAK,SAAS,MAAM,MAAM,CAAC,CAAC,CAAC;AAAA,EAC5G;AACJ;AAIA,IAAM,WAAN,MAAM,kBAAiB,KAAK;AAAA,EACxB,YAAY,MAAM,SAAS,WAAW,IAAI,GAAG;AACzC,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,IAAI,QAAQ;AAAE,WAAO,KAAK,KAAK;AAAA,EAAQ;AAAA,EACvC,IAAI,WAAW;AAAE,WAAO;AAAA,EAAM;AAAA,EAC9B,UAAU,QAAQ,QAAQ,MAAM,QAAQ;AACpC,aAAS,IAAI,KAAI,KAAK;AAClB,UAAI,SAAS,KAAK,KAAK,CAAC,GAAG,MAAM,SAAS,OAAO;AACjD,WAAK,SAAS,OAAO,QAAQ;AACzB,eAAO,IAAI,KAAK,QAAQ,KAAK,MAAM,MAAM;AAC7C,eAAS,MAAM;AACf;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,UAAU,MAAM,IAAI,QAAQ,MAAM;AAC9B,QAAI,OAAO,QAAQ,KAAK,MAAM,KAAK,SAAS,OACtC,IAAI,UAAS,UAAU,KAAK,MAAM,MAAM,EAAE,GAAG,KAAK,IAAI,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC;AAChG,QAAI,OAAO,GAAmB;AAC1B,UAAI,OAAO,OAAO,IAAI;AACtB,UAAI,SAAS,WAAW,KAAK,MAAM,KAAK,KAAK,MAAM,GAAG,GAAG,KAAK,MAAM;AACpE,UAAI,OAAO,UAAU,IAAsB;AACvC,eAAO,KAAK,IAAI,UAAS,QAAQ,KAAK,SAAS,KAAK,MAAM,CAAC;AAAA,MAC/D,OACK;AACD,YAAI,MAAM,OAAO,UAAU;AAC3B,eAAO,KAAK,IAAI,UAAS,OAAO,MAAM,GAAG,GAAG,CAAC,GAAG,IAAI,UAAS,OAAO,MAAM,GAAG,CAAC,CAAC;AAAA,MACnF;AAAA,IACJ,OACK;AACD,aAAO,KAAK,IAAI;AAAA,IACpB;AAAA,EACJ;AAAA,EACA,QAAQ,MAAM,IAAI,MAAM;AACpB,QAAI,EAAE,gBAAgB;AAClB,aAAO,MAAM,QAAQ,MAAM,IAAI,IAAI;AACvC,KAAC,MAAM,EAAE,IAAI,KAAK,MAAM,MAAM,EAAE;AAChC,QAAI,QAAQ,WAAW,KAAK,MAAM,WAAW,KAAK,MAAM,UAAU,KAAK,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE;AAC1F,QAAI,SAAS,KAAK,SAAS,KAAK,UAAU,KAAK;AAC/C,QAAI,MAAM,UAAU;AAChB,aAAO,IAAI,UAAS,OAAO,MAAM;AACrC,WAAO,SAAS,KAAK,UAAS,MAAM,OAAO,CAAC,CAAC,GAAG,MAAM;AAAA,EAC1D;AAAA,EACA,YAAY,MAAM,KAAK,KAAK,QAAQ,UAAU,MAAM;AAChD,KAAC,MAAM,EAAE,IAAI,KAAK,MAAM,MAAM,EAAE;AAChC,QAAI,SAAS;AACb,aAAS,MAAM,GAAG,IAAI,GAAG,OAAO,MAAM,IAAI,KAAK,KAAK,QAAQ,KAAK;AAC7D,UAAI,OAAO,KAAK,KAAK,CAAC,GAAG,MAAM,MAAM,KAAK;AAC1C,UAAI,MAAM,QAAQ;AACd,kBAAU;AACd,UAAI,OAAO,OAAO,KAAK;AACnB,kBAAU,KAAK,MAAM,KAAK,IAAI,GAAG,OAAO,GAAG,GAAG,KAAK,GAAG;AAC1D,YAAM,MAAM;AAAA,IAChB;AACA,WAAO;AAAA,EACX;AAAA,EACA,QAAQ,QAAQ;AACZ,aAAS,QAAQ,KAAK;AAClB,aAAO,KAAK,IAAI;AAAA,EACxB;AAAA,EACA,gBAAgB;AAAE,WAAO;AAAA,EAAG;AAAA,EAC5B,OAAO,MAAM,MAAM,QAAQ;AACvB,QAAI,OAAO,CAAC,GAAG,MAAM;AACrB,aAAS,QAAQ,MAAM;AACnB,WAAK,KAAK,IAAI;AACd,aAAO,KAAK,SAAS;AACrB,UAAI,KAAK,UAAU,IAAsB;AACrC,eAAO,KAAK,IAAI,UAAS,MAAM,GAAG,CAAC;AACnC,eAAO,CAAC;AACR,cAAM;AAAA,MACV;AAAA,IACJ;AACA,QAAI,MAAM;AACN,aAAO,KAAK,IAAI,UAAS,MAAM,GAAG,CAAC;AACvC,WAAO;AAAA,EACX;AACJ;AAKA,IAAM,WAAN,MAAM,kBAAiB,KAAK;AAAA,EACxB,YAAY,UAAU,QAAQ;AAC1B,UAAM;AACN,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,aAAS,SAAS;AACd,WAAK,SAAS,MAAM;AAAA,EAC5B;AAAA,EACA,UAAU,QAAQ,QAAQ,MAAM,QAAQ;AACpC,aAAS,IAAI,KAAI,KAAK;AAClB,UAAI,QAAQ,KAAK,SAAS,CAAC,GAAG,MAAM,SAAS,MAAM,QAAQ,UAAU,OAAO,MAAM,QAAQ;AAC1F,WAAK,SAAS,UAAU,QAAQ;AAC5B,eAAO,MAAM,UAAU,QAAQ,QAAQ,MAAM,MAAM;AACvD,eAAS,MAAM;AACf,aAAO,UAAU;AAAA,IACrB;AAAA,EACJ;AAAA,EACA,UAAU,MAAM,IAAI,QAAQ,MAAM;AAC9B,aAAS,IAAI,GAAG,MAAM,GAAG,OAAO,MAAM,IAAI,KAAK,SAAS,QAAQ,KAAK;AACjE,UAAI,QAAQ,KAAK,SAAS,CAAC,GAAG,MAAM,MAAM,MAAM;AAChD,UAAI,QAAQ,OAAO,MAAM,KAAK;AAC1B,YAAI,YAAY,SAAS,OAAO,OAAO,IAAoB,MAAM,OAAO,KAAK,IAAkB;AAC/F,YAAI,OAAO,QAAQ,OAAO,MAAM,CAAC;AAC7B,iBAAO,KAAK,KAAK;AAAA;AAEjB,gBAAM,UAAU,OAAO,KAAK,KAAK,KAAK,QAAQ,SAAS;AAAA,MAC/D;AACA,YAAM,MAAM;AAAA,IAChB;AAAA,EACJ;AAAA,EACA,QAAQ,MAAM,IAAI,MAAM;AACpB,KAAC,MAAM,EAAE,IAAI,KAAK,MAAM,MAAM,EAAE;AAChC,QAAI,KAAK,QAAQ,KAAK;AAClB,eAAS,IAAI,GAAG,MAAM,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AACpD,YAAI,QAAQ,KAAK,SAAS,CAAC,GAAG,MAAM,MAAM,MAAM;AAIhD,YAAI,QAAQ,OAAO,MAAM,KAAK;AAC1B,cAAI,UAAU,MAAM,QAAQ,OAAO,KAAK,KAAK,KAAK,IAAI;AACtD,cAAI,aAAa,KAAK,QAAQ,MAAM,QAAQ,QAAQ;AACpD,cAAI,QAAQ,QAAS,cAAe,IAA2B,KAC3D,QAAQ,QAAS,cAAe,IAA2B,GAAK;AAChE,gBAAI,OAAO,KAAK,SAAS,MAAM;AAC/B,iBAAK,CAAC,IAAI;AACV,mBAAO,IAAI,UAAS,MAAM,KAAK,UAAU,KAAK,QAAQ,KAAK,MAAM;AAAA,UACrE;AACA,iBAAO,MAAM,QAAQ,KAAK,KAAK,OAAO;AAAA,QAC1C;AACA,cAAM,MAAM;AAAA,MAChB;AACJ,WAAO,MAAM,QAAQ,MAAM,IAAI,IAAI;AAAA,EACvC;AAAA,EACA,YAAY,MAAM,KAAK,KAAK,QAAQ,UAAU,MAAM;AAChD,KAAC,MAAM,EAAE,IAAI,KAAK,MAAM,MAAM,EAAE;AAChC,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,MAAM,GAAG,IAAI,KAAK,SAAS,UAAU,OAAO,IAAI,KAAK;AACjE,UAAI,QAAQ,KAAK,SAAS,CAAC,GAAG,MAAM,MAAM,MAAM;AAChD,UAAI,MAAM,QAAQ;AACd,kBAAU;AACd,UAAI,OAAO,OAAO,KAAK;AACnB,kBAAU,MAAM,YAAY,OAAO,KAAK,KAAK,KAAK,OAAO;AAC7D,YAAM,MAAM;AAAA,IAChB;AACA,WAAO;AAAA,EACX;AAAA,EACA,QAAQ,QAAQ;AACZ,aAAS,SAAS,KAAK;AACnB,YAAM,QAAQ,MAAM;AAAA,EAC5B;AAAA,EACA,cAAc,OAAO,KAAK;AACtB,QAAI,EAAE,iBAAiB;AACnB,aAAO;AACX,QAAI,SAAS;AACb,QAAI,CAAC,IAAI,IAAI,IAAI,EAAE,IAAI,MAAM,IAAI,CAAC,GAAG,GAAG,KAAK,SAAS,QAAQ,MAAM,SAAS,MAAM,IAC7E,CAAC,KAAK,SAAS,SAAS,GAAG,MAAM,SAAS,SAAS,GAAG,IAAI,EAAE;AAClE,aAAQ,MAAM,KAAK,MAAM,KAAK;AAC1B,UAAI,MAAM,MAAM,MAAM;AAClB,eAAO;AACX,UAAI,MAAM,KAAK,SAAS,EAAE,GAAG,MAAM,MAAM,SAAS,EAAE;AACpD,UAAI,OAAO;AACP,eAAO,SAAS,IAAI,cAAc,KAAK,GAAG;AAC9C,gBAAU,IAAI,SAAS;AAAA,IAC3B;AAAA,EACJ;AAAA,EACA,OAAO,KAAK,UAAU,SAAS,SAAS,OAAO,CAAC,GAAG,OAAO,IAAI,GAAG,SAAS,GAAG,EAAE,GAAG;AAC9E,QAAI,QAAQ;AACZ,aAAS,MAAM;AACX,eAAS,GAAG;AAChB,QAAI,QAAQ,IAAsB;AAC9B,UAAI,OAAO,CAAC;AACZ,eAAS,MAAM;AACX,WAAG,QAAQ,IAAI;AACnB,aAAO,IAAI,SAAS,MAAM,MAAM;AAAA,IACpC;AACA,QAAI,QAAQ,KAAK;AAAA,MAAI;AAAA,MAAsB,SAAS;AAAA;AAAA,IAAwB,GAAG,WAAW,SAAS,GAAG,WAAW,SAAS;AAC1H,QAAI,UAAU,CAAC,GAAG,eAAe,GAAG,aAAa,IAAI,eAAe,CAAC;AACrE,aAAS,IAAI,OAAO;AAChB,UAAI;AACJ,UAAI,MAAM,QAAQ,YAAY,iBAAiB,WAAU;AACrD,iBAAS,QAAQ,MAAM;AACnB,cAAI,IAAI;AAAA,MAChB,WACS,MAAM,QAAQ,aAAa,eAAe,YAAY,CAAC,eAAe;AAC3E,cAAM;AACN,gBAAQ,KAAK,KAAK;AAAA,MACtB,WACS,iBAAiB,YAAY,iBACjC,OAAO,aAAa,aAAa,SAAS,CAAC,cAAc,YAC1D,MAAM,QAAQ,KAAK,SAAS,IAAsB;AAClD,wBAAgB,MAAM;AACtB,sBAAc,MAAM,SAAS;AAC7B,qBAAa,aAAa,SAAS,CAAC,IAAI,IAAI,SAAS,KAAK,KAAK,OAAO,MAAM,IAAI,GAAG,KAAK,SAAS,IAAI,MAAM,MAAM;AAAA,MACrH,OACK;AACD,YAAI,eAAe,MAAM,QAAQ;AAC7B,gBAAM;AACV,wBAAgB,MAAM;AACtB,sBAAc,MAAM,SAAS;AAC7B,qBAAa,KAAK,KAAK;AAAA,MAC3B;AAAA,IACJ;AACA,aAAS,QAAQ;AACb,UAAI,gBAAgB;AAChB;AACJ,cAAQ,KAAK,aAAa,UAAU,IAAI,aAAa,CAAC,IAAI,UAAS,KAAK,cAAc,UAAU,CAAC;AACjG,mBAAa;AACb,qBAAe,aAAa,SAAS;AAAA,IACzC;AACA,aAAS,SAAS;AACd,UAAI,KAAK;AACb,UAAM;AACN,WAAO,QAAQ,UAAU,IAAI,QAAQ,CAAC,IAAI,IAAI,UAAS,SAAS,MAAM;AAAA,EAC1E;AACJ;AACA,KAAK,QAAqB,IAAI,SAAS,CAAC,EAAE,GAAG,CAAC;AAC9C,SAAS,WAAW,MAAM;AACtB,MAAI,SAAS;AACb,WAAS,QAAQ;AACb,cAAU,KAAK,SAAS;AAC5B,SAAO;AACX;AACA,SAAS,WAAW,MAAM,QAAQ,OAAO,GAAG,KAAK,KAAK;AAClD,WAAS,MAAM,GAAG,IAAI,GAAG,QAAQ,MAAM,IAAI,KAAK,UAAU,OAAO,IAAI,KAAK;AACtE,QAAI,OAAO,KAAK,CAAC,GAAG,MAAM,MAAM,KAAK;AACrC,QAAI,OAAO,MAAM;AACb,UAAI,MAAM;AACN,eAAO,KAAK,MAAM,GAAG,KAAK,GAAG;AACjC,UAAI,MAAM;AACN,eAAO,KAAK,MAAM,OAAO,GAAG;AAChC,UAAI,OAAO;AACP,eAAO,OAAO,SAAS,CAAC,KAAK;AAC7B,gBAAQ;AAAA,MACZ;AAEI,eAAO,KAAK,IAAI;AAAA,IACxB;AACA,UAAM,MAAM;AAAA,EAChB;AACA,SAAO;AACX;AACA,SAAS,UAAU,MAAM,MAAM,IAAI;AAC/B,SAAO,WAAW,MAAM,CAAC,EAAE,GAAG,MAAM,EAAE;AAC1C;AACA,IAAM,gBAAN,MAAoB;AAAA,EAChB,YAAY,MAAM,MAAM,GAAG;AACvB,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,SAAK,YAAY;AACjB,SAAK,QAAQ;AACb,SAAK,QAAQ,CAAC,IAAI;AAClB,SAAK,UAAU,CAAC,MAAM,IAAI,KAAK,gBAAgB,WAAW,KAAK,KAAK,SAAS,KAAK,SAAS,WAAW,CAAC;AAAA,EAC3G;AAAA,EACA,UAAU,MAAM,KAAK;AACjB,SAAK,OAAO,KAAK,YAAY;AAC7B,eAAS;AACL,UAAI,OAAO,KAAK,MAAM,SAAS;AAC/B,UAAI,MAAM,KAAK,MAAM,IAAI,GAAG,cAAc,KAAK,QAAQ,IAAI,GAAG,SAAS,eAAe;AACtF,UAAI,OAAO,eAAe,WAAW,IAAI,KAAK,SAAS,IAAI,SAAS;AACpE,UAAI,WAAW,MAAM,IAAI,OAAO,IAAI;AAChC,YAAI,QAAQ,GAAG;AACX,eAAK,OAAO;AACZ,eAAK,QAAQ;AACb,iBAAO;AAAA,QACX;AACA,YAAI,MAAM;AACN,eAAK,QAAQ,OAAO,CAAC;AACzB,aAAK,MAAM,IAAI;AACf,aAAK,QAAQ,IAAI;AAAA,MACrB,YACU,cAAc,OAAO,MAAM,IAAI,IAAI,IAAI;AAC7C,aAAK,QAAQ,IAAI,KAAK;AACtB,YAAI,QAAQ,GAAG;AACX,eAAK,YAAY;AACjB,eAAK,QAAQ;AACb,iBAAO;AAAA,QACX;AACA;AAAA,MACJ,WACS,eAAe,UAAU;AAE9B,YAAI,OAAO,IAAI,KAAK,UAAU,MAAM,IAAI,KAAK,EAAE;AAC/C,aAAK,QAAQ,IAAI,KAAK;AACtB,YAAI,KAAK,SAAS,KAAK,IAAI,GAAG,IAAI,GAAG;AACjC,eAAK,QAAQ,QAAQ,IAAI,OAAO,MAAM,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,MAAM,GAAG,KAAK,SAAS,IAAI;AAC7F,iBAAO;AAAA,QACX;AACA,gBAAQ,KAAK;AAAA,MACjB,OACK;AACD,YAAI,OAAO,IAAI,SAAS,UAAU,MAAM,IAAI,KAAK,EAAE;AACnD,YAAI,OAAO,KAAK,QAAQ;AACpB,kBAAQ,KAAK;AACb,eAAK,QAAQ,IAAI,KAAK;AAAA,QAC1B,OACK;AACD,cAAI,MAAM;AACN,iBAAK,QAAQ,IAAI;AACrB,eAAK,MAAM,KAAK,IAAI;AACpB,eAAK,QAAQ,KAAK,MAAM,IAAI,KAAK,gBAAgB,WAAW,KAAK,KAAK,SAAS,KAAK,SAAS,WAAW,CAAC;AAAA,QAC7G;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,KAAK,OAAO,GAAG;AACX,QAAI,OAAO,GAAG;AACV,WAAK,UAAU,CAAC,MAAO,CAAC,KAAK,GAAI;AACjC,aAAO,KAAK,MAAM;AAAA,IACtB;AACA,WAAO,KAAK,UAAU,MAAM,KAAK,GAAG;AAAA,EACxC;AACJ;AACA,IAAM,oBAAN,MAAwB;AAAA,EACpB,YAAY,MAAM,OAAO,KAAK;AAC1B,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,SAAS,IAAI,cAAc,MAAM,QAAQ,MAAM,KAAK,CAAC;AAC1D,SAAK,MAAM,QAAQ,MAAM,KAAK,SAAS;AACvC,SAAK,OAAO,KAAK,IAAI,OAAO,GAAG;AAC/B,SAAK,KAAK,KAAK,IAAI,OAAO,GAAG;AAAA,EACjC;AAAA,EACA,UAAU,MAAM,KAAK;AACjB,QAAI,MAAM,IAAI,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI;AACvD,WAAK,QAAQ;AACb,WAAK,OAAO;AACZ,aAAO;AAAA,IACX;AACA,YAAQ,KAAK,IAAI,GAAG,MAAM,IAAI,KAAK,MAAM,KAAK,KAAK,KAAK,OAAO,KAAK,GAAG;AACvE,QAAI,QAAQ,MAAM,IAAI,KAAK,MAAM,KAAK,OAAO,KAAK,KAAK,KAAK;AAC5D,QAAI,OAAO;AACP,aAAO;AACX,aAAS;AACT,QAAI,EAAE,MAAM,IAAI,KAAK,OAAO,KAAK,IAAI;AACrC,SAAK,QAAQ,MAAM,SAAS,QAAQ;AACpC,SAAK,QAAQ,MAAM,UAAU,QAAQ,QAAQ,MAAM,IAAI,MAAM,MAAM,MAAM,SAAS,KAAK,IAAI,MAAM,MAAM,GAAG,KAAK;AAC/G,SAAK,OAAO,CAAC,KAAK;AAClB,WAAO;AAAA,EACX;AAAA,EACA,KAAK,OAAO,GAAG;AACX,QAAI,OAAO;AACP,aAAO,KAAK,IAAI,MAAM,KAAK,OAAO,KAAK,GAAG;AAAA,aACrC,OAAO;AACZ,aAAO,KAAK,IAAI,MAAM,KAAK,KAAK,KAAK,GAAG;AAC5C,WAAO,KAAK,UAAU,MAAM,KAAK,OAAO,GAAG;AAAA,EAC/C;AAAA,EACA,IAAI,YAAY;AAAE,WAAO,KAAK,OAAO,aAAa,KAAK,SAAS;AAAA,EAAI;AACxE;AACA,IAAM,aAAN,MAAiB;AAAA,EACb,YAAY,OAAO;AACf,SAAK,QAAQ;AACb,SAAK,aAAa;AAClB,SAAK,QAAQ;AACb,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,KAAK,OAAO,GAAG;AACX,QAAI,EAAE,MAAM,WAAW,MAAM,IAAI,KAAK,MAAM,KAAK,IAAI;AACrD,QAAI,QAAQ,KAAK,YAAY;AACzB,WAAK,QAAQ;AACb,WAAK,aAAa;AAAA,IACtB,WACS,MAAM;AACX,WAAK,OAAO;AACZ,WAAK,QAAQ;AAAA,IACjB,WACS,WAAW;AAChB,UAAI,KAAK,YAAY;AACjB,aAAK,QAAQ;AAAA,MACjB,OACK;AACD,aAAK,aAAa;AAClB,aAAK,KAAK;AAAA,MACd;AAAA,IACJ,OACK;AACD,WAAK,QAAQ;AACb,WAAK,aAAa;AAAA,IACtB;AACA,WAAO;AAAA,EACX;AAAA,EACA,IAAI,YAAY;AAAE,WAAO;AAAA,EAAO;AACpC;AACA,IAAI,OAAO,UAAU,aAAa;AAC9B,OAAK,UAAU,OAAO,QAAQ,IAAI,WAAY;AAAE,WAAO,KAAK,KAAK;AAAA,EAAG;AACpE,gBAAc,UAAU,OAAO,QAAQ,IAAI,kBAAkB,UAAU,OAAO,QAAQ,IAClF,WAAW,UAAU,OAAO,QAAQ,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM;AAC3E;AAKA,IAAM,OAAN,MAAW;AAAA;AAAA;AAAA;AAAA,EAIP,YAIA,MAKA,IAIA,QAIA,MAAM;AACF,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,SAAS;AAAE,WAAO,KAAK,KAAK,KAAK;AAAA,EAAM;AAC/C;AACA,SAAS,KAAK,MAAM,MAAM,IAAI;AAC1B,SAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,QAAQ,IAAI,CAAC;AAC9C,SAAO,CAAC,MAAM,KAAK,IAAI,MAAM,KAAK,IAAI,KAAK,QAAQ,EAAE,CAAC,CAAC;AAC3D;AAUA,SAASA,kBAAiB,KAAK,KAAK,UAAU,MAAM,mBAAmB,MAAM;AACzE,SAAO,iBAAmB,KAAK,KAAK,SAAS,gBAAgB;AACjE;AACA,SAASC,cAAa,IAAI;AAAE,SAAO,MAAM,SAAU,KAAK;AAAQ;AAChE,SAASC,eAAc,IAAI;AAAE,SAAO,MAAM,SAAU,KAAK;AAAQ;AAMjE,SAASC,aAAY,KAAK,KAAK;AAC3B,MAAI,QAAQ,IAAI,WAAW,GAAG;AAC9B,MAAI,CAACD,eAAc,KAAK,KAAK,MAAM,KAAK,IAAI;AACxC,WAAO;AACX,MAAI,QAAQ,IAAI,WAAW,MAAM,CAAC;AAClC,MAAI,CAACD,cAAa,KAAK;AACnB,WAAO;AACX,UAAS,QAAQ,SAAW,OAAO,QAAQ,SAAU;AACzD;AAMA,SAAS,cAAc,MAAM;AACzB,MAAI,QAAQ;AACR,WAAO,OAAO,aAAa,IAAI;AACnC,UAAQ;AACR,SAAO,OAAO,cAAc,QAAQ,MAAM,QAAS,OAAO,QAAQ,KAAM;AAC5E;AAIA,SAASG,eAAc,MAAM;AAAE,SAAO,OAAO,QAAU,IAAI;AAAG;AAE9D,IAAM,eAAe;AAIrB,IAAI,UAAwB,SAAUC,UAAS;AAK3C,EAAAA,SAAQA,SAAQ,QAAQ,IAAI,CAAC,IAAI;AAIjC,EAAAA,SAAQA,SAAQ,UAAU,IAAI,CAAC,IAAI;AAInC,EAAAA,SAAQA,SAAQ,aAAa,IAAI,CAAC,IAAI;AAItC,EAAAA,SAAQA,SAAQ,YAAY,IAAI,CAAC,IAAI;AACzC,SAAOA;AAAO,EAAG,YAAY,UAAU,CAAC,EAAE;AAM1C,IAAM,aAAN,MAAM,YAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASb,YAIA,UAAU;AACN,SAAK,WAAW;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,SAAS;AACT,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC3C,gBAAU,KAAK,SAAS,CAAC;AAC7B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,YAAY;AACZ,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK,GAAG;AAC9C,UAAI,MAAM,KAAK,SAAS,IAAI,CAAC;AAC7B,gBAAU,MAAM,IAAI,KAAK,SAAS,CAAC,IAAI;AAAA,IAC3C;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,QAAQ;AAAE,WAAO,KAAK,SAAS,UAAU,KAAK,KAAK,SAAS,UAAU,KAAK,KAAK,SAAS,CAAC,IAAI;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrG,SAAS,GAAG;AACR,aAAS,IAAI,GAAG,OAAO,GAAG,OAAO,GAAG,IAAI,KAAK,SAAS,UAAS;AAC3D,UAAI,MAAM,KAAK,SAAS,GAAG,GAAG,MAAM,KAAK,SAAS,GAAG;AACrD,UAAI,MAAM,GAAG;AACT,UAAE,MAAM,MAAM,GAAG;AACjB,gBAAQ;AAAA,MACZ,OACK;AACD,gBAAQ;AAAA,MACZ;AACA,cAAQ;AAAA,IACZ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,kBAAkB,GAAG,aAAa,OAAO;AACrC,gBAAY,MAAM,GAAG,UAAU;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,eAAe;AACf,QAAI,WAAW,CAAC;AAChB,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,UAAS;AACvC,UAAI,MAAM,KAAK,SAAS,GAAG,GAAG,MAAM,KAAK,SAAS,GAAG;AACrD,UAAI,MAAM;AACN,iBAAS,KAAK,KAAK,GAAG;AAAA;AAEtB,iBAAS,KAAK,KAAK,GAAG;AAAA,IAC9B;AACA,WAAO,IAAI,YAAW,QAAQ;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,OAAO;AAAE,WAAO,KAAK,QAAQ,QAAQ,MAAM,QAAQ,OAAO,YAAY,MAAM,KAAK;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOhG,QAAQ,OAAO,SAAS,OAAO;AAAE,WAAO,MAAM,QAAQ,OAAO,OAAO,MAAM,OAAO,MAAM;AAAA,EAAG;AAAA,EAC1F,OAAO,KAAK,QAAQ,IAAI,OAAO,QAAQ,QAAQ;AAC3C,QAAI,OAAO,GAAG,OAAO;AACrB,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,UAAS;AACvC,UAAI,MAAM,KAAK,SAAS,GAAG,GAAG,MAAM,KAAK,SAAS,GAAG,GAAG,OAAO,OAAO;AACtE,UAAI,MAAM,GAAG;AACT,YAAI,OAAO;AACP,iBAAO,QAAQ,MAAM;AACzB,gBAAQ;AAAA,MACZ,OACK;AACD,YAAI,QAAQ,QAAQ,UAAU,QAAQ,QACjC,QAAQ,QAAQ,YAAY,OAAO,OAAO,OAAO,OAC9C,QAAQ,QAAQ,eAAe,OAAO,OACtC,QAAQ,QAAQ,cAAc,OAAO;AACzC,iBAAO;AACX,YAAI,OAAO,OAAO,QAAQ,OAAO,QAAQ,KAAK,CAAC;AAC3C,iBAAO,OAAO,QAAQ,QAAQ,IAAI,OAAO,OAAO;AACpD,gBAAQ;AAAA,MACZ;AACA,aAAO;AAAA,IACX;AACA,QAAI,MAAM;AACN,YAAM,IAAI,WAAW,YAAY,GAAG,4CAA4C,IAAI,EAAE;AAC1F,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,MAAM,KAAK,MAAM;AAC1B,aAAS,IAAI,GAAG,MAAM,GAAG,IAAI,KAAK,SAAS,UAAU,OAAO,MAAK;AAC7D,UAAI,MAAM,KAAK,SAAS,GAAG,GAAG,MAAM,KAAK,SAAS,GAAG,GAAG,MAAM,MAAM;AACpE,UAAI,OAAO,KAAK,OAAO,MAAM,OAAO;AAChC,eAAO,MAAM,QAAQ,MAAM,KAAK,UAAU;AAC9C,YAAM;AAAA,IACV;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACP,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,UAAS;AACvC,UAAI,MAAM,KAAK,SAAS,GAAG,GAAG,MAAM,KAAK,SAAS,GAAG;AACrD,iBAAW,SAAS,MAAM,MAAM,OAAO,OAAO,IAAI,MAAM,MAAM;AAAA,IAClE;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AAAE,WAAO,KAAK;AAAA,EAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjC,OAAO,SAAS,MAAM;AAClB,QAAI,CAAC,MAAM,QAAQ,IAAI,KAAK,KAAK,SAAS,KAAK,KAAK,KAAK,OAAK,OAAO,KAAK,QAAQ;AAC9E,YAAM,IAAI,WAAW,2CAA2C;AACpE,WAAO,IAAI,YAAW,IAAI;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,OAAO,UAAU;AAAE,WAAO,IAAI,YAAW,QAAQ;AAAA,EAAG;AAC/D;AAMA,IAAM,YAAN,MAAM,mBAAkB,WAAW;AAAA,EAC/B,YAAY,UAIZ,UAAU;AACN,UAAM,QAAQ;AACd,SAAK,WAAW;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,KAAK;AACP,QAAI,KAAK,UAAU,IAAI;AACnB,YAAM,IAAI,WAAW,yDAAyD;AAClF,gBAAY,MAAM,CAAC,OAAO,KAAK,OAAO,MAAM,SAAS,MAAM,IAAI,QAAQ,OAAO,SAAS,MAAM,QAAQ,IAAI,GAAG,KAAK;AACjH,WAAO;AAAA,EACX;AAAA,EACA,QAAQ,OAAO,SAAS,OAAO;AAAE,WAAO,OAAO,MAAM,OAAO,QAAQ,IAAI;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO3E,OAAO,KAAK;AACR,QAAI,WAAW,KAAK,SAAS,MAAM,GAAG,WAAW,CAAC;AAClD,aAAS,IAAI,GAAG,MAAM,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG;AAClD,UAAI,MAAM,SAAS,CAAC,GAAG,MAAM,SAAS,IAAI,CAAC;AAC3C,UAAI,OAAO,GAAG;AACV,iBAAS,CAAC,IAAI;AACd,iBAAS,IAAI,CAAC,IAAI;AAClB,YAAI,QAAQ,KAAK;AACjB,eAAO,SAAS,SAAS;AACrB,mBAAS,KAAK,KAAK,KAAK;AAC5B,iBAAS,KAAK,MAAM,IAAI,MAAM,KAAK,MAAM,GAAG,IAAI,KAAK,KAAK;AAAA,MAC9D;AACA,aAAO;AAAA,IACX;AACA,WAAO,IAAI,WAAU,UAAU,QAAQ;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,OAAO;AAAE,WAAO,KAAK,QAAQ,QAAQ,MAAM,QAAQ,OAAO,YAAY,MAAM,OAAO,IAAI;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAclG,IAAI,OAAO,SAAS,OAAO;AAAE,WAAO,MAAM,QAAQ,OAAO,OAAO,MAAM,OAAO,QAAQ,IAAI;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU5F,YAAY,GAAG,aAAa,OAAO;AAC/B,gBAAY,MAAM,GAAG,UAAU;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,OAAO;AAAE,WAAO,WAAW,OAAO,KAAK,QAAQ;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAItD,OAAO,QAAQ;AACX,QAAI,iBAAiB,CAAC,GAAG,iBAAiB,CAAC,GAAG,mBAAmB,CAAC;AAClE,QAAI,OAAO,IAAI,YAAY,IAAI;AAC/B,SAAM,UAAS,IAAI,GAAG,MAAM,OAAK;AAC7B,UAAI,OAAO,KAAK,OAAO,SAAS,MAAM,OAAO,GAAG;AAChD,aAAO,MAAM,QAAQ,OAAO,QAAQ,KAAK,OAAO,GAAG;AAC/C,YAAI,KAAK;AACL,gBAAM;AACV,YAAI,MAAM,KAAK,IAAI,KAAK,KAAK,OAAO,GAAG;AACvC,mBAAW,kBAAkB,KAAK,EAAE;AACpC,YAAI,MAAM,KAAK,OAAO,KAAK,KAAK,KAAK,OAAO,IAAI,KAAK,MAAM;AAC3D,mBAAW,gBAAgB,KAAK,GAAG;AACnC,YAAI,MAAM;AACN,oBAAU,gBAAgB,gBAAgB,KAAK,IAAI;AACvD,aAAK,QAAQ,GAAG;AAChB,eAAO;AAAA,MACX;AACA,UAAI,MAAM,OAAO,GAAG;AACpB,aAAO,MAAM,KAAK;AACd,YAAI,KAAK;AACL,gBAAM;AACV,YAAI,MAAM,KAAK,IAAI,KAAK,KAAK,MAAM,GAAG;AACtC,mBAAW,gBAAgB,KAAK,EAAE;AAClC,mBAAW,kBAAkB,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK,OAAO,IAAI,KAAK,MAAM,CAAC;AACpF,aAAK,QAAQ,GAAG;AAChB,eAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAO;AAAA,MAAE,SAAS,IAAI,WAAU,gBAAgB,cAAc;AAAA,MAC1D,UAAU,WAAW,OAAO,gBAAgB;AAAA,IAAE;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACL,QAAI,QAAQ,CAAC;AACb,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK,GAAG;AAC9C,UAAI,MAAM,KAAK,SAAS,CAAC,GAAG,MAAM,KAAK,SAAS,IAAI,CAAC;AACrD,UAAI,MAAM;AACN,cAAM,KAAK,GAAG;AAAA,eACT,OAAO;AACZ,cAAM,KAAK,CAAC,GAAG,CAAC;AAAA;AAEhB,cAAM,KAAK,CAAC,GAAG,EAAE,OAAO,KAAK,SAAS,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;AAAA,IAC/D;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,GAAG,SAAS,QAAQ,SAAS;AAChC,QAAI,WAAW,CAAC,GAAG,WAAW,CAAC,GAAG,MAAM;AACxC,QAAI,QAAQ;AACZ,aAAS,MAAM,QAAQ,OAAO;AAC1B,UAAI,CAAC,SAAS,CAAC,SAAS;AACpB;AACJ,UAAI,MAAM;AACN,mBAAW,UAAU,SAAS,KAAK,EAAE;AACzC,UAAI,MAAM,IAAI,WAAU,UAAU,QAAQ;AAC1C,cAAQ,QAAQ,MAAM,QAAQ,IAAI,IAAI,KAAK,CAAC,IAAI;AAChD,iBAAW,CAAC;AACZ,iBAAW,CAAC;AACZ,YAAM;AAAA,IACV;AACA,aAAS,QAAQ,MAAM;AACnB,UAAI,MAAM,QAAQ,IAAI,GAAG;AACrB,iBAAS,OAAO;AACZ,kBAAQ,GAAG;AAAA,MACnB,WACS,gBAAgB,YAAW;AAChC,YAAI,KAAK,UAAU;AACf,gBAAM,IAAI,WAAW,qCAAqC,KAAK,MAAM,cAAc,MAAM,GAAG;AAChG,cAAM;AACN,gBAAQ,QAAQ,MAAM,QAAQ,KAAK,IAAI,KAAK,CAAC,IAAI;AAAA,MACrD,OACK;AACD,YAAI,EAAE,MAAM,KAAK,MAAM,QAAAC,QAAO,IAAI;AAClC,YAAI,OAAO,MAAM,OAAO,KAAK,KAAK;AAC9B,gBAAM,IAAI,WAAW,wBAAwB,IAAI,OAAO,EAAE,sBAAsB,MAAM,GAAG;AAC7F,YAAI,UAAU,CAACA,UAAS,KAAK,QAAQ,OAAOA,WAAU,WAAW,KAAK,GAAGA,QAAO,MAAM,WAAW,YAAY,CAAC,IAAIA;AAClH,YAAI,SAAS,QAAQ;AACrB,YAAI,QAAQ,MAAM,UAAU;AACxB;AACJ,YAAI,OAAO;AACP,gBAAM;AACV,YAAI,OAAO;AACP,qBAAW,UAAU,OAAO,KAAK,EAAE;AACvC,mBAAW,UAAU,KAAK,MAAM,MAAM;AACtC,kBAAU,UAAU,UAAU,OAAO;AACrC,cAAM;AAAA,MACV;AAAA,IACJ;AACA,YAAQ,OAAO;AACf,UAAM,CAAC,KAAK;AACZ,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,MAAM,QAAQ;AACjB,WAAO,IAAI,WAAU,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,SAAS,MAAM;AAClB,QAAI,CAAC,MAAM,QAAQ,IAAI;AACnB,YAAM,IAAI,WAAW,0CAA0C;AACnE,QAAI,WAAW,CAAC,GAAG,WAAW,CAAC;AAC/B,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,UAAI,OAAO,KAAK,CAAC;AACjB,UAAI,OAAO,QAAQ,UAAU;AACzB,iBAAS,KAAK,MAAM,EAAE;AAAA,MAC1B,WACS,CAAC,MAAM,QAAQ,IAAI,KAAK,OAAO,KAAK,CAAC,KAAK,YAAY,KAAK,KAAK,CAAC,GAAGC,OAAMA,MAAK,OAAO,KAAK,QAAQ,GAAG;AAC3G,cAAM,IAAI,WAAW,0CAA0C;AAAA,MACnE,WACS,KAAK,UAAU,GAAG;AACvB,iBAAS,KAAK,KAAK,CAAC,GAAG,CAAC;AAAA,MAC5B,OACK;AACD,eAAO,SAAS,SAAS;AACrB,mBAAS,KAAK,KAAK,KAAK;AAC5B,iBAAS,CAAC,IAAI,KAAK,GAAG,KAAK,MAAM,CAAC,CAAC;AACnC,iBAAS,KAAK,KAAK,CAAC,GAAG,SAAS,CAAC,EAAE,MAAM;AAAA,MAC7C;AAAA,IACJ;AACA,WAAO,IAAI,WAAU,UAAU,QAAQ;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,UAAU,UAAU,UAAU;AACjC,WAAO,IAAI,WAAU,UAAU,QAAQ;AAAA,EAC3C;AACJ;AACA,SAAS,WAAW,UAAU,KAAK,KAAK,YAAY,OAAO;AACvD,MAAI,OAAO,KAAK,OAAO;AACnB;AACJ,MAAI,OAAO,SAAS,SAAS;AAC7B,MAAI,QAAQ,KAAK,OAAO,KAAK,OAAO,SAAS,OAAO,CAAC;AACjD,aAAS,IAAI,KAAK;AAAA,WACb,QAAQ,KAAK,OAAO,KAAK,SAAS,IAAI,KAAK;AAChD,aAAS,OAAO,CAAC,KAAK;AAAA,WACjB,WAAW;AAChB,aAAS,IAAI,KAAK;AAClB,aAAS,OAAO,CAAC,KAAK;AAAA,EAC1B;AAEI,aAAS,KAAK,KAAK,GAAG;AAC9B;AACA,SAAS,UAAU,QAAQ,UAAU,OAAO;AACxC,MAAI,MAAM,UAAU;AAChB;AACJ,MAAI,QAAS,SAAS,SAAS,KAAM;AACrC,MAAI,QAAQ,OAAO,QAAQ;AACvB,WAAO,OAAO,SAAS,CAAC,IAAI,OAAO,OAAO,SAAS,CAAC,EAAE,OAAO,KAAK;AAAA,EACtE,OACK;AACD,WAAO,OAAO,SAAS;AACnB,aAAO,KAAK,KAAK,KAAK;AAC1B,WAAO,KAAK,KAAK;AAAA,EACrB;AACJ;AACA,SAAS,YAAY,MAAM,GAAG,YAAY;AACtC,MAAI,WAAW,KAAK;AACpB,WAAS,OAAO,GAAG,OAAO,GAAG,IAAI,GAAG,IAAI,KAAK,SAAS,UAAS;AAC3D,QAAI,MAAM,KAAK,SAAS,GAAG,GAAG,MAAM,KAAK,SAAS,GAAG;AACrD,QAAI,MAAM,GAAG;AACT,cAAQ;AACR,cAAQ;AAAA,IACZ,OACK;AACD,UAAI,OAAO,MAAM,OAAO,MAAM,OAAO,KAAK;AAC1C,iBAAS;AACL,gBAAQ;AACR,gBAAQ;AACR,YAAI,OAAO;AACP,iBAAO,KAAK,OAAO,SAAU,IAAI,KAAM,CAAC,CAAC;AAC7C,YAAI,cAAc,KAAK,KAAK,SAAS,UAAU,KAAK,SAAS,IAAI,CAAC,IAAI;AAClE;AACJ,cAAM,KAAK,SAAS,GAAG;AACvB,cAAM,KAAK,SAAS,GAAG;AAAA,MAC3B;AACA,QAAE,MAAM,MAAM,MAAM,MAAM,IAAI;AAC9B,aAAO;AACP,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AACA,SAAS,OAAO,MAAM,MAAM,QAAQ,QAAQ,OAAO;AAG/C,MAAI,WAAW,CAAC,GAAGD,UAAS,QAAQ,CAAC,IAAI;AACzC,MAAI,IAAI,IAAI,YAAY,IAAI,GAAG,IAAI,IAAI,YAAY,IAAI;AAKvD,WAAS,WAAW,QAAM;AACtB,QAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK;AACpC,YAAM,IAAI,MAAM,+BAA+B;AAAA,IACnD,WACS,EAAE,OAAO,MAAM,EAAE,OAAO,IAAI;AAEjC,UAAI,MAAM,KAAK,IAAI,EAAE,KAAK,EAAE,GAAG;AAC/B,iBAAW,UAAU,KAAK,EAAE;AAC5B,QAAE,QAAQ,GAAG;AACb,QAAE,QAAQ,GAAG;AAAA,IACjB,WACS,EAAE,OAAO,MAAM,EAAE,MAAM,KAAK,YAAY,EAAE,KAAK,EAAE,OAAO,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,UAAU;AAIjH,UAAI,MAAM,EAAE;AACZ,iBAAW,UAAU,EAAE,KAAK,EAAE;AAC9B,aAAO,KAAK;AACR,YAAI,QAAQ,KAAK,IAAI,EAAE,KAAK,GAAG;AAC/B,YAAI,EAAE,OAAO,KAAK,WAAW,EAAE,KAAK,EAAE,OAAO,OAAO;AAChD,qBAAW,UAAU,GAAG,EAAE,GAAG;AAC7B,cAAIA;AACA,sBAAUA,SAAQ,UAAU,EAAE,IAAI;AACtC,qBAAW,EAAE;AAAA,QACjB;AACA,UAAE,QAAQ,KAAK;AACf,eAAO;AAAA,MACX;AACA,QAAE,KAAK;AAAA,IACX,WACS,EAAE,OAAO,GAAG;AAGjB,UAAI,MAAM,GAAG,OAAO,EAAE;AACtB,aAAO,MAAM;AACT,YAAI,EAAE,OAAO,IAAI;AACb,cAAI,QAAQ,KAAK,IAAI,MAAM,EAAE,GAAG;AAChC,iBAAO;AACP,kBAAQ;AACR,YAAE,QAAQ,KAAK;AAAA,QACnB,WACS,EAAE,OAAO,KAAK,EAAE,MAAM,MAAM;AACjC,kBAAQ,EAAE;AACV,YAAE,KAAK;AAAA,QACX,OACK;AACD;AAAA,QACJ;AAAA,MACJ;AACA,iBAAW,UAAU,KAAK,WAAW,EAAE,IAAI,EAAE,MAAM,CAAC;AACpD,UAAIA,WAAU,WAAW,EAAE;AACvB,kBAAUA,SAAQ,UAAU,EAAE,IAAI;AACtC,iBAAW,EAAE;AACb,QAAE,QAAQ,EAAE,MAAM,IAAI;AAAA,IAC1B,WACS,EAAE,QAAQ,EAAE,MAAM;AACvB,aAAOA,UAAS,UAAU,UAAU,UAAUA,OAAM,IAAI,WAAW,OAAO,QAAQ;AAAA,IACtF,OACK;AACD,YAAM,IAAI,MAAM,+BAA+B;AAAA,IACnD;AAAA,EACJ;AACJ;AACA,SAAS,YAAY,MAAM,MAAM,QAAQ,OAAO;AAC5C,MAAI,WAAW,CAAC;AAChB,MAAIA,UAAS,QAAQ,CAAC,IAAI;AAC1B,MAAI,IAAI,IAAI,YAAY,IAAI,GAAG,IAAI,IAAI,YAAY,IAAI;AACvD,WAAS,OAAO,WAAS;AACrB,QAAI,EAAE,QAAQ,EAAE,MAAM;AAClB,aAAOA,UAAS,UAAU,UAAU,UAAUA,OAAM,IAAI,WAAW,OAAO,QAAQ;AAAA,IACtF,WACS,EAAE,OAAO,GAAG;AACjB,iBAAW,UAAU,EAAE,KAAK,GAAG,IAAI;AACnC,QAAE,KAAK;AAAA,IACX,WACS,EAAE,OAAO,KAAK,CAAC,EAAE,MAAM;AAC5B,iBAAW,UAAU,GAAG,EAAE,KAAK,IAAI;AACnC,UAAIA;AACA,kBAAUA,SAAQ,UAAU,EAAE,IAAI;AACtC,QAAE,KAAK;AAAA,IACX,WACS,EAAE,QAAQ,EAAE,MAAM;AACvB,YAAM,IAAI,MAAM,+BAA+B;AAAA,IACnD,OACK;AACD,UAAI,MAAM,KAAK,IAAI,EAAE,MAAM,EAAE,GAAG,GAAG,aAAa,SAAS;AACzD,UAAI,EAAE,OAAO,IAAI;AACb,YAAI,OAAO,EAAE,OAAO,KAAK,KAAK,EAAE,MAAM,IAAI,EAAE;AAC5C,mBAAW,UAAU,KAAK,MAAM,IAAI;AACpC,YAAIA,WAAU;AACV,oBAAUA,SAAQ,UAAU,EAAE,IAAI;AAAA,MAC1C,WACS,EAAE,OAAO,IAAI;AAClB,mBAAW,UAAU,EAAE,MAAM,IAAI,EAAE,KAAK,KAAK,IAAI;AACjD,YAAIA;AACA,oBAAUA,SAAQ,UAAU,EAAE,QAAQ,GAAG,CAAC;AAAA,MAClD,OACK;AACD,mBAAW,UAAU,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,MAAM,IAAI,EAAE,KAAK,IAAI;AAC/D,YAAIA,WAAU,CAAC,EAAE;AACb,oBAAUA,SAAQ,UAAU,EAAE,IAAI;AAAA,MAC1C;AACA,cAAQ,EAAE,MAAM,OAAO,EAAE,OAAO,KAAK,EAAE,MAAM,SAAS,QAAQ,SAAS,SAAS;AAChF,QAAE,SAAS,GAAG;AACd,QAAE,QAAQ,GAAG;AAAA,IACjB;AAAA,EACJ;AACJ;AACA,IAAM,cAAN,MAAkB;AAAA,EACd,YAAY,KAAK;AACb,SAAK,MAAM;AACX,SAAK,IAAI;AACT,SAAK,KAAK;AAAA,EACd;AAAA,EACA,OAAO;AACH,QAAI,EAAE,SAAS,IAAI,KAAK;AACxB,QAAI,KAAK,IAAI,SAAS,QAAQ;AAC1B,WAAK,MAAM,SAAS,KAAK,GAAG;AAC5B,WAAK,MAAM,SAAS,KAAK,GAAG;AAAA,IAChC,OACK;AACD,WAAK,MAAM;AACX,WAAK,MAAM;AAAA,IACf;AACA,SAAK,MAAM;AAAA,EACf;AAAA,EACA,IAAI,OAAO;AAAE,WAAO,KAAK,OAAO;AAAA,EAAI;AAAA,EACpC,IAAI,OAAO;AAAE,WAAO,KAAK,MAAM,IAAI,KAAK,MAAM,KAAK;AAAA,EAAK;AAAA,EACxD,IAAI,OAAO;AACP,QAAI,EAAE,SAAS,IAAI,KAAK,KAAK,QAAS,KAAK,IAAI,KAAM;AACrD,WAAO,SAAS,SAAS,SAAS,KAAK,QAAQ,SAAS,KAAK;AAAA,EACjE;AAAA,EACA,QAAQ,KAAK;AACT,QAAI,EAAE,SAAS,IAAI,KAAK,KAAK,QAAS,KAAK,IAAI,KAAM;AACrD,WAAO,SAAS,SAAS,UAAU,CAAC,MAAM,KAAK,QACzC,SAAS,KAAK,EAAE,MAAM,KAAK,KAAK,OAAO,OAAO,SAAY,KAAK,MAAM,GAAG;AAAA,EAClF;AAAA,EACA,QAAQ,KAAK;AACT,QAAI,OAAO,KAAK;AACZ,WAAK,KAAK;AAAA,SACT;AACD,WAAK,OAAO;AACZ,WAAK,OAAO;AAAA,IAChB;AAAA,EACJ;AAAA,EACA,SAAS,KAAK;AACV,QAAI,KAAK,OAAO;AACZ,WAAK,QAAQ,GAAG;AAAA,aACX,OAAO,KAAK;AACjB,WAAK,KAAK;AAAA,SACT;AACD,WAAK,OAAO;AACZ,WAAK,OAAO;AAAA,IAChB;AAAA,EACJ;AACJ;AAQA,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACjB,YAIA,MAIA,IAAI,OAAO;AACP,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,QAAQ;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,SAAS;AAAE,WAAO,KAAK,QAAQ,KAA8B,KAAK,KAAK,KAAK;AAAA,EAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtF,IAAI,OAAO;AAAE,WAAO,KAAK,QAAQ,KAA8B,KAAK,OAAO,KAAK;AAAA,EAAI;AAAA;AAAA;AAAA;AAAA,EAIpF,IAAI,QAAQ;AAAE,WAAO,KAAK,QAAQ,KAAK;AAAA,EAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO3C,IAAI,QAAQ;AAAE,WAAO,KAAK,QAAQ,IAAgC,KAAK,KAAK,QAAQ,KAAgC,IAAI;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3H,IAAI,YAAY;AACZ,QAAI,QAAQ,KAAK,QAAQ;AACzB,WAAO,SAAS,IAAI,OAAO;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,aAAa;AACb,QAAI,QAAQ,KAAK,SAAS;AAC1B,WAAO,SAAS,WAAwC,SAAY;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,QAAQ,QAAQ,IAAI;AACpB,QAAI,MAAM;AACV,QAAI,KAAK,OAAO;AACZ,aAAO,KAAK,OAAO,OAAO,KAAK,MAAM,KAAK;AAAA,IAC9C,OACK;AACD,aAAO,OAAO,OAAO,KAAK,MAAM,CAAC;AACjC,WAAK,OAAO,OAAO,KAAK,IAAI,EAAE;AAAA,IAClC;AACA,WAAO,QAAQ,KAAK,QAAQ,MAAM,KAAK,KAAK,OAAO,IAAI,gBAAe,MAAM,IAAI,KAAK,KAAK;AAAA,EAC9F;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,MAAM,KAAK,MAAM;AACpB,QAAI,QAAQ,KAAK,UAAU,MAAM,KAAK;AAClC,aAAO,gBAAgB,MAAM,MAAM,EAAE;AACzC,QAAI,OAAO,KAAK,IAAI,OAAO,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK,MAAM,IAAI,OAAO;AAC9E,WAAO,gBAAgB,MAAM,KAAK,QAAQ,IAAI;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA,EAIA,GAAG,OAAO,eAAe,OAAO;AAC5B,WAAO,KAAK,UAAU,MAAM,UAAU,KAAK,QAAQ,MAAM,SACpD,CAAC,gBAAgB,CAAC,KAAK,SAAS,KAAK,SAAS,MAAM;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AAAE,WAAO,EAAE,QAAQ,KAAK,QAAQ,MAAM,KAAK,KAAK;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5D,OAAO,SAAS,MAAM;AAClB,QAAI,CAAC,QAAQ,OAAO,KAAK,UAAU,YAAY,OAAO,KAAK,QAAQ;AAC/D,YAAM,IAAI,WAAW,gDAAgD;AACzE,WAAO,gBAAgB,MAAM,KAAK,QAAQ,KAAK,IAAI;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,OAAO,MAAM,IAAI,OAAO;AAC3B,WAAO,IAAI,gBAAe,MAAM,IAAI,KAAK;AAAA,EAC7C;AACJ;AAIA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EAClB,YAKA,QAKA,WAAW;AACP,SAAK,SAAS;AACd,SAAK,YAAY;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,QAAQ,QAAQ,IAAI;AACpB,QAAI,OAAO;AACP,aAAO;AACX,WAAO,iBAAgB,OAAO,KAAK,OAAO,IAAI,OAAK,EAAE,IAAI,QAAQ,KAAK,CAAC,GAAG,KAAK,SAAS;AAAA,EAC5F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,GAAG,OAAO,eAAe,OAAO;AAC5B,QAAI,KAAK,OAAO,UAAU,MAAM,OAAO,UACnC,KAAK,aAAa,MAAM;AACxB,aAAO;AACX,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ;AACpC,UAAI,CAAC,KAAK,OAAO,CAAC,EAAE,GAAG,MAAM,OAAO,CAAC,GAAG,YAAY;AAChD,eAAO;AACf,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,OAAO;AAAE,WAAO,KAAK,OAAO,KAAK,SAAS;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjD,WAAW;AACP,WAAO,KAAK,OAAO,UAAU,IAAI,OAAO,IAAI,iBAAgB,CAAC,KAAK,IAAI,GAAG,CAAC;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,OAAO,OAAO,MAAM;AACzB,WAAO,iBAAgB,OAAO,CAAC,KAAK,EAAE,OAAO,KAAK,MAAM,GAAG,OAAO,IAAI,KAAK,YAAY,CAAC;AAAA,EAC5F;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,OAAO,QAAQ,KAAK,WAAW;AACxC,QAAI,SAAS,KAAK,OAAO,MAAM;AAC/B,WAAO,KAAK,IAAI;AAChB,WAAO,iBAAgB,OAAO,QAAQ,KAAK,SAAS;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACL,WAAO,EAAE,QAAQ,KAAK,OAAO,IAAI,OAAK,EAAE,OAAO,CAAC,GAAG,MAAM,KAAK,UAAU;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS,MAAM;AAClB,QAAI,CAAC,QAAQ,CAAC,MAAM,QAAQ,KAAK,MAAM,KAAK,OAAO,KAAK,QAAQ,YAAY,KAAK,QAAQ,KAAK,OAAO;AACjG,YAAM,IAAI,WAAW,iDAAiD;AAC1E,WAAO,IAAI,iBAAgB,KAAK,OAAO,IAAI,CAAC,MAAM,eAAe,SAAS,CAAC,CAAC,GAAG,KAAK,IAAI;AAAA,EAC5F;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,OAAO,QAAQ,OAAO,QAAQ;AACjC,WAAO,IAAI,iBAAgB,CAAC,iBAAgB,MAAM,QAAQ,IAAI,CAAC,GAAG,CAAC;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,OAAO,QAAQ,YAAY,GAAG;AACjC,QAAI,OAAO,UAAU;AACjB,YAAM,IAAI,WAAW,sCAAsC;AAC/D,aAAS,MAAM,GAAG,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AAC7C,UAAI,QAAQ,OAAO,CAAC;AACpB,UAAI,MAAM,QAAQ,MAAM,QAAQ,MAAM,MAAM,OAAO;AAC/C,eAAO,iBAAgB,WAAW,OAAO,MAAM,GAAG,SAAS;AAC/D,YAAM,MAAM;AAAA,IAChB;AACA,WAAO,IAAI,iBAAgB,QAAQ,SAAS;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,OAAO,KAAK,QAAQ,GAAG,WAAW,YAAY;AACjD,WAAO,eAAe,OAAO,KAAK,MAAM,SAAS,IAAI,IAAI,QAAQ,IAAI,IAAgC,OAChG,aAAa,OAAO,IAAI,KAAK,IAAI,GAAG,SAAS,MAC5C,eAAe,QAAQ,eAAe,SAAS,aAAa,aAA0C,CAAmC;AAAA,EACnJ;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,MAAM,QAAQ,MAAM,YAAY,WAAW;AAC9C,QAAI,SAAU,eAAe,QAAQ,eAAe,SAAS,aAAa,aAA0C,KAC/G,aAAa,OAAO,IAAI,KAAK,IAAI,GAAG,SAAS;AAClD,WAAO,OAAO,SAAS,eAAe,OAAO,MAAM,QAAQ,KAA8B,KAAgC,KAAK,IACxH,eAAe,OAAO,QAAQ,OAAO,OAAO,SAAS,IAAgC,KAAK,KAAK;AAAA,EACzG;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,WAAW,QAAQ,YAAY,GAAG;AACrC,QAAI,OAAO,OAAO,SAAS;AAC3B,WAAO,KAAK,CAAC,GAAG,MAAM,EAAE,OAAO,EAAE,IAAI;AACrC,gBAAY,OAAO,QAAQ,IAAI;AAC/B,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,UAAI,QAAQ,OAAO,CAAC,GAAG,OAAO,OAAO,IAAI,CAAC;AAC1C,UAAI,MAAM,QAAQ,MAAM,QAAQ,KAAK,KAAK,MAAM,OAAO,KAAK,IAAI;AAC5D,YAAI,OAAO,KAAK,MAAM,KAAK,KAAK,IAAI,MAAM,IAAI,KAAK,EAAE;AACrD,YAAI,KAAK;AACL;AACJ,eAAO,OAAO,EAAE,GAAG,GAAG,MAAM,SAAS,MAAM,OAAO,iBAAgB,MAAM,IAAI,IAAI,IAAI,iBAAgB,MAAM,MAAM,EAAE,CAAC;AAAA,MACvH;AAAA,IACJ;AACA,WAAO,IAAI,iBAAgB,QAAQ,SAAS;AAAA,EAChD;AACJ;AACA,SAAS,eAAe,WAAW,WAAW;AAC1C,WAAS,SAAS,UAAU;AACxB,QAAI,MAAM,KAAK;AACX,YAAM,IAAI,WAAW,sCAAsC;AACvE;AAEA,IAAI,SAAS;AAcb,IAAM,QAAN,MAAM,OAAM;AAAA,EACR,YAIA,SAIA,cAIAE,UAAS,UAAU,SAAS;AACxB,SAAK,UAAU;AACf,SAAK,eAAe;AACpB,SAAK,UAAUA;AACf,SAAK,WAAW;AAIhB,SAAK,KAAK;AACV,SAAK,UAAU,QAAQ,CAAC,CAAC;AACzB,SAAK,aAAa,OAAO,WAAW,aAAa,QAAQ,IAAI,IAAI;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,SAAS;AAAE,WAAO;AAAA,EAAM;AAAA;AAAA;AAAA;AAAA,EAI5B,OAAO,OAAO,SAAS,CAAC,GAAG;AACvB,WAAO,IAAI,OAAM,OAAO,YAAY,CAAC,MAAM,IAAI,OAAO,iBAAiB,CAAC,GAAG,MAAM,MAAM,IAAI,OAAO,YAAY,CAAC,OAAO,UAAU,YAAY,CAAC,GAAG,MAAM,MAAM,IAAI,CAAC,CAAC,OAAO,QAAQ,OAAO,OAAO;AAAA,EACnM;AAAA;AAAA;AAAA;AAAA,EAIA,GAAG,OAAO;AACN,WAAO,IAAI,cAAc,CAAC,GAAG,MAAM,GAAyB,KAAK;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,QAAQ,MAAM,KAAK;AACf,QAAI,KAAK;AACL,YAAM,IAAI,MAAM,8BAA8B;AAClD,WAAO,IAAI,cAAc,MAAM,MAAM,GAAyB,GAAG;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,MAAM,KAAK;AAChB,QAAI,KAAK;AACL,YAAM,IAAI,MAAM,8BAA8B;AAClD,WAAO,IAAI,cAAc,MAAM,MAAM,GAAwB,GAAG;AAAA,EACpE;AAAA,EACA,KAAK,OAAO,KAAK;AACb,QAAI,CAAC;AACD,YAAM,OAAK;AACf,WAAO,KAAK,QAAQ,CAAC,KAAK,GAAG,WAAS,IAAI,MAAM,MAAM,KAAK,CAAC,CAAC;AAAA,EACjE;AACJ;AACA,SAAS,UAAU,GAAG,GAAG;AACrB,SAAO,KAAK,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,CAAC,GAAG,MAAM,MAAM,EAAE,CAAC,CAAC;AACzE;AACA,IAAM,gBAAN,MAAoB;AAAA,EAChB,YAAY,cAAc,OAAO,MAAM,OAAO;AAC1C,SAAK,eAAe;AACpB,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,KAAK;AAAA,EACd;AAAA,EACA,YAAY,WAAW;AACnB,QAAI;AACJ,QAAI,SAAS,KAAK;AAClB,QAAIA,WAAU,KAAK,MAAM;AACzB,QAAI,KAAK,KAAK,IAAI,MAAM,UAAU,EAAE,KAAK,GAAG,QAAQ,KAAK,QAAQ;AACjE,QAAI,SAAS,OAAO,SAAS,OAAO,WAAW,CAAC;AAChD,aAAS,OAAO,KAAK,cAAc;AAC/B,UAAI,OAAO;AACP,iBAAS;AAAA,eACJ,OAAO;AACZ,iBAAS;AAAA,kBACD,KAAK,UAAU,IAAI,EAAE,OAAO,QAAQ,OAAO,SAAS,KAAK,KAAK,MAAM;AAC5E,iBAAS,KAAK,UAAU,IAAI,EAAE,CAAC;AAAA,IACvC;AACA,WAAO;AAAA,MACH,OAAO,OAAO;AACV,cAAM,OAAO,GAAG,IAAI,OAAO,KAAK;AAChC,eAAO;AAAA,MACX;AAAA,MACA,OAAO,OAAO,IAAI;AACd,YAAK,UAAU,GAAG,cAAgB,WAAW,GAAG,cAAc,GAAG,cAAe,UAAU,OAAO,QAAQ,GAAG;AACxG,cAAI,SAAS,OAAO,KAAK;AACzB,cAAI,QAAQ,CAAC,aAAa,QAAQ,MAAM,OAAO,GAAG,GAAGA,QAAO,IAAI,CAACA,SAAQ,QAAQ,MAAM,OAAO,GAAG,CAAC,GAAG;AACjG,kBAAM,OAAO,GAAG,IAAI;AACpB,mBAAO;AAAA,UACX;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AAAA,MACA,aAAa,CAAC,OAAO,aAAa;AAC9B,YAAI,QAAQ,UAAU,SAAS,OAAO,QAAQ,EAAE;AAChD,YAAI,WAAW,MAAM;AACjB,cAAI,SAAS,QAAQ,UAAU,OAAO;AACtC,cAAI,KAAK,aAAa,MAAM,SAAO;AAC/B,mBAAO,eAAe,QAAQ,SAAS,MAAM,GAAG,MAAM,MAAM,MAAM,GAAG,IACjE,eAAe,aAAa,SAAS,MAAM,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,IAAI;AAAA,UAC5F,CAAC,MAAM,QAAQ,aAAa,SAAS,OAAO,KAAK,GAAG,QAAQA,QAAO,IAAIA,SAAQ,SAAS,OAAO,KAAK,GAAG,MAAM,IAAI;AAC7G,kBAAM,OAAO,GAAG,IAAI;AACpB,mBAAO;AAAA,UACX;AAAA,QACJ,OACK;AACD,mBAAS,OAAO,KAAK;AAAA,QACzB;AACA,cAAM,OAAO,GAAG,IAAI;AACpB,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,SAAS,aAAa,GAAG,GAAGA,UAAS;AACjC,MAAI,EAAE,UAAU,EAAE;AACd,WAAO;AACX,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ;AAC1B,QAAI,CAACA,SAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACnB,aAAO;AACf,SAAO;AACX;AACA,SAAS,UAAU,OAAO,OAAO;AAC7B,MAAI,UAAU;AACd,WAAS,QAAQ;AACb,QAAI,WAAW,OAAO,IAAI,IAAI;AAC1B,gBAAU;AAClB,SAAO;AACX;AACA,SAAS,iBAAiB,WAAW,OAAO,WAAW;AACnD,MAAI,gBAAgB,UAAU,IAAI,OAAK,UAAU,EAAE,EAAE,CAAC;AACtD,MAAI,gBAAgB,UAAU,IAAI,OAAK,EAAE,IAAI;AAC7C,MAAI,UAAU,cAAc,OAAO,OAAK,EAAE,IAAI,EAAE;AAChD,MAAI,MAAM,UAAU,MAAM,EAAE,KAAK;AACjC,WAAS,IAAI,OAAO;AAChB,QAAI,SAAS,CAAC;AACd,aAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC3C,UAAI,QAAQ,QAAQ,OAAO,cAAc,CAAC,CAAC;AAC3C,UAAI,cAAc,CAAC,KAAK;AACpB,iBAAS,OAAO;AACZ,iBAAO,KAAK,GAAG;AAAA;AAEnB,eAAO,KAAK,KAAK;AAAA,IACzB;AACA,WAAO,MAAM,QAAQ,MAAM;AAAA,EAC/B;AACA,SAAO;AAAA,IACH,OAAO,OAAO;AACV,eAAS,QAAQ;AACb,mBAAW,OAAO,IAAI;AAC1B,YAAM,OAAO,GAAG,IAAI,IAAI,KAAK;AAC7B,aAAO;AAAA,IACX;AAAA,IACA,OAAO,OAAO,IAAI;AACd,UAAI,CAAC,UAAU,OAAO,OAAO;AACzB,eAAO;AACX,UAAI,QAAQ,IAAI,KAAK;AACrB,UAAI,MAAM,QAAQ,OAAO,MAAM,OAAO,GAAG,CAAC;AACtC,eAAO;AACX,YAAM,OAAO,GAAG,IAAI;AACpB,aAAO;AAAA,IACX;AAAA,IACA,YAAY,OAAO,UAAU;AACzB,UAAI,aAAa,UAAU,OAAO,aAAa;AAC/C,UAAI,eAAe,SAAS,OAAO,OAAO,MAAM,EAAE,GAAG,WAAW,SAAS,MAAM,KAAK;AACpF,UAAI,gBAAgB,CAAC,cAAc,UAAU,WAAW,YAAY,GAAG;AACnE,cAAM,OAAO,GAAG,IAAI;AACpB,eAAO;AAAA,MACX;AACA,UAAI,QAAQ,IAAI,KAAK;AACrB,UAAI,MAAM,QAAQ,OAAO,QAAQ,GAAG;AAChC,cAAM,OAAO,GAAG,IAAI;AACpB,eAAO;AAAA,MACX;AACA,YAAM,OAAO,GAAG,IAAI;AACpB,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AACA,IAAM,YAAyB,MAAM,OAAO,EAAE,QAAQ,KAAK,CAAC;AAK5D,IAAM,aAAN,MAAM,YAAW;AAAA,EACb,YAIA,IAAI,SAAS,SAAS,UAItB,MAAM;AACF,SAAK,KAAK;AACV,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,OAAO;AAIZ,SAAK,WAAW;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,OAAO,QAAQ;AAClB,QAAI,QAAQ,IAAI,YAAW,UAAU,OAAO,QAAQ,OAAO,QAAQ,OAAO,YAAY,CAAC,GAAG,MAAM,MAAM,IAAI,MAAM;AAChH,QAAI,OAAO;AACP,YAAM,WAAW,OAAO,QAAQ,KAAK;AACzC,WAAO;AAAA,EACX;AAAA,EACA,OAAO,OAAO;AACV,QAAI,OAAO,MAAM,MAAM,SAAS,EAAE,KAAK,OAAK,EAAE,SAAS,IAAI;AAC3D,aAAS,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,WAAW,KAAK,SAAS,KAAK;AAAA,EAC5F;AAAA;AAAA;AAAA;AAAA,EAIA,KAAK,WAAW;AACZ,QAAI,MAAM,UAAU,KAAK,EAAE,KAAK;AAChC,WAAO;AAAA,MACH,QAAQ,CAAC,UAAU;AACf,cAAM,OAAO,GAAG,IAAI,KAAK,OAAO,KAAK;AACrC,eAAO;AAAA,MACX;AAAA,MACA,QAAQ,CAAC,OAAO,OAAO;AACnB,YAAI,SAAS,MAAM,OAAO,GAAG;AAC7B,YAAI,QAAQ,KAAK,QAAQ,QAAQ,EAAE;AACnC,YAAI,KAAK,SAAS,QAAQ,KAAK;AAC3B,iBAAO;AACX,cAAM,OAAO,GAAG,IAAI;AACpB,eAAO;AAAA,MACX;AAAA,MACA,aAAa,CAAC,OAAO,aAAa;AAC9B,YAAI,OAAO,MAAM,MAAM,SAAS,GAAG,UAAU,SAAS,MAAM,SAAS,GAAG;AACxE,aAAK,SAAS,KAAK,KAAK,OAAK,EAAE,SAAS,IAAI,MAAM,UAAU,QAAQ,KAAK,OAAK,EAAE,SAAS,IAAI,GAAG;AAC5F,gBAAM,OAAO,GAAG,IAAI,OAAO,OAAO,KAAK;AACvC,iBAAO;AAAA,QACX;AACA,YAAI,SAAS,OAAO,QAAQ,KAAK,EAAE,KAAK,MAAM;AAC1C,gBAAM,OAAO,GAAG,IAAI,SAAS,MAAM,IAAI;AACvC,iBAAO;AAAA,QACX;AACA,cAAM,OAAO,GAAG,IAAI,KAAK,OAAO,KAAK;AACrC,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,KAAK,QAAQ;AACT,WAAO,CAAC,MAAM,UAAU,GAAG,EAAE,OAAO,MAAM,OAAO,CAAC,CAAC;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,YAAY;AAAE,WAAO;AAAA,EAAM;AACnC;AACA,IAAM,QAAQ,EAAE,QAAQ,GAAG,KAAK,GAAG,SAAS,GAAG,MAAM,GAAG,SAAS,EAAE;AACnE,SAAS,KAAK,OAAO;AACjB,SAAO,CAAC,QAAQ,IAAI,cAAc,KAAK,KAAK;AAChD;AAWA,IAAM,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKT,SAAsB,KAAK,MAAM,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxC,MAAmB,KAAK,MAAM,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlC,SAAsB,KAAK,MAAM,OAAO;AAAA;AAAA;AAAA;AAAA,EAIxC,KAAkB,KAAK,MAAM,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhC,QAAqB,KAAK,MAAM,MAAM;AAC1C;AACA,IAAM,gBAAN,MAAoB;AAAA,EAChB,YAAY,OAAOC,OAAM;AACrB,SAAK,QAAQ;AACb,SAAK,OAAOA;AAAA,EAChB;AACJ;AAQA,IAAM,cAAN,MAAM,aAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd,GAAG,KAAK;AAAE,WAAO,IAAI,oBAAoB,MAAM,GAAG;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrD,YAAY,SAAS;AACjB,WAAO,aAAY,YAAY,GAAG,EAAE,aAAa,MAAM,WAAW,QAAQ,CAAC;AAAA,EAC/E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,OAAO;AACP,WAAO,MAAM,OAAO,aAAa,IAAI,IAAI;AAAA,EAC7C;AACJ;AACA,IAAM,sBAAN,MAA0B;AAAA,EACtB,YAAY,aAAa,OAAO;AAC5B,SAAK,cAAc;AACnB,SAAK,QAAQ;AAAA,EACjB;AACJ;AACA,IAAM,gBAAN,MAAM,eAAc;AAAA,EAChB,YAAY,MAAM,cAAc,cAAc,SAAS,cAAc,QAAQ;AACzE,SAAK,OAAO;AACZ,SAAK,eAAe;AACpB,SAAK,eAAe;AACpB,SAAK,UAAU;AACf,SAAK,eAAe;AACpB,SAAK,SAAS;AACd,SAAK,iBAAiB,CAAC;AACvB,WAAO,KAAK,eAAe,SAAS,aAAa;AAC7C,WAAK,eAAe;AAAA,QAAK;AAAA;AAAA,MAA6B;AAAA,EAC9D;AAAA,EACA,YAAY,OAAO;AACf,QAAI,OAAO,KAAK,QAAQ,MAAM,EAAE;AAChC,WAAO,QAAQ,OAAO,MAAM,UAAU,KAAK,aAAa,QAAQ,CAAC;AAAA,EACrE;AAAA,EACA,OAAO,QAAQ,MAAM,cAAc,UAAU;AACzC,QAAI,SAAS,CAAC;AACd,QAAI,SAAS,uBAAO,OAAO,IAAI;AAC/B,QAAI,kBAAkB,oBAAI,IAAI;AAC9B,aAAS,OAAO,QAAQ,MAAM,cAAc,eAAe,GAAG;AAC1D,UAAI,eAAe;AACf,eAAO,KAAK,GAAG;AAAA;AAEf,SAAC,OAAO,IAAI,MAAM,EAAE,MAAM,OAAO,IAAI,MAAM,EAAE,IAAI,CAAC,IAAI,KAAK,GAAG;AAAA,IACtE;AACA,QAAI,UAAU,uBAAO,OAAO,IAAI;AAChC,QAAI,eAAe,CAAC;AACpB,QAAI,eAAe,CAAC;AACpB,aAAS,SAAS,QAAQ;AACtB,cAAQ,MAAM,EAAE,IAAI,aAAa,UAAU;AAC3C,mBAAa,KAAK,OAAK,MAAM,KAAK,CAAC,CAAC;AAAA,IACxC;AACA,QAAI,YAAY,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,OAAO;AACpF,aAAS,MAAM,QAAQ;AACnB,UAAI,YAAY,OAAO,EAAE,GAAG,QAAQ,UAAU,CAAC,EAAE;AACjD,UAAI,eAAe,aAAa,UAAU,EAAE,KAAK,CAAC;AAClD,UAAI,UAAU;AAAA,QAAM,OAAK,EAAE,QAAQ;AAAA;AAAA,MAAuB,GAAG;AACzD,gBAAQ,MAAM,EAAE,IAAK,aAAa,UAAU,IAAK;AACjD,YAAI,UAAU,cAAc,SAAS,GAAG;AACpC,uBAAa,KAAK,SAAS,MAAM,KAAK,CAAC;AAAA,QAC3C,OACK;AACD,cAAI,QAAQ,MAAM,QAAQ,UAAU,IAAI,OAAK,EAAE,KAAK,CAAC;AACrD,uBAAa,KAAK,YAAY,MAAM,QAAQ,OAAO,SAAS,MAAM,KAAK,CAAC,IAAI,SAAS,MAAM,KAAK,IAAI,KAAK;AAAA,QAC7G;AAAA,MACJ,OACK;AACD,iBAAS,KAAK,WAAW;AACrB,cAAI,EAAE,QAAQ,GAAyB;AACnC,oBAAQ,EAAE,EAAE,IAAK,aAAa,UAAU,IAAK;AAC7C,yBAAa,KAAK,EAAE,KAAK;AAAA,UAC7B,OACK;AACD,oBAAQ,EAAE,EAAE,IAAI,aAAa,UAAU;AACvC,yBAAa,KAAK,OAAK,EAAE,YAAY,CAAC,CAAC;AAAA,UAC3C;AAAA,QACJ;AACA,gBAAQ,MAAM,EAAE,IAAI,aAAa,UAAU;AAC3C,qBAAa,KAAK,OAAK,iBAAiB,GAAG,OAAO,SAAS,CAAC;AAAA,MAChE;AAAA,IACJ;AACA,QAAI,UAAU,aAAa,IAAI,OAAK,EAAE,OAAO,CAAC;AAC9C,WAAO,IAAI,eAAc,MAAM,iBAAiB,SAAS,SAAS,cAAc,MAAM;AAAA,EAC1F;AACJ;AACA,SAAS,QAAQ,WAAW,cAAc,iBAAiB;AACvD,MAAI,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAChC,MAAI,OAAO,oBAAI,IAAI;AACnB,WAAS,MAAM,KAAKA,OAAM;AACtB,QAAI,QAAQ,KAAK,IAAI,GAAG;AACxB,QAAI,SAAS,MAAM;AACf,UAAI,SAASA;AACT;AACJ,UAAI,QAAQ,OAAO,KAAK,EAAE,QAAQ,GAAG;AACrC,UAAI,QAAQ;AACR,eAAO,KAAK,EAAE,OAAO,OAAO,CAAC;AACjC,UAAI,eAAe;AACf,wBAAgB,OAAO,IAAI,WAAW;AAAA,IAC9C;AACA,SAAK,IAAI,KAAKA,KAAI;AAClB,QAAI,MAAM,QAAQ,GAAG,GAAG;AACpB,eAAS,KAAK;AACV,cAAM,GAAGA,KAAI;AAAA,IACrB,WACS,eAAe,qBAAqB;AACzC,UAAI,gBAAgB,IAAI,IAAI,WAAW;AACnC,cAAM,IAAI,WAAW,4CAA4C;AACrE,UAAI,UAAU,aAAa,IAAI,IAAI,WAAW,KAAK,IAAI;AACvD,sBAAgB,IAAI,IAAI,aAAa,OAAO;AAC5C,YAAM,SAASA,KAAI;AAAA,IACvB,WACS,eAAe,eAAe;AACnC,YAAM,IAAI,OAAO,IAAI,IAAI;AAAA,IAC7B,WACS,eAAe,YAAY;AAChC,aAAOA,KAAI,EAAE,KAAK,GAAG;AACrB,UAAI,IAAI;AACJ,cAAM,IAAI,UAAUA,KAAI;AAAA,IAChC,WACS,eAAe,eAAe;AACnC,aAAOA,KAAI,EAAE,KAAK,GAAG;AACrB,UAAI,IAAI,MAAM;AACV,cAAM,IAAI,MAAM,YAAY,MAAM,OAAO;AAAA,IACjD,OACK;AACD,UAAI,UAAU,IAAI;AAClB,UAAI,CAAC;AACD,cAAM,IAAI,MAAM,kDAAkD,GAAG,mHAAmH;AAC5L,YAAM,SAASA,KAAI;AAAA,IACvB;AAAA,EACJ;AACA,QAAM,WAAW,MAAM,OAAO;AAC9B,SAAO,OAAO,OAAO,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,CAAC;AAC9C;AACA,SAAS,WAAW,OAAO,MAAM;AAC7B,MAAI,OAAO;AACP,WAAO;AACX,MAAI,MAAM,QAAQ;AAClB,MAAI,SAAS,MAAM,OAAO,GAAG;AAC7B,MAAI,UAAU;AACV,UAAM,IAAI,MAAM,gDAAgD;AACpE,MAAI,SAAS;AACT,WAAO;AACX,QAAM,OAAO,GAAG,IAAI;AACpB,MAAI,UAAU,MAAM,YAAY,OAAO,MAAM,OAAO,aAAa,GAAG,CAAC;AACrE,SAAO,MAAM,OAAO,GAAG,IAAI,IAA8B;AAC7D;AACA,SAAS,QAAQ,OAAO,MAAM;AAC1B,SAAO,OAAO,IAAI,MAAM,OAAO,aAAa,QAAQ,CAAC,IAAI,MAAM,OAAO,QAAQ,CAAC;AACnF;AAEA,IAAM,eAA4B,MAAM,OAAO;AAC/C,IAAM,0BAAuC,MAAM,OAAO;AAAA,EACtD,SAAS,YAAU,OAAO,KAAK,OAAK,CAAC;AAAA,EACrC,QAAQ;AACZ,CAAC;AACD,IAAM,gBAA6B,MAAM,OAAO;AAAA,EAC5C,SAAS,YAAU,OAAO,SAAS,OAAO,CAAC,IAAI;AAAA,EAC/C,QAAQ;AACZ,CAAC;AACD,IAAM,eAA4B,MAAM,OAAO;AAC/C,IAAM,oBAAiC,MAAM,OAAO;AACpD,IAAM,sBAAmC,MAAM,OAAO;AACtD,IAAM,WAAwB,MAAM,OAAO;AAAA,EACvC,SAAS,YAAU,OAAO,SAAS,OAAO,CAAC,IAAI;AACnD,CAAC;AAWD,IAAM,aAAN,MAAiB;AAAA;AAAA;AAAA;AAAA,EAIb,YAIA,MAIA,OAAO;AACH,SAAK,OAAO;AACZ,SAAK,QAAQ;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS;AAAE,WAAO,IAAI,eAAe;AAAA,EAAG;AACnD;AAIA,IAAM,iBAAN,MAAqB;AAAA;AAAA;AAAA;AAAA,EAIjB,GAAG,OAAO;AAAE,WAAO,IAAI,WAAW,MAAM,KAAK;AAAA,EAAG;AACpD;AAKA,IAAM,kBAAN,MAAsB;AAAA;AAAA;AAAA;AAAA,EAIlB,YAQA,KAAK;AACD,SAAK,MAAM;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,GAAG,OAAO;AAAE,WAAO,IAAI,YAAY,MAAM,KAAK;AAAA,EAAG;AACrD;AAQA,IAAM,cAAN,MAAM,aAAY;AAAA;AAAA;AAAA;AAAA,EAId,YAIA,MAIA,OAAO;AACH,SAAK,OAAO;AACZ,SAAK,QAAQ;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,SAAS;AACT,QAAI,SAAS,KAAK,KAAK,IAAI,KAAK,OAAO,OAAO;AAC9C,WAAO,WAAW,SAAY,SAAY,UAAU,KAAK,QAAQ,OAAO,IAAI,aAAY,KAAK,MAAM,MAAM;AAAA,EAC7G;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,GAAG,MAAM;AAAE,WAAO,KAAK,QAAQ;AAAA,EAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQrC,OAAO,OAAO,OAAO,CAAC,GAAG;AACrB,WAAO,IAAI,gBAAgB,KAAK,QAAQ,OAAK,EAAE;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,WAAW,SAAS,SAAS;AAChC,QAAI,CAAC,QAAQ;AACT,aAAO;AACX,QAAI,SAAS,CAAC;AACd,aAAS,UAAU,SAAS;AACxB,UAAI,SAAS,OAAO,IAAI,OAAO;AAC/B,UAAI;AACA,eAAO,KAAK,MAAM;AAAA,IAC1B;AACA,WAAO;AAAA,EACX;AACJ;AAQA,YAAY,cAA2B,YAAY,OAAO;AAI1D,YAAY,eAA4B,YAAY,OAAO;AAU3D,IAAM,cAAN,MAAM,aAAY;AAAA,EACd,YAIA,YAIA,SAKA,WAIA,SAIA,aAKA,gBAAgB;AACZ,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,iBAAiB;AAItB,SAAK,OAAO;AAIZ,SAAK,SAAS;AACd,QAAI;AACA,qBAAe,WAAW,QAAQ,SAAS;AAC/C,QAAI,CAAC,YAAY,KAAK,CAAC,MAAM,EAAE,QAAQ,aAAY,IAAI;AACnD,WAAK,cAAc,YAAY,OAAO,aAAY,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC;AAAA,EAC7E;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,OAAO,YAAY,SAAS,WAAW,SAAS,aAAa,gBAAgB;AAChF,WAAO,IAAI,aAAY,YAAY,SAAS,WAAW,SAAS,aAAa,cAAc;AAAA,EAC/F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,SAAS;AACT,WAAO,KAAK,SAAS,KAAK,OAAO,KAAK,QAAQ,MAAM,KAAK,WAAW,GAAG;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,eAAe;AACf,WAAO,KAAK,aAAa,KAAK,WAAW,UAAU,IAAI,KAAK,OAAO;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,QAAQ;AACR,QAAI,CAAC,KAAK;AACN,WAAK,WAAW,iBAAiB,IAAI;AACzC,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,MAAM;AACb,aAAS,OAAO,KAAK;AACjB,UAAI,IAAI,QAAQ;AACZ,eAAO,IAAI;AACnB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,aAAa;AAAE,WAAO,CAAC,KAAK,QAAQ;AAAA,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO/C,IAAI,eAAe;AAAE,WAAO,KAAK,WAAW,UAAU,KAAK,MAAM;AAAA,EAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQzE,YAAY,OAAO;AACf,QAAI,IAAI,KAAK,WAAW,aAAY,SAAS;AAC7C,WAAO,CAAC,EAAE,MAAM,KAAK,SAAS,EAAE,SAAS,MAAM,UAAU,EAAE,MAAM,GAAG,MAAM,MAAM,KAAK,SAAS,EAAE,MAAM,MAAM,KAAK;AAAA,EACrH;AACJ;AAKA,YAAY,OAAoB,WAAW,OAAO;AA2BlD,YAAY,YAAyB,WAAW,OAAO;AAKvD,YAAY,eAA4B,WAAW,OAAO;AAO1D,YAAY,SAAsB,WAAW,OAAO;AACpD,SAAS,WAAW,GAAG,GAAG;AACtB,MAAI,SAAS,CAAC;AACd,WAAS,KAAK,GAAG,KAAK,OAAK;AACvB,QAAI,MAAM;AACV,QAAI,KAAK,EAAE,WAAW,MAAM,EAAE,UAAU,EAAE,EAAE,KAAK,EAAE,EAAE,IAAI;AACrD,aAAO,EAAE,IAAI;AACb,WAAK,EAAE,IAAI;AAAA,IACf,WACS,KAAK,EAAE,QAAQ;AACpB,aAAO,EAAE,IAAI;AACb,WAAK,EAAE,IAAI;AAAA,IACf;AAEI,aAAO;AACX,QAAI,CAAC,OAAO,UAAU,OAAO,OAAO,SAAS,CAAC,IAAI;AAC9C,aAAO,KAAK,MAAM,EAAE;AAAA,aACf,OAAO,OAAO,SAAS,CAAC,IAAI;AACjC,aAAO,OAAO,SAAS,CAAC,IAAI;AAAA,EACpC;AACJ;AACA,SAAS,iBAAiB,GAAG,GAAG,YAAY;AACxC,MAAI;AACJ,MAAI,SAAS,SAAS;AACtB,MAAI,YAAY;AACZ,cAAU,EAAE;AACZ,cAAU,UAAU,MAAM,EAAE,QAAQ,MAAM;AAC1C,cAAU,EAAE,QAAQ,QAAQ,EAAE,OAAO;AAAA,EACzC,OACK;AACD,cAAU,EAAE,QAAQ,IAAI,EAAE,OAAO;AACjC,cAAU,EAAE,QAAQ,QAAQ,EAAE,SAAS,IAAI;AAC3C,cAAU,EAAE,QAAQ,QAAQ,OAAO;AAAA,EACvC;AACA,SAAO;AAAA,IACH;AAAA,IACA,WAAW,EAAE,YAAY,EAAE,UAAU,IAAI,OAAO,KAAK,KAAK,EAAE,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI,OAAO;AAAA,IAC1H,SAAS,YAAY,WAAW,EAAE,SAAS,OAAO,EAAE,OAAO,YAAY,WAAW,EAAE,SAAS,OAAO,CAAC;AAAA,IACrG,aAAa,EAAE,YAAY,SAAS,EAAE,YAAY,OAAO,EAAE,WAAW,IAAI,EAAE;AAAA,IAC5E,gBAAgB,EAAE,kBAAkB,EAAE;AAAA,EAC1C;AACJ;AACA,SAAS,wBAAwB,OAAO,MAAM,SAAS;AACnD,MAAI,MAAM,KAAK,WAAW,cAAc,QAAQ,KAAK,WAAW;AAChE,MAAI,KAAK;AACL,kBAAc,YAAY,OAAO,YAAY,UAAU,GAAG,KAAK,SAAS,CAAC;AAC7E,SAAO;AAAA,IACH,SAAS,KAAK,mBAAmB,YAAY,KAAK,UAC5C,UAAU,GAAG,KAAK,WAAW,CAAC,GAAG,SAAS,MAAM,MAAM,aAAa,CAAC;AAAA,IAC1E,WAAW,QAAQ,eAAe,kBAAkB,MAAM,gBAAgB,OAAO,IAAI,QAAQ,IAAI,IAAI;AAAA,IACrG,SAAS,QAAQ,KAAK,OAAO;AAAA,IAC7B;AAAA,IACA,gBAAgB,CAAC,CAAC,KAAK;AAAA,EAC3B;AACJ;AACA,SAAS,mBAAmB,OAAO,OAAO,QAAQ;AAC9C,MAAI,IAAI,wBAAwB,OAAO,MAAM,SAAS,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,MAAM;AACrF,MAAI,MAAM,UAAU,MAAM,CAAC,EAAE,WAAW;AACpC,aAAS;AACb,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,QAAI,MAAM,CAAC,EAAE,WAAW;AACpB,eAAS;AACb,QAAI,MAAM,CAAC,CAAC,MAAM,CAAC,EAAE;AACrB,QAAI,iBAAiB,GAAG,wBAAwB,OAAO,MAAM,CAAC,GAAG,MAAM,EAAE,QAAQ,YAAY,MAAM,IAAI,MAAM,GAAG,GAAG;AAAA,EACvH;AACA,MAAI,KAAK,YAAY,OAAO,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,aAAa,EAAE,cAAc;AACrG,SAAO,kBAAkB,SAAS,kBAAkB,EAAE,IAAI,EAAE;AAChE;AAEA,SAAS,kBAAkB,IAAI;AAC3B,MAAI,QAAQ,GAAG;AAEf,MAAI,SAAS;AACb,WAAS,UAAU,MAAM,MAAM,YAAY,GAAG;AAC1C,QAAI,QAAQ,OAAO,EAAE;AACrB,QAAI,UAAU,OAAO;AACjB,eAAS;AACT;AAAA,IACJ;AACA,QAAI,MAAM,QAAQ,KAAK;AACnB,eAAS,WAAW,OAAO,QAAQ,WAAW,QAAQ,KAAK;AAAA,EACnE;AACA,MAAI,WAAW,MAAM;AACjB,QAAI,SAAS;AACb,QAAI,WAAW,OAAO;AAClB,aAAO,GAAG,QAAQ;AAClB,gBAAU,UAAU,MAAM,MAAM,IAAI,MAAM;AAAA,IAC9C,OACK;AACD,UAAI,WAAW,GAAG,QAAQ,OAAO,MAAM;AACvC,gBAAU,SAAS;AACnB,aAAO,SAAS,SAAS,QAAQ,SAAS,OAAO,EAAE;AAAA,IACvD;AACA,SAAK,YAAY,OAAO,OAAO,SAAS,GAAG,aAAa,GAAG,UAAU,IAAI,IAAI,GAAG,YAAY,WAAW,GAAG,SAAS,IAAI,GAAG,GAAG,aAAa,GAAG,cAAc;AAAA,EAC/J;AAEA,MAAI,UAAU,MAAM,MAAM,iBAAiB;AAC3C,WAAS,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AAC1C,QAAI,WAAW,QAAQ,CAAC,EAAE,EAAE;AAC5B,QAAI,oBAAoB;AACpB,WAAK;AAAA,aACA,MAAM,QAAQ,QAAQ,KAAK,SAAS,UAAU,KAAK,SAAS,CAAC,aAAa;AAC/E,WAAK,SAAS,CAAC;AAAA;AAEf,WAAK,mBAAmB,OAAO,QAAQ,QAAQ,GAAG,KAAK;AAAA,EAC/D;AACA,SAAO;AACX;AACA,SAAS,kBAAkB,IAAI;AAC3B,MAAI,QAAQ,GAAG,YAAY,YAAY,MAAM,MAAM,mBAAmB,GAAG,OAAO;AAChF,WAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,KAAK;AAC5C,QAAI,YAAY,UAAU,CAAC,EAAE,EAAE;AAC/B,QAAI,aAAa,OAAO,KAAK,SAAS,EAAE;AACpC,aAAO,iBAAiB,MAAM,wBAAwB,OAAO,WAAW,GAAG,QAAQ,SAAS,GAAG,IAAI;AAAA,EAC3G;AACA,SAAO,QAAQ,KAAK,KAAK,YAAY,OAAO,OAAO,GAAG,SAAS,GAAG,WAAW,KAAK,SAAS,KAAK,aAAa,KAAK,cAAc;AACpI;AACA,IAAM,OAAO,CAAC;AACd,SAAS,QAAQ,OAAO;AACpB,SAAO,SAAS,OAAO,OAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AACvE;AAOA,IAAI,eAA6B,SAAUC,eAAc;AAIrD,EAAAA,cAAaA,cAAa,MAAM,IAAI,CAAC,IAAI;AAIzC,EAAAA,cAAaA,cAAa,OAAO,IAAI,CAAC,IAAI;AAI1C,EAAAA,cAAaA,cAAa,OAAO,IAAI,CAAC,IAAI;AAC9C,SAAOA;AAAY,EAAG,iBAAiB,eAAe,CAAC,EAAE;AACzD,IAAM,6BAA6B;AACnC,IAAI;AACJ,IAAI;AACA,aAAwB,IAAI,OAAO,iCAAiC,GAAG;AAC3E,SACO,GAAG;AAAE;AACZ,SAAS,YAAY,KAAK;AACtB,MAAI;AACA,WAAO,SAAS,KAAK,GAAG;AAC5B,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,QAAI,KAAK,IAAI,CAAC;AACd,QAAI,KAAK,KAAK,EAAE,KAAK,KAAK,QAAW,GAAG,YAAY,KAAK,GAAG,YAAY,KAAK,2BAA2B,KAAK,EAAE;AAC3G,aAAO;AAAA,EACf;AACA,SAAO;AACX;AACA,SAAS,gBAAgB,WAAW;AAChC,SAAO,CAAC,SAAS;AACb,QAAI,CAAC,KAAK,KAAK,IAAI;AACf,aAAO,aAAa;AACxB,QAAI,YAAY,IAAI;AAChB,aAAO,aAAa;AACxB,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ;AAClC,UAAI,KAAK,QAAQ,UAAU,CAAC,CAAC,IAAI;AAC7B,eAAO,aAAa;AAC5B,WAAO,aAAa;AAAA,EACxB;AACJ;AAWA,IAAM,cAAN,MAAM,aAAY;AAAA,EACd,YAIA,QAIA,KAIA,WAIA,QAAQ,aAAa,IAAI;AACrB,SAAK,SAAS;AACd,SAAK,MAAM;AACX,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,SAAS,OAAO,eAAe,MAAM;AAC1C,SAAK,cAAc;AAGnB,QAAI;AACA,SAAG,SAAS;AAChB,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,aAAa,QAAQ;AACjD,iBAAW,MAAM,KAAK,CAAC;AAC3B,SAAK,cAAc;AAAA,EACvB;AAAA,EACA,MAAM,OAAOC,WAAU,MAAM;AACzB,QAAI,OAAO,KAAK,OAAO,QAAQ,MAAM,EAAE;AACvC,QAAI,QAAQ,MAAM;AACd,UAAIA;AACA,cAAM,IAAI,WAAW,oCAAoC;AAC7D,aAAO;AAAA,IACX;AACA,eAAW,MAAM,IAAI;AACrB,WAAO,QAAQ,MAAM,IAAI;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,UAAU,OAAO;AACb,WAAO,mBAAmB,MAAM,OAAO,IAAI;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB,IAAI;AACjB,QAAI,OAAO,KAAK,QAAQ,EAAE,MAAM,aAAa,IAAI;AACjD,aAAS,UAAU,GAAG,SAAS;AAC3B,UAAI,OAAO,GAAG,YAAY,WAAW,GAAG;AACpC,YAAI,MAAM;AACN,yBAAe,oBAAI;AACnB,eAAK,aAAa,QAAQ,CAAC,KAAK,QAAQ,aAAa,IAAI,KAAK,GAAG,CAAC;AAClE,iBAAO;AAAA,QACX;AACA,qBAAa,IAAI,OAAO,MAAM,aAAa,OAAO,MAAM,SAAS;AAAA,MACrE,WACS,OAAO,GAAG,YAAY,WAAW,GAAG;AACzC,eAAO;AACP,eAAO,OAAO;AAAA,MAClB,WACS,OAAO,GAAG,YAAY,YAAY,GAAG;AAC1C,eAAO;AACP,eAAO,QAAQ,IAAI,EAAE,OAAO,OAAO,KAAK;AAAA,MAC5C;AAAA,IACJ;AACA,QAAI;AACJ,QAAI,CAAC,MAAM;AACP,aAAO,cAAc,QAAQ,MAAM,cAAc,IAAI;AACrD,UAAI,oBAAoB,IAAI,aAAY,MAAM,KAAK,KAAK,KAAK,WAAW,KAAK,aAAa,IAAI,MAAM,IAAI,GAAG,CAAC,OAAO,SAAS,KAAK,YAAY,OAAO,IAAI,GAAG,IAAI;AAC/J,oBAAc,kBAAkB;AAAA,IACpC,OACK;AACD,oBAAc,GAAG,WAAW,OAAO,MAAM;AAAA,IAC7C;AACA,QAAI,YAAY,GAAG,WAAW,MAAM,uBAAuB,IAAI,GAAG,eAAe,GAAG,aAAa,SAAS;AAC1G,QAAI,aAAY,MAAM,GAAG,QAAQ,WAAW,aAAa,CAAC,OAAO,SAAS,KAAK,OAAO,OAAO,EAAE,GAAG,EAAE;AAAA,EACxG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,MAAM;AACnB,QAAI,OAAO,QAAQ;AACf,aAAO,KAAK,OAAO,IAAI;AAC3B,WAAO,KAAK,cAAc,YAAU;AAAA,MAAE,SAAS,EAAE,MAAM,MAAM,MAAM,IAAI,MAAM,IAAI,QAAQ,KAAK;AAAA,MAC1F,OAAO,gBAAgB,OAAO,MAAM,OAAO,KAAK,MAAM;AAAA,IAAE,EAAE;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,cAAc,GAAG;AACb,QAAI,MAAM,KAAK;AACf,QAAI,UAAU,EAAE,IAAI,OAAO,CAAC,CAAC;AAC7B,QAAI,UAAU,KAAK,QAAQ,QAAQ,OAAO,GAAG,SAAS,CAAC,QAAQ,KAAK;AACpE,QAAI,UAAU,QAAQ,QAAQ,OAAO;AACrC,aAAS,IAAI,GAAG,IAAI,IAAI,OAAO,QAAQ,KAAK;AACxC,UAAI,SAAS,EAAE,IAAI,OAAO,CAAC,CAAC;AAC5B,UAAI,aAAa,KAAK,QAAQ,OAAO,OAAO,GAAG,YAAY,WAAW,IAAI,OAAO;AACjF,eAAS,IAAI,GAAG,IAAI,GAAG;AACnB,eAAO,CAAC,IAAI,OAAO,CAAC,EAAE,IAAI,SAAS;AACvC,UAAI,QAAQ,QAAQ,QAAQ,YAAY,IAAI;AAC5C,aAAO,KAAK,OAAO,MAAM,IAAI,KAAK,CAAC;AACnC,gBAAU,QAAQ,QAAQ,SAAS;AACnC,gBAAU,YAAY,WAAW,SAAS,SAAS,EAAE,OAAO,YAAY,WAAW,QAAQ,OAAO,OAAO,GAAG,KAAK,CAAC;AAAA,IACtH;AACA,WAAO;AAAA,MACH;AAAA,MACA,WAAW,gBAAgB,OAAO,QAAQ,IAAI,SAAS;AAAA,MACvD;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,OAAO,CAAC,GAAG;AACf,QAAI,gBAAgB;AAChB,aAAO;AACX,WAAO,UAAU,GAAG,MAAM,KAAK,IAAI,QAAQ,KAAK,MAAM,aAAY,aAAa,CAAC;AAAA,EACpF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,QAAQ;AACX,WAAO,KAAK,GAAG,OAAO,MAAM,KAAK,MAAM,aAAY,aAAa,KAAK,YAAY,CAAC;AAAA,EACtF;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,OAAO,GAAG,KAAK,KAAK,IAAI,QAAQ;AACrC,WAAO,KAAK,IAAI,YAAY,MAAM,IAAI,KAAK,SAAS;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,OAAO;AACT,QAAI,OAAO,KAAK,OAAO,QAAQ,MAAM,EAAE;AACvC,QAAI,QAAQ;AACR,aAAO,MAAM;AACjB,eAAW,MAAM,IAAI;AACrB,WAAO,QAAQ,MAAM,IAAI;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,QAAQ;AACX,QAAI,SAAS;AAAA,MACT,KAAK,KAAK,SAAS;AAAA,MACnB,WAAW,KAAK,UAAU,OAAO;AAAA,IACrC;AACA,QAAI;AACA,eAAS,QAAQ,QAAQ;AACrB,YAAI,QAAQ,OAAO,IAAI;AACvB,YAAI,iBAAiB,cAAc,KAAK,OAAO,QAAQ,MAAM,EAAE,KAAK;AAChE,iBAAO,IAAI,IAAI,MAAM,KAAK,OAAO,KAAK,MAAM,OAAO,IAAI,CAAC,GAAG,IAAI;AAAA,MACvE;AACJ,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,SAAS,MAAM,SAAS,CAAC,GAAG,QAAQ;AACvC,QAAI,CAAC,QAAQ,OAAO,KAAK,OAAO;AAC5B,YAAM,IAAI,WAAW,6CAA6C;AACtE,QAAI,YAAY,CAAC;AACjB,QAAI;AACA,eAAS,QAAQ,QAAQ;AACrB,YAAI,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,GAAG;AAClD,cAAI,QAAQ,OAAO,IAAI,GAAG,QAAQ,KAAK,IAAI;AAC3C,oBAAU,KAAK,MAAM,KAAK,WAAS,MAAM,KAAK,SAAS,OAAO,KAAK,CAAC,CAAC;AAAA,QACzE;AAAA,MACJ;AACJ,WAAO,aAAY,OAAO;AAAA,MACtB,KAAK,KAAK;AAAA,MACV,WAAW,gBAAgB,SAAS,KAAK,SAAS;AAAA,MAClD,YAAY,OAAO,aAAa,UAAU,OAAO,CAAC,OAAO,UAAU,CAAC,IAAI;AAAA,IAC5E,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,OAAO,SAAS,CAAC,GAAG;AACvB,QAAI,gBAAgB,cAAc,QAAQ,OAAO,cAAc,CAAC,GAAG,oBAAI,KAAG;AAC1E,QAAI,MAAM,OAAO,eAAe,OAAO,OAAO,MACxC,KAAK,IAAI,OAAO,OAAO,IAAI,MAAM,cAAc,YAAY,aAAY,aAAa,KAAK,YAAY,CAAC;AAC5G,QAAI,YAAY,CAAC,OAAO,YAAY,gBAAgB,OAAO,CAAC,IACtD,OAAO,qBAAqB,kBAAkB,OAAO,YACjD,gBAAgB,OAAO,OAAO,UAAU,QAAQ,OAAO,UAAU,IAAI;AAC/E,mBAAe,WAAW,IAAI,MAAM;AACpC,QAAI,CAAC,cAAc,YAAY,uBAAuB;AAClD,kBAAY,UAAU,SAAS;AACnC,WAAO,IAAI,aAAY,eAAe,KAAK,WAAW,cAAc,aAAa,IAAI,MAAM,IAAI,GAAG,CAAC,OAAO,SAAS,KAAK,OAAO,KAAK,GAAG,IAAI;AAAA,EAC/I;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,UAAU;AAAE,WAAO,KAAK,MAAM,aAAY,OAAO;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxD,IAAI,YAAY;AAAE,WAAO,KAAK,MAAM,aAAY,aAAa,KAAK;AAAA,EAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxE,IAAI,WAAW;AAAE,WAAO,KAAK,MAAM,QAAQ;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAW9C,OAAO,WAAWL,SAAQ;AACtB,aAAS,OAAO,KAAK,MAAM,aAAY,OAAO;AAC1C,UAAI,OAAO,UAAU,eAAe,KAAK,KAAK,MAAM,GAAG;AACnD,iBAAS,IAAI,MAAM;AACnB;AAAA,MACJ;AACJ,QAAIA,QAAO;AACP,eAAS,OAAO,QAAQ,eAAe,CAAC,GAAG,MAAM;AAC7C,YAAI,KAAK;AACL,iBAAO;AACX,YAAI,IAAI,EAAE,KAAK;AACf,eAAO,CAAC,KAAK,IAAIA,QAAO,SAAS,IAAIA,QAAO,IAAI,CAAC;AAAA,MACrD,CAAC;AACL,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,eAAe,MAAM,KAAK,OAAO,IAAI;AACjC,QAAI,SAAS,CAAC;AACd,aAAS,YAAY,KAAK,MAAM,YAAY,GAAG;AAC3C,eAAS,UAAU,SAAS,MAAM,KAAK,IAAI,GAAG;AAC1C,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,IAAI;AACjD,iBAAO,KAAK,OAAO,IAAI,CAAC;AAAA,MAChC;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,gBAAgB,IAAI;AAChB,WAAO,gBAAgB,KAAK,eAAe,aAAa,EAAE,EAAE,KAAK,EAAE,CAAC;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,KAAK;AACR,QAAI,EAAE,MAAM,MAAM,OAAO,IAAI,KAAK,IAAI,OAAO,GAAG;AAChD,QAAI,MAAM,KAAK,gBAAgB,GAAG;AAClC,QAAI,QAAQ,MAAM,MAAM,MAAM,MAAM;AACpC,WAAO,QAAQ,GAAG;AACd,UAAI,OAAON,kBAAiB,MAAM,OAAO,KAAK;AAC9C,UAAI,IAAI,KAAK,MAAM,MAAM,KAAK,CAAC,KAAK,aAAa;AAC7C;AACJ,cAAQ;AAAA,IACZ;AACA,WAAO,MAAM,QAAQ;AACjB,UAAI,OAAOA,kBAAiB,MAAM,GAAG;AACrC,UAAI,IAAI,KAAK,MAAM,KAAK,IAAI,CAAC,KAAK,aAAa;AAC3C;AACJ,YAAM;AAAA,IACV;AACA,WAAO,SAAS,MAAM,OAAO,gBAAgB,MAAM,QAAQ,MAAM,MAAM,IAAI;AAAA,EAC/E;AACJ;AASA,YAAY,0BAA0B;AAMtC,YAAY,UAAuB,MAAM,OAAO;AAAA,EAC5C,SAAS,YAAU,OAAO,SAAS,OAAO,CAAC,IAAI;AACnD,CAAC;AAUD,YAAY,gBAAgB;AAc5B,YAAY,WAAW;AAOvB,YAAY,UAAuB,MAAM,OAAO;AAAA,EAC5C,QAAQ,GAAG,GAAG;AACV,QAAI,KAAK,OAAO,KAAK,CAAC,GAAG,KAAK,OAAO,KAAK,CAAC;AAC3C,WAAO,GAAG,UAAU,GAAG,UAAU,GAAG,MAAM,OAAK,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC;AAAA,EAC/D;AACJ,CAAC;AAKD,YAAY,eAAe;AAe3B,YAAY,eAAe;AAoB3B,YAAY,oBAAoB;AAchC,YAAY,sBAAsB;AAClC,YAAY,cAA2B,YAAY,OAAO;AAW1D,SAAS,cAAc,SAAS,UAChC,UAAU,CAAC,GAAG;AACV,MAAI,SAAS,CAAC;AACd,WAAS,UAAU;AACf,aAAS,OAAO,OAAO,KAAK,MAAM,GAAG;AACjC,UAAI,QAAQ,OAAO,GAAG,GAAG,UAAU,OAAO,GAAG;AAC7C,UAAI,YAAY;AACZ,eAAO,GAAG,IAAI;AAAA,eACT,YAAY,SAAS,UAAU,OAAW;AAAA,eAC1C,OAAO,eAAe,KAAK,SAAS,GAAG;AAC5C,eAAO,GAAG,IAAI,QAAQ,GAAG,EAAE,SAAS,KAAK;AAAA;AAEzC,cAAM,IAAI,MAAM,qCAAqC,GAAG;AAAA,IAChE;AACJ,WAAS,OAAO;AACZ,QAAI,OAAO,GAAG,MAAM;AAChB,aAAO,GAAG,IAAI,SAAS,GAAG;AAClC,SAAO;AACX;AAMA,IAAM,aAAN,MAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQb,GAAG,OAAO;AAAE,WAAO,QAAQ;AAAA,EAAO;AAAA;AAAA;AAAA;AAAA,EAIlC,MAAM,MAAM,KAAK,MAAM;AAAE,WAAO,MAAM,OAAO,MAAM,IAAI,IAAI;AAAA,EAAG;AAClE;AACA,WAAW,UAAU,YAAY,WAAW,UAAU,UAAU;AAChE,WAAW,UAAU,QAAQ;AAC7B,WAAW,UAAU,UAAU,QAAQ;AAIvC,IAAM,QAAN,MAAM,OAAM;AAAA,EACR,YAIA,MAIA,IAIA,OAAO;AACH,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,QAAQ;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,OAAO,MAAM,IAAI,OAAO;AAC3B,WAAO,IAAI,OAAM,MAAM,IAAI,KAAK;AAAA,EACpC;AACJ;AACA,SAAS,SAAS,GAAG,GAAG;AACpB,SAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,EAAE,MAAM;AAC1D;AACA,IAAM,QAAN,MAAM,OAAM;AAAA,EACR,YAAY,MAAM,IAAI,OAKtB,UAAU;AACN,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,QAAQ;AACb,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,IAAI,SAAS;AAAE,WAAO,KAAK,GAAG,KAAK,GAAG,SAAS,CAAC;AAAA,EAAG;AAAA;AAAA;AAAA,EAGnD,UAAU,KAAK,MAAM,KAAK,UAAU,GAAG;AACnC,QAAI,MAAM,MAAM,KAAK,KAAK,KAAK;AAC/B,aAAS,KAAK,SAAS,KAAK,IAAI,YAAU;AACtC,UAAI,MAAM;AACN,eAAO;AACX,UAAI,MAAO,KAAK,MAAO;AACvB,UAAI,OAAO,IAAI,GAAG,IAAI,QAAQ,MAAM,KAAK,MAAM,GAAG,EAAE,UAAU,KAAK,MAAM,GAAG,EAAE,aAAa;AAC3F,UAAI,OAAO;AACP,eAAO,QAAQ,IAAI,KAAK;AAC5B,UAAI,QAAQ;AACR,aAAK;AAAA;AAEL,aAAK,MAAM;AAAA,IACnB;AAAA,EACJ;AAAA,EACA,QAAQ,QAAQ,MAAM,IAAI,GAAG;AACzB,aAAS,IAAI,KAAK,UAAU,MAAM,MAAyB,IAAI,GAAG,IAAI,KAAK,UAAU,IAAI,KAAwB,OAAO,CAAC,GAAG,IAAI,GAAG;AAC/H,UAAI,EAAE,KAAK,KAAK,CAAC,IAAI,QAAQ,KAAK,GAAG,CAAC,IAAI,QAAQ,KAAK,MAAM,CAAC,CAAC,MAAM;AACjE,eAAO;AAAA,EACnB;AAAA,EACA,IAAI,QAAQ,SAAS;AACjB,QAAI,QAAQ,CAAC,GAAG,OAAO,CAAC,GAAG,KAAK,CAAC,GAAG,SAAS,IAAI,WAAW;AAC5D,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AACxC,UAAI,MAAM,KAAK,MAAM,CAAC,GAAG,UAAU,KAAK,KAAK,CAAC,IAAI,QAAQ,QAAQ,KAAK,GAAG,CAAC,IAAI,QAAQ,SAAS;AAChG,UAAI,WAAW,OAAO;AAClB,YAAI,SAAS,QAAQ,OAAO,SAAS,IAAI,WAAW,IAAI,OAAO;AAC/D,YAAI,UAAU;AACV;AACJ,kBAAU,QAAQ;AAClB,YAAI,IAAI,aAAa,IAAI,SAAS;AAC9B,kBAAQ,QAAQ,OAAO,SAAS,IAAI,OAAO;AAC3C,cAAI,QAAQ;AACR;AAAA,QACR;AAAA,MACJ,OACK;AACD,kBAAU,QAAQ,OAAO,SAAS,IAAI,SAAS;AAC/C,gBAAQ,QAAQ,OAAO,OAAO,IAAI,OAAO;AACzC,YAAI,UAAU,SAAS,WAAW,SAAS,IAAI,YAAY,KAAK,IAAI,WAAW;AAC3E;AAAA,MACR;AACA,WAAK,QAAQ,WAAW,IAAI,UAAU,IAAI,aAAa;AACnD;AACJ,UAAI,SAAS;AACT,iBAAS;AACb,UAAI,IAAI;AACJ,mBAAW,KAAK,IAAI,UAAU,QAAQ,OAAO;AACjD,YAAM,KAAK,GAAG;AACd,WAAK,KAAK,UAAU,MAAM;AAC1B,SAAG,KAAK,QAAQ,MAAM;AAAA,IAC1B;AACA,WAAO,EAAE,QAAQ,MAAM,SAAS,IAAI,OAAM,MAAM,IAAI,OAAO,QAAQ,IAAI,MAAM,KAAK,OAAO;AAAA,EAC7F;AACJ;AAOA,IAAM,WAAN,MAAM,UAAS;AAAA,EACX,YAIA,UAIA,OAIA,WAIA,UAAU;AACN,SAAK,WAAW;AAChB,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,WAAW;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,OAAO,UAAU,OAAO,WAAW,UAAU;AAChD,WAAO,IAAI,UAAS,UAAU,OAAO,WAAW,QAAQ;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,SAAS;AACT,QAAI,OAAO,KAAK,MAAM,SAAS;AAC/B,WAAO,OAAO,IAAI,IAAI,KAAK,IAAI,KAAK,SAAS,IAAI,GAAG,KAAK,UAAU,MAAM;AAAA,EAC7E;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,OAAO;AACP,QAAI,KAAK;AACL,aAAO;AACX,QAAI,OAAO,KAAK,UAAU;AAC1B,aAAS,SAAS,KAAK;AACnB,cAAQ,MAAM,MAAM;AACxB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,OAAO;AACZ,WAAO,KAAK,SAAS,KAAK,IAAI,KAAK,MAAM,KAAK,EAAE;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,YAAY;AACf,QAAI,EAAE,MAAM,CAAC,GAAG,OAAO,OAAO,aAAa,GAAG,WAAW,KAAK,OAAO,IAAI;AACzE,QAAI,SAAS,WAAW;AACxB,QAAI,IAAI,UAAU,KAAK,CAAC;AACpB,aAAO;AACX,QAAI;AACA,YAAM,IAAI,MAAM,EAAE,KAAK,QAAQ;AACnC,QAAI,KAAK;AACL,aAAO,IAAI,SAAS,UAAS,GAAG,GAAG,IAAI;AAC3C,QAAI,MAAM,IAAI,YAAY,MAAM,MAAM,EAAE,EAAE,KAAK,CAAC,GAAG,IAAI,GAAG,QAAQ,CAAC;AACnE,QAAI,UAAU,IAAI,gBAAgB;AAClC,WAAO,IAAI,SAAS,IAAI,IAAI,QAAQ;AAChC,UAAI,IAAI,IAAI,WAAW,IAAI,OAAO,IAAI,CAAC,EAAE,QAAQ,IAAI,YAAY,IAAI,CAAC,EAAE,MAAM,cAAc,GAAG;AAC3F,YAAI,QAAQ,IAAI,GAAG;AACnB,YAAI,CAAC,QAAQ,SAAS,MAAM,MAAM,MAAM,IAAI,MAAM,KAAK;AACnD,gBAAM,KAAK,KAAK;AAAA,MACxB,WACS,IAAI,cAAc,KAAK,IAAI,aAAa,KAAK,MAAM,WACvD,KAAK,IAAI,UAAU,KAAK,SAAS,IAAI,UAAU,IAAI,IAAI,CAAC,EAAE,UAC1D,CAAC,UAAU,aAAa,KAAK,SAAS,IAAI,UAAU,KAAK,WAAW,KAAK,SAAS,IAAI,UAAU,MACjG,QAAQ,SAAS,KAAK,SAAS,IAAI,UAAU,GAAG,KAAK,MAAM,IAAI,UAAU,CAAC,GAAG;AAC7E,YAAI,UAAU;AAAA,MAClB,OACK;AACD,YAAI,CAAC,UAAU,aAAa,IAAI,MAAM,WAAW,IAAI,QAAQ,OAAO,IAAI,MAAM,IAAI,IAAI,IAAI,KAAK,GAAG;AAC9F,cAAI,CAAC,QAAQ,SAAS,IAAI,MAAM,IAAI,IAAI,IAAI,KAAK;AAC7C,kBAAM,KAAK,MAAM,OAAO,IAAI,MAAM,IAAI,IAAI,IAAI,KAAK,CAAC;AAAA,QAC5D;AACA,YAAI,KAAK;AAAA,MACb;AAAA,IACJ;AACA,WAAO,QAAQ,YAAY,KAAK,UAAU,WAAW,CAAC,MAAM,SAAS,UAAS,QACxE,KAAK,UAAU,OAAO,EAAE,KAAK,OAAO,QAAQ,YAAY,SAAS,CAAC,CAAC;AAAA,EAC7E;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,SAAS;AACT,QAAI,QAAQ,SAAS,KAAK;AACtB,aAAO;AACX,QAAI,SAAS,CAAC,GAAG,WAAW,CAAC,GAAG,WAAW;AAC3C,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AACxC,UAAI,QAAQ,KAAK,SAAS,CAAC,GAAG,QAAQ,KAAK,MAAM,CAAC;AAClD,UAAI,QAAQ,QAAQ,aAAa,OAAO,QAAQ,MAAM,MAAM;AAC5D,UAAI,UAAU,OAAO;AACjB,mBAAW,KAAK,IAAI,UAAU,MAAM,QAAQ;AAC5C,eAAO,KAAK,KAAK;AACjB,iBAAS,KAAK,QAAQ,OAAO,KAAK,CAAC;AAAA,MACvC,WACS,UAAU,MAAM;AACrB,YAAI,EAAE,QAAQ,IAAI,IAAI,MAAM,IAAI,OAAO,OAAO;AAC9C,YAAI,QAAQ;AACR,qBAAW,KAAK,IAAI,UAAU,OAAO,QAAQ;AAC7C,iBAAO,KAAK,MAAM;AAClB,mBAAS,KAAK,GAAG;AAAA,QACrB;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,OAAO,KAAK,UAAU,IAAI,OAAO;AACrC,WAAO,OAAO,UAAU,IAAI,OAAO,IAAI,UAAS,UAAU,QAAQ,QAAQ,UAAS,OAAO,QAAQ;AAAA,EACtG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,MAAM,IAAI,GAAG;AACjB,QAAI,KAAK;AACL;AACJ,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AACxC,UAAI,QAAQ,KAAK,SAAS,CAAC,GAAG,QAAQ,KAAK,MAAM,CAAC;AAClD,UAAI,MAAM,SAAS,QAAQ,QAAQ,MAAM,UACrC,MAAM,QAAQ,OAAO,OAAO,OAAO,KAAK,OAAO,CAAC,MAAM;AACtD;AAAA,IACR;AACA,SAAK,UAAU,QAAQ,MAAM,IAAI,CAAC;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,OAAO,GAAG;AACX,WAAO,WAAW,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,IAAI;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,UAAU;AAAE,WAAO,KAAK,aAAa;AAAA,EAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/C,OAAO,KAAK,MAAM,OAAO,GAAG;AACxB,WAAO,WAAW,KAAK,IAAI,EAAE,KAAK,IAAI;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,QAAQ,SAAS,SAKxB,UAAU,YAKV,eAAe,IAAI;AACf,QAAI,IAAI,QAAQ,OAAO,SAAO,IAAI,WAAW,KAAK,CAAC,IAAI,WAAW,IAAI,YAAY,YAAY;AAC9F,QAAI,IAAI,QAAQ,OAAO,SAAO,IAAI,WAAW,KAAK,CAAC,IAAI,WAAW,IAAI,YAAY,YAAY;AAC9F,QAAI,eAAe,iBAAiB,GAAG,GAAG,QAAQ;AAClD,QAAI,QAAQ,IAAI,WAAW,GAAG,cAAc,YAAY;AACxD,QAAI,QAAQ,IAAI,WAAW,GAAG,cAAc,YAAY;AACxD,aAAS,SAAS,CAAC,OAAO,OAAO,WAAW,QAAQ,OAAO,OAAO,OAAO,OAAO,QAAQ,UAAU,CAAC;AACnG,QAAI,SAAS,SAAS,SAAS,UAAU;AACrC,cAAQ,OAAO,GAAG,OAAO,GAAG,GAAG,UAAU;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,GAAG,SAAS,SAAS,OAAO,GAAG,IAAI;AACtC,QAAI,MAAM;AACN,WAAK,MAAyB;AAClC,QAAI,IAAI,QAAQ,OAAO,SAAO,CAAC,IAAI,WAAW,QAAQ,QAAQ,GAAG,IAAI,CAAC;AACtE,QAAI,IAAI,QAAQ,OAAO,SAAO,CAAC,IAAI,WAAW,QAAQ,QAAQ,GAAG,IAAI,CAAC;AACtE,QAAI,EAAE,UAAU,EAAE;AACd,aAAO;AACX,QAAI,CAAC,EAAE;AACH,aAAO;AACX,QAAI,eAAe,iBAAiB,GAAG,CAAC;AACxC,QAAI,QAAQ,IAAI,WAAW,GAAG,cAAc,CAAC,EAAE,KAAK,IAAI,GAAG,QAAQ,IAAI,WAAW,GAAG,cAAc,CAAC,EAAE,KAAK,IAAI;AAC/G,eAAS;AACL,UAAI,MAAM,MAAM,MAAM,MAClB,CAAC,WAAW,MAAM,QAAQ,MAAM,MAAM,KACtC,MAAM,UAAU,CAAC,MAAM,SAAS,CAAC,MAAM,MAAM,GAAG,MAAM,KAAK;AAC3D,eAAO;AACX,UAAI,MAAM,KAAK;AACX,eAAO;AACX,YAAM,KAAK;AACX,YAAM,KAAK;AAAA,IACf;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,MAAM,MAAM,MAAM,IAAI,UAK7B,eAAe,IAAI;AACf,QAAI,SAAS,IAAI,WAAW,MAAM,MAAM,YAAY,EAAE,KAAK,IAAI,GAAG,MAAM;AACxE,QAAI,aAAa,OAAO;AACxB,eAAS;AACL,UAAI,QAAQ,KAAK,IAAI,OAAO,IAAI,EAAE;AAClC,UAAI,OAAO,OAAO;AACd,YAAI,SAAS,OAAO,eAAe,OAAO,EAAE;AAC5C,YAAI,YAAY,OAAO,YAAY,OAAO,OAAO,SAAS,IACpD,OAAO,MAAM,YAAY,IAAI,OAAO,SAChC,KAAK,IAAI,OAAO,QAAQ,UAAU;AAC5C,iBAAS,MAAM,KAAK,OAAO,OAAO,OAAO,QAAQ,WAAW,OAAO,SAAS;AAC5E,qBAAa,KAAK,IAAI,OAAO,QAAQ,KAAK,GAAG,OAAO,MAAM;AAAA,MAC9D,WACS,QAAQ,KAAK;AAClB,iBAAS,KAAK,KAAK,OAAO,OAAO,QAAQ,UAAU;AACnD,qBAAa,OAAO,QAAQ,KAAK;AAAA,MACrC;AACA,UAAI,OAAO,KAAK;AACZ,eAAO,cAAc,OAAO,SAAS,OAAO,KAAK,KAAK,IAAI;AAC9D,YAAM,OAAO;AACb,aAAO,KAAK;AAAA,IAChB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,GAAG,QAAQ,OAAO,OAAO;AAC5B,QAAI,QAAQ,IAAI,gBAAgB;AAChC,aAAS,SAAS,kBAAkB,QAAQ,CAAC,MAAM,IAAI,OAAO,SAAS,MAAM,IAAI;AAC7E,YAAM,IAAI,MAAM,MAAM,MAAM,IAAI,MAAM,KAAK;AAC/C,WAAO,MAAM,OAAO;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,KAAK,MAAM;AACd,QAAI,CAAC,KAAK;AACN,aAAO,UAAS;AACpB,QAAI,SAAS,KAAK,KAAK,SAAS,CAAC;AACjC,aAAS,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AACvC,eAAS,QAAQ,KAAK,CAAC,GAAG,SAAS,UAAS,OAAO,QAAQ,MAAM;AAC7D,iBAAS,IAAI,UAAS,MAAM,UAAU,MAAM,OAAO,QAAQ,KAAK,IAAI,MAAM,UAAU,OAAO,QAAQ,CAAC;AAAA,IAC5G;AACA,WAAO;AAAA,EACX;AACJ;AAIA,SAAS,QAAqB,IAAI,SAAS,CAAC,GAAG,CAAC,GAAG,MAAM,EAAE;AAC3D,SAAS,SAAS,QAAQ;AACtB,MAAI,OAAO,SAAS;AAChB,aAAS,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtD,UAAI,MAAM,OAAO,CAAC;AAClB,UAAI,SAAS,MAAM,GAAG,IAAI;AACtB,eAAO,OAAO,MAAM,EAAE,KAAK,QAAQ;AACvC,aAAO;AAAA,IACX;AACJ,SAAO;AACX;AACA,SAAS,MAAM,YAAY,SAAS;AAMpC,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EAClB,YAAY,WAAW;AACnB,SAAK,OAAO,KAAK,IAAI,MAAM,KAAK,MAAM,KAAK,IAAI,KAAK,OAAO,KAAK,QAAQ,CAAC;AACzE,SAAK,SAAS,KAAK,KAAK,UAAU;AAClC,SAAK,aAAa;AAClB,SAAK,cAAc,KAAK,IAAI,KAAK,aAAa,KAAK,QAAQ;AAC3D,SAAK,WAAW;AAChB,QAAI,WAAW;AACX,WAAK,OAAO,CAAC;AACb,WAAK,KAAK,CAAC;AACX,WAAK,QAAQ,CAAC;AAAA,IAClB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACV,SAAK,SAAS,CAAC;AACf,SAAK,WAAW,CAAC;AACjB,SAAK,aAAa;AAClB,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,OAAO,CAAC;AACb,SAAK,KAAK,CAAC;AACX,SAAK,QAAQ,CAAC;AACd,SAAK,WAAW;AAChB,SAAK,cAAc;AACnB,SAAK,YAAY;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,MAAM,IAAI,OAAO;AACjB,QAAI,CAAC,KAAK,SAAS,MAAM,IAAI,KAAK;AAC9B,OAAC,KAAK,cAAc,KAAK,YAAY,IAAI,qBAAkB,IAAI,MAAM,IAAI,KAAK;AAAA,EACtF;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,MAAM,IAAI,OAAO;AACtB,QAAI,OAAO,OAAO,KAAK,UAAU,MAAM,YAAY,KAAK,KAAK;AAC7D,QAAI,QAAQ,MAAM,OAAO,KAAK,YAAY,MAAM,YAAY,KAAK,KAAK,aAAa;AAC/E,YAAM,IAAI,MAAM,gEAAgE;AACpF,QAAI,OAAO;AACP,aAAO;AACX,QAAI,KAAK,KAAK,UAAU;AACpB,WAAK,YAAY,IAAI;AACzB,QAAI,KAAK,aAAa;AAClB,WAAK,aAAa;AACtB,SAAK,KAAK,KAAK,OAAO,KAAK,UAAU;AACrC,SAAK,GAAG,KAAK,KAAK,KAAK,UAAU;AACjC,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,MAAM,KAAK,KAAK;AACrB,QAAI,MAAM;AACN,WAAK,WAAW,KAAK,IAAI,KAAK,UAAU,KAAK,IAAI;AACrD,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,MAAM,OAAO;AAClB,SAAK,OAAO,KAAK,UAAU,MAAM,MAAM,CAAC,EAAE,YAAY,KAAK,KAAK,WAAW;AACvE,aAAO;AACX,QAAI,KAAK,KAAK;AACV,WAAK,YAAY,IAAI;AACzB,SAAK,cAAc,KAAK,IAAI,KAAK,aAAa,MAAM,QAAQ;AAC5D,SAAK,OAAO,KAAK,KAAK;AACtB,SAAK,SAAS,KAAK,IAAI;AACvB,QAAI,OAAO,MAAM,MAAM,SAAS;AAChC,SAAK,OAAO,MAAM,MAAM,IAAI;AAC5B,SAAK,WAAW,MAAM,KAAK,IAAI,IAAI;AACnC,SAAK,SAAS,MAAM,GAAG,IAAI,IAAI;AAC/B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AAAE,WAAO,KAAK,YAAY,SAAS,KAAK;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAIpD,YAAY,MAAM;AACd,QAAI,KAAK,KAAK;AACV,WAAK,YAAY,KAAK;AAC1B,QAAI,KAAK,OAAO,UAAU;AACtB,aAAO;AACX,QAAI,SAAS,SAAS,OAAO,KAAK,UAAU,KAAK,QAAQ,KAAK,YAAY,KAAK,UAAU,YAAY,IAAI,IAAI,MAAM,KAAK,WAAW;AACnI,SAAK,OAAO;AACZ,WAAO;AAAA,EACX;AACJ;AACA,SAAS,iBAAiB,GAAG,GAAG,UAAU;AACtC,MAAI,MAAM,oBAAI,IAAI;AAClB,WAAS,OAAO;AACZ,aAAS,IAAI,GAAG,IAAI,IAAI,MAAM,QAAQ;AAClC,UAAI,IAAI,MAAM,CAAC,EAAE,YAAY;AACzB,YAAI,IAAI,IAAI,MAAM,CAAC,GAAG,IAAI,SAAS,CAAC,CAAC;AACjD,MAAI,SAAS,oBAAI,IAAI;AACrB,WAAS,OAAO;AACZ,aAAS,IAAI,GAAG,IAAI,IAAI,MAAM,QAAQ,KAAK;AACvC,UAAI,QAAQ,IAAI,IAAI,IAAI,MAAM,CAAC,CAAC;AAChC,UAAI,SAAS,SAAS,WAAW,SAAS,OAAO,KAAK,IAAI,UAAU,IAAI,SAAS,CAAC,KAC9E,EAAE,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,aAAa,OAAO,QAAQ,IAAI,MAAM,CAAC,EAAE,MAAM;AAC9G,eAAO,IAAI,IAAI,MAAM,CAAC,CAAC;AAAA,IAC/B;AACJ,SAAO;AACX;AACA,IAAM,cAAN,MAAkB;AAAA,EACd,YAAY,OAAO,MAAM,UAAU,OAAO,GAAG;AACzC,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,IAAI,YAAY;AAAE,WAAO,KAAK,QAAQ,KAAK,MAAM,YAAY;AAAA,EAAG;AAAA,EAChE,IAAI,UAAU;AAAE,WAAO,KAAK,QAAQ,KAAK,MAAM,UAAU;AAAA,EAAG;AAAA,EAC5D,KAAK,KAAK,OAAO,MAAyB;AACtC,SAAK,aAAa,KAAK,aAAa;AACpC,SAAK,UAAU,KAAK,MAAM,KAAK;AAC/B,WAAO;AAAA,EACX;AAAA,EACA,UAAU,KAAK,MAAM,SAAS;AAC1B,WAAO,KAAK,aAAa,KAAK,MAAM,MAAM,QAAQ;AAC9C,UAAI,OAAO,KAAK,MAAM,MAAM,KAAK,UAAU;AAC3C,UAAI,EAAE,KAAK,QAAQ,KAAK,KAAK,IAAI,IAAI,KACjC,KAAK,MAAM,SAAS,KAAK,UAAU,IAAI,OACvC,KAAK,WAAW,KAAK;AACrB;AACJ,WAAK;AACL,gBAAU;AAAA,IACd;AACA,QAAI,KAAK,aAAa,KAAK,MAAM,MAAM,QAAQ;AAC3C,UAAI,aAAa,KAAK,MAAM,MAAM,KAAK,UAAU,EAAE,UAAU,MAAM,KAAK,MAAM,SAAS,KAAK,UAAU,GAAG,MAAM,IAAI;AACnH,UAAI,CAAC,WAAW,KAAK,aAAa;AAC9B,aAAK,cAAc,UAAU;AAAA,IACrC;AACA,SAAK,KAAK;AAAA,EACd;AAAA,EACA,QAAQ,KAAK,MAAM;AACf,SAAK,KAAK,KAAK,OAAO,KAAK,UAAU,QAAQ;AACzC,WAAK,UAAU,KAAK,MAAM,IAAI;AAAA,EACtC;AAAA,EACA,OAAO;AACH,eAAS;AACL,UAAI,KAAK,cAAc,KAAK,MAAM,MAAM,QAAQ;AAC5C,aAAK,OAAO,KAAK,KAAK;AACtB,aAAK,QAAQ;AACb;AAAA,MACJ,OACK;AACD,YAAI,WAAW,KAAK,MAAM,SAAS,KAAK,UAAU,GAAG,QAAQ,KAAK,MAAM,MAAM,KAAK,UAAU;AAC7F,YAAI,OAAO,WAAW,MAAM,KAAK,KAAK,UAAU;AAChD,aAAK,OAAO;AACZ,aAAK,KAAK,WAAW,MAAM,GAAG,KAAK,UAAU;AAC7C,aAAK,QAAQ,MAAM,MAAM,KAAK,UAAU;AACxC,aAAK,cAAc,KAAK,aAAa,CAAC;AACtC,YAAI,KAAK,WAAW,KAAK,KAAK,MAAM,SAAS,KAAK,KAAK,KAAK,QAAQ,KAAK;AACrE;AAAA,MACR;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,cAAc,OAAO;AACjB,QAAI,SAAS,KAAK,MAAM,MAAM,KAAK,UAAU,EAAE,MAAM,QAAQ;AACzD,WAAK;AACL,UAAI,KAAK,MAAM;AACX,eAAO,KAAK,aAAa,KAAK,MAAM,MAAM,UAAU,KAAK,KAAK,IAAI,KAAK,MAAM,MAAM,KAAK,UAAU,CAAC;AAC/F,eAAK;AAAA,MACb;AACA,WAAK,aAAa;AAAA,IACtB,OACK;AACD,WAAK,aAAa;AAAA,IACtB;AAAA,EACJ;AAAA,EACA,YAAY;AACR,SAAK;AACL,SAAK,aAAa;AAClB,SAAK,KAAK;AAAA,EACd;AAAA,EACA,QAAQ,OAAO;AACX,WAAO,KAAK,OAAO,MAAM,QAAQ,KAAK,YAAY,MAAM,aAAa,KAAK,OAAO,MAAM,QACnF,KAAK,KAAK,MAAM,MAAM,KAAK,UAAU,MAAM;AAAA,EACnD;AACJ;AACA,IAAM,aAAN,MAAM,YAAW;AAAA,EACb,YAAY,MAAM;AACd,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,OAAO,KAAK,MAAM,OAAO,MAAM,WAAW,IAAI;AAC1C,QAAI,OAAO,CAAC;AACZ,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,eAAS,MAAM,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,MAAM,IAAI,WAAW;AACvD,YAAI,IAAI,YAAY;AAChB,eAAK,KAAK,IAAI,YAAY,KAAK,MAAM,UAAU,CAAC,CAAC;AAAA,MACzD;AAAA,IACJ;AACA,WAAO,KAAK,UAAU,IAAI,KAAK,CAAC,IAAI,IAAI,YAAW,IAAI;AAAA,EAC3D;AAAA,EACA,IAAI,YAAY;AAAE,WAAO,KAAK,QAAQ,KAAK,MAAM,YAAY;AAAA,EAAG;AAAA,EAChE,KAAK,KAAK,OAAO,MAAyB;AACtC,aAAS,OAAO,KAAK;AACjB,UAAI,KAAK,KAAK,IAAI;AACtB,aAAS,IAAI,KAAK,KAAK,UAAU,GAAG,KAAK,GAAG;AACxC,iBAAW,KAAK,MAAM,CAAC;AAC3B,SAAK,KAAK;AACV,WAAO;AAAA,EACX;AAAA,EACA,QAAQ,KAAK,MAAM;AACf,aAAS,OAAO,KAAK;AACjB,UAAI,QAAQ,KAAK,IAAI;AACzB,aAAS,IAAI,KAAK,KAAK,UAAU,GAAG,KAAK,GAAG;AACxC,iBAAW,KAAK,MAAM,CAAC;AAC3B,SAAK,KAAK,KAAK,OAAO,KAAK,MAAM,UAAU,QAAQ;AAC/C,WAAK,KAAK;AAAA,EAClB;AAAA,EACA,OAAO;AACH,QAAI,KAAK,KAAK,UAAU,GAAG;AACvB,WAAK,OAAO,KAAK,KAAK;AACtB,WAAK,QAAQ;AACb,WAAK,OAAO;AAAA,IAChB,OACK;AACD,UAAI,MAAM,KAAK,KAAK,CAAC;AACrB,WAAK,OAAO,IAAI;AAChB,WAAK,KAAK,IAAI;AACd,WAAK,QAAQ,IAAI;AACjB,WAAK,OAAO,IAAI;AAChB,UAAI,IAAI;AACJ,YAAI,KAAK;AACb,iBAAW,KAAK,MAAM,CAAC;AAAA,IAC3B;AAAA,EACJ;AACJ;AACA,SAAS,WAAW,MAAM,OAAO;AAC7B,WAAS,MAAM,KAAK,KAAK,OAAK;AAC1B,QAAI,cAAc,SAAS,KAAK;AAChC,QAAI,cAAc,KAAK;AACnB;AACJ,QAAI,QAAQ,KAAK,UAAU;AAC3B,QAAI,aAAa,IAAI,KAAK,UAAU,MAAM,QAAQ,KAAK,aAAa,CAAC,CAAC,KAAK,GAAG;AAC1E,cAAQ,KAAK,aAAa,CAAC;AAC3B;AAAA,IACJ;AACA,QAAI,IAAI,QAAQ,KAAK,IAAI;AACrB;AACJ,SAAK,UAAU,IAAI;AACnB,SAAK,KAAK,IAAI;AACd,YAAQ;AAAA,EACZ;AACJ;AACA,IAAM,aAAN,MAAiB;AAAA,EACb,YAAY,MAAM,MAAM,UAAU;AAC9B,SAAK,WAAW;AAChB,SAAK,SAAS,CAAC;AACf,SAAK,WAAW,CAAC;AACjB,SAAK,aAAa,CAAC;AACnB,SAAK,YAAY;AAEjB,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,KAAK;AACV,SAAK,UAAU;AAGf,SAAK,YAAY;AACjB,SAAK,SAAS,WAAW,KAAK,MAAM,MAAM,QAAQ;AAAA,EACtD;AAAA,EACA,KAAK,KAAK,OAAO,MAAyB;AACtC,SAAK,OAAO,KAAK,KAAK,IAAI;AAC1B,SAAK,OAAO,SAAS,KAAK,SAAS,SAAS,KAAK,WAAW,SAAS;AACrE,SAAK,YAAY;AACjB,SAAK,KAAK;AACV,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,KAAK;AACV,WAAO;AAAA,EACX;AAAA,EACA,QAAQ,KAAK,MAAM;AACf,WAAO,KAAK,YAAY,OAAO,KAAK,SAAS,KAAK,SAAS,IAAI,OAAO,KAAK,OAAO,KAAK,SAAS,EAAE,UAAU,QAAQ;AAChH,WAAK,aAAa,KAAK,SAAS;AACpC,SAAK,OAAO,QAAQ,KAAK,IAAI;AAAA,EACjC;AAAA,EACA,aAAa,OAAO;AAChB,WAAO,KAAK,QAAQ,KAAK;AACzB,WAAO,KAAK,UAAU,KAAK;AAC3B,WAAO,KAAK,YAAY,KAAK;AAC7B,SAAK,YAAY,aAAa,KAAK,QAAQ,KAAK,QAAQ;AAAA,EAC5D;AAAA,EACA,UAAU,WAAW;AACjB,QAAI,IAAI,GAAG,EAAE,OAAO,IAAI,KAAK,IAAI,KAAK;AAEtC,WAAO,IAAI,KAAK,WAAW,WAAW,OAAO,KAAK,WAAW,CAAC,KAAK,KAAK,KAAK,SAAS,CAAC,KAAK;AACxF;AACJ,WAAO,KAAK,QAAQ,GAAG,KAAK;AAC5B,WAAO,KAAK,UAAU,GAAG,EAAE;AAC3B,WAAO,KAAK,YAAY,GAAG,IAAI;AAC/B,QAAI;AACA,aAAO,WAAW,GAAG,KAAK,OAAO,IAAI;AACzC,SAAK,YAAY,aAAa,KAAK,QAAQ,KAAK,QAAQ;AAAA,EAC5D;AAAA;AAAA;AAAA,EAGA,OAAO;AACH,QAAI,OAAO,KAAK,IAAI,WAAW,KAAK;AACpC,SAAK,QAAQ;AACb,QAAI,YAAY,KAAK,YAAY,IAAI,CAAC,IAAI;AAC1C,eAAS;AACL,UAAI,IAAI,KAAK;AACb,UAAI,IAAI,OAAO,KAAK,SAAS,CAAC,IAAI,KAAK,OAAO,QAAQ,KAAK,OAAO,CAAC,EAAE,UAAU,KAAK,OAAO,aAAa,GAAG;AACvG,YAAI,KAAK,SAAS,CAAC,IAAI,MAAM;AACzB,eAAK,KAAK,KAAK,SAAS,CAAC;AACzB,eAAK,UAAU,KAAK,OAAO,CAAC,EAAE;AAC9B;AAAA,QACJ;AACA,aAAK,aAAa,CAAC;AACnB,YAAI;AACA,iBAAO,WAAW,CAAC;AAAA,MAC3B,WACS,CAAC,KAAK,OAAO,OAAO;AACzB,aAAK,KAAK,KAAK,UAAU;AACzB;AAAA,MACJ,WACS,KAAK,OAAO,OAAO,MAAM;AAC9B,aAAK,KAAK,KAAK,OAAO;AACtB,aAAK,UAAU,KAAK,OAAO;AAC3B;AAAA,MACJ,OACK;AACD,YAAI,UAAU,KAAK,OAAO;AAC1B,YAAI,CAAC,QAAQ,OAAO;AAChB,eAAK,UAAU,SAAS;AACxB,eAAK,OAAO,KAAK;AAAA,QACrB,WACS,YAAY,KAAK,OAAO,MAAM,KAAK,MAAM,KAAK,OAAO,OAAO,KAAK,OAAO,IAAI;AAEjF,eAAK,OAAO,KAAK;AAAA,QACrB,OACK;AACD,eAAK,QAAQ;AACb,eAAK,YAAY,KAAK,OAAO;AAC7B,eAAK,YAAY,KAAK,OAAO;AAC7B,eAAK,KAAK,KAAK,OAAO;AACtB,eAAK,UAAU,QAAQ;AACvB,eAAK,OAAO,KAAK;AACjB,eAAK,QAAQ,KAAK,IAAI,KAAK,OAAO;AAClC;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,WAAW;AACX,WAAK,YAAY;AACjB,eAAS,IAAI,UAAU,SAAS,GAAG,KAAK,KAAK,UAAU,CAAC,IAAI,MAAM;AAC9D,aAAK;AAAA,IACb;AAAA,EACJ;AAAA,EACA,eAAe,IAAI;AACf,QAAI,CAAC,KAAK,OAAO;AACb,aAAO,KAAK;AAChB,QAAI,SAAS,CAAC;AACd,aAAS,IAAI,KAAK,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC9C,UAAI,KAAK,WAAW,CAAC,IAAI,KAAK;AAC1B;AACJ,UAAI,KAAK,SAAS,CAAC,IAAI,MAAM,KAAK,SAAS,CAAC,KAAK,MAAM,KAAK,OAAO,CAAC,EAAE,WAAW,KAAK,MAAM;AACxF,eAAO,KAAK,KAAK,OAAO,CAAC,CAAC;AAAA,IAClC;AACA,WAAO,OAAO,QAAQ;AAAA,EAC1B;AAAA,EACA,QAAQ,IAAI;AACR,QAAI,OAAO;AACX,aAAS,IAAI,KAAK,SAAS,SAAS,GAAG,KAAK,KAAK,KAAK,SAAS,CAAC,IAAI,IAAI;AACpE;AACJ,WAAO;AAAA,EACX;AACJ;AACA,SAAS,QAAQ,GAAG,QAAQ,GAAG,QAAQ,QAAQ,YAAY;AACvD,IAAE,KAAK,MAAM;AACb,IAAE,KAAK,MAAM;AACb,MAAI,OAAO,SAAS;AACpB,MAAI,MAAM,QAAQ,OAAO,SAAS;AAClC,aAAS;AACL,QAAI,OAAQ,EAAE,KAAK,OAAQ,EAAE,IAAI,OAAO,QAAQ,EAAE,UAAU,EAAE;AAC9D,QAAI,MAAM,OAAO,IAAI,EAAE,KAAK,OAAO,EAAE,IAAI,UAAU,KAAK,IAAI,KAAK,IAAI;AACrE,QAAI,EAAE,SAAS,EAAE,OAAO;AACpB,UAAI,EAAE,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,GAAG,EAAE,KAAK,MACjE,WAAW,EAAE,eAAe,EAAE,EAAE,GAAG,EAAE,eAAe,EAAE,EAAE,CAAC;AACzD,mBAAW,aAAa,KAAK,SAAS,EAAE,OAAO,EAAE,KAAK;AAAA,IAC9D,OACK;AACD,UAAI,UAAU,OAAO,CAAC,WAAW,EAAE,QAAQ,EAAE,MAAM;AAC/C,mBAAW,aAAa,KAAK,SAAS,EAAE,QAAQ,EAAE,MAAM;AAAA,IAChE;AACA,QAAI,MAAM;AACN;AACJ,SAAK,QAAQ,EAAE,WAAW,EAAE,YAAY,WAAW;AAC/C,iBAAW,YAAY,GAAG;AAC9B,UAAM;AACN,QAAI,QAAQ;AACR,QAAE,KAAK;AACX,QAAI,QAAQ;AACR,QAAE,KAAK;AAAA,EACf;AACJ;AACA,SAAS,WAAW,GAAG,GAAG;AACtB,MAAI,EAAE,UAAU,EAAE;AACd,WAAO;AACX,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ;AAC1B,QAAI,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AAC7B,aAAO;AACf,SAAO;AACX;AACA,SAAS,OAAO,OAAO,OAAO;AAC1B,WAAS,IAAI,OAAO,IAAI,MAAM,SAAS,GAAG,IAAI,GAAG;AAC7C,UAAM,CAAC,IAAI,MAAM,IAAI,CAAC;AAC1B,QAAM,IAAI;AACd;AACA,SAAS,OAAO,OAAO,OAAO,OAAO;AACjC,WAAS,IAAI,MAAM,SAAS,GAAG,KAAK,OAAO;AACvC,UAAM,IAAI,CAAC,IAAI,MAAM,CAAC;AAC1B,QAAM,KAAK,IAAI;AACnB;AACA,SAAS,aAAa,OAAO,OAAO;AAChC,MAAI,QAAQ,IAAI,WAAW;AAC3B,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAC9B,SAAK,MAAM,CAAC,IAAI,YAAY,MAAM,CAAC,EAAE,UAAU,MAAM,KAAK,EAAE,WAAW,GAAG;AACtE,cAAQ;AACR,iBAAW,MAAM,CAAC;AAAA,IACtB;AACJ,SAAO;AACX;AAMA,SAAS,YAAY,QAAQ,SAAS,KAAK,OAAO,QAAQ;AACtD,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,IAAI,MAAM,IAAI,OAAO,UAAS;AAC1C,QAAI,OAAO,WAAW,CAAC,KAAK,GAAG;AAC3B,WAAK,UAAW,IAAI;AACpB;AAAA,IACJ,OACK;AACD;AACA,UAAIA,kBAAiB,QAAQ,CAAC;AAAA,IAClC;AAAA,EACJ;AACA,SAAO;AACX;AAQA,SAAS,WAAW,QAAQ,KAAK,SAAS,QAAQ;AAC9C,WAAS,IAAI,GAAG,IAAI,OAAK;AACrB,QAAI,KAAK;AACL,aAAO;AACX,QAAI,KAAK,OAAO;AACZ;AACJ,SAAK,OAAO,WAAW,CAAC,KAAK,IAAI,UAAW,IAAI,UAAW;AAC3D,QAAIA,kBAAiB,QAAQ,CAAC;AAAA,EAClC;AACA,SAAO,WAAW,OAAO,KAAK,OAAO;AACzC;", "names": ["findClusterBreak", "surrogate<PERSON>ow", "surrogateHigh", "codePointAt", "codePointSize", "MapMode", "insert", "i", "compare", "prec", "CharCategor<PERSON>", "require"]}