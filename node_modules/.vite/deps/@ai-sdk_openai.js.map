{"version": 3, "sources": ["../../@ai-sdk/openai/node_modules/@ai-sdk/provider/src/errors/ai-sdk-error.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider/src/errors/api-call-error.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider/src/errors/empty-response-body-error.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider/src/errors/get-error-message.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider/src/errors/invalid-argument-error.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider/src/errors/invalid-prompt-error.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider/src/errors/invalid-response-data-error.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider/src/errors/json-parse-error.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider/src/errors/load-api-key-error.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider/src/errors/load-setting-error.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider/src/errors/no-content-generated-error.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider/src/errors/no-such-model-error.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider/src/errors/too-many-embedding-values-for-call-error.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider/src/errors/type-validation-error.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider/src/errors/unsupported-functionality-error.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider/src/json-value/is-json.ts", "../../@ai-sdk/openai/node_modules/nanoid/non-secure/index.js", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider-utils/src/combine-headers.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider-utils/src/convert-async-iterator-to-readable-stream.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider-utils/src/delay.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider-utils/src/event-source-parser-stream.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider-utils/src/extract-response-headers.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider-utils/src/generate-id.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider-utils/src/get-error-message.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider-utils/src/get-from-api.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider-utils/src/remove-undefined-entries.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider-utils/src/is-abort-error.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider-utils/src/load-api-key.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider-utils/src/load-optional-setting.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider-utils/src/load-setting.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider-utils/src/parse-json.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider-utils/src/validate-types.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider-utils/src/validator.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider-utils/src/parse-provider-options.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider-utils/src/post-to-api.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider-utils/src/resolve.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider-utils/src/response-handler.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider-utils/src/uint8-utils.ts", "../../@ai-sdk/openai/node_modules/@ai-sdk/provider-utils/src/without-trailing-slash.ts", "../../@ai-sdk/openai/src/openai-provider.ts", "../../@ai-sdk/openai/src/openai-chat-language-model.ts", "../../@ai-sdk/openai/src/convert-to-openai-chat-messages.ts", "../../@ai-sdk/openai/src/map-openai-chat-logprobs.ts", "../../@ai-sdk/openai/src/map-openai-finish-reason.ts", "../../@ai-sdk/openai/src/openai-error.ts", "../../@ai-sdk/openai/src/get-response-metadata.ts", "../../@ai-sdk/openai/src/openai-prepare-tools.ts", "../../@ai-sdk/openai/src/openai-completion-language-model.ts", "../../@ai-sdk/openai/src/convert-to-openai-completion-prompt.ts", "../../@ai-sdk/openai/src/map-openai-completion-logprobs.ts", "../../@ai-sdk/openai/src/openai-embedding-model.ts", "../../@ai-sdk/openai/src/openai-image-model.ts", "../../@ai-sdk/openai/src/openai-image-settings.ts", "../../@ai-sdk/openai/src/openai-transcription-model.ts", "../../@ai-sdk/openai/src/responses/openai-responses-language-model.ts", "../../@ai-sdk/openai/src/responses/convert-to-openai-responses-messages.ts", "../../@ai-sdk/openai/src/responses/map-openai-responses-finish-reason.ts", "../../@ai-sdk/openai/src/responses/openai-responses-prepare-tools.ts", "../../@ai-sdk/openai/src/openai-tools.ts", "../../@ai-sdk/openai/src/openai-speech-model.ts"], "sourcesContent": ["/**\n * Symbol used for identifying AI SDK Error instances.\n * Enables checking if an error is an instance of AISDKError across package versions.\n */\nconst marker = 'vercel.ai.error';\nconst symbol = Symbol.for(marker);\n\n/**\n * Custom error class for AI SDK related errors.\n * @extends Error\n */\nexport class AISDKError extends Error {\n  private readonly [symbol] = true; // used in isInstance\n\n  /**\n   * The underlying cause of the error, if any.\n   */\n  readonly cause?: unknown;\n\n  /**\n   * Creates an AI SDK Error.\n   *\n   * @param {Object} params - The parameters for creating the error.\n   * @param {string} params.name - The name of the error.\n   * @param {string} params.message - The error message.\n   * @param {unknown} [params.cause] - The underlying cause of the error.\n   */\n  constructor({\n    name,\n    message,\n    cause,\n  }: {\n    name: string;\n    message: string;\n    cause?: unknown;\n  }) {\n    super(message);\n\n    this.name = name;\n    this.cause = cause;\n  }\n\n  /**\n   * Checks if the given error is an AI SDK Error.\n   * @param {unknown} error - The error to check.\n   * @returns {boolean} True if the error is an AI SDK Error, false otherwise.\n   */\n  static isInstance(error: unknown): error is AISDKError {\n    return AISDKError.hasMarker(error, marker);\n  }\n\n  protected static hasMarker(error: unknown, marker: string): boolean {\n    const markerSymbol = Symbol.for(marker);\n    return (\n      error != null &&\n      typeof error === 'object' &&\n      markerSymbol in error &&\n      typeof error[markerSymbol] === 'boolean' &&\n      error[markerSymbol] === true\n    );\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_APICallError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class APICallError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly url: string;\n  readonly requestBodyValues: unknown;\n  readonly statusCode?: number;\n\n  readonly responseHeaders?: Record<string, string>;\n  readonly responseBody?: string;\n\n  readonly isRetryable: boolean;\n  readonly data?: unknown;\n\n  constructor({\n    message,\n    url,\n    requestBodyValues,\n    statusCode,\n    responseHeaders,\n    responseBody,\n    cause,\n    isRetryable = statusCode != null &&\n      (statusCode === 408 || // request timeout\n        statusCode === 409 || // conflict\n        statusCode === 429 || // too many requests\n        statusCode >= 500), // server error\n    data,\n  }: {\n    message: string;\n    url: string;\n    requestBodyValues: unknown;\n    statusCode?: number;\n    responseHeaders?: Record<string, string>;\n    responseBody?: string;\n    cause?: unknown;\n    isRetryable?: boolean;\n    data?: unknown;\n  }) {\n    super({ name, message, cause });\n\n    this.url = url;\n    this.requestBodyValues = requestBodyValues;\n    this.statusCode = statusCode;\n    this.responseHeaders = responseHeaders;\n    this.responseBody = responseBody;\n    this.isRetryable = isRetryable;\n    this.data = data;\n  }\n\n  static isInstance(error: unknown): error is APICallError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_EmptyResponseBodyError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class EmptyResponseBodyError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  constructor({ message = 'Empty response body' }: { message?: string } = {}) {\n    super({ name, message });\n  }\n\n  static isInstance(error: unknown): error is EmptyResponseBodyError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "export function getErrorMessage(error: unknown | undefined) {\n  if (error == null) {\n    return 'unknown error';\n  }\n\n  if (typeof error === 'string') {\n    return error;\n  }\n\n  if (error instanceof Error) {\n    return error.message;\n  }\n\n  return JSON.stringify(error);\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_InvalidArgumentError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\n * A function argument is invalid.\n */\nexport class InvalidArgumentError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly argument: string;\n\n  constructor({\n    message,\n    cause,\n    argument,\n  }: {\n    argument: string;\n    message: string;\n    cause?: unknown;\n  }) {\n    super({ name, message, cause });\n\n    this.argument = argument;\n  }\n\n  static isInstance(error: unknown): error is InvalidArgumentError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_InvalidPromptError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\n * A prompt is invalid. This error should be thrown by providers when they cannot\n * process a prompt.\n */\nexport class InvalidPromptError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly prompt: unknown;\n\n  constructor({\n    prompt,\n    message,\n    cause,\n  }: {\n    prompt: unknown;\n    message: string;\n    cause?: unknown;\n  }) {\n    super({ name, message: `Invalid prompt: ${message}`, cause });\n\n    this.prompt = prompt;\n  }\n\n  static isInstance(error: unknown): error is InvalidPromptError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_InvalidResponseDataError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\n * Server returned a response with invalid data content.\n * This should be thrown by providers when they cannot parse the response from the API.\n */\nexport class InvalidResponseDataError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly data: unknown;\n\n  constructor({\n    data,\n    message = `Invalid response data: ${JSON.stringify(data)}.`,\n  }: {\n    data: unknown;\n    message?: string;\n  }) {\n    super({ name, message });\n\n    this.data = data;\n  }\n\n  static isInstance(error: unknown): error is InvalidResponseDataError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\nimport { getErrorMessage } from './get-error-message';\n\nconst name = 'AI_JSONParseError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n// TODO v5: rename to ParseError\nexport class JSONParseError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly text: string;\n\n  constructor({ text, cause }: { text: string; cause: unknown }) {\n    super({\n      name,\n      message:\n        `JSON parsing failed: ` +\n        `Text: ${text}.\\n` +\n        `Error message: ${getErrorMessage(cause)}`,\n      cause,\n    });\n\n    this.text = text;\n  }\n\n  static isInstance(error: unknown): error is JSONParseError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_LoadAPIKeyError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class LoadAPIKeyError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  constructor({ message }: { message: string }) {\n    super({ name, message });\n  }\n\n  static isInstance(error: unknown): error is LoadAPIKeyError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_LoadSettingError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class LoadSettingError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  constructor({ message }: { message: string }) {\n    super({ name, message });\n  }\n\n  static isInstance(error: unknown): error is LoadSettingError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_NoContentGeneratedError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\nThrown when the AI provider fails to generate any content.\n */\nexport class NoContentGeneratedError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  constructor({\n    message = 'No content generated.',\n  }: { message?: string } = {}) {\n    super({ name, message });\n  }\n\n  static isInstance(error: unknown): error is NoContentGeneratedError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_NoSuchModelError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class NoSuchModelError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly modelId: string;\n  readonly modelType: 'languageModel' | 'textEmbeddingModel' | 'imageModel';\n\n  constructor({\n    errorName = name,\n    modelId,\n    modelType,\n    message = `No such ${modelType}: ${modelId}`,\n  }: {\n    errorName?: string;\n    modelId: string;\n    modelType: 'languageModel' | 'textEmbeddingModel' | 'imageModel';\n    message?: string;\n  }) {\n    super({ name: errorName, message });\n\n    this.modelId = modelId;\n    this.modelType = modelType;\n  }\n\n  static isInstance(error: unknown): error is NoSuchModelError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_TooManyEmbeddingValuesForCallError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class TooManyEmbeddingValuesForCallError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly provider: string;\n  readonly modelId: string;\n  readonly maxEmbeddingsPerCall: number;\n  readonly values: Array<unknown>;\n\n  constructor(options: {\n    provider: string;\n    modelId: string;\n    maxEmbeddingsPerCall: number;\n    values: Array<unknown>;\n  }) {\n    super({\n      name,\n      message:\n        `Too many values for a single embedding call. ` +\n        `The ${options.provider} model \"${options.modelId}\" can only embed up to ` +\n        `${options.maxEmbeddingsPerCall} values per call, but ${options.values.length} values were provided.`,\n    });\n\n    this.provider = options.provider;\n    this.modelId = options.modelId;\n    this.maxEmbeddingsPerCall = options.maxEmbeddingsPerCall;\n    this.values = options.values;\n  }\n\n  static isInstance(\n    error: unknown,\n  ): error is TooManyEmbeddingValuesForCallError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\nimport { getErrorMessage } from './get-error-message';\n\nconst name = 'AI_TypeValidationError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class TypeValidationError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly value: unknown;\n\n  constructor({ value, cause }: { value: unknown; cause: unknown }) {\n    super({\n      name,\n      message:\n        `Type validation failed: ` +\n        `Value: ${JSON.stringify(value)}.\\n` +\n        `Error message: ${getErrorMessage(cause)}`,\n      cause,\n    });\n\n    this.value = value;\n  }\n\n  static isInstance(error: unknown): error is TypeValidationError {\n    return AISDKError.hasMarker(error, marker);\n  }\n\n  /**\n   * Wraps an error into a TypeValidationError.\n   * If the cause is already a TypeValidationError with the same value, it returns the cause.\n   * Otherwise, it creates a new TypeValidationError.\n   *\n   * @param {Object} params - The parameters for wrapping the error.\n   * @param {unknown} params.value - The value that failed validation.\n   * @param {unknown} params.cause - The original error or cause of the validation failure.\n   * @returns {TypeValidationError} A TypeValidationError instance.\n   */\n  static wrap({\n    value,\n    cause,\n  }: {\n    value: unknown;\n    cause: unknown;\n  }): TypeValidationError {\n    return TypeValidationError.isInstance(cause) && cause.value === value\n      ? cause\n      : new TypeValidationError({ value, cause });\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_UnsupportedFunctionalityError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class UnsupportedFunctionalityError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly functionality: string;\n\n  constructor({\n    functionality,\n    message = `'${functionality}' functionality not supported.`,\n  }: {\n    functionality: string;\n    message?: string;\n  }) {\n    super({ name, message });\n    this.functionality = functionality;\n  }\n\n  static isInstance(error: unknown): error is UnsupportedFunctionalityError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { JSONArray, JSONObject, JSONValue } from './json-value';\n\nexport function isJSONValue(value: unknown): value is JSONValue {\n  if (\n    value === null ||\n    typeof value === 'string' ||\n    typeof value === 'number' ||\n    typeof value === 'boolean'\n  ) {\n    return true;\n  }\n\n  if (Array.isArray(value)) {\n    return value.every(isJSONValue);\n  }\n\n  if (typeof value === 'object') {\n    return Object.entries(value).every(\n      ([key, val]) => typeof key === 'string' && isJSONValue(val),\n    );\n  }\n\n  return false;\n}\n\nexport function isJSONArray(value: unknown): value is JSONArray {\n  return Array.isArray(value) && value.every(isJSONValue);\n}\n\nexport function isJSONObject(value: unknown): value is JSONObject {\n  return (\n    value != null &&\n    typeof value === 'object' &&\n    Object.entries(value).every(\n      ([key, val]) => typeof key === 'string' && isJSONValue(val),\n    )\n  );\n}\n", "let urlAlphabet =\n  'useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict'\nlet customAlphabet = (alphabet, defaultSize = 21) => {\n  return (size = defaultSize) => {\n    let id = ''\n    let i = size | 0\n    while (i--) {\n      id += alphabet[(Math.random() * alphabet.length) | 0]\n    }\n    return id\n  }\n}\nlet nanoid = (size = 21) => {\n  let id = ''\n  let i = size | 0\n  while (i--) {\n    id += urlAlphabet[(Math.random() * 64) | 0]\n  }\n  return id\n}\nexport { nanoid, customAlphabet }\n", "export function combineHeaders(\n  ...headers: Array<Record<string, string | undefined> | undefined>\n): Record<string, string | undefined> {\n  return headers.reduce(\n    (combinedHeaders, currentHeaders) => ({\n      ...combinedHeaders,\n      ...(currentHeaders ?? {}),\n    }),\n    {},\n  ) as Record<string, string | undefined>;\n}\n", "/**\n * Converts an AsyncIterator to a ReadableStream.\n *\n * @template T - The type of elements produced by the AsyncIterator.\n * @param { <T>} iterator - The AsyncIterator to convert.\n * @returns {ReadableStream<T>} - A ReadableStream that provides the same data as the AsyncIterator.\n */\nexport function convertAsyncIteratorToReadableStream<T>(\n  iterator: AsyncIterator<T>,\n): ReadableStream<T> {\n  return new ReadableStream<T>({\n    /**\n     * Called when the consumer wants to pull more data from the stream.\n     *\n     * @param {ReadableStreamDefaultController<T>} controller - The controller to enqueue data into the stream.\n     * @returns {Promise<void>}\n     */\n    async pull(controller) {\n      try {\n        const { value, done } = await iterator.next();\n        if (done) {\n          controller.close();\n        } else {\n          controller.enqueue(value);\n        }\n      } catch (error) {\n        controller.error(error);\n      }\n    },\n    /**\n     * Called when the consumer cancels the stream.\n     */\n    cancel() {},\n  });\n}\n", "/**\n * Creates a Promise that resolves after a specified delay\n * @param delayInMs - The delay duration in milliseconds. If null or undefined, resolves immediately.\n * @returns A Promise that resolves after the specified delay\n */\nexport async function delay(delayInMs?: number | null): Promise<void> {\n  return delayInMs == null\n    ? Promise.resolve()\n    : new Promise(resolve => setTimeout(resolve, delayInMs));\n}\n", "export type EventSourceChunk = {\n  event: string | undefined;\n  data: string;\n  id?: string;\n  retry?: number;\n};\n\nexport function createEventSourceParserStream() {\n  let buffer = '';\n  let event: string | undefined = undefined;\n  let data: string[] = [];\n  let lastEventId: string | undefined = undefined;\n  let retry: number | undefined = undefined;\n\n  function parseLine(\n    line: string,\n    controller: TransformStreamDefaultController<EventSourceChunk>,\n  ) {\n    // Empty line means dispatch the event\n    if (line === '') {\n      dispatchEvent(controller);\n      return;\n    }\n\n    // Comments start with colon\n    if (line.startsWith(':')) {\n      return;\n    }\n\n    // Field parsing\n    const colonIndex = line.indexOf(':');\n    if (colonIndex === -1) {\n      // field with no value\n      handleField(line, '');\n      return;\n    }\n\n    const field = line.slice(0, colonIndex);\n    // If there's a space after the colon, it should be ignored\n    const valueStart = colonIndex + 1;\n    const value =\n      valueStart < line.length && line[valueStart] === ' '\n        ? line.slice(valueStart + 1)\n        : line.slice(valueStart);\n\n    handleField(field, value);\n  }\n\n  function dispatchEvent(\n    controller: TransformStreamDefaultController<EventSourceChunk>,\n  ) {\n    if (data.length > 0) {\n      controller.enqueue({\n        event,\n        data: data.join('\\n'),\n        id: lastEventId,\n        retry,\n      });\n\n      // Reset data but keep lastEventId as per spec\n      data = [];\n      event = undefined;\n      retry = undefined;\n    }\n  }\n\n  function handleField(field: string, value: string) {\n    switch (field) {\n      case 'event':\n        event = value;\n        break;\n      case 'data':\n        data.push(value);\n        break;\n      case 'id':\n        lastEventId = value;\n        break;\n      case 'retry':\n        const parsedRetry = parseInt(value, 10);\n        if (!isNaN(parsedRetry)) {\n          retry = parsedRetry;\n        }\n        break;\n    }\n  }\n\n  return new TransformStream<string, EventSourceChunk>({\n    transform(chunk, controller) {\n      const { lines, incompleteLine } = splitLines(buffer, chunk);\n\n      buffer = incompleteLine;\n\n      // using for loop for performance\n      for (let i = 0; i < lines.length; i++) {\n        parseLine(lines[i], controller);\n      }\n    },\n\n    flush(controller) {\n      parseLine(buffer, controller);\n      dispatchEvent(controller);\n    },\n  });\n}\n\n// performance: send in already scanned buffer separately, do not scan again\nfunction splitLines(buffer: string, chunk: string) {\n  const lines: Array<string> = [];\n  let currentLine = buffer;\n\n  // using for loop for performance\n  for (let i = 0; i < chunk.length; ) {\n    const char = chunk[i++];\n\n    // order is performance-optimized\n    if (char === '\\n') {\n      // Standalone LF\n      lines.push(currentLine);\n      currentLine = '';\n    } else if (char === '\\r') {\n      lines.push(currentLine);\n      currentLine = '';\n      if (chunk[i] === '\\n') {\n        i++; // CRLF case: Skip the LF character\n      }\n    } else {\n      currentLine += char;\n    }\n  }\n\n  return { lines, incompleteLine: currentLine };\n}\n", "/**\nExtracts the headers from a response object and returns them as a key-value object.\n\n@param response - The response object to extract headers from.\n@returns The headers as a key-value object.\n*/\nexport function extractResponseHeaders(\n  response: Response,\n): Record<string, string> {\n  const headers: Record<string, string> = {};\n  response.headers.forEach((value, key) => {\n    headers[key] = value;\n  });\n  return headers;\n}\n", "import { InvalidArgumentError } from '@ai-sdk/provider';\nimport { customAlphabet } from 'nanoid/non-secure';\n\n/**\nCreates an ID generator.\nThe total length of the ID is the sum of the prefix, separator, and random part length.\nNon-secure.\n\n@param alphabet - The alphabet to use for the ID. Default: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.\n@param prefix - The prefix of the ID to generate. Default: ''.\n@param separator - The separator between the prefix and the random part of the ID. Default: '-'.\n@param size - The size of the random part of the ID to generate. Default: 16.\n */\n// TODO 5.0 breaking change: change the return type to IDGenerator\nexport const createIdGenerator = ({\n  prefix,\n  size: defaultSize = 16,\n  alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',\n  separator = '-',\n}: {\n  prefix?: string;\n  separator?: string;\n  size?: number;\n  alphabet?: string;\n} = {}): ((size?: number) => string) => {\n  const generator = customAlphabet(alphabet, defaultSize);\n\n  if (prefix == null) {\n    return generator;\n  }\n\n  // check that the prefix is not part of the alphabet (otherwise prefix checking can fail randomly)\n  if (alphabet.includes(separator)) {\n    throw new InvalidArgumentError({\n      argument: 'separator',\n      message: `The separator \"${separator}\" must not be part of the alphabet \"${alphabet}\".`,\n    });\n  }\n\n  return size => `${prefix}${separator}${generator(size)}`;\n};\n\n/**\nA function that generates an ID.\n */\nexport type IDGenerator = () => string;\n\n/**\nGenerates a 16-character random string to use for IDs. Not secure.\n\n@param size - The size of the ID to generate. Default: 16.\n */\nexport const generateId = createIdGenerator();\n", "export function getErrorMessage(error: unknown | undefined) {\n  if (error == null) {\n    return 'unknown error';\n  }\n\n  if (typeof error === 'string') {\n    return error;\n  }\n\n  if (error instanceof Error) {\n    return error.message;\n  }\n\n  return JSON.stringify(error);\n}\n", "import { APICallError } from '@ai-sdk/provider';\nimport { FetchFunction } from './fetch-function';\nimport { removeUndefinedEntries } from './remove-undefined-entries';\nimport { ResponseHandler } from './response-handler';\nimport { isAbortError } from './is-abort-error';\nimport { extractResponseHeaders } from './extract-response-headers';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => globalThis.fetch;\n\nexport const getFromApi = async <T>({\n  url,\n  headers = {},\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch(),\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  failedResponseHandler: ResponseHandler<Error>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: 'GET',\n      headers: removeUndefinedEntries(headers),\n      signal: abortSignal,\n    });\n\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!response.ok) {\n      let errorInformation: {\n        value: Error;\n        responseHeaders?: Record<string, string> | undefined;\n      };\n\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: {},\n        });\n      } catch (error) {\n        if (isAbortError(error) || APICallError.isInstance(error)) {\n          throw error;\n        }\n\n        throw new APICallError({\n          message: 'Failed to process error response',\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: {},\n        });\n      }\n\n      throw errorInformation.value;\n    }\n\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: {},\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || APICallError.isInstance(error)) {\n          throw error;\n        }\n      }\n\n      throw new APICallError({\n        message: 'Failed to process successful response',\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: {},\n      });\n    }\n  } catch (error) {\n    if (isAbortError(error)) {\n      throw error;\n    }\n\n    if (error instanceof TypeError && error.message === 'fetch failed') {\n      const cause = (error as any).cause;\n      if (cause != null) {\n        throw new APICallError({\n          message: `Cannot connect to API: ${cause.message}`,\n          cause,\n          url,\n          isRetryable: true,\n          requestBodyValues: {},\n        });\n      }\n    }\n\n    throw error;\n  }\n};\n", "/**\n * Removes entries from a record where the value is null or undefined.\n * @param record - The input object whose entries may be null or undefined.\n * @returns A new object containing only entries with non-null and non-undefined values.\n */\nexport function removeUndefinedEntries<T>(\n  record: Record<string, T | undefined>,\n): Record<string, T> {\n  return Object.fromEntries(\n    Object.entries(record).filter(([_key, value]) => value != null),\n  ) as Record<string, T>;\n}\n", "export function isAbortError(error: unknown): error is Error {\n  return (\n    error instanceof Error &&\n    (error.name === 'AbortError' || error.name === 'TimeoutError')\n  );\n}\n", "import { LoadAPIKeyError } from '@ai-sdk/provider';\n\nexport function loadApiKey({\n  apiKey,\n  environmentVariableName,\n  apiKeyParameterName = 'apiKey',\n  description,\n}: {\n  apiKey: string | undefined;\n  environmentVariableName: string;\n  apiKeyParameterName?: string;\n  description: string;\n}): string {\n  if (typeof apiKey === 'string') {\n    return apiKey;\n  }\n\n  if (apiKey != null) {\n    throw new LoadAPIKeyError({\n      message: `${description} API key must be a string.`,\n    });\n  }\n\n  if (typeof process === 'undefined') {\n    throw new LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter. Environment variables is not supported in this environment.`,\n    });\n  }\n\n  apiKey = process.env[environmentVariableName];\n\n  if (apiKey == null) {\n    throw new LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter or the ${environmentVariableName} environment variable.`,\n    });\n  }\n\n  if (typeof apiKey !== 'string') {\n    throw new LoadAPIKeyError({\n      message: `${description} API key must be a string. The value of the ${environmentVariableName} environment variable is not a string.`,\n    });\n  }\n\n  return apiKey;\n}\n", "/**\n * Loads an optional `string` setting from the environment or a parameter.\n *\n * @param settingValue - The setting value.\n * @param environmentVariableName - The environment variable name.\n * @returns The setting value.\n */\nexport function loadOptionalSetting({\n  settingValue,\n  environmentVariableName,\n}: {\n  settingValue: string | undefined;\n  environmentVariableName: string;\n}): string | undefined {\n  if (typeof settingValue === 'string') {\n    return settingValue;\n  }\n\n  if (settingValue != null || typeof process === 'undefined') {\n    return undefined;\n  }\n\n  settingValue = process.env[environmentVariableName];\n\n  if (settingValue == null || typeof settingValue !== 'string') {\n    return undefined;\n  }\n\n  return settingValue;\n}\n", "import { LoadSettingError } from '@ai-sdk/provider';\n\n/**\n * Loads a `string` setting from the environment or a parameter.\n *\n * @param settingValue - The setting value.\n * @param environmentVariableName - The environment variable name.\n * @param settingName - The setting name.\n * @param description - The description of the setting.\n * @returns The setting value.\n */\nexport function loadSetting({\n  settingValue,\n  environmentVariableName,\n  settingName,\n  description,\n}: {\n  settingValue: string | undefined;\n  environmentVariableName: string;\n  settingName: string;\n  description: string;\n}): string {\n  if (typeof settingValue === 'string') {\n    return settingValue;\n  }\n\n  if (settingValue != null) {\n    throw new LoadSettingError({\n      message: `${description} setting must be a string.`,\n    });\n  }\n\n  if (typeof process === 'undefined') {\n    throw new LoadSettingError({\n      message:\n        `${description} setting is missing. ` +\n        `Pass it using the '${settingName}' parameter. ` +\n        `Environment variables is not supported in this environment.`,\n    });\n  }\n\n  settingValue = process.env[environmentVariableName];\n\n  if (settingValue == null) {\n    throw new LoadSettingError({\n      message:\n        `${description} setting is missing. ` +\n        `Pass it using the '${settingName}' parameter ` +\n        `or the ${environmentVariableName} environment variable.`,\n    });\n  }\n\n  if (typeof settingValue !== 'string') {\n    throw new LoadSettingError({\n      message:\n        `${description} setting must be a string. ` +\n        `The value of the ${environmentVariableName} environment variable is not a string.`,\n    });\n  }\n\n  return settingValue;\n}\n", "import {\n  JSONParseError,\n  <PERSON>SO<PERSON>V<PERSON><PERSON>,\n  TypeValidationError,\n} from '@ai-sdk/provider';\nimport SecureJSON from 'secure-json-parse';\nimport { ZodSchema } from 'zod';\nimport { safeValidateTypes, validateTypes } from './validate-types';\nimport { Validator } from './validator';\n\n/**\n * Parses a JSON string into an unknown object.\n *\n * @param text - The JSON string to parse.\n * @returns {JSONValue} - The parsed JSON object.\n */\nexport function parseJSON(options: {\n  text: string;\n  schema?: undefined;\n}): JSONValue;\n/**\n * Parses a JSON string into a strongly-typed object using the provided schema.\n *\n * @template T - The type of the object to parse the JSON into.\n * @param {string} text - The JSON string to parse.\n * @param {Validator<T>} schema - The schema to use for parsing the JSON.\n * @returns {T} - The parsed object.\n */\nexport function parseJSON<T>(options: {\n  text: string;\n  schema: ZodSchema<T> | Validator<T>;\n}): T;\nexport function parseJSON<T>({\n  text,\n  schema,\n}: {\n  text: string;\n  schema?: ZodSchema<T> | Validator<T>;\n}): T {\n  try {\n    const value = SecureJSON.parse(text);\n\n    if (schema == null) {\n      return value;\n    }\n\n    return validateTypes({ value, schema });\n  } catch (error) {\n    if (\n      JSONParseError.isInstance(error) ||\n      TypeValidationError.isInstance(error)\n    ) {\n      throw error;\n    }\n\n    throw new JSONParseError({ text, cause: error });\n  }\n}\n\nexport type ParseResult<T> =\n  | { success: true; value: T; rawValue: unknown }\n  | { success: false; error: JSONParseError | TypeValidationError };\n\n/**\n * Safely parses a JSON string and returns the result as an object of type `unknown`.\n *\n * @param text - The JSON string to parse.\n * @returns {object} Either an object with `success: true` and the parsed data, or an object with `success: false` and the error that occurred.\n */\nexport function safeParseJSON(options: {\n  text: string;\n  schema?: undefined;\n}): ParseResult<JSONValue>;\n/**\n * Safely parses a JSON string into a strongly-typed object, using a provided schema to validate the object.\n *\n * @template T - The type of the object to parse the JSON into.\n * @param {string} text - The JSON string to parse.\n * @param {Validator<T>} schema - The schema to use for parsing the JSON.\n * @returns An object with either a `success` flag and the parsed and typed data, or a `success` flag and an error object.\n */\nexport function safeParseJSON<T>(options: {\n  text: string;\n  schema: ZodSchema<T> | Validator<T>;\n}): ParseResult<T>;\nexport function safeParseJSON<T>({\n  text,\n  schema,\n}: {\n  text: string;\n  schema?: ZodSchema<T> | Validator<T>;\n}): ParseResult<T> {\n  try {\n    const value = SecureJSON.parse(text);\n\n    if (schema == null) {\n      return { success: true, value: value as T, rawValue: value };\n    }\n\n    const validationResult = safeValidateTypes({ value, schema });\n\n    return validationResult.success\n      ? { ...validationResult, rawValue: value }\n      : validationResult;\n  } catch (error) {\n    return {\n      success: false,\n      error: JSONParseError.isInstance(error)\n        ? error\n        : new JSONParseError({ text, cause: error }),\n    };\n  }\n}\n\nexport function isParsableJson(input: string): boolean {\n  try {\n    SecureJSON.parse(input);\n    return true;\n  } catch {\n    return false;\n  }\n}\n", "import { TypeValidationError } from '@ai-sdk/provider';\nimport { z } from 'zod';\nimport { Validator, asValidator } from './validator';\n\n/**\n * Validates the types of an unknown object using a schema and\n * return a strongly-typed object.\n *\n * @template T - The type of the object to validate.\n * @param {string} options.value - The object to validate.\n * @param {Validator<T>} options.schema - The schema to use for validating the JSON.\n * @returns {T} - The typed object.\n */\nexport function validateTypes<T>({\n  value,\n  schema: inputSchema,\n}: {\n  value: unknown;\n  schema: z.Schema<T, z.ZodTypeDef, any> | Validator<T>;\n}): T {\n  const result = safeValidateTypes({ value, schema: inputSchema });\n\n  if (!result.success) {\n    throw TypeValidationError.wrap({ value, cause: result.error });\n  }\n\n  return result.value;\n}\n\n/**\n * Safely validates the types of an unknown object using a schema and\n * return a strongly-typed object.\n *\n * @template T - The type of the object to validate.\n * @param {string} options.value - The JSON object to validate.\n * @param {Validator<T>} options.schema - The schema to use for validating the JSON.\n * @returns An object with either a `success` flag and the parsed and typed data, or a `success` flag and an error object.\n */\nexport function safeValidateTypes<T>({\n  value,\n  schema,\n}: {\n  value: unknown;\n  schema: z.Schema<T, z.ZodTypeDef, any> | Validator<T>;\n}):\n  | { success: true; value: T }\n  | { success: false; error: TypeValidationError } {\n  const validator = asValidator(schema);\n\n  try {\n    if (validator.validate == null) {\n      return { success: true, value: value as T };\n    }\n\n    const result = validator.validate(value);\n\n    if (result.success) {\n      return result;\n    }\n\n    return {\n      success: false,\n      error: TypeValidationError.wrap({ value, cause: result.error }),\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: TypeValidationError.wrap({ value, cause: error }),\n    };\n  }\n}\n", "import { z } from 'zod';\n\n/**\n * Used to mark validator functions so we can support both Zod and custom schemas.\n */\nexport const validatorSymbol = Symbol.for('vercel.ai.validator');\n\nexport type ValidationResult<OBJECT> =\n  | { success: true; value: OBJECT }\n  | { success: false; error: Error };\n\nexport type Validator<OBJECT = unknown> = {\n  /**\n   * Used to mark validator functions so we can support both Zod and custom schemas.\n   */\n  [validatorSymbol]: true;\n\n  /**\n   * Optional. Validates that the structure of a value matches this schema,\n   * and returns a typed version of the value if it does.\n   */\n  readonly validate?: (value: unknown) => ValidationResult<OBJECT>;\n};\n\n/**\n * Create a validator.\n *\n * @param validate A validation function for the schema.\n */\nexport function validator<OBJECT>(\n  validate?: undefined | ((value: unknown) => ValidationResult<OBJECT>),\n): Validator<OBJECT> {\n  return { [validatorSymbol]: true, validate };\n}\n\nexport function isValidator(value: unknown): value is Validator {\n  return (\n    typeof value === 'object' &&\n    value !== null &&\n    validatorSymbol in value &&\n    value[validatorSymbol] === true &&\n    'validate' in value\n  );\n}\n\nexport function asValidator<OBJECT>(\n  value: Validator<OBJECT> | z.Schema<OBJECT, z.ZodTypeDef, any>,\n): Validator<OBJECT> {\n  return isValidator(value) ? value : zodValidator(value);\n}\n\nexport function zodValidator<OBJECT>(\n  zodSchema: z.Schema<OBJECT, z.ZodTypeDef, any>,\n): Validator<OBJECT> {\n  return validator(value => {\n    const result = zodSchema.safeParse(value);\n    return result.success\n      ? { success: true, value: result.data }\n      : { success: false, error: result.error };\n  });\n}\n", "import { InvalidArgumentError } from '@ai-sdk/provider';\nimport { safeValidateTypes } from './validate-types';\nimport { z } from 'zod';\n\nexport function parseProviderOptions<T>({\n  provider,\n  providerOptions,\n  schema,\n}: {\n  provider: string;\n  providerOptions: Record<string, unknown> | undefined;\n  schema: z.ZodSchema<T>;\n}): T | undefined {\n  if (providerOptions?.[provider] == null) {\n    return undefined;\n  }\n\n  const parsedProviderOptions = safeValidateTypes({\n    value: providerOptions[provider],\n    schema,\n  });\n\n  if (!parsedProviderOptions.success) {\n    throw new InvalidArgumentError({\n      argument: 'providerOptions',\n      message: `invalid ${provider} provider options`,\n      cause: parsedProviderOptions.error,\n    });\n  }\n\n  return parsedProviderOptions.value;\n}\n", "import { APICallError } from '@ai-sdk/provider';\nimport { extractResponseHeaders } from './extract-response-headers';\nimport { FetchFunction } from './fetch-function';\nimport { isAbortError } from './is-abort-error';\nimport { removeUndefinedEntries } from './remove-undefined-entries';\nimport { ResponseHandler } from './response-handler';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => globalThis.fetch;\n\nexport const postJsonToApi = async <T>({\n  url,\n  headers,\n  body,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch,\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  body: unknown;\n  failedResponseHandler: ResponseHandler<APICallError>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) =>\n  postToApi({\n    url,\n    headers: {\n      'Content-Type': 'application/json',\n      ...headers,\n    },\n    body: {\n      content: JSON.stringify(body),\n      values: body,\n    },\n    failedResponse<PERSON>and<PERSON>,\n    successfulResponseHandler,\n    abortSignal,\n    fetch,\n  });\n\nexport const postFormDataToApi = async <T>({\n  url,\n  headers,\n  formData,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch,\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  formData: FormData;\n  failedResponseHandler: ResponseHandler<APICallError>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) =>\n  postToApi({\n    url,\n    headers,\n    body: {\n      content: formData,\n      values: Object.fromEntries((formData as any).entries()),\n    },\n    failedResponseHandler,\n    successfulResponseHandler,\n    abortSignal,\n    fetch,\n  });\n\nexport const postToApi = async <T>({\n  url,\n  headers = {},\n  body,\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch(),\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  body: {\n    content: string | FormData | Uint8Array;\n    values: unknown;\n  };\n  failedResponseHandler: ResponseHandler<Error>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: 'POST',\n      headers: removeUndefinedEntries(headers),\n      body: body.content,\n      signal: abortSignal,\n    });\n\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!response.ok) {\n      let errorInformation: {\n        value: Error;\n        responseHeaders?: Record<string, string> | undefined;\n      };\n\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: body.values,\n        });\n      } catch (error) {\n        if (isAbortError(error) || APICallError.isInstance(error)) {\n          throw error;\n        }\n\n        throw new APICallError({\n          message: 'Failed to process error response',\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: body.values,\n        });\n      }\n\n      throw errorInformation.value;\n    }\n\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: body.values,\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || APICallError.isInstance(error)) {\n          throw error;\n        }\n      }\n\n      throw new APICallError({\n        message: 'Failed to process successful response',\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: body.values,\n      });\n    }\n  } catch (error) {\n    if (isAbortError(error)) {\n      throw error;\n    }\n\n    // unwrap original error when fetch failed (for easier debugging):\n    if (error instanceof TypeError && error.message === 'fetch failed') {\n      const cause = (error as any).cause;\n\n      if (cause != null) {\n        // Failed to connect to server:\n        throw new APICallError({\n          message: `Cannot connect to API: ${cause.message}`,\n          cause,\n          url,\n          requestBodyValues: body.values,\n          isRetryable: true, // retry when network error\n        });\n      }\n    }\n\n    throw error;\n  }\n};\n", "export type Resolvable<T> =\n  | T // Raw value\n  | Promise<T> // Promise of value\n  | (() => T) // Function returning value\n  | (() => Promise<T>); // Function returning promise of value\n\n/**\n * Resolves a value that could be a raw value, a Promise, a function returning a value,\n * or a function returning a Promise.\n */\nexport async function resolve<T>(value: Resolvable<T>): Promise<T> {\n  // If it's a function, call it to get the value/promise\n  if (typeof value === 'function') {\n    value = (value as Function)();\n  }\n\n  // Otherwise just resolve whatever we got (value or promise)\n  return Promise.resolve(value as T);\n}\n", "import { APICallError, EmptyResponseBodyError } from '@ai-sdk/provider';\nimport { ZodSchema } from 'zod';\nimport {\n  createEventSourceParserStream,\n  EventSourceChunk,\n} from './event-source-parser-stream';\nimport { extractResponseHeaders } from './extract-response-headers';\nimport { parseJSON, ParseResult, safeParseJSON } from './parse-json';\n\nexport type ResponseHandler<RETURN_TYPE> = (options: {\n  url: string;\n  requestBodyValues: unknown;\n  response: Response;\n}) => PromiseLike<{\n  value: RETURN_TYPE;\n  rawValue?: unknown;\n  responseHeaders?: Record<string, string>;\n}>;\n\nexport const createJsonErrorResponseHandler =\n  <T>({\n    errorSchema,\n    errorToMessage,\n    isRetryable,\n  }: {\n    errorSchema: ZodSchema<T>;\n    errorToMessage: (error: T) => string;\n    isRetryable?: (response: Response, error?: T) => boolean;\n  }): ResponseHandler<APICallError> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseBody = await response.text();\n    const responseHeaders = extractResponseHeaders(response);\n\n    // Some providers return an empty response body for some errors:\n    if (responseBody.trim() === '') {\n      return {\n        responseHeaders,\n        value: new APICallError({\n          message: response.statusText,\n          url,\n          requestBodyValues,\n          statusCode: response.status,\n          responseHeaders,\n          responseBody,\n          isRetryable: isRetryable?.(response),\n        }),\n      };\n    }\n\n    // resilient parsing in case the response is not JSON or does not match the schema:\n    try {\n      const parsedError = parseJSON({\n        text: responseBody,\n        schema: errorSchema,\n      });\n\n      return {\n        responseHeaders,\n        value: new APICallError({\n          message: errorToMessage(parsedError),\n          url,\n          requestBodyValues,\n          statusCode: response.status,\n          responseHeaders,\n          responseBody,\n          data: parsedError,\n          isRetryable: isRetryable?.(response, parsedError),\n        }),\n      };\n    } catch (parseError) {\n      return {\n        responseHeaders,\n        value: new APICallError({\n          message: response.statusText,\n          url,\n          requestBodyValues,\n          statusCode: response.status,\n          responseHeaders,\n          responseBody,\n          isRetryable: isRetryable?.(response),\n        }),\n      };\n    }\n  };\n\nexport const createEventSourceResponseHandler =\n  <T>(\n    chunkSchema: ZodSchema<T>,\n  ): ResponseHandler<ReadableStream<ParseResult<T>>> =>\n  async ({ response }: { response: Response }) => {\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (response.body == null) {\n      throw new EmptyResponseBodyError({});\n    }\n\n    return {\n      responseHeaders,\n      value: response.body\n        .pipeThrough(new TextDecoderStream())\n        .pipeThrough(createEventSourceParserStream())\n        .pipeThrough(\n          new TransformStream<EventSourceChunk, ParseResult<T>>({\n            transform({ data }, controller) {\n              // ignore the 'DONE' event that e.g. OpenAI sends:\n              if (data === '[DONE]') {\n                return;\n              }\n\n              controller.enqueue(\n                safeParseJSON({\n                  text: data,\n                  schema: chunkSchema,\n                }),\n              );\n            },\n          }),\n        ),\n    };\n  };\n\nexport const createJsonStreamResponseHandler =\n  <T>(\n    chunkSchema: ZodSchema<T>,\n  ): ResponseHandler<ReadableStream<ParseResult<T>>> =>\n  async ({ response }: { response: Response }) => {\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (response.body == null) {\n      throw new EmptyResponseBodyError({});\n    }\n\n    let buffer = '';\n\n    return {\n      responseHeaders,\n      value: response.body.pipeThrough(new TextDecoderStream()).pipeThrough(\n        new TransformStream<string, ParseResult<T>>({\n          transform(chunkText, controller) {\n            if (chunkText.endsWith('\\n')) {\n              controller.enqueue(\n                safeParseJSON({\n                  text: buffer + chunkText,\n                  schema: chunkSchema,\n                }),\n              );\n              buffer = '';\n            } else {\n              buffer += chunkText;\n            }\n          },\n        }),\n      ),\n    };\n  };\n\nexport const createJsonResponseHandler =\n  <T>(responseSchema: ZodSchema<T>): ResponseHandler<T> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseBody = await response.text();\n\n    const parsedResult = safeParseJSON({\n      text: responseBody,\n      schema: responseSchema,\n    });\n\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!parsedResult.success) {\n      throw new APICallError({\n        message: 'Invalid JSON response',\n        cause: parsedResult.error,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        url,\n        requestBodyValues,\n      });\n    }\n\n    return {\n      responseHeaders,\n      value: parsedResult.value,\n      rawValue: parsedResult.rawValue,\n    };\n  };\n\nexport const createBinaryResponseHandler =\n  (): ResponseHandler<Uint8Array> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!response.body) {\n      throw new APICallError({\n        message: 'Response body is empty',\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody: undefined,\n      });\n    }\n\n    try {\n      const buffer = await response.arrayBuffer();\n      return {\n        responseHeaders,\n        value: new Uint8Array(buffer),\n      };\n    } catch (error) {\n      throw new APICallError({\n        message: 'Failed to read response as array buffer',\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody: undefined,\n        cause: error,\n      });\n    }\n  };\n\nexport const createStatusCodeErrorResponseHandler =\n  (): ResponseHandler<APICallError> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseHeaders = extractResponseHeaders(response);\n    const responseBody = await response.text();\n\n    return {\n      responseHeaders,\n      value: new APICallError({\n        message: response.statusText,\n        url,\n        requestBodyValues: requestBodyValues as Record<string, unknown>,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n      }),\n    };\n  };\n", "// btoa and atob need to be invoked as a function call, not as a method call.\n// Otherwise CloudFlare will throw a\n// \"TypeError: Illegal invocation: function called with incorrect this reference\"\nconst { btoa, atob } = globalThis;\n\nexport function convertBase64ToUint8Array(base64String: string) {\n  const base64Url = base64String.replace(/-/g, '+').replace(/_/g, '/');\n  const latin1string = atob(base64Url);\n  return Uint8Array.from(latin1string, byte => byte.codePointAt(0)!);\n}\n\nexport function convertUint8ArrayToBase64(array: Uint8Array): string {\n  let latin1string = '';\n\n  // Note: regular for loop to support older JavaScript versions that\n  // do not support for..of on Uint8Array\n  for (let i = 0; i < array.length; i++) {\n    latin1string += String.fromCodePoint(array[i]);\n  }\n\n  return btoa(latin1string);\n}\n", "export function withoutTrailingSlash(url: string | undefined) {\n  return url?.replace(/\\/$/, '');\n}\n", "import {\n  EmbeddingModelV1,\n  ImageModelV1,\n  TranscriptionModelV1,\n  LanguageModelV1,\n  ProviderV1,\n  SpeechModelV1,\n} from '@ai-sdk/provider';\nimport {\n  FetchFunction,\n  loadApiKey,\n  withoutTrailingSlash,\n} from '@ai-sdk/provider-utils';\nimport { OpenAIChatLanguageModel } from './openai-chat-language-model';\nimport { OpenAIChatModelId, OpenAIChatSettings } from './openai-chat-settings';\nimport { OpenAICompletionLanguageModel } from './openai-completion-language-model';\nimport {\n  OpenAICompletionModelId,\n  OpenAICompletionSettings,\n} from './openai-completion-settings';\nimport { OpenAIEmbeddingModel } from './openai-embedding-model';\nimport {\n  OpenAIEmbeddingModelId,\n  OpenAIEmbeddingSettings,\n} from './openai-embedding-settings';\nimport { OpenAIImageModel } from './openai-image-model';\nimport {\n  OpenAIImageModelId,\n  OpenAIImageSettings,\n} from './openai-image-settings';\nimport { OpenAITranscriptionModel } from './openai-transcription-model';\nimport { OpenAITranscriptionModelId } from './openai-transcription-settings';\nimport { OpenAIResponsesLanguageModel } from './responses/openai-responses-language-model';\nimport { OpenAIResponsesModelId } from './responses/openai-responses-settings';\nimport { openaiTools } from './openai-tools';\nimport { OpenAISpeechModel } from './openai-speech-model';\nimport { OpenAISpeechModelId } from './openai-speech-settings';\n\nexport interface OpenAIProvider extends ProviderV1 {\n  (\n    modelId: 'gpt-3.5-turbo-instruct',\n    settings?: OpenAICompletionSettings,\n  ): OpenAICompletionLanguageModel;\n  (modelId: OpenAIChatModelId, settings?: OpenAIChatSettings): LanguageModelV1;\n\n  /**\nCreates an OpenAI model for text generation.\n   */\n  languageModel(\n    modelId: 'gpt-3.5-turbo-instruct',\n    settings?: OpenAICompletionSettings,\n  ): OpenAICompletionLanguageModel;\n  languageModel(\n    modelId: OpenAIChatModelId,\n    settings?: OpenAIChatSettings,\n  ): LanguageModelV1;\n\n  /**\nCreates an OpenAI chat model for text generation.\n   */\n  chat(\n    modelId: OpenAIChatModelId,\n    settings?: OpenAIChatSettings,\n  ): LanguageModelV1;\n\n  /**\nCreates an OpenAI responses API model for text generation.\n   */\n  responses(modelId: OpenAIResponsesModelId): LanguageModelV1;\n\n  /**\nCreates an OpenAI completion model for text generation.\n   */\n  completion(\n    modelId: OpenAICompletionModelId,\n    settings?: OpenAICompletionSettings,\n  ): LanguageModelV1;\n\n  /**\nCreates a model for text embeddings.\n   */\n  embedding(\n    modelId: OpenAIEmbeddingModelId,\n    settings?: OpenAIEmbeddingSettings,\n  ): EmbeddingModelV1<string>;\n\n  /**\nCreates a model for text embeddings.\n\n@deprecated Use `textEmbeddingModel` instead.\n   */\n  textEmbedding(\n    modelId: OpenAIEmbeddingModelId,\n    settings?: OpenAIEmbeddingSettings,\n  ): EmbeddingModelV1<string>;\n\n  /**\nCreates a model for text embeddings.\n   */\n  textEmbeddingModel(\n    modelId: OpenAIEmbeddingModelId,\n    settings?: OpenAIEmbeddingSettings,\n  ): EmbeddingModelV1<string>;\n\n  /**\nCreates a model for image generation.\n   */\n  image(\n    modelId: OpenAIImageModelId,\n    settings?: OpenAIImageSettings,\n  ): ImageModelV1;\n\n  /**\nCreates a model for image generation.\n   */\n  imageModel(\n    modelId: OpenAIImageModelId,\n    settings?: OpenAIImageSettings,\n  ): ImageModelV1;\n\n  /**\nCreates a model for transcription.\n   */\n  transcription(modelId: OpenAITranscriptionModelId): TranscriptionModelV1;\n\n  /**\nCreates a model for speech generation.\n   */\n  speech(modelId: OpenAISpeechModelId): SpeechModelV1;\n\n  /**\nOpenAI-specific tools.\n   */\n  tools: typeof openaiTools;\n}\n\nexport interface OpenAIProviderSettings {\n  /**\nBase URL for the OpenAI API calls.\n     */\n  baseURL?: string;\n\n  /**\nAPI key for authenticating requests.\n     */\n  apiKey?: string;\n\n  /**\nOpenAI Organization.\n     */\n  organization?: string;\n\n  /**\nOpenAI project.\n     */\n  project?: string;\n\n  /**\nCustom headers to include in the requests.\n     */\n  headers?: Record<string, string>;\n\n  /**\nOpenAI compatibility mode. Should be set to `strict` when using the OpenAI API,\nand `compatible` when using 3rd party providers. In `compatible` mode, newer\ninformation such as streamOptions are not being sent. Defaults to 'compatible'.\n   */\n  compatibility?: 'strict' | 'compatible';\n\n  /**\nProvider name. Overrides the `openai` default name for 3rd party providers.\n   */\n  name?: string;\n\n  /**\nCustom fetch implementation. You can use it as a middleware to intercept requests,\nor to provide a custom fetch implementation for e.g. testing.\n    */\n  fetch?: FetchFunction;\n}\n\n/**\nCreate an OpenAI provider instance.\n */\nexport function createOpenAI(\n  options: OpenAIProviderSettings = {},\n): OpenAIProvider {\n  const baseURL =\n    withoutTrailingSlash(options.baseURL) ?? 'https://api.openai.com/v1';\n\n  // we default to compatible, because strict breaks providers like Groq:\n  const compatibility = options.compatibility ?? 'compatible';\n\n  const providerName = options.name ?? 'openai';\n\n  const getHeaders = () => ({\n    Authorization: `Bearer ${loadApiKey({\n      apiKey: options.apiKey,\n      environmentVariableName: 'OPENAI_API_KEY',\n      description: 'OpenAI',\n    })}`,\n    'OpenAI-Organization': options.organization,\n    'OpenAI-Project': options.project,\n    ...options.headers,\n  });\n\n  const createChatModel = (\n    modelId: OpenAIChatModelId,\n    settings: OpenAIChatSettings = {},\n  ) =>\n    new OpenAIChatLanguageModel(modelId, settings, {\n      provider: `${providerName}.chat`,\n      url: ({ path }) => `${baseURL}${path}`,\n      headers: getHeaders,\n      compatibility,\n      fetch: options.fetch,\n    });\n\n  const createCompletionModel = (\n    modelId: OpenAICompletionModelId,\n    settings: OpenAICompletionSettings = {},\n  ) =>\n    new OpenAICompletionLanguageModel(modelId, settings, {\n      provider: `${providerName}.completion`,\n      url: ({ path }) => `${baseURL}${path}`,\n      headers: getHeaders,\n      compatibility,\n      fetch: options.fetch,\n    });\n\n  const createEmbeddingModel = (\n    modelId: OpenAIEmbeddingModelId,\n    settings: OpenAIEmbeddingSettings = {},\n  ) =>\n    new OpenAIEmbeddingModel(modelId, settings, {\n      provider: `${providerName}.embedding`,\n      url: ({ path }) => `${baseURL}${path}`,\n      headers: getHeaders,\n      fetch: options.fetch,\n    });\n\n  const createImageModel = (\n    modelId: OpenAIImageModelId,\n    settings: OpenAIImageSettings = {},\n  ) =>\n    new OpenAIImageModel(modelId, settings, {\n      provider: `${providerName}.image`,\n      url: ({ path }) => `${baseURL}${path}`,\n      headers: getHeaders,\n      fetch: options.fetch,\n    });\n\n  const createTranscriptionModel = (modelId: OpenAITranscriptionModelId) =>\n    new OpenAITranscriptionModel(modelId, {\n      provider: `${providerName}.transcription`,\n      url: ({ path }) => `${baseURL}${path}`,\n      headers: getHeaders,\n      fetch: options.fetch,\n    });\n\n  const createSpeechModel = (modelId: OpenAISpeechModelId) =>\n    new OpenAISpeechModel(modelId, {\n      provider: `${providerName}.speech`,\n      url: ({ path }) => `${baseURL}${path}`,\n      headers: getHeaders,\n      fetch: options.fetch,\n    });\n\n  const createLanguageModel = (\n    modelId: OpenAIChatModelId | OpenAICompletionModelId,\n    settings?: OpenAIChatSettings | OpenAICompletionSettings,\n  ) => {\n    if (new.target) {\n      throw new Error(\n        'The OpenAI model function cannot be called with the new keyword.',\n      );\n    }\n\n    if (modelId === 'gpt-3.5-turbo-instruct') {\n      return createCompletionModel(\n        modelId,\n        settings as OpenAICompletionSettings,\n      );\n    }\n\n    return createChatModel(modelId, settings as OpenAIChatSettings);\n  };\n\n  const createResponsesModel = (modelId: OpenAIResponsesModelId) => {\n    return new OpenAIResponsesLanguageModel(modelId, {\n      provider: `${providerName}.responses`,\n      url: ({ path }) => `${baseURL}${path}`,\n      headers: getHeaders,\n      fetch: options.fetch,\n    });\n  };\n\n  const provider = function (\n    modelId: OpenAIChatModelId | OpenAICompletionModelId,\n    settings?: OpenAIChatSettings | OpenAICompletionSettings,\n  ) {\n    return createLanguageModel(modelId, settings);\n  };\n\n  provider.languageModel = createLanguageModel;\n  provider.chat = createChatModel;\n  provider.completion = createCompletionModel;\n  provider.responses = createResponsesModel;\n  provider.embedding = createEmbeddingModel;\n  provider.textEmbedding = createEmbeddingModel;\n  provider.textEmbeddingModel = createEmbeddingModel;\n\n  provider.image = createImageModel;\n  provider.imageModel = createImageModel;\n\n  provider.transcription = createTranscriptionModel;\n  provider.transcriptionModel = createTranscriptionModel;\n\n  provider.speech = createSpeechModel;\n  provider.speechModel = createSpeechModel;\n\n  provider.tools = openaiTools;\n\n  return provider as OpenAIProvider;\n}\n\n/**\nDefault OpenAI provider instance. It uses 'strict' compatibility mode.\n */\nexport const openai = createOpenAI({\n  compatibility: 'strict', // strict for OpenAI API\n});\n", "import {\n  InvalidResponseDataError,\n  LanguageModelV1,\n  LanguageModelV1CallWarning,\n  LanguageModelV1FinishReason,\n  LanguageModelV1LogProbs,\n  LanguageModelV1ProviderMetadata,\n  LanguageModelV1StreamPart,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport {\n  FetchFunction,\n  ParseResult,\n  combineHeaders,\n  createEventSourceResponseHandler,\n  createJsonResponseHandler,\n  generateId,\n  isParsableJson,\n  postJsonToApi,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { convertToOpenAIChatMessages } from './convert-to-openai-chat-messages';\nimport { mapOpenAIChatLogProbsOutput } from './map-openai-chat-logprobs';\nimport { mapOpenAIFinishReason } from './map-openai-finish-reason';\nimport { OpenAIChatModelId, OpenAIChatSettings } from './openai-chat-settings';\nimport {\n  openaiErrorDataSchema,\n  openaiFailedResponseHandler,\n} from './openai-error';\nimport { getResponseMetadata } from './get-response-metadata';\nimport { prepareTools } from './openai-prepare-tools';\n\ntype OpenAIChatConfig = {\n  provider: string;\n  compatibility: 'strict' | 'compatible';\n  headers: () => Record<string, string | undefined>;\n  url: (options: { modelId: string; path: string }) => string;\n  fetch?: FetchFunction;\n};\n\nexport class OpenAIChatLanguageModel implements LanguageModelV1 {\n  readonly specificationVersion = 'v1';\n\n  readonly modelId: OpenAIChatModelId;\n  readonly settings: OpenAIChatSettings;\n\n  private readonly config: OpenAIChatConfig;\n\n  constructor(\n    modelId: OpenAIChatModelId,\n    settings: OpenAIChatSettings,\n    config: OpenAIChatConfig,\n  ) {\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n\n  get supportsStructuredOutputs(): boolean {\n    // enable structured outputs for reasoning models by default:\n    // TODO in the next major version, remove this and always use json mode for models\n    // that support structured outputs (blacklist other models)\n    return this.settings.structuredOutputs ?? isReasoningModel(this.modelId);\n  }\n\n  get defaultObjectGenerationMode() {\n    // audio models don't support structured outputs:\n    if (isAudioModel(this.modelId)) {\n      return 'tool';\n    }\n\n    return this.supportsStructuredOutputs ? 'json' : 'tool';\n  }\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  get supportsImageUrls(): boolean {\n    // image urls can be sent if downloadImages is disabled (default):\n    return !this.settings.downloadImages;\n  }\n\n  private getArgs({\n    mode,\n    prompt,\n    maxTokens,\n    temperature,\n    topP,\n    topK,\n    frequencyPenalty,\n    presencePenalty,\n    stopSequences,\n    responseFormat,\n    seed,\n    providerMetadata,\n  }: Parameters<LanguageModelV1['doGenerate']>[0]) {\n    const type = mode.type;\n\n    const warnings: LanguageModelV1CallWarning[] = [];\n\n    if (topK != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'topK',\n      });\n    }\n\n    if (\n      responseFormat?.type === 'json' &&\n      responseFormat.schema != null &&\n      !this.supportsStructuredOutputs\n    ) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'responseFormat',\n        details:\n          'JSON response format schema is only supported with structuredOutputs',\n      });\n    }\n\n    const useLegacyFunctionCalling = this.settings.useLegacyFunctionCalling;\n\n    if (useLegacyFunctionCalling && this.settings.parallelToolCalls === true) {\n      throw new UnsupportedFunctionalityError({\n        functionality: 'useLegacyFunctionCalling with parallelToolCalls',\n      });\n    }\n\n    if (useLegacyFunctionCalling && this.supportsStructuredOutputs) {\n      throw new UnsupportedFunctionalityError({\n        functionality: 'structuredOutputs with useLegacyFunctionCalling',\n      });\n    }\n\n    const { messages, warnings: messageWarnings } = convertToOpenAIChatMessages(\n      {\n        prompt,\n        useLegacyFunctionCalling,\n        systemMessageMode: getSystemMessageMode(this.modelId),\n      },\n    );\n\n    warnings.push(...messageWarnings);\n\n    const baseArgs = {\n      // model id:\n      model: this.modelId,\n\n      // model specific settings:\n      logit_bias: this.settings.logitBias,\n      logprobs:\n        this.settings.logprobs === true ||\n        typeof this.settings.logprobs === 'number'\n          ? true\n          : undefined,\n      top_logprobs:\n        typeof this.settings.logprobs === 'number'\n          ? this.settings.logprobs\n          : typeof this.settings.logprobs === 'boolean'\n            ? this.settings.logprobs\n              ? 0\n              : undefined\n            : undefined,\n      user: this.settings.user,\n      parallel_tool_calls: this.settings.parallelToolCalls,\n\n      // standardized settings:\n      max_tokens: maxTokens,\n      temperature,\n      top_p: topP,\n      frequency_penalty: frequencyPenalty,\n      presence_penalty: presencePenalty,\n      response_format:\n        responseFormat?.type === 'json'\n          ? this.supportsStructuredOutputs && responseFormat.schema != null\n            ? {\n                type: 'json_schema',\n                json_schema: {\n                  schema: responseFormat.schema,\n                  strict: true,\n                  name: responseFormat.name ?? 'response',\n                  description: responseFormat.description,\n                },\n              }\n            : { type: 'json_object' }\n          : undefined,\n      stop: stopSequences,\n      seed,\n\n      // openai specific settings:\n      // TODO remove in next major version; we auto-map maxTokens now\n      max_completion_tokens: providerMetadata?.openai?.maxCompletionTokens,\n      store: providerMetadata?.openai?.store,\n      metadata: providerMetadata?.openai?.metadata,\n      prediction: providerMetadata?.openai?.prediction,\n      reasoning_effort:\n        providerMetadata?.openai?.reasoningEffort ??\n        this.settings.reasoningEffort,\n\n      // messages:\n      messages,\n    };\n\n    if (isReasoningModel(this.modelId)) {\n      // remove unsupported settings for reasoning models\n      // see https://platform.openai.com/docs/guides/reasoning#limitations\n      if (baseArgs.temperature != null) {\n        baseArgs.temperature = undefined;\n        warnings.push({\n          type: 'unsupported-setting',\n          setting: 'temperature',\n          details: 'temperature is not supported for reasoning models',\n        });\n      }\n      if (baseArgs.top_p != null) {\n        baseArgs.top_p = undefined;\n        warnings.push({\n          type: 'unsupported-setting',\n          setting: 'topP',\n          details: 'topP is not supported for reasoning models',\n        });\n      }\n      if (baseArgs.frequency_penalty != null) {\n        baseArgs.frequency_penalty = undefined;\n        warnings.push({\n          type: 'unsupported-setting',\n          setting: 'frequencyPenalty',\n          details: 'frequencyPenalty is not supported for reasoning models',\n        });\n      }\n      if (baseArgs.presence_penalty != null) {\n        baseArgs.presence_penalty = undefined;\n        warnings.push({\n          type: 'unsupported-setting',\n          setting: 'presencePenalty',\n          details: 'presencePenalty is not supported for reasoning models',\n        });\n      }\n      if (baseArgs.logit_bias != null) {\n        baseArgs.logit_bias = undefined;\n        warnings.push({\n          type: 'other',\n          message: 'logitBias is not supported for reasoning models',\n        });\n      }\n      if (baseArgs.logprobs != null) {\n        baseArgs.logprobs = undefined;\n        warnings.push({\n          type: 'other',\n          message: 'logprobs is not supported for reasoning models',\n        });\n      }\n      if (baseArgs.top_logprobs != null) {\n        baseArgs.top_logprobs = undefined;\n        warnings.push({\n          type: 'other',\n          message: 'topLogprobs is not supported for reasoning models',\n        });\n      }\n\n      // reasoning models use max_completion_tokens instead of max_tokens:\n      if (baseArgs.max_tokens != null) {\n        if (baseArgs.max_completion_tokens == null) {\n          baseArgs.max_completion_tokens = baseArgs.max_tokens;\n        }\n        baseArgs.max_tokens = undefined;\n      }\n    } else if (\n      this.modelId.startsWith('gpt-4o-search-preview') ||\n      this.modelId.startsWith('gpt-4o-mini-search-preview')\n    ) {\n      if (baseArgs.temperature != null) {\n        baseArgs.temperature = undefined;\n        warnings.push({\n          type: 'unsupported-setting',\n          setting: 'temperature',\n          details:\n            'temperature is not supported for the search preview models and has been removed.',\n        });\n      }\n    }\n    switch (type) {\n      case 'regular': {\n        const { tools, tool_choice, functions, function_call, toolWarnings } =\n          prepareTools({\n            mode,\n            useLegacyFunctionCalling,\n            structuredOutputs: this.supportsStructuredOutputs,\n          });\n\n        return {\n          args: {\n            ...baseArgs,\n            tools,\n            tool_choice,\n            functions,\n            function_call,\n          },\n          warnings: [...warnings, ...toolWarnings],\n        };\n      }\n\n      case 'object-json': {\n        return {\n          args: {\n            ...baseArgs,\n            response_format:\n              this.supportsStructuredOutputs && mode.schema != null\n                ? {\n                    type: 'json_schema',\n                    json_schema: {\n                      schema: mode.schema,\n                      strict: true,\n                      name: mode.name ?? 'response',\n                      description: mode.description,\n                    },\n                  }\n                : { type: 'json_object' },\n          },\n          warnings,\n        };\n      }\n\n      case 'object-tool': {\n        return {\n          args: useLegacyFunctionCalling\n            ? {\n                ...baseArgs,\n                function_call: {\n                  name: mode.tool.name,\n                },\n                functions: [\n                  {\n                    name: mode.tool.name,\n                    description: mode.tool.description,\n                    parameters: mode.tool.parameters,\n                  },\n                ],\n              }\n            : {\n                ...baseArgs,\n                tool_choice: {\n                  type: 'function',\n                  function: { name: mode.tool.name },\n                },\n                tools: [\n                  {\n                    type: 'function',\n                    function: {\n                      name: mode.tool.name,\n                      description: mode.tool.description,\n                      parameters: mode.tool.parameters,\n                      strict: this.supportsStructuredOutputs ? true : undefined,\n                    },\n                  },\n                ],\n              },\n          warnings,\n        };\n      }\n\n      default: {\n        const _exhaustiveCheck: never = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  async doGenerate(\n    options: Parameters<LanguageModelV1['doGenerate']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doGenerate']>>> {\n    const { args: body, warnings } = this.getArgs(options);\n\n    const {\n      responseHeaders,\n      value: response,\n      rawValue: rawResponse,\n    } = await postJsonToApi({\n      url: this.config.url({\n        path: '/chat/completions',\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        openaiChatResponseSchema,\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { messages: rawPrompt, ...rawSettings } = body;\n    const choice = response.choices[0];\n\n    // provider metadata:\n    const completionTokenDetails = response.usage?.completion_tokens_details;\n    const promptTokenDetails = response.usage?.prompt_tokens_details;\n    const providerMetadata: LanguageModelV1ProviderMetadata = { openai: {} };\n    if (completionTokenDetails?.reasoning_tokens != null) {\n      providerMetadata.openai.reasoningTokens =\n        completionTokenDetails?.reasoning_tokens;\n    }\n    if (completionTokenDetails?.accepted_prediction_tokens != null) {\n      providerMetadata.openai.acceptedPredictionTokens =\n        completionTokenDetails?.accepted_prediction_tokens;\n    }\n    if (completionTokenDetails?.rejected_prediction_tokens != null) {\n      providerMetadata.openai.rejectedPredictionTokens =\n        completionTokenDetails?.rejected_prediction_tokens;\n    }\n    if (promptTokenDetails?.cached_tokens != null) {\n      providerMetadata.openai.cachedPromptTokens =\n        promptTokenDetails?.cached_tokens;\n    }\n\n    return {\n      text: choice.message.content ?? undefined,\n      toolCalls:\n        this.settings.useLegacyFunctionCalling && choice.message.function_call\n          ? [\n              {\n                toolCallType: 'function',\n                toolCallId: generateId(),\n                toolName: choice.message.function_call.name,\n                args: choice.message.function_call.arguments,\n              },\n            ]\n          : choice.message.tool_calls?.map(toolCall => ({\n              toolCallType: 'function',\n              toolCallId: toolCall.id ?? generateId(),\n              toolName: toolCall.function.name,\n              args: toolCall.function.arguments!,\n            })),\n      finishReason: mapOpenAIFinishReason(choice.finish_reason),\n      usage: {\n        promptTokens: response.usage?.prompt_tokens ?? NaN,\n        completionTokens: response.usage?.completion_tokens ?? NaN,\n      },\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders, body: rawResponse },\n      request: { body: JSON.stringify(body) },\n      response: getResponseMetadata(response),\n      warnings,\n      logprobs: mapOpenAIChatLogProbsOutput(choice.logprobs),\n      providerMetadata,\n    };\n  }\n\n  async doStream(\n    options: Parameters<LanguageModelV1['doStream']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doStream']>>> {\n    if (this.settings.simulateStreaming) {\n      const result = await this.doGenerate(options);\n\n      const simulatedStream = new ReadableStream<LanguageModelV1StreamPart>({\n        start(controller) {\n          controller.enqueue({ type: 'response-metadata', ...result.response });\n          if (result.text) {\n            controller.enqueue({\n              type: 'text-delta',\n              textDelta: result.text,\n            });\n          }\n          if (result.toolCalls) {\n            for (const toolCall of result.toolCalls) {\n              controller.enqueue({\n                type: 'tool-call-delta',\n                toolCallType: 'function',\n                toolCallId: toolCall.toolCallId,\n                toolName: toolCall.toolName,\n                argsTextDelta: toolCall.args,\n              });\n\n              controller.enqueue({\n                type: 'tool-call',\n                ...toolCall,\n              });\n            }\n          }\n          controller.enqueue({\n            type: 'finish',\n            finishReason: result.finishReason,\n            usage: result.usage,\n            logprobs: result.logprobs,\n            providerMetadata: result.providerMetadata,\n          });\n          controller.close();\n        },\n      });\n      return {\n        stream: simulatedStream,\n        rawCall: result.rawCall,\n        rawResponse: result.rawResponse,\n        warnings: result.warnings,\n      };\n    }\n\n    const { args, warnings } = this.getArgs(options);\n\n    const body = {\n      ...args,\n      stream: true,\n\n      // only include stream_options when in strict compatibility mode:\n      stream_options:\n        this.config.compatibility === 'strict'\n          ? { include_usage: true }\n          : undefined,\n    };\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: this.config.url({\n        path: '/chat/completions',\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createEventSourceResponseHandler(\n        openaiChatChunkSchema,\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { messages: rawPrompt, ...rawSettings } = args;\n\n    const toolCalls: Array<{\n      id: string;\n      type: 'function';\n      function: {\n        name: string;\n        arguments: string;\n      };\n      hasFinished: boolean;\n    }> = [];\n\n    let finishReason: LanguageModelV1FinishReason = 'unknown';\n    let usage: {\n      promptTokens: number | undefined;\n      completionTokens: number | undefined;\n    } = {\n      promptTokens: undefined,\n      completionTokens: undefined,\n    };\n    let logprobs: LanguageModelV1LogProbs;\n    let isFirstChunk = true;\n\n    const { useLegacyFunctionCalling } = this.settings;\n\n    const providerMetadata: LanguageModelV1ProviderMetadata = { openai: {} };\n\n    return {\n      stream: response.pipeThrough(\n        new TransformStream<\n          ParseResult<z.infer<typeof openaiChatChunkSchema>>,\n          LanguageModelV1StreamPart\n        >({\n          transform(chunk, controller) {\n            // handle failed chunk parsing / validation:\n            if (!chunk.success) {\n              finishReason = 'error';\n              controller.enqueue({ type: 'error', error: chunk.error });\n              return;\n            }\n\n            const value = chunk.value;\n\n            // handle error chunks:\n            if ('error' in value) {\n              finishReason = 'error';\n              controller.enqueue({ type: 'error', error: value.error });\n              return;\n            }\n\n            if (isFirstChunk) {\n              isFirstChunk = false;\n\n              controller.enqueue({\n                type: 'response-metadata',\n                ...getResponseMetadata(value),\n              });\n            }\n\n            if (value.usage != null) {\n              const {\n                prompt_tokens,\n                completion_tokens,\n                prompt_tokens_details,\n                completion_tokens_details,\n              } = value.usage;\n\n              usage = {\n                promptTokens: prompt_tokens ?? undefined,\n                completionTokens: completion_tokens ?? undefined,\n              };\n\n              if (completion_tokens_details?.reasoning_tokens != null) {\n                providerMetadata.openai.reasoningTokens =\n                  completion_tokens_details?.reasoning_tokens;\n              }\n              if (\n                completion_tokens_details?.accepted_prediction_tokens != null\n              ) {\n                providerMetadata.openai.acceptedPredictionTokens =\n                  completion_tokens_details?.accepted_prediction_tokens;\n              }\n              if (\n                completion_tokens_details?.rejected_prediction_tokens != null\n              ) {\n                providerMetadata.openai.rejectedPredictionTokens =\n                  completion_tokens_details?.rejected_prediction_tokens;\n              }\n              if (prompt_tokens_details?.cached_tokens != null) {\n                providerMetadata.openai.cachedPromptTokens =\n                  prompt_tokens_details?.cached_tokens;\n              }\n            }\n\n            const choice = value.choices[0];\n\n            if (choice?.finish_reason != null) {\n              finishReason = mapOpenAIFinishReason(choice.finish_reason);\n            }\n\n            if (choice?.delta == null) {\n              return;\n            }\n\n            const delta = choice.delta;\n\n            if (delta.content != null) {\n              controller.enqueue({\n                type: 'text-delta',\n                textDelta: delta.content,\n              });\n            }\n\n            const mappedLogprobs = mapOpenAIChatLogProbsOutput(\n              choice?.logprobs,\n            );\n            if (mappedLogprobs?.length) {\n              if (logprobs === undefined) logprobs = [];\n              logprobs.push(...mappedLogprobs);\n            }\n\n            const mappedToolCalls: typeof delta.tool_calls =\n              useLegacyFunctionCalling && delta.function_call != null\n                ? [\n                    {\n                      type: 'function',\n                      id: generateId(),\n                      function: delta.function_call,\n                      index: 0,\n                    },\n                  ]\n                : delta.tool_calls;\n\n            if (mappedToolCalls != null) {\n              for (const toolCallDelta of mappedToolCalls) {\n                const index = toolCallDelta.index;\n\n                // Tool call start. OpenAI returns all information except the arguments in the first chunk.\n                if (toolCalls[index] == null) {\n                  if (toolCallDelta.type !== 'function') {\n                    throw new InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'function' type.`,\n                    });\n                  }\n\n                  if (toolCallDelta.id == null) {\n                    throw new InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'id' to be a string.`,\n                    });\n                  }\n\n                  if (toolCallDelta.function?.name == null) {\n                    throw new InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'function.name' to be a string.`,\n                    });\n                  }\n\n                  toolCalls[index] = {\n                    id: toolCallDelta.id,\n                    type: 'function',\n                    function: {\n                      name: toolCallDelta.function.name,\n                      arguments: toolCallDelta.function.arguments ?? '',\n                    },\n                    hasFinished: false,\n                  };\n\n                  const toolCall = toolCalls[index];\n\n                  if (\n                    toolCall.function?.name != null &&\n                    toolCall.function?.arguments != null\n                  ) {\n                    // send delta if the argument text has already started:\n                    if (toolCall.function.arguments.length > 0) {\n                      controller.enqueue({\n                        type: 'tool-call-delta',\n                        toolCallType: 'function',\n                        toolCallId: toolCall.id,\n                        toolName: toolCall.function.name,\n                        argsTextDelta: toolCall.function.arguments,\n                      });\n                    }\n\n                    // check if tool call is complete\n                    // (some providers send the full tool call in one chunk):\n                    if (isParsableJson(toolCall.function.arguments)) {\n                      controller.enqueue({\n                        type: 'tool-call',\n                        toolCallType: 'function',\n                        toolCallId: toolCall.id ?? generateId(),\n                        toolName: toolCall.function.name,\n                        args: toolCall.function.arguments,\n                      });\n                      toolCall.hasFinished = true;\n                    }\n                  }\n\n                  continue;\n                }\n\n                // existing tool call, merge if not finished\n                const toolCall = toolCalls[index];\n\n                if (toolCall.hasFinished) {\n                  continue;\n                }\n\n                if (toolCallDelta.function?.arguments != null) {\n                  toolCall.function!.arguments +=\n                    toolCallDelta.function?.arguments ?? '';\n                }\n\n                // send delta\n                controller.enqueue({\n                  type: 'tool-call-delta',\n                  toolCallType: 'function',\n                  toolCallId: toolCall.id,\n                  toolName: toolCall.function.name,\n                  argsTextDelta: toolCallDelta.function.arguments ?? '',\n                });\n\n                // check if tool call is complete\n                if (\n                  toolCall.function?.name != null &&\n                  toolCall.function?.arguments != null &&\n                  isParsableJson(toolCall.function.arguments)\n                ) {\n                  controller.enqueue({\n                    type: 'tool-call',\n                    toolCallType: 'function',\n                    toolCallId: toolCall.id ?? generateId(),\n                    toolName: toolCall.function.name,\n                    args: toolCall.function.arguments,\n                  });\n                  toolCall.hasFinished = true;\n                }\n              }\n            }\n          },\n\n          flush(controller) {\n            controller.enqueue({\n              type: 'finish',\n              finishReason,\n              logprobs,\n              usage: {\n                promptTokens: usage.promptTokens ?? NaN,\n                completionTokens: usage.completionTokens ?? NaN,\n              },\n              ...(providerMetadata != null ? { providerMetadata } : {}),\n            });\n          },\n        }),\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      request: { body: JSON.stringify(body) },\n      warnings,\n    };\n  }\n}\n\nconst openaiTokenUsageSchema = z\n  .object({\n    prompt_tokens: z.number().nullish(),\n    completion_tokens: z.number().nullish(),\n    prompt_tokens_details: z\n      .object({\n        cached_tokens: z.number().nullish(),\n      })\n      .nullish(),\n    completion_tokens_details: z\n      .object({\n        reasoning_tokens: z.number().nullish(),\n        accepted_prediction_tokens: z.number().nullish(),\n        rejected_prediction_tokens: z.number().nullish(),\n      })\n      .nullish(),\n  })\n  .nullish();\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst openaiChatResponseSchema = z.object({\n  id: z.string().nullish(),\n  created: z.number().nullish(),\n  model: z.string().nullish(),\n  choices: z.array(\n    z.object({\n      message: z.object({\n        role: z.literal('assistant').nullish(),\n        content: z.string().nullish(),\n        function_call: z\n          .object({\n            arguments: z.string(),\n            name: z.string(),\n          })\n          .nullish(),\n        tool_calls: z\n          .array(\n            z.object({\n              id: z.string().nullish(),\n              type: z.literal('function'),\n              function: z.object({\n                name: z.string(),\n                arguments: z.string(),\n              }),\n            }),\n          )\n          .nullish(),\n      }),\n      index: z.number(),\n      logprobs: z\n        .object({\n          content: z\n            .array(\n              z.object({\n                token: z.string(),\n                logprob: z.number(),\n                top_logprobs: z.array(\n                  z.object({\n                    token: z.string(),\n                    logprob: z.number(),\n                  }),\n                ),\n              }),\n            )\n            .nullable(),\n        })\n        .nullish(),\n      finish_reason: z.string().nullish(),\n    }),\n  ),\n  usage: openaiTokenUsageSchema,\n});\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst openaiChatChunkSchema = z.union([\n  z.object({\n    id: z.string().nullish(),\n    created: z.number().nullish(),\n    model: z.string().nullish(),\n    choices: z.array(\n      z.object({\n        delta: z\n          .object({\n            role: z.enum(['assistant']).nullish(),\n            content: z.string().nullish(),\n            function_call: z\n              .object({\n                name: z.string().optional(),\n                arguments: z.string().optional(),\n              })\n              .nullish(),\n            tool_calls: z\n              .array(\n                z.object({\n                  index: z.number(),\n                  id: z.string().nullish(),\n                  type: z.literal('function').nullish(),\n                  function: z.object({\n                    name: z.string().nullish(),\n                    arguments: z.string().nullish(),\n                  }),\n                }),\n              )\n              .nullish(),\n          })\n          .nullish(),\n        logprobs: z\n          .object({\n            content: z\n              .array(\n                z.object({\n                  token: z.string(),\n                  logprob: z.number(),\n                  top_logprobs: z.array(\n                    z.object({\n                      token: z.string(),\n                      logprob: z.number(),\n                    }),\n                  ),\n                }),\n              )\n              .nullable(),\n          })\n          .nullish(),\n        finish_reason: z.string().nullish(),\n        index: z.number(),\n      }),\n    ),\n    usage: openaiTokenUsageSchema,\n  }),\n  openaiErrorDataSchema,\n]);\n\nfunction isReasoningModel(modelId: string) {\n  return modelId.startsWith('o');\n}\n\nfunction isAudioModel(modelId: string) {\n  return modelId.startsWith('gpt-4o-audio-preview');\n}\n\nfunction getSystemMessageMode(modelId: string) {\n  if (!isReasoningModel(modelId)) {\n    return 'system';\n  }\n\n  return (\n    reasoningModels[modelId as keyof typeof reasoningModels]\n      ?.systemMessageMode ?? 'developer'\n  );\n}\n\nconst reasoningModels = {\n  'o1-mini': {\n    systemMessageMode: 'remove',\n  },\n  'o1-mini-2024-09-12': {\n    systemMessageMode: 'remove',\n  },\n  'o1-preview': {\n    systemMessageMode: 'remove',\n  },\n  'o1-preview-2024-09-12': {\n    systemMessageMode: 'remove',\n  },\n  o3: {\n    systemMessageMode: 'developer',\n  },\n  'o3-2025-04-16': {\n    systemMessageMode: 'developer',\n  },\n  'o3-mini': {\n    systemMessageMode: 'developer',\n  },\n  'o3-mini-2025-01-31': {\n    systemMessageMode: 'developer',\n  },\n  'o4-mini': {\n    systemMessageMode: 'developer',\n  },\n  'o4-mini-2025-04-16': {\n    systemMessageMode: 'developer',\n  },\n} as const;\n", "import {\n  LanguageModelV1CallWarning,\n  LanguageModelV1Prompt,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport { convertUint8ArrayToBase64 } from '@ai-sdk/provider-utils';\nimport { OpenAIChatPrompt } from './openai-chat-prompt';\n\nexport function convertToOpenAIChatMessages({\n  prompt,\n  useLegacyFunctionCalling = false,\n  systemMessageMode = 'system',\n}: {\n  prompt: LanguageModelV1Prompt;\n  useLegacyFunctionCalling?: boolean;\n  systemMessageMode?: 'system' | 'developer' | 'remove';\n}): {\n  messages: OpenAIChatPrompt;\n  warnings: Array<LanguageModelV1CallWarning>;\n} {\n  const messages: OpenAIChatPrompt = [];\n  const warnings: Array<LanguageModelV1CallWarning> = [];\n\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case 'system': {\n        switch (systemMessageMode) {\n          case 'system': {\n            messages.push({ role: 'system', content });\n            break;\n          }\n          case 'developer': {\n            messages.push({ role: 'developer', content });\n            break;\n          }\n          case 'remove': {\n            warnings.push({\n              type: 'other',\n              message: 'system messages are removed for this model',\n            });\n            break;\n          }\n          default: {\n            const _exhaustiveCheck: never = systemMessageMode;\n            throw new Error(\n              `Unsupported system message mode: ${_exhaustiveCheck}`,\n            );\n          }\n        }\n        break;\n      }\n\n      case 'user': {\n        if (content.length === 1 && content[0].type === 'text') {\n          messages.push({ role: 'user', content: content[0].text });\n          break;\n        }\n\n        messages.push({\n          role: 'user',\n          content: content.map((part, index) => {\n            switch (part.type) {\n              case 'text': {\n                return { type: 'text', text: part.text };\n              }\n              case 'image': {\n                return {\n                  type: 'image_url',\n                  image_url: {\n                    url:\n                      part.image instanceof URL\n                        ? part.image.toString()\n                        : `data:${\n                            part.mimeType ?? 'image/jpeg'\n                          };base64,${convertUint8ArrayToBase64(part.image)}`,\n\n                    // OpenAI specific extension: image detail\n                    detail: part.providerMetadata?.openai?.imageDetail,\n                  },\n                };\n              }\n              case 'file': {\n                if (part.data instanceof URL) {\n                  throw new UnsupportedFunctionalityError({\n                    functionality:\n                      \"'File content parts with URL data' functionality not supported.\",\n                  });\n                }\n\n                switch (part.mimeType) {\n                  case 'audio/wav': {\n                    return {\n                      type: 'input_audio',\n                      input_audio: { data: part.data, format: 'wav' },\n                    };\n                  }\n                  case 'audio/mp3':\n                  case 'audio/mpeg': {\n                    return {\n                      type: 'input_audio',\n                      input_audio: { data: part.data, format: 'mp3' },\n                    };\n                  }\n                  case 'application/pdf': {\n                    return {\n                      type: 'file',\n                      file: {\n                        filename: part.filename ?? `part-${index}.pdf`,\n                        file_data: `data:application/pdf;base64,${part.data}`,\n                      },\n                    };\n                  }\n                  default: {\n                    throw new UnsupportedFunctionalityError({\n                      functionality: `File content part type ${part.mimeType} in user messages`,\n                    });\n                  }\n                }\n              }\n            }\n          }),\n        });\n\n        break;\n      }\n\n      case 'assistant': {\n        let text = '';\n        const toolCalls: Array<{\n          id: string;\n          type: 'function';\n          function: { name: string; arguments: string };\n        }> = [];\n\n        for (const part of content) {\n          switch (part.type) {\n            case 'text': {\n              text += part.text;\n              break;\n            }\n            case 'tool-call': {\n              toolCalls.push({\n                id: part.toolCallId,\n                type: 'function',\n                function: {\n                  name: part.toolName,\n                  arguments: JSON.stringify(part.args),\n                },\n              });\n              break;\n            }\n          }\n        }\n\n        if (useLegacyFunctionCalling) {\n          if (toolCalls.length > 1) {\n            throw new UnsupportedFunctionalityError({\n              functionality:\n                'useLegacyFunctionCalling with multiple tool calls in one message',\n            });\n          }\n\n          messages.push({\n            role: 'assistant',\n            content: text,\n            function_call:\n              toolCalls.length > 0 ? toolCalls[0].function : undefined,\n          });\n        } else {\n          messages.push({\n            role: 'assistant',\n            content: text,\n            tool_calls: toolCalls.length > 0 ? toolCalls : undefined,\n          });\n        }\n\n        break;\n      }\n\n      case 'tool': {\n        for (const toolResponse of content) {\n          if (useLegacyFunctionCalling) {\n            messages.push({\n              role: 'function',\n              name: toolResponse.toolName,\n              content: JSON.stringify(toolResponse.result),\n            });\n          } else {\n            messages.push({\n              role: 'tool',\n              tool_call_id: toolResponse.toolCallId,\n              content: JSON.stringify(toolResponse.result),\n            });\n          }\n        }\n        break;\n      }\n\n      default: {\n        const _exhaustiveCheck: never = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  return { messages, warnings };\n}\n", "import { LanguageModelV1LogProbs } from '@ai-sdk/provider';\n\ntype OpenAIChatLogProbs = {\n  content:\n    | {\n        token: string;\n        logprob: number;\n        top_logprobs:\n          | {\n              token: string;\n              logprob: number;\n            }[]\n          | null;\n      }[]\n    | null;\n};\n\nexport function mapOpenAIChatLogProbsOutput(\n  logprobs: OpenAIChatLogProbs | null | undefined,\n): LanguageModelV1LogProbs | undefined {\n  return (\n    logprobs?.content?.map(({ token, logprob, top_logprobs }) => ({\n      token,\n      logprob,\n      topLogprobs: top_logprobs\n        ? top_logprobs.map(({ token, logprob }) => ({\n            token,\n            logprob,\n          }))\n        : [],\n    })) ?? undefined\n  );\n}\n", "import { LanguageModelV1FinishReason } from '@ai-sdk/provider';\n\nexport function mapOpenAIFinishReason(\n  finishReason: string | null | undefined,\n): LanguageModelV1FinishReason {\n  switch (finishReason) {\n    case 'stop':\n      return 'stop';\n    case 'length':\n      return 'length';\n    case 'content_filter':\n      return 'content-filter';\n    case 'function_call':\n    case 'tool_calls':\n      return 'tool-calls';\n    default:\n      return 'unknown';\n  }\n}\n", "import { z } from 'zod';\nimport { createJsonErrorResponseHandler } from '@ai-sdk/provider-utils';\n\nexport const openaiErrorDataSchema = z.object({\n  error: z.object({\n    message: z.string(),\n\n    // The additional information below is handled loosely to support\n    // OpenAI-compatible providers that have slightly different error\n    // responses:\n    type: z.string().nullish(),\n    param: z.any().nullish(),\n    code: z.union([z.string(), z.number()]).nullish(),\n  }),\n});\n\nexport type OpenAIErrorData = z.infer<typeof openaiErrorDataSchema>;\n\nexport const openaiFailedResponseHandler = createJsonErrorResponseHandler({\n  errorSchema: openaiErrorDataSchema,\n  errorToMessage: data => data.error.message,\n});\n", "export function getResponseMetadata({\n  id,\n  model,\n  created,\n}: {\n  id?: string | undefined | null;\n  created?: number | undefined | null;\n  model?: string | undefined | null;\n}) {\n  return {\n    id: id ?? undefined,\n    modelId: model ?? undefined,\n    timestamp: created != null ? new Date(created * 1000) : undefined,\n  };\n}\n", "import {\n  JSONSchema7,\n  LanguageModelV1,\n  LanguageModelV1CallWarning,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\n\nexport function prepareTools({\n  mode,\n  useLegacyFunctionCalling = false,\n  structuredOutputs,\n}: {\n  mode: Parameters<LanguageModelV1['doGenerate']>[0]['mode'] & {\n    type: 'regular';\n  };\n  useLegacyFunctionCalling: boolean | undefined;\n  structuredOutputs: boolean;\n}): {\n  tools?: {\n    type: 'function';\n    function: {\n      name: string;\n      description: string | undefined;\n      parameters: JSONSchema7;\n      strict?: boolean;\n    };\n  }[];\n  tool_choice?:\n    | 'auto'\n    | 'none'\n    | 'required'\n    | { type: 'function'; function: { name: string } };\n\n  // legacy support\n  functions?: {\n    name: string;\n    description: string | undefined;\n    parameters: JSONSchema7;\n  }[];\n  function_call?: { name: string };\n  toolWarnings: Array<LanguageModelV1CallWarning>;\n} {\n  // when the tools array is empty, change it to undefined to prevent errors:\n  const tools = mode.tools?.length ? mode.tools : undefined;\n\n  const toolWarnings: LanguageModelV1CallWarning[] = [];\n\n  if (tools == null) {\n    return { tools: undefined, tool_choice: undefined, toolWarnings };\n  }\n\n  const toolChoice = mode.toolChoice;\n\n  if (useLegacyFunctionCalling) {\n    const openaiFunctions: Array<{\n      name: string;\n      description: string | undefined;\n      parameters: JSONSchema7;\n    }> = [];\n\n    for (const tool of tools) {\n      if (tool.type === 'provider-defined') {\n        toolWarnings.push({ type: 'unsupported-tool', tool });\n      } else {\n        openaiFunctions.push({\n          name: tool.name,\n          description: tool.description,\n          parameters: tool.parameters,\n        });\n      }\n    }\n\n    if (toolChoice == null) {\n      return {\n        functions: openaiFunctions,\n        function_call: undefined,\n        toolWarnings,\n      };\n    }\n\n    const type = toolChoice.type;\n\n    switch (type) {\n      case 'auto':\n      case 'none':\n      case undefined:\n        return {\n          functions: openaiFunctions,\n          function_call: undefined,\n          toolWarnings,\n        };\n      case 'required':\n        throw new UnsupportedFunctionalityError({\n          functionality: 'useLegacyFunctionCalling and toolChoice: required',\n        });\n      default:\n        return {\n          functions: openaiFunctions,\n          function_call: { name: toolChoice.toolName },\n          toolWarnings,\n        };\n    }\n  }\n\n  const openaiTools: Array<{\n    type: 'function';\n    function: {\n      name: string;\n      description: string | undefined;\n      parameters: JSONSchema7;\n      strict: boolean | undefined;\n    };\n  }> = [];\n\n  for (const tool of tools) {\n    if (tool.type === 'provider-defined') {\n      toolWarnings.push({ type: 'unsupported-tool', tool });\n    } else {\n      openaiTools.push({\n        type: 'function',\n        function: {\n          name: tool.name,\n          description: tool.description,\n          parameters: tool.parameters,\n          strict: structuredOutputs ? true : undefined,\n        },\n      });\n    }\n  }\n\n  if (toolChoice == null) {\n    return { tools: openaiTools, tool_choice: undefined, toolWarnings };\n  }\n\n  const type = toolChoice.type;\n\n  switch (type) {\n    case 'auto':\n    case 'none':\n    case 'required':\n      return { tools: openaiTools, tool_choice: type, toolWarnings };\n    case 'tool':\n      return {\n        tools: openaiTools,\n        tool_choice: {\n          type: 'function',\n          function: {\n            name: toolChoice.toolName,\n          },\n        },\n        toolWarnings,\n      };\n    default: {\n      const _exhaustiveCheck: never = type;\n      throw new UnsupportedFunctionalityError({\n        functionality: `Unsupported tool choice type: ${_exhaustiveCheck}`,\n      });\n    }\n  }\n}\n", "import {\n  LanguageModelV1,\n  LanguageModelV1Call<PERSON>arning,\n  LanguageModelV1FinishReason,\n  LanguageModelV1LogProbs,\n  LanguageModelV1StreamPart,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport {\n  FetchFunction,\n  ParseResult,\n  combineHeaders,\n  createEventSourceResponseHandler,\n  createJsonResponseHandler,\n  postJsonToApi,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { convertToOpenAICompletionPrompt } from './convert-to-openai-completion-prompt';\nimport { mapOpenAICompletionLogProbs } from './map-openai-completion-logprobs';\nimport { mapOpenAIFinishReason } from './map-openai-finish-reason';\nimport {\n  OpenAICompletionModelId,\n  OpenAICompletionSettings,\n} from './openai-completion-settings';\nimport {\n  openaiErrorDataSchema,\n  openaiFailedResponseHandler,\n} from './openai-error';\nimport { getResponseMetadata } from './get-response-metadata';\n\ntype OpenAICompletionConfig = {\n  provider: string;\n  compatibility: 'strict' | 'compatible';\n  headers: () => Record<string, string | undefined>;\n  url: (options: { modelId: string; path: string }) => string;\n  fetch?: FetchFunction;\n};\n\nexport class OpenAICompletionLanguageModel implements LanguageModelV1 {\n  readonly specificationVersion = 'v1';\n  readonly defaultObjectGenerationMode = undefined;\n\n  readonly modelId: OpenAICompletionModelId;\n  readonly settings: OpenAICompletionSettings;\n\n  private readonly config: OpenAICompletionConfig;\n\n  constructor(\n    modelId: OpenAICompletionModelId,\n    settings: OpenAICompletionSettings,\n    config: OpenAICompletionConfig,\n  ) {\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  private getArgs({\n    mode,\n    inputFormat,\n    prompt,\n    maxTokens,\n    temperature,\n    topP,\n    topK,\n    frequencyPenalty,\n    presencePenalty,\n    stopSequences: userStopSequences,\n    responseFormat,\n    seed,\n  }: Parameters<LanguageModelV1['doGenerate']>[0]) {\n    const type = mode.type;\n\n    const warnings: LanguageModelV1CallWarning[] = [];\n\n    if (topK != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'topK',\n      });\n    }\n\n    if (responseFormat != null && responseFormat.type !== 'text') {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'responseFormat',\n        details: 'JSON response format is not supported.',\n      });\n    }\n\n    const { prompt: completionPrompt, stopSequences } =\n      convertToOpenAICompletionPrompt({ prompt, inputFormat });\n\n    const stop = [...(stopSequences ?? []), ...(userStopSequences ?? [])];\n\n    const baseArgs = {\n      // model id:\n      model: this.modelId,\n\n      // model specific settings:\n      echo: this.settings.echo,\n      logit_bias: this.settings.logitBias,\n      logprobs:\n        typeof this.settings.logprobs === 'number'\n          ? this.settings.logprobs\n          : typeof this.settings.logprobs === 'boolean'\n            ? this.settings.logprobs\n              ? 0\n              : undefined\n            : undefined,\n      suffix: this.settings.suffix,\n      user: this.settings.user,\n\n      // standardized settings:\n      max_tokens: maxTokens,\n      temperature,\n      top_p: topP,\n      frequency_penalty: frequencyPenalty,\n      presence_penalty: presencePenalty,\n      seed,\n\n      // prompt:\n      prompt: completionPrompt,\n\n      // stop sequences:\n      stop: stop.length > 0 ? stop : undefined,\n    };\n\n    switch (type) {\n      case 'regular': {\n        if (mode.tools?.length) {\n          throw new UnsupportedFunctionalityError({\n            functionality: 'tools',\n          });\n        }\n\n        if (mode.toolChoice) {\n          throw new UnsupportedFunctionalityError({\n            functionality: 'toolChoice',\n          });\n        }\n\n        return { args: baseArgs, warnings };\n      }\n\n      case 'object-json': {\n        throw new UnsupportedFunctionalityError({\n          functionality: 'object-json mode',\n        });\n      }\n\n      case 'object-tool': {\n        throw new UnsupportedFunctionalityError({\n          functionality: 'object-tool mode',\n        });\n      }\n\n      default: {\n        const _exhaustiveCheck: never = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  async doGenerate(\n    options: Parameters<LanguageModelV1['doGenerate']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doGenerate']>>> {\n    const { args, warnings } = this.getArgs(options);\n\n    const {\n      responseHeaders,\n      value: response,\n      rawValue: rawResponse,\n    } = await postJsonToApi({\n      url: this.config.url({\n        path: '/completions',\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body: args,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        openaiCompletionResponseSchema,\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { prompt: rawPrompt, ...rawSettings } = args;\n    const choice = response.choices[0];\n\n    return {\n      text: choice.text,\n      usage: {\n        promptTokens: response.usage.prompt_tokens,\n        completionTokens: response.usage.completion_tokens,\n      },\n      finishReason: mapOpenAIFinishReason(choice.finish_reason),\n      logprobs: mapOpenAICompletionLogProbs(choice.logprobs),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders, body: rawResponse },\n      response: getResponseMetadata(response),\n      warnings,\n      request: { body: JSON.stringify(args) },\n    };\n  }\n\n  async doStream(\n    options: Parameters<LanguageModelV1['doStream']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doStream']>>> {\n    const { args, warnings } = this.getArgs(options);\n\n    const body = {\n      ...args,\n      stream: true,\n\n      // only include stream_options when in strict compatibility mode:\n      stream_options:\n        this.config.compatibility === 'strict'\n          ? { include_usage: true }\n          : undefined,\n    };\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: this.config.url({\n        path: '/completions',\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createEventSourceResponseHandler(\n        openaiCompletionChunkSchema,\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { prompt: rawPrompt, ...rawSettings } = args;\n\n    let finishReason: LanguageModelV1FinishReason = 'unknown';\n    let usage: { promptTokens: number; completionTokens: number } = {\n      promptTokens: Number.NaN,\n      completionTokens: Number.NaN,\n    };\n    let logprobs: LanguageModelV1LogProbs;\n    let isFirstChunk = true;\n\n    return {\n      stream: response.pipeThrough(\n        new TransformStream<\n          ParseResult<z.infer<typeof openaiCompletionChunkSchema>>,\n          LanguageModelV1StreamPart\n        >({\n          transform(chunk, controller) {\n            // handle failed chunk parsing / validation:\n            if (!chunk.success) {\n              finishReason = 'error';\n              controller.enqueue({ type: 'error', error: chunk.error });\n              return;\n            }\n\n            const value = chunk.value;\n\n            // handle error chunks:\n            if ('error' in value) {\n              finishReason = 'error';\n              controller.enqueue({ type: 'error', error: value.error });\n              return;\n            }\n\n            if (isFirstChunk) {\n              isFirstChunk = false;\n\n              controller.enqueue({\n                type: 'response-metadata',\n                ...getResponseMetadata(value),\n              });\n            }\n\n            if (value.usage != null) {\n              usage = {\n                promptTokens: value.usage.prompt_tokens,\n                completionTokens: value.usage.completion_tokens,\n              };\n            }\n\n            const choice = value.choices[0];\n\n            if (choice?.finish_reason != null) {\n              finishReason = mapOpenAIFinishReason(choice.finish_reason);\n            }\n\n            if (choice?.text != null) {\n              controller.enqueue({\n                type: 'text-delta',\n                textDelta: choice.text,\n              });\n            }\n\n            const mappedLogprobs = mapOpenAICompletionLogProbs(\n              choice?.logprobs,\n            );\n            if (mappedLogprobs?.length) {\n              if (logprobs === undefined) logprobs = [];\n              logprobs.push(...mappedLogprobs);\n            }\n          },\n\n          flush(controller) {\n            controller.enqueue({\n              type: 'finish',\n              finishReason,\n              logprobs,\n              usage,\n            });\n          },\n        }),\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings,\n      request: { body: JSON.stringify(body) },\n    };\n  }\n}\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst openaiCompletionResponseSchema = z.object({\n  id: z.string().nullish(),\n  created: z.number().nullish(),\n  model: z.string().nullish(),\n  choices: z.array(\n    z.object({\n      text: z.string(),\n      finish_reason: z.string(),\n      logprobs: z\n        .object({\n          tokens: z.array(z.string()),\n          token_logprobs: z.array(z.number()),\n          top_logprobs: z.array(z.record(z.string(), z.number())).nullable(),\n        })\n        .nullish(),\n    }),\n  ),\n  usage: z.object({\n    prompt_tokens: z.number(),\n    completion_tokens: z.number(),\n  }),\n});\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst openaiCompletionChunkSchema = z.union([\n  z.object({\n    id: z.string().nullish(),\n    created: z.number().nullish(),\n    model: z.string().nullish(),\n    choices: z.array(\n      z.object({\n        text: z.string(),\n        finish_reason: z.string().nullish(),\n        index: z.number(),\n        logprobs: z\n          .object({\n            tokens: z.array(z.string()),\n            token_logprobs: z.array(z.number()),\n            top_logprobs: z.array(z.record(z.string(), z.number())).nullable(),\n          })\n          .nullish(),\n      }),\n    ),\n    usage: z\n      .object({\n        prompt_tokens: z.number(),\n        completion_tokens: z.number(),\n      })\n      .nullish(),\n  }),\n  openaiErrorDataSchema,\n]);\n", "import {\n  InvalidPromptError,\n  LanguageModelV1Prompt,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\n\nexport function convertToOpenAICompletionPrompt({\n  prompt,\n  inputFormat,\n  user = 'user',\n  assistant = 'assistant',\n}: {\n  prompt: LanguageModelV1Prompt;\n  inputFormat: 'prompt' | 'messages';\n  user?: string;\n  assistant?: string;\n}): {\n  prompt: string;\n  stopSequences?: string[];\n} {\n  // When the user supplied a prompt input, we don't transform it:\n  if (\n    inputFormat === 'prompt' &&\n    prompt.length === 1 &&\n    prompt[0].role === 'user' &&\n    prompt[0].content.length === 1 &&\n    prompt[0].content[0].type === 'text'\n  ) {\n    return { prompt: prompt[0].content[0].text };\n  }\n\n  // otherwise transform to a chat message format:\n  let text = '';\n\n  // if first message is a system message, add it to the text:\n  if (prompt[0].role === 'system') {\n    text += `${prompt[0].content}\\n\\n`;\n    prompt = prompt.slice(1);\n  }\n\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case 'system': {\n        throw new InvalidPromptError({\n          message: 'Unexpected system message in prompt: ${content}',\n          prompt,\n        });\n      }\n\n      case 'user': {\n        const userMessage = content\n          .map(part => {\n            switch (part.type) {\n              case 'text': {\n                return part.text;\n              }\n              case 'image': {\n                throw new UnsupportedFunctionalityError({\n                  functionality: 'images',\n                });\n              }\n            }\n          })\n          .join('');\n\n        text += `${user}:\\n${userMessage}\\n\\n`;\n        break;\n      }\n\n      case 'assistant': {\n        const assistantMessage = content\n          .map(part => {\n            switch (part.type) {\n              case 'text': {\n                return part.text;\n              }\n              case 'tool-call': {\n                throw new UnsupportedFunctionalityError({\n                  functionality: 'tool-call messages',\n                });\n              }\n            }\n          })\n          .join('');\n\n        text += `${assistant}:\\n${assistantMessage}\\n\\n`;\n        break;\n      }\n\n      case 'tool': {\n        throw new UnsupportedFunctionalityError({\n          functionality: 'tool messages',\n        });\n      }\n\n      default: {\n        const _exhaustiveCheck: never = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  // Assistant message prefix:\n  text += `${assistant}:\\n`;\n\n  return {\n    prompt: text,\n    stopSequences: [`\\n${user}:`],\n  };\n}\n", "import { LanguageModelV1LogProbs } from '@ai-sdk/provider';\n\ntype OpenAICompletionLogProps = {\n  tokens: string[];\n  token_logprobs: number[];\n  top_logprobs: Record<string, number>[] | null;\n};\n\nexport function mapOpenAICompletionLogProbs(\n  logprobs: OpenAICompletionLogProps | null | undefined,\n): LanguageModelV1LogProbs | undefined {\n  return logprobs?.tokens.map((token, index) => ({\n    token,\n    logprob: logprobs.token_logprobs[index],\n    topLogprobs: logprobs.top_logprobs\n      ? Object.entries(logprobs.top_logprobs[index]).map(\n          ([token, logprob]) => ({\n            token,\n            logprob,\n          }),\n        )\n      : [],\n  }));\n}\n", "import {\n  EmbeddingModelV1,\n  TooManyEmbeddingValuesForCallError,\n} from '@ai-sdk/provider';\nimport {\n  combineHeaders,\n  createJsonResponseHandler,\n  postJsonToApi,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { OpenAIConfig } from './openai-config';\nimport {\n  OpenAIEmbeddingModelId,\n  OpenAIEmbeddingSettings,\n} from './openai-embedding-settings';\nimport { openaiFailedResponseHandler } from './openai-error';\n\nexport class OpenAIEmbeddingModel implements EmbeddingModelV1<string> {\n  readonly specificationVersion = 'v1';\n  readonly modelId: OpenAIEmbeddingModelId;\n\n  private readonly config: OpenAIConfig;\n  private readonly settings: OpenAIEmbeddingSettings;\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  get maxEmbeddingsPerCall(): number {\n    return this.settings.maxEmbeddingsPerCall ?? 2048;\n  }\n\n  get supportsParallelCalls(): boolean {\n    return this.settings.supportsParallelCalls ?? true;\n  }\n\n  constructor(\n    modelId: OpenAIEmbeddingModelId,\n    settings: OpenAIEmbeddingSettings,\n    config: OpenAIConfig,\n  ) {\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n\n  async doEmbed({\n    values,\n    headers,\n    abortSignal,\n  }: Parameters<EmbeddingModelV1<string>['doEmbed']>[0]): Promise<\n    Awaited<ReturnType<EmbeddingModelV1<string>['doEmbed']>>\n  > {\n    if (values.length > this.maxEmbeddingsPerCall) {\n      throw new TooManyEmbeddingValuesForCallError({\n        provider: this.provider,\n        modelId: this.modelId,\n        maxEmbeddingsPerCall: this.maxEmbeddingsPerCall,\n        values,\n      });\n    }\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: this.config.url({\n        path: '/embeddings',\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), headers),\n      body: {\n        model: this.modelId,\n        input: values,\n        encoding_format: 'float',\n        dimensions: this.settings.dimensions,\n        user: this.settings.user,\n      },\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        openaiTextEmbeddingResponseSchema,\n      ),\n      abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    return {\n      embeddings: response.data.map(item => item.embedding),\n      usage: response.usage\n        ? { tokens: response.usage.prompt_tokens }\n        : undefined,\n      rawResponse: { headers: responseHeaders },\n    };\n  }\n}\n\n// minimal version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst openaiTextEmbeddingResponseSchema = z.object({\n  data: z.array(z.object({ embedding: z.array(z.number()) })),\n  usage: z.object({ prompt_tokens: z.number() }).nullish(),\n});\n", "import { ImageModelV1, ImageModelV1CallWarning } from '@ai-sdk/provider';\nimport {\n  combineHeaders,\n  createJsonResponseHandler,\n  postJsonToApi,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { OpenAIConfig } from './openai-config';\nimport { openaiFailedResponseHandler } from './openai-error';\nimport {\n  OpenAIImageModelId,\n  OpenAIImageSettings,\n  modelMaxImagesPerCall,\n  hasDefaultResponseFormat,\n} from './openai-image-settings';\n\ninterface OpenAIImageModelConfig extends OpenAIConfig {\n  _internal?: {\n    currentDate?: () => Date;\n  };\n}\n\nexport class OpenAIImageModel implements ImageModelV1 {\n  readonly specificationVersion = 'v1';\n\n  get maxImagesPerCall(): number {\n    return (\n      this.settings.maxImagesPerCall ?? modelMaxImagesPerCall[this.modelId] ?? 1\n    );\n  }\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  constructor(\n    readonly modelId: OpenAIImageModelId,\n    private readonly settings: OpenAIImageSettings,\n    private readonly config: OpenAIImageModelConfig,\n  ) {}\n\n  async doGenerate({\n    prompt,\n    n,\n    size,\n    aspectRatio,\n    seed,\n    providerOptions,\n    headers,\n    abortSignal,\n  }: Parameters<ImageModelV1['doGenerate']>[0]): Promise<\n    Awaited<ReturnType<ImageModelV1['doGenerate']>>\n  > {\n    const warnings: Array<ImageModelV1CallWarning> = [];\n\n    if (aspectRatio != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'aspectRatio',\n        details:\n          'This model does not support aspect ratio. Use `size` instead.',\n      });\n    }\n\n    if (seed != null) {\n      warnings.push({ type: 'unsupported-setting', setting: 'seed' });\n    }\n\n    const currentDate = this.config._internal?.currentDate?.() ?? new Date();\n    const { value: response, responseHeaders } = await postJsonToApi({\n      url: this.config.url({\n        path: '/images/generations',\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), headers),\n      body: {\n        model: this.modelId,\n        prompt,\n        n,\n        size,\n        ...(providerOptions.openai ?? {}),\n        ...(!hasDefaultResponseFormat.has(this.modelId)\n          ? { response_format: 'b64_json' }\n          : {}),\n      },\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        openaiImageResponseSchema,\n      ),\n      abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    return {\n      images: response.data.map(item => item.b64_json),\n      warnings,\n      response: {\n        timestamp: currentDate,\n        modelId: this.modelId,\n        headers: responseHeaders,\n      },\n    };\n  }\n}\n\n// minimal version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst openaiImageResponseSchema = z.object({\n  data: z.array(z.object({ b64_json: z.string() })),\n});\n", "export type OpenAIImageModelId =\n  | 'gpt-image-1'\n  | 'dall-e-3'\n  | 'dall-e-2'\n  | (string & {});\n\n// https://platform.openai.com/docs/guides/images\nexport const modelMaxImagesPerCall: Record<OpenAIImageModelId, number> = {\n  'dall-e-3': 1,\n  'dall-e-2': 10,\n  'gpt-image-1': 10,\n};\n\nexport const hasDefaultResponseFormat = new Set(['gpt-image-1']);\n\nexport interface OpenAIImageSettings {\n  /**\nOverride the maximum number of images per call (default is dependent on the\nmodel, or 1 for an unknown model).\n   */\n  maxImagesPerCall?: number;\n}\n", "import {\n  TranscriptionModelV1,\n  TranscriptionModelV1CallOptions,\n  TranscriptionModelV1CallWarning,\n} from '@ai-sdk/provider';\nimport {\n  combineHeaders,\n  convertBase64ToUint8Array,\n  createJsonResponseHandler,\n  parseProviderOptions,\n  postFormDataToApi,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { OpenAIConfig } from './openai-config';\nimport { openaiFailedResponseHandler } from './openai-error';\nimport {\n  OpenAITranscriptionModelId,\n  OpenAITranscriptionModelOptions,\n} from './openai-transcription-settings';\n\n// https://platform.openai.com/docs/api-reference/audio/createTranscription\nconst openAIProviderOptionsSchema = z.object({\n  include: z.array(z.string()).nullish(),\n  language: z.string().nullish(),\n  prompt: z.string().nullish(),\n  temperature: z.number().min(0).max(1).nullish().default(0),\n  timestampGranularities: z\n    .array(z.enum(['word', 'segment']))\n    .nullish()\n    .default(['segment']),\n});\n\nexport type OpenAITranscriptionCallOptions = Omit<\n  TranscriptionModelV1CallOptions,\n  'providerOptions'\n> & {\n  providerOptions?: {\n    openai?: z.infer<typeof openAIProviderOptionsSchema>;\n  };\n};\n\ninterface OpenAITranscriptionModelConfig extends OpenAIConfig {\n  _internal?: {\n    currentDate?: () => Date;\n  };\n}\n\n// https://platform.openai.com/docs/guides/speech-to-text#supported-languages\nconst languageMap = {\n  afrikaans: 'af',\n  arabic: 'ar',\n  armenian: 'hy',\n  azerbaijani: 'az',\n  belarusian: 'be',\n  bosnian: 'bs',\n  bulgarian: 'bg',\n  catalan: 'ca',\n  chinese: 'zh',\n  croatian: 'hr',\n  czech: 'cs',\n  danish: 'da',\n  dutch: 'nl',\n  english: 'en',\n  estonian: 'et',\n  finnish: 'fi',\n  french: 'fr',\n  galician: 'gl',\n  german: 'de',\n  greek: 'el',\n  hebrew: 'he',\n  hindi: 'hi',\n  hungarian: 'hu',\n  icelandic: 'is',\n  indonesian: 'id',\n  italian: 'it',\n  japanese: 'ja',\n  kannada: 'kn',\n  kazakh: 'kk',\n  korean: 'ko',\n  latvian: 'lv',\n  lithuanian: 'lt',\n  macedonian: 'mk',\n  malay: 'ms',\n  marathi: 'mr',\n  maori: 'mi',\n  nepali: 'ne',\n  norwegian: 'no',\n  persian: 'fa',\n  polish: 'pl',\n  portuguese: 'pt',\n  romanian: 'ro',\n  russian: 'ru',\n  serbian: 'sr',\n  slovak: 'sk',\n  slovenian: 'sl',\n  spanish: 'es',\n  swahili: 'sw',\n  swedish: 'sv',\n  tagalog: 'tl',\n  tamil: 'ta',\n  thai: 'th',\n  turkish: 'tr',\n  ukrainian: 'uk',\n  urdu: 'ur',\n  vietnamese: 'vi',\n  welsh: 'cy',\n};\n\nexport class OpenAITranscriptionModel implements TranscriptionModelV1 {\n  readonly specificationVersion = 'v1';\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  constructor(\n    readonly modelId: OpenAITranscriptionModelId,\n    private readonly config: OpenAITranscriptionModelConfig,\n  ) {}\n\n  private getArgs({\n    audio,\n    mediaType,\n    providerOptions,\n  }: OpenAITranscriptionCallOptions) {\n    const warnings: TranscriptionModelV1CallWarning[] = [];\n\n    // Parse provider options\n    const openAIOptions = parseProviderOptions({\n      provider: 'openai',\n      providerOptions,\n      schema: openAIProviderOptionsSchema,\n    });\n\n    // Create form data with base fields\n    const formData = new FormData();\n    const blob =\n      audio instanceof Uint8Array\n        ? new Blob([audio])\n        : new Blob([convertBase64ToUint8Array(audio)]);\n\n    formData.append('model', this.modelId);\n    formData.append('file', new File([blob], 'audio', { type: mediaType }));\n\n    // Add provider-specific options\n    if (openAIOptions) {\n      const transcriptionModelOptions: OpenAITranscriptionModelOptions = {\n        include: openAIOptions.include ?? undefined,\n        language: openAIOptions.language ?? undefined,\n        prompt: openAIOptions.prompt ?? undefined,\n        temperature: openAIOptions.temperature ?? undefined,\n        timestamp_granularities:\n          openAIOptions.timestampGranularities ?? undefined,\n      };\n\n      for (const key in transcriptionModelOptions) {\n        const value =\n          transcriptionModelOptions[\n            key as keyof OpenAITranscriptionModelOptions\n          ];\n        if (value !== undefined) {\n          formData.append(key, String(value));\n        }\n      }\n    }\n\n    return {\n      formData,\n      warnings,\n    };\n  }\n\n  async doGenerate(\n    options: OpenAITranscriptionCallOptions,\n  ): Promise<Awaited<ReturnType<TranscriptionModelV1['doGenerate']>>> {\n    const currentDate = this.config._internal?.currentDate?.() ?? new Date();\n    const { formData, warnings } = this.getArgs(options);\n\n    const {\n      value: response,\n      responseHeaders,\n      rawValue: rawResponse,\n    } = await postFormDataToApi({\n      url: this.config.url({\n        path: '/audio/transcriptions',\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      formData,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        openaiTranscriptionResponseSchema,\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const language =\n      response.language != null && response.language in languageMap\n        ? languageMap[response.language as keyof typeof languageMap]\n        : undefined;\n\n    return {\n      text: response.text,\n      segments:\n        response.words?.map(word => ({\n          text: word.word,\n          startSecond: word.start,\n          endSecond: word.end,\n        })) ?? [],\n      language,\n      durationInSeconds: response.duration ?? undefined,\n      warnings,\n      response: {\n        timestamp: currentDate,\n        modelId: this.modelId,\n        headers: responseHeaders,\n        body: rawResponse,\n      },\n    };\n  }\n}\n\nconst openaiTranscriptionResponseSchema = z.object({\n  text: z.string(),\n  language: z.string().nullish(),\n  duration: z.number().nullish(),\n  words: z\n    .array(\n      z.object({\n        word: z.string(),\n        start: z.number(),\n        end: z.number(),\n      }),\n    )\n    .nullish(),\n});\n", "import {\n  API<PERSON>all<PERSON><PERSON><PERSON>,\n  LanguageModelV1,\n  LanguageModelV1CallWarning,\n  LanguageModelV1FinishReason,\n  LanguageModelV1StreamPart,\n} from '@ai-sdk/provider';\nimport {\n  combineHeaders,\n  createEventSourceResponseHandler,\n  createJsonResponseHandler,\n  generateId,\n  parse<PERSON>roviderOptions,\n  ParseResult,\n  postJsonToApi,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { OpenAIConfig } from '../openai-config';\nimport { openaiFailedResponseHandler } from '../openai-error';\nimport { convertToOpenAIResponsesMessages } from './convert-to-openai-responses-messages';\nimport { mapOpenAIResponseFinishReason } from './map-openai-responses-finish-reason';\nimport { prepareResponsesTools } from './openai-responses-prepare-tools';\nimport { OpenAIResponsesModelId } from './openai-responses-settings';\n\nexport class OpenAIResponsesLanguageModel implements LanguageModelV1 {\n  readonly specificationVersion = 'v1';\n  readonly defaultObjectGenerationMode = 'json';\n  readonly supportsStructuredOutputs = true;\n\n  readonly modelId: OpenAIResponsesModelId;\n\n  private readonly config: OpenAIConfig;\n\n  constructor(modelId: OpenAIResponsesModelId, config: OpenAIConfig) {\n    this.modelId = modelId;\n    this.config = config;\n  }\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  private getArgs({\n    mode,\n    maxTokens,\n    temperature,\n    stopSequences,\n    topP,\n    topK,\n    presencePenalty,\n    frequencyPenalty,\n    seed,\n    prompt,\n    providerMetadata,\n    responseFormat,\n  }: Parameters<LanguageModelV1['doGenerate']>[0]) {\n    const warnings: LanguageModelV1CallWarning[] = [];\n    const modelConfig = getResponsesModelConfig(this.modelId);\n    const type = mode.type;\n\n    if (topK != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'topK',\n      });\n    }\n\n    if (seed != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'seed',\n      });\n    }\n\n    if (presencePenalty != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'presencePenalty',\n      });\n    }\n\n    if (frequencyPenalty != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'frequencyPenalty',\n      });\n    }\n\n    if (stopSequences != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'stopSequences',\n      });\n    }\n\n    const { messages, warnings: messageWarnings } =\n      convertToOpenAIResponsesMessages({\n        prompt,\n        systemMessageMode: modelConfig.systemMessageMode,\n      });\n\n    warnings.push(...messageWarnings);\n\n    const openaiOptions = parseProviderOptions({\n      provider: 'openai',\n      providerOptions: providerMetadata,\n      schema: openaiResponsesProviderOptionsSchema,\n    });\n\n    const isStrict = openaiOptions?.strictSchemas ?? true;\n\n    const baseArgs = {\n      model: this.modelId,\n      input: messages,\n      temperature,\n      top_p: topP,\n      max_output_tokens: maxTokens,\n\n      ...(responseFormat?.type === 'json' && {\n        text: {\n          format:\n            responseFormat.schema != null\n              ? {\n                  type: 'json_schema',\n                  strict: isStrict,\n                  name: responseFormat.name ?? 'response',\n                  description: responseFormat.description,\n                  schema: responseFormat.schema,\n                }\n              : { type: 'json_object' },\n        },\n      }),\n\n      // provider options:\n      metadata: openaiOptions?.metadata,\n      parallel_tool_calls: openaiOptions?.parallelToolCalls,\n      previous_response_id: openaiOptions?.previousResponseId,\n      store: openaiOptions?.store,\n      user: openaiOptions?.user,\n      instructions: openaiOptions?.instructions,\n\n      // model-specific settings:\n      ...(modelConfig.isReasoningModel &&\n        (openaiOptions?.reasoningEffort != null ||\n          openaiOptions?.reasoningSummary != null) && {\n          reasoning: {\n            ...(openaiOptions?.reasoningEffort != null && {\n              effort: openaiOptions.reasoningEffort,\n            }),\n            ...(openaiOptions?.reasoningSummary != null && {\n              summary: openaiOptions.reasoningSummary,\n            }),\n          },\n        }),\n      ...(modelConfig.requiredAutoTruncation && {\n        truncation: 'auto',\n      }),\n    };\n\n    if (modelConfig.isReasoningModel) {\n      // remove unsupported settings for reasoning models\n      // see https://platform.openai.com/docs/guides/reasoning#limitations\n      if (baseArgs.temperature != null) {\n        baseArgs.temperature = undefined;\n        warnings.push({\n          type: 'unsupported-setting',\n          setting: 'temperature',\n          details: 'temperature is not supported for reasoning models',\n        });\n      }\n\n      if (baseArgs.top_p != null) {\n        baseArgs.top_p = undefined;\n        warnings.push({\n          type: 'unsupported-setting',\n          setting: 'topP',\n          details: 'topP is not supported for reasoning models',\n        });\n      }\n    }\n\n    switch (type) {\n      case 'regular': {\n        const { tools, tool_choice, toolWarnings } = prepareResponsesTools({\n          mode,\n          strict: isStrict, // TODO support provider options on tools\n        });\n\n        return {\n          args: {\n            ...baseArgs,\n            tools,\n            tool_choice,\n          },\n          warnings: [...warnings, ...toolWarnings],\n        };\n      }\n\n      case 'object-json': {\n        return {\n          args: {\n            ...baseArgs,\n            text: {\n              format:\n                mode.schema != null\n                  ? {\n                      type: 'json_schema',\n                      strict: isStrict,\n                      name: mode.name ?? 'response',\n                      description: mode.description,\n                      schema: mode.schema,\n                    }\n                  : { type: 'json_object' },\n            },\n          },\n          warnings,\n        };\n      }\n\n      case 'object-tool': {\n        return {\n          args: {\n            ...baseArgs,\n            tool_choice: { type: 'function', name: mode.tool.name },\n            tools: [\n              {\n                type: 'function',\n                name: mode.tool.name,\n                description: mode.tool.description,\n                parameters: mode.tool.parameters,\n                strict: isStrict,\n              },\n            ],\n          },\n          warnings,\n        };\n      }\n\n      default: {\n        const _exhaustiveCheck: never = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  async doGenerate(\n    options: Parameters<LanguageModelV1['doGenerate']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doGenerate']>>> {\n    const { args: body, warnings } = this.getArgs(options);\n    const url = this.config.url({\n      path: '/responses',\n      modelId: this.modelId,\n    });\n\n    const {\n      responseHeaders,\n      value: response,\n      rawValue: rawResponse,\n    } = await postJsonToApi({\n      url,\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        z.object({\n          id: z.string(),\n          created_at: z.number(),\n          error: z\n            .object({\n              message: z.string(),\n              code: z.string(),\n            })\n            .nullish(),\n          model: z.string(),\n          output: z.array(\n            z.discriminatedUnion('type', [\n              z.object({\n                type: z.literal('message'),\n                role: z.literal('assistant'),\n                content: z.array(\n                  z.object({\n                    type: z.literal('output_text'),\n                    text: z.string(),\n                    annotations: z.array(\n                      z.object({\n                        type: z.literal('url_citation'),\n                        start_index: z.number(),\n                        end_index: z.number(),\n                        url: z.string(),\n                        title: z.string(),\n                      }),\n                    ),\n                  }),\n                ),\n              }),\n              z.object({\n                type: z.literal('function_call'),\n                call_id: z.string(),\n                name: z.string(),\n                arguments: z.string(),\n              }),\n              z.object({\n                type: z.literal('web_search_call'),\n              }),\n              z.object({\n                type: z.literal('computer_call'),\n              }),\n              z.object({\n                type: z.literal('reasoning'),\n                summary: z.array(\n                  z.object({\n                    type: z.literal('summary_text'),\n                    text: z.string(),\n                  }),\n                ),\n              }),\n            ]),\n          ),\n          incomplete_details: z.object({ reason: z.string() }).nullable(),\n          usage: usageSchema,\n        }),\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    if (response.error) {\n      throw new APICallError({\n        message: response.error.message,\n        url,\n        requestBodyValues: body,\n        statusCode: 400,\n        responseHeaders,\n        responseBody: rawResponse as string,\n        isRetryable: false,\n      });\n    }\n\n    const outputTextElements = response.output\n      .filter(output => output.type === 'message')\n      .flatMap(output => output.content)\n      .filter(content => content.type === 'output_text');\n\n    const toolCalls = response.output\n      .filter(output => output.type === 'function_call')\n      .map(output => ({\n        toolCallType: 'function' as const,\n        toolCallId: output.call_id,\n        toolName: output.name,\n        args: output.arguments,\n      }));\n\n    const reasoningSummary =\n      response.output.find(item => item.type === 'reasoning')?.summary ?? null;\n\n    return {\n      text: outputTextElements.map(content => content.text).join('\\n'),\n      sources: outputTextElements.flatMap(content =>\n        content.annotations.map(annotation => ({\n          sourceType: 'url',\n          id: this.config.generateId?.() ?? generateId(),\n          url: annotation.url,\n          title: annotation.title,\n        })),\n      ),\n      finishReason: mapOpenAIResponseFinishReason({\n        finishReason: response.incomplete_details?.reason,\n        hasToolCalls: toolCalls.length > 0,\n      }),\n      toolCalls: toolCalls.length > 0 ? toolCalls : undefined,\n      reasoning: reasoningSummary\n        ? reasoningSummary.map(summary => ({\n            type: 'text' as const,\n            text: summary.text,\n          }))\n        : undefined,\n      usage: {\n        promptTokens: response.usage.input_tokens,\n        completionTokens: response.usage.output_tokens,\n      },\n      rawCall: {\n        rawPrompt: undefined,\n        rawSettings: {},\n      },\n      rawResponse: {\n        headers: responseHeaders,\n        body: rawResponse,\n      },\n      request: {\n        body: JSON.stringify(body),\n      },\n      response: {\n        id: response.id,\n        timestamp: new Date(response.created_at * 1000),\n        modelId: response.model,\n      },\n      providerMetadata: {\n        openai: {\n          responseId: response.id,\n          cachedPromptTokens:\n            response.usage.input_tokens_details?.cached_tokens ?? null,\n          reasoningTokens:\n            response.usage.output_tokens_details?.reasoning_tokens ?? null,\n        },\n      },\n      warnings,\n    };\n  }\n\n  async doStream(\n    options: Parameters<LanguageModelV1['doStream']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doStream']>>> {\n    const { args: body, warnings } = this.getArgs(options);\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: this.config.url({\n        path: '/responses',\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body: {\n        ...body,\n        stream: true,\n      },\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createEventSourceResponseHandler(\n        openaiResponsesChunkSchema,\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const self = this;\n\n    let finishReason: LanguageModelV1FinishReason = 'unknown';\n    let promptTokens = NaN;\n    let completionTokens = NaN;\n    let cachedPromptTokens: number | null = null;\n    let reasoningTokens: number | null = null;\n    let responseId: string | null = null;\n    const ongoingToolCalls: Record<\n      number,\n      { toolName: string; toolCallId: string } | undefined\n    > = {};\n    let hasToolCalls = false;\n\n    return {\n      stream: response.pipeThrough(\n        new TransformStream<\n          ParseResult<z.infer<typeof openaiResponsesChunkSchema>>,\n          LanguageModelV1StreamPart\n        >({\n          transform(chunk, controller) {\n            // handle failed chunk parsing / validation:\n            if (!chunk.success) {\n              finishReason = 'error';\n              controller.enqueue({ type: 'error', error: chunk.error });\n              return;\n            }\n\n            const value = chunk.value;\n\n            if (isResponseOutputItemAddedChunk(value)) {\n              if (value.item.type === 'function_call') {\n                ongoingToolCalls[value.output_index] = {\n                  toolName: value.item.name,\n                  toolCallId: value.item.call_id,\n                };\n\n                controller.enqueue({\n                  type: 'tool-call-delta',\n                  toolCallType: 'function',\n                  toolCallId: value.item.call_id,\n                  toolName: value.item.name,\n                  argsTextDelta: value.item.arguments,\n                });\n              }\n            } else if (isResponseFunctionCallArgumentsDeltaChunk(value)) {\n              const toolCall = ongoingToolCalls[value.output_index];\n\n              if (toolCall != null) {\n                controller.enqueue({\n                  type: 'tool-call-delta',\n                  toolCallType: 'function',\n                  toolCallId: toolCall.toolCallId,\n                  toolName: toolCall.toolName,\n                  argsTextDelta: value.delta,\n                });\n              }\n            } else if (isResponseCreatedChunk(value)) {\n              responseId = value.response.id;\n              controller.enqueue({\n                type: 'response-metadata',\n                id: value.response.id,\n                timestamp: new Date(value.response.created_at * 1000),\n                modelId: value.response.model,\n              });\n            } else if (isTextDeltaChunk(value)) {\n              controller.enqueue({\n                type: 'text-delta',\n                textDelta: value.delta,\n              });\n            } else if (isResponseReasoningSummaryTextDeltaChunk(value)) {\n              controller.enqueue({\n                type: 'reasoning',\n                textDelta: value.delta,\n              });\n            } else if (\n              isResponseOutputItemDoneChunk(value) &&\n              value.item.type === 'function_call'\n            ) {\n              ongoingToolCalls[value.output_index] = undefined;\n              hasToolCalls = true;\n              controller.enqueue({\n                type: 'tool-call',\n                toolCallType: 'function',\n                toolCallId: value.item.call_id,\n                toolName: value.item.name,\n                args: value.item.arguments,\n              });\n            } else if (isResponseFinishedChunk(value)) {\n              finishReason = mapOpenAIResponseFinishReason({\n                finishReason: value.response.incomplete_details?.reason,\n                hasToolCalls,\n              });\n              promptTokens = value.response.usage.input_tokens;\n              completionTokens = value.response.usage.output_tokens;\n              cachedPromptTokens =\n                value.response.usage.input_tokens_details?.cached_tokens ??\n                cachedPromptTokens;\n              reasoningTokens =\n                value.response.usage.output_tokens_details?.reasoning_tokens ??\n                reasoningTokens;\n            } else if (isResponseAnnotationAddedChunk(value)) {\n              controller.enqueue({\n                type: 'source',\n                source: {\n                  sourceType: 'url',\n                  id: self.config.generateId?.() ?? generateId(),\n                  url: value.annotation.url,\n                  title: value.annotation.title,\n                },\n              });\n            } else if (isErrorChunk(value)) {\n              controller.enqueue({ type: 'error', error: value });\n            }\n          },\n\n          flush(controller) {\n            controller.enqueue({\n              type: 'finish',\n              finishReason,\n              usage: { promptTokens, completionTokens },\n              ...((cachedPromptTokens != null || reasoningTokens != null) && {\n                providerMetadata: {\n                  openai: {\n                    responseId,\n                    cachedPromptTokens,\n                    reasoningTokens,\n                  },\n                },\n              }),\n            });\n          },\n        }),\n      ),\n      rawCall: {\n        rawPrompt: undefined,\n        rawSettings: {},\n      },\n      rawResponse: { headers: responseHeaders },\n      request: { body: JSON.stringify(body) },\n      warnings,\n    };\n  }\n}\n\nconst usageSchema = z.object({\n  input_tokens: z.number(),\n  input_tokens_details: z\n    .object({ cached_tokens: z.number().nullish() })\n    .nullish(),\n  output_tokens: z.number(),\n  output_tokens_details: z\n    .object({ reasoning_tokens: z.number().nullish() })\n    .nullish(),\n});\n\nconst textDeltaChunkSchema = z.object({\n  type: z.literal('response.output_text.delta'),\n  delta: z.string(),\n});\n\nconst responseFinishedChunkSchema = z.object({\n  type: z.enum(['response.completed', 'response.incomplete']),\n  response: z.object({\n    incomplete_details: z.object({ reason: z.string() }).nullish(),\n    usage: usageSchema,\n  }),\n});\n\nconst responseCreatedChunkSchema = z.object({\n  type: z.literal('response.created'),\n  response: z.object({\n    id: z.string(),\n    created_at: z.number(),\n    model: z.string(),\n  }),\n});\n\nconst responseOutputItemDoneSchema = z.object({\n  type: z.literal('response.output_item.done'),\n  output_index: z.number(),\n  item: z.discriminatedUnion('type', [\n    z.object({\n      type: z.literal('message'),\n    }),\n    z.object({\n      type: z.literal('function_call'),\n      id: z.string(),\n      call_id: z.string(),\n      name: z.string(),\n      arguments: z.string(),\n      status: z.literal('completed'),\n    }),\n  ]),\n});\n\nconst responseFunctionCallArgumentsDeltaSchema = z.object({\n  type: z.literal('response.function_call_arguments.delta'),\n  item_id: z.string(),\n  output_index: z.number(),\n  delta: z.string(),\n});\n\nconst responseOutputItemAddedSchema = z.object({\n  type: z.literal('response.output_item.added'),\n  output_index: z.number(),\n  item: z.discriminatedUnion('type', [\n    z.object({\n      type: z.literal('message'),\n    }),\n    z.object({\n      type: z.literal('function_call'),\n      id: z.string(),\n      call_id: z.string(),\n      name: z.string(),\n      arguments: z.string(),\n    }),\n  ]),\n});\n\nconst responseAnnotationAddedSchema = z.object({\n  type: z.literal('response.output_text.annotation.added'),\n  annotation: z.object({\n    type: z.literal('url_citation'),\n    url: z.string(),\n    title: z.string(),\n  }),\n});\n\nconst responseReasoningSummaryTextDeltaSchema = z.object({\n  type: z.literal('response.reasoning_summary_text.delta'),\n  item_id: z.string(),\n  output_index: z.number(),\n  summary_index: z.number(),\n  delta: z.string(),\n});\n\nconst errorChunkSchema = z.object({\n  type: z.literal('error'),\n  code: z.string(),\n  message: z.string(),\n  param: z.string().nullish(),\n  sequence_number: z.number(),\n});\n\nconst openaiResponsesChunkSchema = z.union([\n  textDeltaChunkSchema,\n  responseFinishedChunkSchema,\n  responseCreatedChunkSchema,\n  responseOutputItemDoneSchema,\n  responseFunctionCallArgumentsDeltaSchema,\n  responseOutputItemAddedSchema,\n  responseAnnotationAddedSchema,\n  responseReasoningSummaryTextDeltaSchema,\n  errorChunkSchema,\n  z.object({ type: z.string() }).passthrough(), // fallback for unknown chunks\n]);\n\nfunction isTextDeltaChunk(\n  chunk: z.infer<typeof openaiResponsesChunkSchema>,\n): chunk is z.infer<typeof textDeltaChunkSchema> {\n  return chunk.type === 'response.output_text.delta';\n}\n\nfunction isResponseOutputItemDoneChunk(\n  chunk: z.infer<typeof openaiResponsesChunkSchema>,\n): chunk is z.infer<typeof responseOutputItemDoneSchema> {\n  return chunk.type === 'response.output_item.done';\n}\n\nfunction isResponseFinishedChunk(\n  chunk: z.infer<typeof openaiResponsesChunkSchema>,\n): chunk is z.infer<typeof responseFinishedChunkSchema> {\n  return (\n    chunk.type === 'response.completed' || chunk.type === 'response.incomplete'\n  );\n}\n\nfunction isResponseCreatedChunk(\n  chunk: z.infer<typeof openaiResponsesChunkSchema>,\n): chunk is z.infer<typeof responseCreatedChunkSchema> {\n  return chunk.type === 'response.created';\n}\n\nfunction isResponseFunctionCallArgumentsDeltaChunk(\n  chunk: z.infer<typeof openaiResponsesChunkSchema>,\n): chunk is z.infer<typeof responseFunctionCallArgumentsDeltaSchema> {\n  return chunk.type === 'response.function_call_arguments.delta';\n}\n\nfunction isResponseOutputItemAddedChunk(\n  chunk: z.infer<typeof openaiResponsesChunkSchema>,\n): chunk is z.infer<typeof responseOutputItemAddedSchema> {\n  return chunk.type === 'response.output_item.added';\n}\n\nfunction isResponseAnnotationAddedChunk(\n  chunk: z.infer<typeof openaiResponsesChunkSchema>,\n): chunk is z.infer<typeof responseAnnotationAddedSchema> {\n  return chunk.type === 'response.output_text.annotation.added';\n}\n\nfunction isResponseReasoningSummaryTextDeltaChunk(\n  chunk: z.infer<typeof openaiResponsesChunkSchema>,\n): chunk is z.infer<typeof responseReasoningSummaryTextDeltaSchema> {\n  return chunk.type === 'response.reasoning_summary_text.delta';\n}\n\nfunction isErrorChunk(\n  chunk: z.infer<typeof openaiResponsesChunkSchema>,\n): chunk is z.infer<typeof errorChunkSchema> {\n  return chunk.type === 'error';\n}\n\ntype ResponsesModelConfig = {\n  isReasoningModel: boolean;\n  systemMessageMode: 'remove' | 'system' | 'developer';\n  requiredAutoTruncation: boolean;\n};\n\nfunction getResponsesModelConfig(modelId: string): ResponsesModelConfig {\n  // o series reasoning models:\n  if (modelId.startsWith('o')) {\n    if (modelId.startsWith('o1-mini') || modelId.startsWith('o1-preview')) {\n      return {\n        isReasoningModel: true,\n        systemMessageMode: 'remove',\n        requiredAutoTruncation: false,\n      };\n    }\n\n    return {\n      isReasoningModel: true,\n      systemMessageMode: 'developer',\n      requiredAutoTruncation: false,\n    };\n  }\n\n  // gpt models:\n  return {\n    isReasoningModel: false,\n    systemMessageMode: 'system',\n    requiredAutoTruncation: false,\n  };\n}\n\nconst openaiResponsesProviderOptionsSchema = z.object({\n  metadata: z.any().nullish(),\n  parallelToolCalls: z.boolean().nullish(),\n  previousResponseId: z.string().nullish(),\n  store: z.boolean().nullish(),\n  user: z.string().nullish(),\n  reasoningEffort: z.string().nullish(),\n  strictSchemas: z.boolean().nullish(),\n  instructions: z.string().nullish(),\n  reasoningSummary: z.string().nullish(),\n});\n\nexport type OpenAIResponsesProviderOptions = z.infer<\n  typeof openaiResponsesProviderOptionsSchema\n>;\n", "import {\n  LanguageModelV1CallWarning,\n  LanguageModelV1Prompt,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport { convertUint8ArrayToBase64 } from '@ai-sdk/provider-utils';\nimport { OpenAIResponsesPrompt } from './openai-responses-api-types';\n\nexport function convertToOpenAIResponsesMessages({\n  prompt,\n  systemMessageMode,\n}: {\n  prompt: LanguageModelV1Prompt;\n  systemMessageMode: 'system' | 'developer' | 'remove';\n}): {\n  messages: OpenAIResponsesPrompt;\n  warnings: Array<LanguageModelV1CallWarning>;\n} {\n  const messages: OpenAIResponsesPrompt = [];\n  const warnings: Array<LanguageModelV1CallWarning> = [];\n\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case 'system': {\n        switch (systemMessageMode) {\n          case 'system': {\n            messages.push({ role: 'system', content });\n            break;\n          }\n          case 'developer': {\n            messages.push({ role: 'developer', content });\n            break;\n          }\n          case 'remove': {\n            warnings.push({\n              type: 'other',\n              message: 'system messages are removed for this model',\n            });\n            break;\n          }\n          default: {\n            const _exhaustiveCheck: never = systemMessageMode;\n            throw new Error(\n              `Unsupported system message mode: ${_exhaustiveCheck}`,\n            );\n          }\n        }\n        break;\n      }\n\n      case 'user': {\n        messages.push({\n          role: 'user',\n          content: content.map((part, index) => {\n            switch (part.type) {\n              case 'text': {\n                return { type: 'input_text', text: part.text };\n              }\n              case 'image': {\n                return {\n                  type: 'input_image',\n                  image_url:\n                    part.image instanceof URL\n                      ? part.image.toString()\n                      : `data:${\n                          part.mimeType ?? 'image/jpeg'\n                        };base64,${convertUint8ArrayToBase64(part.image)}`,\n\n                  // OpenAI specific extension: image detail\n                  detail: part.providerMetadata?.openai?.imageDetail,\n                };\n              }\n              case 'file': {\n                if (part.data instanceof URL) {\n                  // The AI SDK automatically downloads files for user file parts with URLs\n                  throw new UnsupportedFunctionalityError({\n                    functionality: 'File URLs in user messages',\n                  });\n                }\n\n                switch (part.mimeType) {\n                  case 'application/pdf': {\n                    return {\n                      type: 'input_file',\n                      filename: part.filename ?? `part-${index}.pdf`,\n                      file_data: `data:application/pdf;base64,${part.data}`,\n                    };\n                  }\n                  default: {\n                    throw new UnsupportedFunctionalityError({\n                      functionality:\n                        'Only PDF files are supported in user messages',\n                    });\n                  }\n                }\n              }\n            }\n          }),\n        });\n\n        break;\n      }\n\n      case 'assistant': {\n        for (const part of content) {\n          switch (part.type) {\n            case 'text': {\n              messages.push({\n                role: 'assistant',\n                content: [{ type: 'output_text', text: part.text }],\n              });\n              break;\n            }\n            case 'tool-call': {\n              messages.push({\n                type: 'function_call',\n                call_id: part.toolCallId,\n                name: part.toolName,\n                arguments: JSON.stringify(part.args),\n              });\n              break;\n            }\n          }\n        }\n\n        break;\n      }\n\n      case 'tool': {\n        for (const part of content) {\n          messages.push({\n            type: 'function_call_output',\n            call_id: part.toolCallId,\n            output: JSON.stringify(part.result),\n          });\n        }\n\n        break;\n      }\n\n      default: {\n        const _exhaustiveCheck: never = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  return { messages, warnings };\n}\n", "import { LanguageModelV1FinishReason } from '@ai-sdk/provider';\n\nexport function mapOpenAIResponseFinishReason({\n  finishReason,\n  hasToolCalls,\n}: {\n  finishReason: string | null | undefined;\n  hasToolCalls: boolean;\n}): LanguageModelV1FinishReason {\n  switch (finishReason) {\n    case undefined:\n    case null:\n      return hasToolCalls ? 'tool-calls' : 'stop';\n    case 'max_output_tokens':\n      return 'length';\n    case 'content_filter':\n      return 'content-filter';\n    default:\n      return hasToolCalls ? 'tool-calls' : 'unknown';\n  }\n}\n", "import {\n  LanguageModelV1,\n  LanguageModelV1CallWarning,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport { OpenAIResponsesTool } from './openai-responses-api-types';\n\nexport function prepareResponsesTools({\n  mode,\n  strict,\n}: {\n  mode: Parameters<LanguageModelV1['doGenerate']>[0]['mode'] & {\n    type: 'regular';\n  };\n  strict: boolean;\n}): {\n  tools?: Array<OpenAIResponsesTool>;\n  tool_choice?:\n    | 'auto'\n    | 'none'\n    | 'required'\n    | { type: 'web_search_preview' }\n    | { type: 'function'; name: string };\n  toolWarnings: LanguageModelV1CallWarning[];\n} {\n  // when the tools array is empty, change it to undefined to prevent errors:\n  const tools = mode.tools?.length ? mode.tools : undefined;\n\n  const toolWarnings: LanguageModelV1CallWarning[] = [];\n\n  if (tools == null) {\n    return { tools: undefined, tool_choice: undefined, toolWarnings };\n  }\n\n  const toolChoice = mode.toolChoice;\n\n  const openaiTools: Array<OpenAIResponsesTool> = [];\n\n  for (const tool of tools) {\n    switch (tool.type) {\n      case 'function':\n        openaiTools.push({\n          type: 'function',\n          name: tool.name,\n          description: tool.description,\n          parameters: tool.parameters,\n          strict: strict ? true : undefined,\n        });\n        break;\n      case 'provider-defined':\n        switch (tool.id) {\n          case 'openai.web_search_preview':\n            openaiTools.push({\n              type: 'web_search_preview',\n              search_context_size: tool.args.searchContextSize as\n                | 'low'\n                | 'medium'\n                | 'high',\n              user_location: tool.args.userLocation as {\n                type: 'approximate';\n                city: string;\n                region: string;\n              },\n            });\n            break;\n          default:\n            toolWarnings.push({ type: 'unsupported-tool', tool });\n            break;\n        }\n        break;\n      default:\n        toolWarnings.push({ type: 'unsupported-tool', tool });\n        break;\n    }\n  }\n\n  if (toolChoice == null) {\n    return { tools: openaiTools, tool_choice: undefined, toolWarnings };\n  }\n\n  const type = toolChoice.type;\n\n  switch (type) {\n    case 'auto':\n    case 'none':\n    case 'required':\n      return { tools: openaiTools, tool_choice: type, toolWarnings };\n    case 'tool': {\n      if (toolChoice.toolName === 'web_search_preview') {\n        return {\n          tools: openaiTools,\n          tool_choice: {\n            type: 'web_search_preview',\n          },\n          toolWarnings,\n        };\n      }\n      return {\n        tools: openaiTools,\n        tool_choice: {\n          type: 'function',\n          name: toolChoice.toolName,\n        },\n        toolWarnings,\n      };\n    }\n    default: {\n      const _exhaustiveCheck: never = type;\n      throw new UnsupportedFunctionalityError({\n        functionality: `Unsupported tool choice type: ${_exhaustiveCheck}`,\n      });\n    }\n  }\n}\n", "import { z } from 'zod';\n\nconst WebSearchPreviewParameters = z.object({});\n\nfunction webSearchPreviewTool({\n  searchContextSize,\n  userLocation,\n}: {\n  searchContextSize?: 'low' | 'medium' | 'high';\n  userLocation?: {\n    type?: 'approximate';\n    city?: string;\n    region?: string;\n    country?: string;\n    timezone?: string;\n  };\n} = {}): {\n  type: 'provider-defined';\n  id: 'openai.web_search_preview';\n  args: {};\n  parameters: typeof WebSearchPreviewParameters;\n} {\n  return {\n    type: 'provider-defined',\n    id: 'openai.web_search_preview',\n    args: {\n      searchContextSize,\n      userLocation,\n    },\n    parameters: WebSearchPreviewParameters,\n  };\n}\n\nexport const openaiTools = {\n  webSearchPreview: webSearchPreviewTool,\n};\n", "import { SpeechModelV1, SpeechModelV1CallWarning } from '@ai-sdk/provider';\nimport {\n  combineHeaders,\n  createBinaryResponseHandler,\n  parseProviderOptions,\n  postJsonToApi,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { OpenAIConfig } from './openai-config';\nimport { openaiFailedResponseHandler } from './openai-error';\nimport { OpenAISpeechModelId } from './openai-speech-settings';\nimport { OpenAISpeechAPITypes } from './openai-api-types';\n\n// https://platform.openai.com/docs/api-reference/audio/createSpeech\nconst OpenAIProviderOptionsSchema = z.object({\n  instructions: z.string().nullish(),\n  speed: z.number().min(0.25).max(4.0).default(1.0).nullish(),\n});\n\nexport type OpenAISpeechCallOptions = z.infer<\n  typeof OpenAIProviderOptionsSchema\n>;\n\ninterface OpenAISpeechModelConfig extends OpenAIConfig {\n  _internal?: {\n    currentDate?: () => Date;\n  };\n}\n\nexport class OpenAISpeechModel implements SpeechModelV1 {\n  readonly specificationVersion = 'v1';\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  constructor(\n    readonly modelId: OpenAISpeechModelId,\n    private readonly config: OpenAISpeechModelConfig,\n  ) {}\n\n  private getArgs({\n    text,\n    voice = 'alloy',\n    outputFormat = 'mp3',\n    speed,\n    instructions,\n    providerOptions,\n  }: Parameters<SpeechModelV1['doGenerate']>[0]) {\n    const warnings: SpeechModelV1CallWarning[] = [];\n\n    // Parse provider options\n    const openAIOptions = parseProviderOptions({\n      provider: 'openai',\n      providerOptions,\n      schema: OpenAIProviderOptionsSchema,\n    });\n\n    // Create request body\n    const requestBody: Record<string, unknown> = {\n      model: this.modelId,\n      input: text,\n      voice,\n      response_format: 'mp3',\n      speed,\n      instructions,\n    };\n\n    if (outputFormat) {\n      if (['mp3', 'opus', 'aac', 'flac', 'wav', 'pcm'].includes(outputFormat)) {\n        requestBody.response_format = outputFormat;\n      } else {\n        warnings.push({\n          type: 'unsupported-setting',\n          setting: 'outputFormat',\n          details: `Unsupported output format: ${outputFormat}. Using mp3 instead.`,\n        });\n      }\n    }\n\n    // Add provider-specific options\n    if (openAIOptions) {\n      const speechModelOptions: OpenAISpeechAPITypes = {};\n\n      for (const key in speechModelOptions) {\n        const value = speechModelOptions[key as keyof OpenAISpeechAPITypes];\n        if (value !== undefined) {\n          requestBody[key] = value;\n        }\n      }\n    }\n\n    return {\n      requestBody,\n      warnings,\n    };\n  }\n\n  async doGenerate(\n    options: Parameters<SpeechModelV1['doGenerate']>[0],\n  ): Promise<Awaited<ReturnType<SpeechModelV1['doGenerate']>>> {\n    const currentDate = this.config._internal?.currentDate?.() ?? new Date();\n    const { requestBody, warnings } = this.getArgs(options);\n\n    const {\n      value: audio,\n      responseHeaders,\n      rawValue: rawResponse,\n    } = await postJsonToApi({\n      url: this.config.url({\n        path: '/audio/speech',\n        modelId: this.modelId,\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body: requestBody,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createBinaryResponseHandler(),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    return {\n      audio,\n      warnings,\n      request: {\n        body: JSON.stringify(requestBody),\n      },\n      response: {\n        timestamp: currentDate,\n        modelId: this.modelId,\n        headers: responseHeaders,\n        body: rawResponse,\n      },\n    };\n  }\n}\n"], "mappings": ";;;;;;;;;AAIA,IAAM,SAAS;AACf,IAAM,SAAS,OAAO,IAAI,MAAM;AALhC,IAAA;AAWO,IAAM,cAAN,MAAMA,qBAAmB,MAAM;;;;;;;;;EAgBpC,YAAY;IACV,MAAAC;IACA;IACA;EACF,GAIG;AACD,UAAM,OAAO;AAxBf,SAAkB,EAAA,IAAU;AA0B1B,SAAK,OAAOA;AACZ,SAAK,QAAQ;EACf;;;;;;EAOA,OAAO,WAAW,OAAqC;AACrD,WAAOD,aAAW,UAAU,OAAO,MAAM;EAC3C;EAEA,OAAiB,UAAU,OAAgBE,UAAyB;AAClE,UAAM,eAAe,OAAO,IAAIA,QAAM;AACtC,WACE,SAAS,QACT,OAAO,UAAU,YACjB,gBAAgB,SAChB,OAAO,MAAM,YAAY,MAAM,aAC/B,MAAM,YAAY,MAAM;EAE5B;AACF;AAjDoB,KAAA;AADb,IAAM,aAAN;ACTP,IAAM,OAAO;AACb,IAAMA,UAAS,mBAAmB,IAAI;AACtC,IAAMC,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,eAAN,cAA2B,WAAW;EAa3C,YAAY;IACV;IACA;IACA;IACA;IACA;IACA;IACA;IACA,cAAc,cAAc,SACzB,eAAe;IACd,eAAe;IACf,eAAe;IACf,cAAc;;IAClB;EACF,GAUG;AACD,UAAM,EAAE,MAAM,SAAS,MAAM,CAAC;AArChC,SAAkBA,GAAAA,IAAU;AAuC1B,SAAK,MAAM;AACX,SAAK,oBAAoB;AACzB,SAAK,aAAa;AAClB,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,cAAc;AACnB,SAAK,OAAO;EACd;EAEA,OAAO,WAAW,OAAuC;AACvD,WAAO,WAAW,UAAU,OAAOF,OAAM;EAC3C;AACF;AAnDoBE,MAAAD;ACLpB,IAAMF,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,yBAAN,cAAqC,WAAW;;EAGrD,YAAY,EAAE,UAAU,sBAAsB,IAA0B,CAAC,GAAG;AAC1E,UAAM,EAAE,MAAAH,OAAM,QAAQ,CAAC;AAHzB,SAAkBG,GAAAA,IAAU;EAI5B;EAEA,OAAO,WAAW,OAAiD;AACjE,WAAO,WAAW,UAAU,OAAOF,OAAM;EAC3C;AACF;AAToBE,MAAAD;ACPb,SAAS,gBAAgB,OAA4B;AAC1D,MAAI,SAAS,MAAM;AACjB,WAAO;EACT;AAEA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;EACT;AAEA,MAAI,iBAAiB,OAAO;AAC1B,WAAO,MAAM;EACf;AAEA,SAAO,KAAK,UAAU,KAAK;AAC7B;ACZA,IAAMF,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AASO,IAAM,uBAAN,cAAmC,WAAW;EAKnD,YAAY;IACV;IACA;IACA;EACF,GAIG;AACD,UAAM,EAAE,MAAAH,OAAM,SAAS,MAAM,CAAC;AAbhC,SAAkBG,GAAAA,IAAU;AAe1B,SAAK,WAAW;EAClB;EAEA,OAAO,WAAW,OAA+C;AAC/D,WAAO,WAAW,UAAU,OAAOF,OAAM;EAC3C;AACF;AArBoBE,MAAAD;ACRpB,IAAMF,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAUO,IAAM,qBAAN,cAAiC,WAAW;EAKjD,YAAY;IACV;IACA;IACA;EACF,GAIG;AACD,UAAM,EAAE,MAAAH,OAAM,SAAS,mBAAmB,OAAO,IAAI,MAAM,CAAC;AAb9D,SAAkBG,GAAAA,IAAU;AAe1B,SAAK,SAAS;EAChB;EAEA,OAAO,WAAW,OAA6C;AAC7D,WAAO,WAAW,UAAU,OAAOF,OAAM;EAC3C;AACF;AArBoBE,MAAAD;ACTpB,IAAMF,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAUO,IAAM,2BAAN,cAAuC,WAAW;EAKvD,YAAY;IACV;IACA,UAAU,0BAA0B,KAAK,UAAU,IAAI,CAAC;EAC1D,GAGG;AACD,UAAM,EAAE,MAAAH,OAAM,QAAQ,CAAC;AAXzB,SAAkBG,GAAAA,IAAU;AAa1B,SAAK,OAAO;EACd;EAEA,OAAO,WAAW,OAAmD;AACnE,WAAO,WAAW,UAAU,OAAOF,OAAM;EAC3C;AACF;AAnBoBE,MAAAD;ACRpB,IAAMF,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AALhC,IAAAE;AAQO,IAAM,iBAAN,cAA6B,WAAW;EAK7C,YAAY,EAAE,MAAM,MAAM,GAAqC;AAC7D,UAAM;MACJ,MAAAH;MACA,SACE,8BACS,IAAI;iBACK,gBAAgB,KAAK,CAAC;MAC1C;IACF,CAAC;AAZH,SAAkBG,GAAAA,IAAU;AAc1B,SAAK,OAAO;EACd;EAEA,OAAO,WAAW,OAAyC;AACzD,WAAO,WAAW,UAAU,OAAOF,OAAM;EAC3C;AACF;AApBoBE,MAAAD;ACPpB,IAAMF,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,kBAAN,cAA8B,WAAW;;EAG9C,YAAY,EAAE,QAAQ,GAAwB;AAC5C,UAAM,EAAE,MAAAH,OAAM,QAAQ,CAAC;AAHzB,SAAkBG,GAAAA,IAAU;EAI5B;EAEA,OAAO,WAAW,OAA0C;AAC1D,WAAO,WAAW,UAAU,OAAOF,OAAM;EAC3C;AACF;AAToBE,MAAAD;ACLpB,IAAMF,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAOoBC,MAAAC;ACLpB,IAAMC,QAAO;AACb,IAAMC,WAAS,mBAAmBD,KAAI;AACtC,IAAMD,WAAS,OAAO,IAAIE,QAAM;AAJhC,IAAAH;AAUoBI,OAAAC;ACRpB,IAAMC,SAAO;AACb,IAAMC,WAAS,mBAAmBD,MAAI;AACtC,IAAMD,WAAS,OAAO,IAAIE,QAAM;AAJhC,IAAAH;AAOoBI,OAAAC;ACLpB,IAAMC,SAAO;AACb,IAAMC,WAAS,mBAAmBD,MAAI;AACtC,IAAMD,WAAS,OAAO,IAAIE,QAAM;AAJhC,IAAAH;AAMO,IAAM,qCAAN,cAAiD,WAAW;EAQjE,YAAY,SAKT;AACD,UAAM;MACJ,MAAAE;MACA,SACE,oDACO,QAAQ,QAAQ,WAAW,QAAQ,OAAO,0BAC9C,QAAQ,oBAAoB,yBAAyB,QAAQ,OAAO,MAAM;IACjF,CAAC;AAnBH,SAAkBF,IAAAA,IAAU;AAqB1B,SAAK,WAAW,QAAQ;AACxB,SAAK,UAAU,QAAQ;AACvB,SAAK,uBAAuB,QAAQ;AACpC,SAAK,SAAS,QAAQ;EACxB;EAEA,OAAO,WACL,OAC6C;AAC7C,WAAO,WAAW,UAAU,OAAOG,QAAM;EAC3C;AACF;AAhCoBH,OAAAC;ACJpB,IAAMC,SAAO;AACb,IAAMC,WAAS,mBAAmBD,MAAI;AACtC,IAAMD,WAAS,OAAO,IAAIE,QAAM;AALhC,IAAAH;AAOO,IAAM,uBAAN,MAAMI,8BAA4B,WAAW;EAKlD,YAAY,EAAE,OAAO,MAAM,GAAuC;AAChE,UAAM;MACJ,MAAAF;MACA,SACE,kCACU,KAAK,UAAU,KAAK,CAAC;iBACb,gBAAgB,KAAK,CAAC;MAC1C;IACF,CAAC;AAZH,SAAkBF,IAAAA,IAAU;AAc1B,SAAK,QAAQ;EACf;EAEA,OAAO,WAAW,OAA8C;AAC9D,WAAO,WAAW,UAAU,OAAOG,QAAM;EAC3C;;;;;;;;;;;EAYA,OAAO,KAAK;IACV;IACA;EACF,GAGwB;AACtB,WAAOC,sBAAoB,WAAW,KAAK,KAAK,MAAM,UAAU,QAC5D,QACA,IAAIA,sBAAoB,EAAE,OAAO,MAAM,CAAC;EAC9C;AACF;AA1CoBJ,OAAAC;AADb,IAAM,sBAAN;ACLP,IAAMC,SAAO;AACb,IAAMC,WAAS,mBAAmBD,MAAI;AACtC,IAAMD,WAAS,OAAO,IAAIE,QAAM;AAJhC,IAAAH;AAMO,IAAM,gCAAN,cAA4C,WAAW;EAK5D,YAAY;IACV;IACA,UAAU,IAAI,aAAa;EAC7B,GAGG;AACD,UAAM,EAAE,MAAAE,QAAM,QAAQ,CAAC;AAXzB,SAAkBF,IAAAA,IAAU;AAY1B,SAAK,gBAAgB;EACvB;EAEA,OAAO,WAAW,OAAwD;AACxE,WAAO,WAAW,UAAU,OAAOG,QAAM;EAC3C;AACF;AAlBoBH,OAAAC;;;AELpB,IAAI,iBAAiB,CAAC,UAAU,cAAc,OAAO;AACnD,SAAO,CAAC,OAAO,gBAAgB;AAC7B,QAAI,KAAK;AACT,QAAI,IAAI,OAAO;AACf,WAAO,KAAK;AACV,YAAM,SAAU,KAAK,OAAO,IAAI,SAAS,SAAU,CAAC;AAAA,IACtD;AACA,WAAO;AAAA,EACT;AACF;;;AcNA,+BAAuB;AbLhB,SAAS,kBACX,SACiC;AACpC,SAAO,QAAQ;IACb,CAAC,iBAAiB,oBAAoB;MACpC,GAAG;MACH,GAAI,kBAAA,OAAA,iBAAkB,CAAC;IACzB;IACA,CAAC;EACH;AACF;AGHO,SAAS,gCAAgC;AAC9C,MAAI,SAAS;AACb,MAAI,QAA4B;AAChC,MAAI,OAAiB,CAAC;AACtB,MAAI,cAAkC;AACtC,MAAI,QAA4B;AAEhC,WAAS,UACP,MACA,YACA;AAEA,QAAI,SAAS,IAAI;AACf,oBAAc,UAAU;AACxB;IACF;AAGA,QAAI,KAAK,WAAW,GAAG,GAAG;AACxB;IACF;AAGA,UAAM,aAAa,KAAK,QAAQ,GAAG;AACnC,QAAI,eAAe,IAAI;AAErB,kBAAY,MAAM,EAAE;AACpB;IACF;AAEA,UAAM,QAAQ,KAAK,MAAM,GAAG,UAAU;AAEtC,UAAM,aAAa,aAAa;AAChC,UAAM,QACJ,aAAa,KAAK,UAAU,KAAK,UAAU,MAAM,MAC7C,KAAK,MAAM,aAAa,CAAC,IACzB,KAAK,MAAM,UAAU;AAE3B,gBAAY,OAAO,KAAK;EAC1B;AAEA,WAAS,cACP,YACA;AACA,QAAI,KAAK,SAAS,GAAG;AACnB,iBAAW,QAAQ;QACjB;QACA,MAAM,KAAK,KAAK,IAAI;QACpB,IAAI;QACJ;MACF,CAAC;AAGD,aAAO,CAAC;AACR,cAAQ;AACR,cAAQ;IACV;EACF;AAEA,WAAS,YAAY,OAAe,OAAe;AACjD,YAAQ,OAAO;MACb,KAAK;AACH,gBAAQ;AACR;MACF,KAAK;AACH,aAAK,KAAK,KAAK;AACf;MACF,KAAK;AACH,sBAAc;AACd;MACF,KAAK;AACH,cAAM,cAAc,SAAS,OAAO,EAAE;AACtC,YAAI,CAAC,MAAM,WAAW,GAAG;AACvB,kBAAQ;QACV;AACA;IACJ;EACF;AAEA,SAAO,IAAI,gBAA0C;IACnD,UAAU,OAAO,YAAY;AAC3B,YAAM,EAAE,OAAO,eAAe,IAAI,WAAW,QAAQ,KAAK;AAE1D,eAAS;AAGT,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,kBAAU,MAAM,CAAC,GAAG,UAAU;MAChC;IACF;IAEA,MAAM,YAAY;AAChB,gBAAU,QAAQ,UAAU;AAC5B,oBAAc,UAAU;IAC1B;EACF,CAAC;AACH;AAGA,SAAS,WAAW,QAAgB,OAAe;AACjD,QAAM,QAAuB,CAAC;AAC9B,MAAI,cAAc;AAGlB,WAAS,IAAI,GAAG,IAAI,MAAM,UAAU;AAClC,UAAM,OAAO,MAAM,GAAG;AAGtB,QAAI,SAAS,MAAM;AAEjB,YAAM,KAAK,WAAW;AACtB,oBAAc;IAChB,WAAW,SAAS,MAAM;AACxB,YAAM,KAAK,WAAW;AACtB,oBAAc;AACd,UAAI,MAAM,CAAC,MAAM,MAAM;AACrB;MACF;IACF,OAAO;AACL,qBAAe;IACjB;EACF;AAEA,SAAO,EAAE,OAAO,gBAAgB,YAAY;AAC9C;AC7HO,SAAS,uBACd,UACwB;AACxB,QAAM,UAAkC,CAAC;AACzC,WAAS,QAAQ,QAAQ,CAAC,OAAO,QAAQ;AACvC,YAAQ,GAAG,IAAI;EACjB,CAAC;AACD,SAAO;AACT;ACAO,IAAM,oBAAoB,CAAC;EAChC;EACA,MAAM,cAAc;EACpB,WAAW;EACX,YAAY;AACd,IAKI,CAAC,MAAmC;AACtC,QAAM,YAAY,eAAe,UAAU,WAAW;AAEtD,MAAI,UAAU,MAAM;AAClB,WAAO;EACT;AAGA,MAAI,SAAS,SAAS,SAAS,GAAG;AAChC,UAAM,IAAI,qBAAqB;MAC7B,UAAU;MACV,SAAS,kBAAkB,SAAS,uCAAuC,QAAQ;IACrF,CAAC;EACH;AAEA,SAAO,CAAA,SAAQ,GAAG,MAAM,GAAG,SAAS,GAAG,UAAU,IAAI,CAAC;AACxD;AAYO,IAAM,aAAa,kBAAkB;AG/CrC,SAAS,uBACd,QACmB;AACnB,SAAO,OAAO;IACZ,OAAO,QAAQ,MAAM,EAAE,OAAO,CAAC,CAAC,MAAM,KAAK,MAAM,SAAS,IAAI;EAChE;AACF;ACXO,SAAS,aAAa,OAAgC;AAC3D,SACE,iBAAiB,UAChB,MAAM,SAAS,gBAAgB,MAAM,SAAS;AAEnD;ACHO,SAAS,WAAW;EACzB;EACA;EACA,sBAAsB;EACtB;AACF,GAKW;AACT,MAAI,OAAO,WAAW,UAAU;AAC9B,WAAO;EACT;AAEA,MAAI,UAAU,MAAM;AAClB,UAAM,IAAI,gBAAgB;MACxB,SAAS,GAAG,WAAW;IACzB,CAAC;EACH;AAEA,MAAI,OAAO,YAAY,aAAa;AAClC,UAAM,IAAI,gBAAgB;MACxB,SAAS,GAAG,WAAW,2CAA2C,mBAAmB;IACvF,CAAC;EACH;AAEA,WAAS,QAAQ,IAAI,uBAAuB;AAE5C,MAAI,UAAU,MAAM;AAClB,UAAM,IAAI,gBAAgB;MACxB,SAAS,GAAG,WAAW,2CAA2C,mBAAmB,sBAAsB,uBAAuB;IACpI,CAAC;EACH;AAEA,MAAI,OAAO,WAAW,UAAU;AAC9B,UAAM,IAAI,gBAAgB;MACxB,SAAS,GAAG,WAAW,+CAA+C,uBAAuB;IAC/F,CAAC;EACH;AAEA,SAAO;AACT;AKvCO,IAAM,kBAAkB,OAAO,IAAI,qBAAqB;AAwBxD,SAAS,UACd,UACmB;AACnB,SAAO,EAAE,CAAC,eAAe,GAAG,MAAM,SAAS;AAC7C;AAEO,SAAS,YAAY,OAAoC;AAC9D,SACE,OAAO,UAAU,YACjB,UAAU,QACV,mBAAmB,SACnB,MAAM,eAAe,MAAM,QAC3B,cAAc;AAElB;AAEO,SAAS,YACd,OACmB;AACnB,SAAO,YAAY,KAAK,IAAI,QAAQ,aAAa,KAAK;AACxD;AAEO,SAAS,aACd,WACmB;AACnB,SAAO,UAAU,CAAA,UAAS;AACxB,UAAM,SAAS,UAAU,UAAU,KAAK;AACxC,WAAO,OAAO,UACV,EAAE,SAAS,MAAM,OAAO,OAAO,KAAK,IACpC,EAAE,SAAS,OAAO,OAAO,OAAO,MAAM;EAC5C,CAAC;AACH;AD/CO,SAAS,cAAiB;EAC/B;EACA,QAAQ;AACV,GAGM;AACJ,QAAM,SAAS,kBAAkB,EAAE,OAAO,QAAQ,YAAY,CAAC;AAE/D,MAAI,CAAC,OAAO,SAAS;AACnB,UAAM,oBAAoB,KAAK,EAAE,OAAO,OAAO,OAAO,MAAM,CAAC;EAC/D;AAEA,SAAO,OAAO;AAChB;AAWO,SAAS,kBAAqB;EACnC;EACA;AACF,GAKmD;AACjD,QAAMI,aAAY,YAAY,MAAM;AAEpC,MAAI;AACF,QAAIA,WAAU,YAAY,MAAM;AAC9B,aAAO,EAAE,SAAS,MAAM,MAAkB;IAC5C;AAEA,UAAM,SAASA,WAAU,SAAS,KAAK;AAEvC,QAAI,OAAO,SAAS;AAClB,aAAO;IACT;AAEA,WAAO;MACL,SAAS;MACT,OAAO,oBAAoB,KAAK,EAAE,OAAO,OAAO,OAAO,MAAM,CAAC;IAChE;EACF,SAAS,OAAO;AACd,WAAO;MACL,SAAS;MACT,OAAO,oBAAoB,KAAK,EAAE,OAAO,OAAO,MAAM,CAAC;IACzD;EACF;AACF;ADtCO,SAAS,UAAa;EAC3B;EACA;AACF,GAGM;AACJ,MAAI;AACF,UAAM,QAAQ,yBAAAC,QAAW,MAAM,IAAI;AAEnC,QAAI,UAAU,MAAM;AAClB,aAAO;IACT;AAEA,WAAO,cAAc,EAAE,OAAO,OAAO,CAAC;EACxC,SAAS,OAAO;AACd,QACE,eAAe,WAAW,KAAK,KAC/BC,oBAAoB,WAAW,KAAK,GACpC;AACA,YAAM;IACR;AAEA,UAAM,IAAI,eAAe,EAAE,MAAM,OAAO,MAAM,CAAC;EACjD;AACF;AA4BO,SAAS,cAAiB;EAC/B;EACA;AACF,GAGmB;AACjB,MAAI;AACF,UAAM,QAAQ,yBAAAD,QAAW,MAAM,IAAI;AAEnC,QAAI,UAAU,MAAM;AAClB,aAAO,EAAE,SAAS,MAAM,OAAmB,UAAU,MAAM;IAC7D;AAEA,UAAM,mBAAmB,kBAAkB,EAAE,OAAO,OAAO,CAAC;AAE5D,WAAO,iBAAiB,UACpB,EAAE,GAAG,kBAAkB,UAAU,MAAM,IACvC;EACN,SAAS,OAAO;AACd,WAAO;MACL,SAAS;MACT,OAAO,eAAe,WAAW,KAAK,IAClC,QACA,IAAI,eAAe,EAAE,MAAM,OAAO,MAAM,CAAC;IAC/C;EACF;AACF;AAEO,SAAS,eAAe,OAAwB;AACrD,MAAI;AACF,6BAAAA,QAAW,MAAM,KAAK;AACtB,WAAO;EACT,SAAQ,GAAA;AACN,WAAO;EACT;AACF;AGrHO,SAAS,qBAAwB;EACtC;EACA;EACA;AACF,GAIkB;AAChB,OAAI,mBAAA,OAAA,SAAA,gBAAkB,QAAA,MAAa,MAAM;AACvC,WAAO;EACT;AAEA,QAAM,wBAAwB,kBAAkB;IAC9C,OAAO,gBAAgB,QAAQ;IAC/B;EACF,CAAC;AAED,MAAI,CAAC,sBAAsB,SAAS;AAClC,UAAM,IAAIE,qBAAqB;MAC7B,UAAU;MACV,SAAS,WAAW,QAAQ;MAC5B,OAAO,sBAAsB;IAC/B,CAAC;EACH;AAEA,SAAO,sBAAsB;AAC/B;ACvBA,IAAMC,oBAAmB,MAAM,WAAW;AAEnC,IAAM,gBAAgB,OAAU;EACrC;EACA;EACA;EACA;EACA;EACA;EACA;AACF,MASE,UAAU;EACR;EACA,SAAS;IACP,gBAAgB;IAChB,GAAG;EACL;EACA,MAAM;IACJ,SAAS,KAAK,UAAU,IAAI;IAC5B,QAAQ;EACV;EACA;EACA;EACA;EACA;AACF,CAAC;AAEI,IAAM,oBAAoB,OAAU;EACzC;EACA;EACA;EACA;EACA;EACA;EACA;AACF,MASE,UAAU;EACR;EACA;EACA,MAAM;IACJ,SAAS;IACT,QAAQ,OAAO,YAAa,SAAiB,QAAQ,CAAC;EACxD;EACA;EACA;EACA;EACA;AACF,CAAC;AAEI,IAAM,YAAY,OAAU;EACjC;EACA,UAAU,CAAC;EACX;EACA;EACA;EACA;EACA,QAAQA,kBAAiB;AAC3B,MAWM;AACJ,MAAI;AACF,UAAM,WAAW,MAAM,MAAM,KAAK;MAChC,QAAQ;MACR,SAAS,uBAAuB,OAAO;MACvC,MAAM,KAAK;MACX,QAAQ;IACV,CAAC;AAED,UAAM,kBAAkB,uBAAuB,QAAQ;AAEvD,QAAI,CAAC,SAAS,IAAI;AAChB,UAAI;AAKJ,UAAI;AACF,2BAAmB,MAAM,sBAAsB;UAC7C;UACA;UACA,mBAAmB,KAAK;QAC1B,CAAC;MACH,SAAS,OAAO;AACd,YAAI,aAAa,KAAK,KAAKC,aAAa,WAAW,KAAK,GAAG;AACzD,gBAAM;QACR;AAEA,cAAM,IAAIA,aAAa;UACrB,SAAS;UACT,OAAO;UACP,YAAY,SAAS;UACrB;UACA;UACA,mBAAmB,KAAK;QAC1B,CAAC;MACH;AAEA,YAAM,iBAAiB;IACzB;AAEA,QAAI;AACF,aAAO,MAAM,0BAA0B;QACrC;QACA;QACA,mBAAmB,KAAK;MAC1B,CAAC;IACH,SAAS,OAAO;AACd,UAAI,iBAAiB,OAAO;AAC1B,YAAI,aAAa,KAAK,KAAKA,aAAa,WAAW,KAAK,GAAG;AACzD,gBAAM;QACR;MACF;AAEA,YAAM,IAAIA,aAAa;QACrB,SAAS;QACT,OAAO;QACP,YAAY,SAAS;QACrB;QACA;QACA,mBAAmB,KAAK;MAC1B,CAAC;IACH;EACF,SAAS,OAAO;AACd,QAAI,aAAa,KAAK,GAAG;AACvB,YAAM;IACR;AAGA,QAAI,iBAAiB,aAAa,MAAM,YAAY,gBAAgB;AAClE,YAAM,QAAS,MAAc;AAE7B,UAAI,SAAS,MAAM;AAEjB,cAAM,IAAIA,aAAa;UACrB,SAAS,0BAA0B,MAAM,OAAO;UAChD;UACA;UACA,mBAAmB,KAAK;UACxB,aAAa;;QACf,CAAC;MACH;IACF;AAEA,UAAM;EACR;AACF;AE/JO,IAAM,iCACX,CAAI;EACF;EACA;EACA;AACF,MAKA,OAAO,EAAE,UAAU,KAAK,kBAAkB,MAAM;AAC9C,QAAM,eAAe,MAAM,SAAS,KAAK;AACzC,QAAM,kBAAkB,uBAAuB,QAAQ;AAGvD,MAAI,aAAa,KAAK,MAAM,IAAI;AAC9B,WAAO;MACL;MACA,OAAO,IAAIC,aAAa;QACtB,SAAS,SAAS;QAClB;QACA;QACA,YAAY,SAAS;QACrB;QACA;QACA,aAAa,eAAA,OAAA,SAAA,YAAc,QAAA;MAC7B,CAAC;IACH;EACF;AAGA,MAAI;AACF,UAAM,cAAc,UAAU;MAC5B,MAAM;MACN,QAAQ;IACV,CAAC;AAED,WAAO;MACL;MACA,OAAO,IAAIA,aAAa;QACtB,SAAS,eAAe,WAAW;QACnC;QACA;QACA,YAAY,SAAS;QACrB;QACA;QACA,MAAM;QACN,aAAa,eAAA,OAAA,SAAA,YAAc,UAAU,WAAA;MACvC,CAAC;IACH;EACF,SAAS,YAAY;AACnB,WAAO;MACL;MACA,OAAO,IAAIA,aAAa;QACtB,SAAS,SAAS;QAClB;QACA;QACA,YAAY,SAAS;QACrB;QACA;QACA,aAAa,eAAA,OAAA,SAAA,YAAc,QAAA;MAC7B,CAAC;IACH;EACF;AACF;AAEK,IAAM,mCACX,CACE,gBAEF,OAAO,EAAE,SAAS,MAA8B;AAC9C,QAAM,kBAAkB,uBAAuB,QAAQ;AAEvD,MAAI,SAAS,QAAQ,MAAM;AACzB,UAAM,IAAI,uBAAuB,CAAC,CAAC;EACrC;AAEA,SAAO;IACL;IACA,OAAO,SAAS,KACb,YAAY,IAAI,kBAAkB,CAAC,EACnC,YAAY,8BAA8B,CAAC,EAC3C;MACC,IAAI,gBAAkD;QACpD,UAAU,EAAE,KAAK,GAAG,YAAY;AAE9B,cAAI,SAAS,UAAU;AACrB;UACF;AAEA,qBAAW;YACT,cAAc;cACZ,MAAM;cACN,QAAQ;YACV,CAAC;UACH;QACF;MACF,CAAC;IACH;EACJ;AACF;AAqCK,IAAM,4BACX,CAAI,mBACJ,OAAO,EAAE,UAAU,KAAK,kBAAkB,MAAM;AAC9C,QAAM,eAAe,MAAM,SAAS,KAAK;AAEzC,QAAM,eAAe,cAAc;IACjC,MAAM;IACN,QAAQ;EACV,CAAC;AAED,QAAM,kBAAkB,uBAAuB,QAAQ;AAEvD,MAAI,CAAC,aAAa,SAAS;AACzB,UAAM,IAAIC,aAAa;MACrB,SAAS;MACT,OAAO,aAAa;MACpB,YAAY,SAAS;MACrB;MACA;MACA;MACA;IACF,CAAC;EACH;AAEA,SAAO;IACL;IACA,OAAO,aAAa;IACpB,UAAU,aAAa;EACzB;AACF;AAEK,IAAM,8BACX,MACA,OAAO,EAAE,UAAU,KAAK,kBAAkB,MAAM;AAC9C,QAAM,kBAAkB,uBAAuB,QAAQ;AAEvD,MAAI,CAAC,SAAS,MAAM;AAClB,UAAM,IAAIA,aAAa;MACrB,SAAS;MACT;MACA;MACA,YAAY,SAAS;MACrB;MACA,cAAc;IAChB,CAAC;EACH;AAEA,MAAI;AACF,UAAM,SAAS,MAAM,SAAS,YAAY;AAC1C,WAAO;MACL;MACA,OAAO,IAAI,WAAW,MAAM;IAC9B;EACF,SAAS,OAAO;AACd,UAAM,IAAIA,aAAa;MACrB,SAAS;MACT;MACA;MACA,YAAY,SAAS;MACrB;MACA,cAAc;MACd,OAAO;IACT,CAAC;EACH;AACF;ACzNF,IAAM,EAAE,MAAM,KAAK,IAAI;AAEhB,SAAS,0BAA0B,cAAsB;AAC9D,QAAM,YAAY,aAAa,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,GAAG;AACnE,QAAM,eAAe,KAAK,SAAS;AACnC,SAAO,WAAW,KAAK,cAAc,CAAA,SAAQ,KAAK,YAAY,CAAC,CAAE;AACnE;AAEO,SAAS,0BAA0B,OAA2B;AACnE,MAAI,eAAe;AAInB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,oBAAgB,OAAO,cAAc,MAAM,CAAC,CAAC;EAC/C;AAEA,SAAO,KAAK,YAAY;AAC1B;ACrBO,SAAS,qBAAqB,KAAyB;AAC5D,SAAO,OAAA,OAAA,SAAA,IAAK,QAAQ,OAAO,EAAA;AAC7B;;;AGMO,SAAS,4BAA4B;EAC1C;EACA,2BAA2B;EAC3B,oBAAoB;AACtB,GAOE;AACA,QAAM,WAA6B,CAAC;AACpC,QAAM,WAA8C,CAAC;AAErD,aAAW,EAAE,MAAM,QAAQ,KAAK,QAAQ;AACtC,YAAQ,MAAM;MACZ,KAAK,UAAU;AACb,gBAAQ,mBAAmB;UACzB,KAAK,UAAU;AACb,qBAAS,KAAK,EAAE,MAAM,UAAU,QAAQ,CAAC;AACzC;UACF;UACA,KAAK,aAAa;AAChB,qBAAS,KAAK,EAAE,MAAM,aAAa,QAAQ,CAAC;AAC5C;UACF;UACA,KAAK,UAAU;AACb,qBAAS,KAAK;cACZ,MAAM;cACN,SAAS;YACX,CAAC;AACD;UACF;UACA,SAAS;AACP,kBAAM,mBAA0B;AAChC,kBAAM,IAAI;cACR,oCAAoC,gBAAgB;YACtD;UACF;QACF;AACA;MACF;MAEA,KAAK,QAAQ;AACX,YAAI,QAAQ,WAAW,KAAK,QAAQ,CAAC,EAAE,SAAS,QAAQ;AACtD,mBAAS,KAAK,EAAE,MAAM,QAAQ,SAAS,QAAQ,CAAC,EAAE,KAAK,CAAC;AACxD;QACF;AAEA,iBAAS,KAAK;UACZ,MAAM;UACN,SAAS,QAAQ,IAAI,CAAC,MAAM,UAAU;AA5DhD,gBAAAC,MAAA,IAAA,IAAA;AA6DY,oBAAQ,KAAK,MAAM;cACjB,KAAK,QAAQ;AACX,uBAAO,EAAE,MAAM,QAAQ,MAAM,KAAK,KAAK;cACzC;cACA,KAAK,SAAS;AACZ,uBAAO;kBACL,MAAM;kBACN,WAAW;oBACT,KACE,KAAK,iBAAiB,MAClB,KAAK,MAAM,SAAS,IACpB,SACEA,OAAA,KAAK,aAAL,OAAAA,OAAiB,YACnB,WAAW,0BAA0B,KAAK,KAAK,CAAC;;oBAGtD,SAAQ,MAAA,KAAA,KAAK,qBAAL,OAAA,SAAA,GAAuB,WAAvB,OAAA,SAAA,GAA+B;kBACzC;gBACF;cACF;cACA,KAAK,QAAQ;AACX,oBAAI,KAAK,gBAAgB,KAAK;AAC5B,wBAAM,IAAI,8BAA8B;oBACtC,eACE;kBACJ,CAAC;gBACH;AAEA,wBAAQ,KAAK,UAAU;kBACrB,KAAK,aAAa;AAChB,2BAAO;sBACL,MAAM;sBACN,aAAa,EAAE,MAAM,KAAK,MAAM,QAAQ,MAAM;oBAChD;kBACF;kBACA,KAAK;kBACL,KAAK,cAAc;AACjB,2BAAO;sBACL,MAAM;sBACN,aAAa,EAAE,MAAM,KAAK,MAAM,QAAQ,MAAM;oBAChD;kBACF;kBACA,KAAK,mBAAmB;AACtB,2BAAO;sBACL,MAAM;sBACN,MAAM;wBACJ,WAAU,KAAA,KAAK,aAAL,OAAA,KAAiB,QAAQ,KAAK;wBACxC,WAAW,+BAA+B,KAAK,IAAI;sBACrD;oBACF;kBACF;kBACA,SAAS;AACP,0BAAM,IAAI,8BAA8B;sBACtC,eAAe,0BAA0B,KAAK,QAAQ;oBACxD,CAAC;kBACH;gBACF;cACF;YACF;UACF,CAAC;QACH,CAAC;AAED;MACF;MAEA,KAAK,aAAa;AAChB,YAAI,OAAO;AACX,cAAM,YAID,CAAC;AAEN,mBAAW,QAAQ,SAAS;AAC1B,kBAAQ,KAAK,MAAM;YACjB,KAAK,QAAQ;AACX,sBAAQ,KAAK;AACb;YACF;YACA,KAAK,aAAa;AAChB,wBAAU,KAAK;gBACb,IAAI,KAAK;gBACT,MAAM;gBACN,UAAU;kBACR,MAAM,KAAK;kBACX,WAAW,KAAK,UAAU,KAAK,IAAI;gBACrC;cACF,CAAC;AACD;YACF;UACF;QACF;AAEA,YAAI,0BAA0B;AAC5B,cAAI,UAAU,SAAS,GAAG;AACxB,kBAAM,IAAI,8BAA8B;cACtC,eACE;YACJ,CAAC;UACH;AAEA,mBAAS,KAAK;YACZ,MAAM;YACN,SAAS;YACT,eACE,UAAU,SAAS,IAAI,UAAU,CAAC,EAAE,WAAW;UACnD,CAAC;QACH,OAAO;AACL,mBAAS,KAAK;YACZ,MAAM;YACN,SAAS;YACT,YAAY,UAAU,SAAS,IAAI,YAAY;UACjD,CAAC;QACH;AAEA;MACF;MAEA,KAAK,QAAQ;AACX,mBAAW,gBAAgB,SAAS;AAClC,cAAI,0BAA0B;AAC5B,qBAAS,KAAK;cACZ,MAAM;cACN,MAAM,aAAa;cACnB,SAAS,KAAK,UAAU,aAAa,MAAM;YAC7C,CAAC;UACH,OAAO;AACL,qBAAS,KAAK;cACZ,MAAM;cACN,cAAc,aAAa;cAC3B,SAAS,KAAK,UAAU,aAAa,MAAM;YAC7C,CAAC;UACH;QACF;AACA;MACF;MAEA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;MACzD;IACF;EACF;AAEA,SAAO,EAAE,UAAU,SAAS;AAC9B;AC7LO,SAAS,4BACd,UACqC;AAnBvC,MAAAA,MAAA;AAoBE,UACE,MAAAA,OAAA,YAAA,OAAA,SAAA,SAAU,YAAV,OAAA,SAAAA,KAAmB,IAAI,CAAC,EAAE,OAAO,SAAS,aAAa,OAAO;IAC5D;IACA;IACA,aAAa,eACT,aAAa,IAAI,CAAC,EAAE,OAAAC,QAAO,SAAAC,SAAQ,OAAO;MACxC,OAAAD;MACA,SAAAC;IACF,EAAE,IACF,CAAC;EACP,EAAA,MATA,OAAA,KASO;AAEX;AC9BO,SAAS,sBACd,cAC6B;AAC7B,UAAQ,cAAc;IACpB,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;IACL,KAAK;AACH,aAAO;IACT;AACE,aAAO;EACX;AACF;ACfO,IAAM,wBAAwB,iBAAE,OAAO;EAC5C,OAAO,iBAAE,OAAO;IACd,SAAS,iBAAE,OAAO;;;;IAKlB,MAAM,iBAAE,OAAO,EAAE,QAAQ;IACzB,OAAO,iBAAE,IAAI,EAAE,QAAQ;IACvB,MAAM,iBAAE,MAAM,CAAC,iBAAE,OAAO,GAAG,iBAAE,OAAO,CAAC,CAAC,EAAE,QAAQ;EAClD,CAAC;AACH,CAAC;AAIM,IAAM,8BAA8B,+BAA+B;EACxE,aAAa;EACb,gBAAgB,CAAA,SAAQ,KAAK,MAAM;AACrC,CAAC;ACrBM,SAAS,oBAAoB;EAClC;EACA;EACA;AACF,GAIG;AACD,SAAO;IACL,IAAI,MAAA,OAAA,KAAM;IACV,SAAS,SAAA,OAAA,QAAS;IAClB,WAAW,WAAW,OAAO,IAAI,KAAK,UAAU,GAAI,IAAI;EAC1D;AACF;ACPO,SAAS,aAAa;EAC3B;EACA,2BAA2B;EAC3B;AACF,GA8BE;AAzCF,MAAAF;AA2CE,QAAM,UAAQA,OAAA,KAAK,UAAL,OAAA,SAAAA,KAAY,UAAS,KAAK,QAAQ;AAEhD,QAAM,eAA6C,CAAC;AAEpD,MAAI,SAAS,MAAM;AACjB,WAAO,EAAE,OAAO,QAAW,aAAa,QAAW,aAAa;EAClE;AAEA,QAAM,aAAa,KAAK;AAExB,MAAI,0BAA0B;AAC5B,UAAM,kBAID,CAAC;AAEN,eAAW,QAAQ,OAAO;AACxB,UAAI,KAAK,SAAS,oBAAoB;AACpC,qBAAa,KAAK,EAAE,MAAM,oBAAoB,KAAK,CAAC;MACtD,OAAO;AACL,wBAAgB,KAAK;UACnB,MAAM,KAAK;UACX,aAAa,KAAK;UAClB,YAAY,KAAK;QACnB,CAAC;MACH;IACF;AAEA,QAAI,cAAc,MAAM;AACtB,aAAO;QACL,WAAW;QACX,eAAe;QACf;MACF;IACF;AAEA,UAAMG,QAAO,WAAW;AAExB,YAAQA,OAAM;MACZ,KAAK;MACL,KAAK;MACL,KAAK;AACH,eAAO;UACL,WAAW;UACX,eAAe;UACf;QACF;MACF,KAAK;AACH,cAAM,IAAIC,8BAA8B;UACtC,eAAe;QACjB,CAAC;MACH;AACE,eAAO;UACL,WAAW;UACX,eAAe,EAAE,MAAM,WAAW,SAAS;UAC3C;QACF;IACJ;EACF;AAEA,QAAMC,eAQD,CAAC;AAEN,aAAW,QAAQ,OAAO;AACxB,QAAI,KAAK,SAAS,oBAAoB;AACpC,mBAAa,KAAK,EAAE,MAAM,oBAAoB,KAAK,CAAC;IACtD,OAAO;AACLA,mBAAY,KAAK;QACf,MAAM;QACN,UAAU;UACR,MAAM,KAAK;UACX,aAAa,KAAK;UAClB,YAAY,KAAK;UACjB,QAAQ,oBAAoB,OAAO;QACrC;MACF,CAAC;IACH;EACF;AAEA,MAAI,cAAc,MAAM;AACtB,WAAO,EAAE,OAAOA,cAAa,aAAa,QAAW,aAAa;EACpE;AAEA,QAAM,OAAO,WAAW;AAExB,UAAQ,MAAM;IACZ,KAAK;IACL,KAAK;IACL,KAAK;AACH,aAAO,EAAE,OAAOA,cAAa,aAAa,MAAM,aAAa;IAC/D,KAAK;AACH,aAAO;QACL,OAAOA;QACP,aAAa;UACX,MAAM;UACN,UAAU;YACR,MAAM,WAAW;UACnB;QACF;QACA;MACF;IACF,SAAS;AACP,YAAM,mBAA0B;AAChC,YAAM,IAAID,8BAA8B;QACtC,eAAe,iCAAiC,gBAAgB;MAClE,CAAC;IACH;EACF;AACF;ANvHO,IAAM,0BAAN,MAAyD;EAQ9D,YACE,SACA,UACA,QACA;AAXF,SAAS,uBAAuB;AAY9B,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,SAAS;EAChB;EAEA,IAAI,4BAAqC;AA1D3C,QAAAJ;AA8DI,YAAOA,OAAA,KAAK,SAAS,sBAAd,OAAAA,OAAmC,iBAAiB,KAAK,OAAO;EACzE;EAEA,IAAI,8BAA8B;AAEhC,QAAI,aAAa,KAAK,OAAO,GAAG;AAC9B,aAAO;IACT;AAEA,WAAO,KAAK,4BAA4B,SAAS;EACnD;EAEA,IAAI,WAAmB;AACrB,WAAO,KAAK,OAAO;EACrB;EAEA,IAAI,oBAA6B;AAE/B,WAAO,CAAC,KAAK,SAAS;EACxB;EAEQ,QAAQ;IACd;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF,GAAiD;AAhGnD,QAAAA,MAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA;AAiGI,UAAM,OAAO,KAAK;AAElB,UAAM,WAAyC,CAAC;AAEhD,QAAI,QAAQ,MAAM;AAChB,eAAS,KAAK;QACZ,MAAM;QACN,SAAS;MACX,CAAC;IACH;AAEA,SACE,kBAAA,OAAA,SAAA,eAAgB,UAAS,UACzB,eAAe,UAAU,QACzB,CAAC,KAAK,2BACN;AACA,eAAS,KAAK;QACZ,MAAM;QACN,SAAS;QACT,SACE;MACJ,CAAC;IACH;AAEA,UAAM,2BAA2B,KAAK,SAAS;AAE/C,QAAI,4BAA4B,KAAK,SAAS,sBAAsB,MAAM;AACxE,YAAM,IAAII,8BAA8B;QACtC,eAAe;MACjB,CAAC;IACH;AAEA,QAAI,4BAA4B,KAAK,2BAA2B;AAC9D,YAAM,IAAIA,8BAA8B;QACtC,eAAe;MACjB,CAAC;IACH;AAEA,UAAM,EAAE,UAAU,UAAU,gBAAgB,IAAI;MAC9C;QACE;QACA;QACA,mBAAmB,qBAAqB,KAAK,OAAO;MACtD;IACF;AAEA,aAAS,KAAK,GAAG,eAAe;AAEhC,UAAM,WAAW;;MAEf,OAAO,KAAK;;MAGZ,YAAY,KAAK,SAAS;MAC1B,UACE,KAAK,SAAS,aAAa,QAC3B,OAAO,KAAK,SAAS,aAAa,WAC9B,OACA;MACN,cACE,OAAO,KAAK,SAAS,aAAa,WAC9B,KAAK,SAAS,WACd,OAAO,KAAK,SAAS,aAAa,YAChC,KAAK,SAAS,WACZ,IACA,SACF;MACR,MAAM,KAAK,SAAS;MACpB,qBAAqB,KAAK,SAAS;;MAGnC,YAAY;MACZ;MACA,OAAO;MACP,mBAAmB;MACnB,kBAAkB;MAClB,kBACE,kBAAA,OAAA,SAAA,eAAgB,UAAS,SACrB,KAAK,6BAA6B,eAAe,UAAU,OACzD;QACE,MAAM;QACN,aAAa;UACX,QAAQ,eAAe;UACvB,QAAQ;UACR,OAAMJ,OAAA,eAAe,SAAf,OAAAA,OAAuB;UAC7B,aAAa,eAAe;QAC9B;MACF,IACA,EAAE,MAAM,cAAc,IACxB;MACN,MAAM;MACN;;;MAIA,wBAAuB,KAAA,oBAAA,OAAA,SAAA,iBAAkB,WAAlB,OAAA,SAAA,GAA0B;MACjD,QAAO,KAAA,oBAAA,OAAA,SAAA,iBAAkB,WAAlB,OAAA,SAAA,GAA0B;MACjC,WAAU,KAAA,oBAAA,OAAA,SAAA,iBAAkB,WAAlB,OAAA,SAAA,GAA0B;MACpC,aAAY,KAAA,oBAAA,OAAA,SAAA,iBAAkB,WAAlB,OAAA,SAAA,GAA0B;MACtC,mBACE,MAAA,KAAA,oBAAA,OAAA,SAAA,iBAAkB,WAAlB,OAAA,SAAA,GAA0B,oBAA1B,OAAA,KACA,KAAK,SAAS;;MAGhB;IACF;AAEA,QAAI,iBAAiB,KAAK,OAAO,GAAG;AAGlC,UAAI,SAAS,eAAe,MAAM;AAChC,iBAAS,cAAc;AACvB,iBAAS,KAAK;UACZ,MAAM;UACN,SAAS;UACT,SAAS;QACX,CAAC;MACH;AACA,UAAI,SAAS,SAAS,MAAM;AAC1B,iBAAS,QAAQ;AACjB,iBAAS,KAAK;UACZ,MAAM;UACN,SAAS;UACT,SAAS;QACX,CAAC;MACH;AACA,UAAI,SAAS,qBAAqB,MAAM;AACtC,iBAAS,oBAAoB;AAC7B,iBAAS,KAAK;UACZ,MAAM;UACN,SAAS;UACT,SAAS;QACX,CAAC;MACH;AACA,UAAI,SAAS,oBAAoB,MAAM;AACrC,iBAAS,mBAAmB;AAC5B,iBAAS,KAAK;UACZ,MAAM;UACN,SAAS;UACT,SAAS;QACX,CAAC;MACH;AACA,UAAI,SAAS,cAAc,MAAM;AAC/B,iBAAS,aAAa;AACtB,iBAAS,KAAK;UACZ,MAAM;UACN,SAAS;QACX,CAAC;MACH;AACA,UAAI,SAAS,YAAY,MAAM;AAC7B,iBAAS,WAAW;AACpB,iBAAS,KAAK;UACZ,MAAM;UACN,SAAS;QACX,CAAC;MACH;AACA,UAAI,SAAS,gBAAgB,MAAM;AACjC,iBAAS,eAAe;AACxB,iBAAS,KAAK;UACZ,MAAM;UACN,SAAS;QACX,CAAC;MACH;AAGA,UAAI,SAAS,cAAc,MAAM;AAC/B,YAAI,SAAS,yBAAyB,MAAM;AAC1C,mBAAS,wBAAwB,SAAS;QAC5C;AACA,iBAAS,aAAa;MACxB;IACF,WACE,KAAK,QAAQ,WAAW,uBAAuB,KAC/C,KAAK,QAAQ,WAAW,4BAA4B,GACpD;AACA,UAAI,SAAS,eAAe,MAAM;AAChC,iBAAS,cAAc;AACvB,iBAAS,KAAK;UACZ,MAAM;UACN,SAAS;UACT,SACE;QACJ,CAAC;MACH;IACF;AACA,YAAQ,MAAM;MACZ,KAAK,WAAW;AACd,cAAM,EAAE,OAAO,aAAa,WAAW,eAAe,aAAa,IACjE,aAAa;UACX;UACA;UACA,mBAAmB,KAAK;QAC1B,CAAC;AAEH,eAAO;UACL,MAAM;YACJ,GAAG;YACH;YACA;YACA;YACA;UACF;UACA,UAAU,CAAC,GAAG,UAAU,GAAG,YAAY;QACzC;MACF;MAEA,KAAK,eAAe;AAClB,eAAO;UACL,MAAM;YACJ,GAAG;YACH,iBACE,KAAK,6BAA6B,KAAK,UAAU,OAC7C;cACE,MAAM;cACN,aAAa;gBACX,QAAQ,KAAK;gBACb,QAAQ;gBACR,OAAM,KAAA,KAAK,SAAL,OAAA,KAAa;gBACnB,aAAa,KAAK;cACpB;YACF,IACA,EAAE,MAAM,cAAc;UAC9B;UACA;QACF;MACF;MAEA,KAAK,eAAe;AAClB,eAAO;UACL,MAAM,2BACF;YACE,GAAG;YACH,eAAe;cACb,MAAM,KAAK,KAAK;YAClB;YACA,WAAW;cACT;gBACE,MAAM,KAAK,KAAK;gBAChB,aAAa,KAAK,KAAK;gBACvB,YAAY,KAAK,KAAK;cACxB;YACF;UACF,IACA;YACE,GAAG;YACH,aAAa;cACX,MAAM;cACN,UAAU,EAAE,MAAM,KAAK,KAAK,KAAK;YACnC;YACA,OAAO;cACL;gBACE,MAAM;gBACN,UAAU;kBACR,MAAM,KAAK,KAAK;kBAChB,aAAa,KAAK,KAAK;kBACvB,YAAY,KAAK,KAAK;kBACtB,QAAQ,KAAK,4BAA4B,OAAO;gBAClD;cACF;YACF;UACF;UACJ;QACF;MACF;MAEA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;MACzD;IACF;EACF;EAEA,MAAM,WACJ,SAC6D;AAnXjE,QAAAA,MAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA;AAoXI,UAAM,EAAE,MAAM,MAAM,SAAS,IAAI,KAAK,QAAQ,OAAO;AAErD,UAAM;MACJ;MACA,OAAO;MACP,UAAU;IACZ,IAAI,MAAM,cAAc;MACtB,KAAK,KAAK,OAAO,IAAI;QACnB,MAAM;QACN,SAAS,KAAK;MAChB,CAAC;MACD,SAAS,eAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;MAC9D;MACA,uBAAuB;MACvB,2BAA2B;QACzB;MACF;MACA,aAAa,QAAQ;MACrB,OAAO,KAAK,OAAO;IACrB,CAAC;AAED,UAAM,EAAE,UAAU,WAAW,GAAG,YAAY,IAAI;AAChD,UAAM,SAAS,SAAS,QAAQ,CAAC;AAGjC,UAAM,0BAAyBA,OAAA,SAAS,UAAT,OAAA,SAAAA,KAAgB;AAC/C,UAAM,sBAAqB,KAAA,SAAS,UAAT,OAAA,SAAA,GAAgB;AAC3C,UAAM,mBAAoD,EAAE,QAAQ,CAAC,EAAE;AACvE,SAAI,0BAAA,OAAA,SAAA,uBAAwB,qBAAoB,MAAM;AACpD,uBAAiB,OAAO,kBACtB,0BAAA,OAAA,SAAA,uBAAwB;IAC5B;AACA,SAAI,0BAAA,OAAA,SAAA,uBAAwB,+BAA8B,MAAM;AAC9D,uBAAiB,OAAO,2BACtB,0BAAA,OAAA,SAAA,uBAAwB;IAC5B;AACA,SAAI,0BAAA,OAAA,SAAA,uBAAwB,+BAA8B,MAAM;AAC9D,uBAAiB,OAAO,2BACtB,0BAAA,OAAA,SAAA,uBAAwB;IAC5B;AACA,SAAI,sBAAA,OAAA,SAAA,mBAAoB,kBAAiB,MAAM;AAC7C,uBAAiB,OAAO,qBACtB,sBAAA,OAAA,SAAA,mBAAoB;IACxB;AAEA,WAAO;MACL,OAAM,KAAA,OAAO,QAAQ,YAAf,OAAA,KAA0B;MAChC,WACE,KAAK,SAAS,4BAA4B,OAAO,QAAQ,gBACrD;QACE;UACE,cAAc;UACd,YAAY,WAAW;UACvB,UAAU,OAAO,QAAQ,cAAc;UACvC,MAAM,OAAO,QAAQ,cAAc;QACrC;MACF,KACA,KAAA,OAAO,QAAQ,eAAf,OAAA,SAAA,GAA2B,IAAI,CAAA,aAAS;AA7apD,YAAAA;AA6awD,eAAA;UAC1C,cAAc;UACd,aAAYA,OAAA,SAAS,OAAT,OAAAA,OAAe,WAAW;UACtC,UAAU,SAAS,SAAS;UAC5B,MAAM,SAAS,SAAS;QAC1B;MAAA,CAAA;MACN,cAAc,sBAAsB,OAAO,aAAa;MACxD,OAAO;QACL,eAAc,MAAA,KAAA,SAAS,UAAT,OAAA,SAAA,GAAgB,kBAAhB,OAAA,KAAiC;QAC/C,mBAAkB,MAAA,KAAA,SAAS,UAAT,OAAA,SAAA,GAAgB,sBAAhB,OAAA,KAAqC;MACzD;MACA,SAAS,EAAE,WAAW,YAAY;MAClC,aAAa,EAAE,SAAS,iBAAiB,MAAM,YAAY;MAC3D,SAAS,EAAE,MAAM,KAAK,UAAU,IAAI,EAAE;MACtC,UAAU,oBAAoB,QAAQ;MACtC;MACA,UAAU,4BAA4B,OAAO,QAAQ;MACrD;IACF;EACF;EAEA,MAAM,SACJ,SAC2D;AAC3D,QAAI,KAAK,SAAS,mBAAmB;AACnC,YAAM,SAAS,MAAM,KAAK,WAAW,OAAO;AAE5C,YAAM,kBAAkB,IAAI,eAA0C;QACpE,MAAM,YAAY;AAChB,qBAAW,QAAQ,EAAE,MAAM,qBAAqB,GAAG,OAAO,SAAS,CAAC;AACpE,cAAI,OAAO,MAAM;AACf,uBAAW,QAAQ;cACjB,MAAM;cACN,WAAW,OAAO;YACpB,CAAC;UACH;AACA,cAAI,OAAO,WAAW;AACpB,uBAAW,YAAY,OAAO,WAAW;AACvC,yBAAW,QAAQ;gBACjB,MAAM;gBACN,cAAc;gBACd,YAAY,SAAS;gBACrB,UAAU,SAAS;gBACnB,eAAe,SAAS;cAC1B,CAAC;AAED,yBAAW,QAAQ;gBACjB,MAAM;gBACN,GAAG;cACL,CAAC;YACH;UACF;AACA,qBAAW,QAAQ;YACjB,MAAM;YACN,cAAc,OAAO;YACrB,OAAO,OAAO;YACd,UAAU,OAAO;YACjB,kBAAkB,OAAO;UAC3B,CAAC;AACD,qBAAW,MAAM;QACnB;MACF,CAAC;AACD,aAAO;QACL,QAAQ;QACR,SAAS,OAAO;QAChB,aAAa,OAAO;QACpB,UAAU,OAAO;MACnB;IACF;AAEA,UAAM,EAAE,MAAM,SAAS,IAAI,KAAK,QAAQ,OAAO;AAE/C,UAAM,OAAO;MACX,GAAG;MACH,QAAQ;;MAGR,gBACE,KAAK,OAAO,kBAAkB,WAC1B,EAAE,eAAe,KAAK,IACtB;IACR;AAEA,UAAM,EAAE,iBAAiB,OAAO,SAAS,IAAI,MAAM,cAAc;MAC/D,KAAK,KAAK,OAAO,IAAI;QACnB,MAAM;QACN,SAAS,KAAK;MAChB,CAAC;MACD,SAAS,eAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;MAC9D;MACA,uBAAuB;MACvB,2BAA2B;QACzB;MACF;MACA,aAAa,QAAQ;MACrB,OAAO,KAAK,OAAO;IACrB,CAAC;AAED,UAAM,EAAE,UAAU,WAAW,GAAG,YAAY,IAAI;AAEhD,UAAM,YAQD,CAAC;AAEN,QAAI,eAA4C;AAChD,QAAI,QAGA;MACF,cAAc;MACd,kBAAkB;IACpB;AACA,QAAI;AACJ,QAAI,eAAe;AAEnB,UAAM,EAAE,yBAAyB,IAAI,KAAK;AAE1C,UAAM,mBAAoD,EAAE,QAAQ,CAAC,EAAE;AAEvE,WAAO;MACL,QAAQ,SAAS;QACf,IAAI,gBAGF;UACA,UAAU,OAAO,YAAY;AAhjBvC,gBAAAA,MAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA;AAkjBY,gBAAI,CAAC,MAAM,SAAS;AAClB,6BAAe;AACf,yBAAW,QAAQ,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,CAAC;AACxD;YACF;AAEA,kBAAM,QAAQ,MAAM;AAGpB,gBAAI,WAAW,OAAO;AACpB,6BAAe;AACf,yBAAW,QAAQ,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,CAAC;AACxD;YACF;AAEA,gBAAI,cAAc;AAChB,6BAAe;AAEf,yBAAW,QAAQ;gBACjB,MAAM;gBACN,GAAG,oBAAoB,KAAK;cAC9B,CAAC;YACH;AAEA,gBAAI,MAAM,SAAS,MAAM;AACvB,oBAAM;gBACJ;gBACA;gBACA;gBACA;cACF,IAAI,MAAM;AAEV,sBAAQ;gBACN,cAAc,iBAAA,OAAA,gBAAiB;gBAC/B,kBAAkB,qBAAA,OAAA,oBAAqB;cACzC;AAEA,mBAAI,6BAAA,OAAA,SAAA,0BAA2B,qBAAoB,MAAM;AACvD,iCAAiB,OAAO,kBACtB,6BAAA,OAAA,SAAA,0BAA2B;cAC/B;AACA,mBACE,6BAAA,OAAA,SAAA,0BAA2B,+BAA8B,MACzD;AACA,iCAAiB,OAAO,2BACtB,6BAAA,OAAA,SAAA,0BAA2B;cAC/B;AACA,mBACE,6BAAA,OAAA,SAAA,0BAA2B,+BAA8B,MACzD;AACA,iCAAiB,OAAO,2BACtB,6BAAA,OAAA,SAAA,0BAA2B;cAC/B;AACA,mBAAI,yBAAA,OAAA,SAAA,sBAAuB,kBAAiB,MAAM;AAChD,iCAAiB,OAAO,qBACtB,yBAAA,OAAA,SAAA,sBAAuB;cAC3B;YACF;AAEA,kBAAM,SAAS,MAAM,QAAQ,CAAC;AAE9B,iBAAI,UAAA,OAAA,SAAA,OAAQ,kBAAiB,MAAM;AACjC,6BAAe,sBAAsB,OAAO,aAAa;YAC3D;AAEA,iBAAI,UAAA,OAAA,SAAA,OAAQ,UAAS,MAAM;AACzB;YACF;AAEA,kBAAM,QAAQ,OAAO;AAErB,gBAAI,MAAM,WAAW,MAAM;AACzB,yBAAW,QAAQ;gBACjB,MAAM;gBACN,WAAW,MAAM;cACnB,CAAC;YACH;AAEA,kBAAM,iBAAiB;cACrB,UAAA,OAAA,SAAA,OAAQ;YACV;AACA,gBAAI,kBAAA,OAAA,SAAA,eAAgB,QAAQ;AAC1B,kBAAI,aAAa,OAAW,YAAW,CAAC;AACxC,uBAAS,KAAK,GAAG,cAAc;YACjC;AAEA,kBAAM,kBACJ,4BAA4B,MAAM,iBAAiB,OAC/C;cACE;gBACE,MAAM;gBACN,IAAI,WAAW;gBACf,UAAU,MAAM;gBAChB,OAAO;cACT;YACF,IACA,MAAM;AAEZ,gBAAI,mBAAmB,MAAM;AAC3B,yBAAW,iBAAiB,iBAAiB;AAC3C,sBAAM,QAAQ,cAAc;AAG5B,oBAAI,UAAU,KAAK,KAAK,MAAM;AAC5B,sBAAI,cAAc,SAAS,YAAY;AACrC,0BAAM,IAAI,yBAAyB;sBACjC,MAAM;sBACN,SAAS;oBACX,CAAC;kBACH;AAEA,sBAAI,cAAc,MAAM,MAAM;AAC5B,0BAAM,IAAI,yBAAyB;sBACjC,MAAM;sBACN,SAAS;oBACX,CAAC;kBACH;AAEA,wBAAIA,OAAA,cAAc,aAAd,OAAA,SAAAA,KAAwB,SAAQ,MAAM;AACxC,0BAAM,IAAI,yBAAyB;sBACjC,MAAM;sBACN,SAAS;oBACX,CAAC;kBACH;AAEA,4BAAU,KAAK,IAAI;oBACjB,IAAI,cAAc;oBAClB,MAAM;oBACN,UAAU;sBACR,MAAM,cAAc,SAAS;sBAC7B,YAAW,KAAA,cAAc,SAAS,cAAvB,OAAA,KAAoC;oBACjD;oBACA,aAAa;kBACf;AAEA,wBAAMM,YAAW,UAAU,KAAK;AAEhC,wBACE,KAAAA,UAAS,aAAT,OAAA,SAAA,GAAmB,SAAQ,UAC3B,KAAAA,UAAS,aAAT,OAAA,SAAA,GAAmB,cAAa,MAChC;AAEA,wBAAIA,UAAS,SAAS,UAAU,SAAS,GAAG;AAC1C,iCAAW,QAAQ;wBACjB,MAAM;wBACN,cAAc;wBACd,YAAYA,UAAS;wBACrB,UAAUA,UAAS,SAAS;wBAC5B,eAAeA,UAAS,SAAS;sBACnC,CAAC;oBACH;AAIA,wBAAI,eAAeA,UAAS,SAAS,SAAS,GAAG;AAC/C,iCAAW,QAAQ;wBACjB,MAAM;wBACN,cAAc;wBACd,aAAY,KAAAA,UAAS,OAAT,OAAA,KAAe,WAAW;wBACtC,UAAUA,UAAS,SAAS;wBAC5B,MAAMA,UAAS,SAAS;sBAC1B,CAAC;AACDA,gCAAS,cAAc;oBACzB;kBACF;AAEA;gBACF;AAGA,sBAAM,WAAW,UAAU,KAAK;AAEhC,oBAAI,SAAS,aAAa;AACxB;gBACF;AAEA,sBAAI,KAAA,cAAc,aAAd,OAAA,SAAA,GAAwB,cAAa,MAAM;AAC7C,2BAAS,SAAU,cACjB,MAAA,KAAA,cAAc,aAAd,OAAA,SAAA,GAAwB,cAAxB,OAAA,KAAqC;gBACzC;AAGA,2BAAW,QAAQ;kBACjB,MAAM;kBACN,cAAc;kBACd,YAAY,SAAS;kBACrB,UAAU,SAAS,SAAS;kBAC5B,gBAAe,KAAA,cAAc,SAAS,cAAvB,OAAA,KAAoC;gBACrD,CAAC;AAGD,sBACE,KAAA,SAAS,aAAT,OAAA,SAAA,GAAmB,SAAQ,UAC3B,KAAA,SAAS,aAAT,OAAA,SAAA,GAAmB,cAAa,QAChC,eAAe,SAAS,SAAS,SAAS,GAC1C;AACA,6BAAW,QAAQ;oBACjB,MAAM;oBACN,cAAc;oBACd,aAAY,KAAA,SAAS,OAAT,OAAA,KAAe,WAAW;oBACtC,UAAU,SAAS,SAAS;oBAC5B,MAAM,SAAS,SAAS;kBAC1B,CAAC;AACD,2BAAS,cAAc;gBACzB;cACF;YACF;UACF;UAEA,MAAM,YAAY;AAnwB5B,gBAAAN,MAAA;AAowBY,uBAAW,QAAQ;cACjB,MAAM;cACN;cACA;cACA,OAAO;gBACL,eAAcA,OAAA,MAAM,iBAAN,OAAAA,OAAsB;gBACpC,mBAAkB,KAAA,MAAM,qBAAN,OAAA,KAA0B;cAC9C;cACA,GAAI,oBAAoB,OAAO,EAAE,iBAAiB,IAAI,CAAC;YACzD,CAAC;UACH;QACF,CAAC;MACH;MACA,SAAS,EAAE,WAAW,YAAY;MAClC,aAAa,EAAE,SAAS,gBAAgB;MACxC,SAAS,EAAE,MAAM,KAAK,UAAU,IAAI,EAAE;MACtC;IACF;EACF;AACF;AAEA,IAAM,yBAAyBO,iBAC5B,OAAO;EACN,eAAeA,iBAAE,OAAO,EAAE,QAAQ;EAClC,mBAAmBA,iBAAE,OAAO,EAAE,QAAQ;EACtC,uBAAuBA,iBACpB,OAAO;IACN,eAAeA,iBAAE,OAAO,EAAE,QAAQ;EACpC,CAAC,EACA,QAAQ;EACX,2BAA2BA,iBACxB,OAAO;IACN,kBAAkBA,iBAAE,OAAO,EAAE,QAAQ;IACrC,4BAA4BA,iBAAE,OAAO,EAAE,QAAQ;IAC/C,4BAA4BA,iBAAE,OAAO,EAAE,QAAQ;EACjD,CAAC,EACA,QAAQ;AACb,CAAC,EACA,QAAQ;AAIX,IAAM,2BAA2BA,iBAAE,OAAO;EACxC,IAAIA,iBAAE,OAAO,EAAE,QAAQ;EACvB,SAASA,iBAAE,OAAO,EAAE,QAAQ;EAC5B,OAAOA,iBAAE,OAAO,EAAE,QAAQ;EAC1B,SAASA,iBAAE;IACTA,iBAAE,OAAO;MACP,SAASA,iBAAE,OAAO;QAChB,MAAMA,iBAAE,QAAQ,WAAW,EAAE,QAAQ;QACrC,SAASA,iBAAE,OAAO,EAAE,QAAQ;QAC5B,eAAeA,iBACZ,OAAO;UACN,WAAWA,iBAAE,OAAO;UACpB,MAAMA,iBAAE,OAAO;QACjB,CAAC,EACA,QAAQ;QACX,YAAYA,iBACT;UACCA,iBAAE,OAAO;YACP,IAAIA,iBAAE,OAAO,EAAE,QAAQ;YACvB,MAAMA,iBAAE,QAAQ,UAAU;YAC1B,UAAUA,iBAAE,OAAO;cACjB,MAAMA,iBAAE,OAAO;cACf,WAAWA,iBAAE,OAAO;YACtB,CAAC;UACH,CAAC;QACH,EACC,QAAQ;MACb,CAAC;MACD,OAAOA,iBAAE,OAAO;MAChB,UAAUA,iBACP,OAAO;QACN,SAASA,iBACN;UACCA,iBAAE,OAAO;YACP,OAAOA,iBAAE,OAAO;YAChB,SAASA,iBAAE,OAAO;YAClB,cAAcA,iBAAE;cACdA,iBAAE,OAAO;gBACP,OAAOA,iBAAE,OAAO;gBAChB,SAASA,iBAAE,OAAO;cACpB,CAAC;YACH;UACF,CAAC;QACH,EACC,SAAS;MACd,CAAC,EACA,QAAQ;MACX,eAAeA,iBAAE,OAAO,EAAE,QAAQ;IACpC,CAAC;EACH;EACA,OAAO;AACT,CAAC;AAID,IAAM,wBAAwBA,iBAAE,MAAM;EACpCA,iBAAE,OAAO;IACP,IAAIA,iBAAE,OAAO,EAAE,QAAQ;IACvB,SAASA,iBAAE,OAAO,EAAE,QAAQ;IAC5B,OAAOA,iBAAE,OAAO,EAAE,QAAQ;IAC1B,SAASA,iBAAE;MACTA,iBAAE,OAAO;QACP,OAAOA,iBACJ,OAAO;UACN,MAAMA,iBAAE,KAAK,CAAC,WAAW,CAAC,EAAE,QAAQ;UACpC,SAASA,iBAAE,OAAO,EAAE,QAAQ;UAC5B,eAAeA,iBACZ,OAAO;YACN,MAAMA,iBAAE,OAAO,EAAE,SAAS;YAC1B,WAAWA,iBAAE,OAAO,EAAE,SAAS;UACjC,CAAC,EACA,QAAQ;UACX,YAAYA,iBACT;YACCA,iBAAE,OAAO;cACP,OAAOA,iBAAE,OAAO;cAChB,IAAIA,iBAAE,OAAO,EAAE,QAAQ;cACvB,MAAMA,iBAAE,QAAQ,UAAU,EAAE,QAAQ;cACpC,UAAUA,iBAAE,OAAO;gBACjB,MAAMA,iBAAE,OAAO,EAAE,QAAQ;gBACzB,WAAWA,iBAAE,OAAO,EAAE,QAAQ;cAChC,CAAC;YACH,CAAC;UACH,EACC,QAAQ;QACb,CAAC,EACA,QAAQ;QACX,UAAUA,iBACP,OAAO;UACN,SAASA,iBACN;YACCA,iBAAE,OAAO;cACP,OAAOA,iBAAE,OAAO;cAChB,SAASA,iBAAE,OAAO;cAClB,cAAcA,iBAAE;gBACdA,iBAAE,OAAO;kBACP,OAAOA,iBAAE,OAAO;kBAChB,SAASA,iBAAE,OAAO;gBACpB,CAAC;cACH;YACF,CAAC;UACH,EACC,SAAS;QACd,CAAC,EACA,QAAQ;QACX,eAAeA,iBAAE,OAAO,EAAE,QAAQ;QAClC,OAAOA,iBAAE,OAAO;MAClB,CAAC;IACH;IACA,OAAO;EACT,CAAC;EACD;AACF,CAAC;AAED,SAAS,iBAAiB,SAAiB;AACzC,SAAO,QAAQ,WAAW,GAAG;AAC/B;AAEA,SAAS,aAAa,SAAiB;AACrC,SAAO,QAAQ,WAAW,sBAAsB;AAClD;AAEA,SAAS,qBAAqB,SAAiB;AAx6B/C,MAAAP,MAAA;AAy6BE,MAAI,CAAC,iBAAiB,OAAO,GAAG;AAC9B,WAAO;EACT;AAEA,UACE,MAAAA,OAAA,gBAAgB,OAAuC,MAAvD,OAAA,SAAAA,KACI,sBADJ,OAAA,KACyB;AAE7B;AAEA,IAAM,kBAAkB;EACtB,WAAW;IACT,mBAAmB;EACrB;EACA,sBAAsB;IACpB,mBAAmB;EACrB;EACA,cAAc;IACZ,mBAAmB;EACrB;EACA,yBAAyB;IACvB,mBAAmB;EACrB;EACA,IAAI;IACF,mBAAmB;EACrB;EACA,iBAAiB;IACf,mBAAmB;EACrB;EACA,WAAW;IACT,mBAAmB;EACrB;EACA,sBAAsB;IACpB,mBAAmB;EACrB;EACA,WAAW;IACT,mBAAmB;EACrB;EACA,sBAAsB;IACpB,mBAAmB;EACrB;AACF;AQ58BO,SAAS,gCAAgC;EAC9C;EACA;EACA,OAAO;EACP,YAAY;AACd,GAQE;AAEA,MACE,gBAAgB,YAChB,OAAO,WAAW,KAClB,OAAO,CAAC,EAAE,SAAS,UACnB,OAAO,CAAC,EAAE,QAAQ,WAAW,KAC7B,OAAO,CAAC,EAAE,QAAQ,CAAC,EAAE,SAAS,QAC9B;AACA,WAAO,EAAE,QAAQ,OAAO,CAAC,EAAE,QAAQ,CAAC,EAAE,KAAK;EAC7C;AAGA,MAAI,OAAO;AAGX,MAAI,OAAO,CAAC,EAAE,SAAS,UAAU;AAC/B,YAAQ,GAAG,OAAO,CAAC,EAAE,OAAO;;;AAC5B,aAAS,OAAO,MAAM,CAAC;EACzB;AAEA,aAAW,EAAE,MAAM,QAAQ,KAAK,QAAQ;AACtC,YAAQ,MAAM;MACZ,KAAK,UAAU;AACb,cAAM,IAAI,mBAAmB;UAC3B,SAAS;UACT;QACF,CAAC;MACH;MAEA,KAAK,QAAQ;AACX,cAAM,cAAc,QACjB,IAAI,CAAA,SAAQ;AACX,kBAAQ,KAAK,MAAM;YACjB,KAAK,QAAQ;AACX,qBAAO,KAAK;YACd;YACA,KAAK,SAAS;AACZ,oBAAM,IAAII,8BAA8B;gBACtC,eAAe;cACjB,CAAC;YACH;UACF;QACF,CAAC,EACA,KAAK,EAAE;AAEV,gBAAQ,GAAG,IAAI;EAAM,WAAW;;;AAChC;MACF;MAEA,KAAK,aAAa;AAChB,cAAM,mBAAmB,QACtB,IAAI,CAAA,SAAQ;AACX,kBAAQ,KAAK,MAAM;YACjB,KAAK,QAAQ;AACX,qBAAO,KAAK;YACd;YACA,KAAK,aAAa;AAChB,oBAAM,IAAIA,8BAA8B;gBACtC,eAAe;cACjB,CAAC;YACH;UACF;QACF,CAAC,EACA,KAAK,EAAE;AAEV,gBAAQ,GAAG,SAAS;EAAM,gBAAgB;;;AAC1C;MACF;MAEA,KAAK,QAAQ;AACX,cAAM,IAAIA,8BAA8B;UACtC,eAAe;QACjB,CAAC;MACH;MAEA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;MACzD;IACF;EACF;AAGA,UAAQ,GAAG,SAAS;;AAEpB,SAAO;IACL,QAAQ;IACR,eAAe,CAAC;EAAK,IAAI,GAAG;EAC9B;AACF;ACrGO,SAAS,4BACd,UACqC;AACrC,SAAO,YAAA,OAAA,SAAA,SAAU,OAAO,IAAI,CAAC,OAAO,WAAW;IAC7C;IACA,SAAS,SAAS,eAAe,KAAK;IACtC,aAAa,SAAS,eAClB,OAAO,QAAQ,SAAS,aAAa,KAAK,CAAC,EAAE;MAC3C,CAAC,CAACH,QAAO,OAAO,OAAO;QACrB,OAAAA;QACA;MACF;IACF,IACA,CAAC;EACP,EAAA;AACF;AFeO,IAAM,gCAAN,MAA+D;EASpE,YACE,SACA,UACA,QACA;AAZF,SAAS,uBAAuB;AAChC,SAAS,8BAA8B;AAYrC,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,SAAS;EAChB;EAEA,IAAI,WAAmB;AACrB,WAAO,KAAK,OAAO;EACrB;EAEQ,QAAQ;IACd;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,eAAe;IACf;IACA;EACF,GAAiD;AA1EnD,QAAAD;AA2EI,UAAM,OAAO,KAAK;AAElB,UAAM,WAAyC,CAAC;AAEhD,QAAI,QAAQ,MAAM;AAChB,eAAS,KAAK;QACZ,MAAM;QACN,SAAS;MACX,CAAC;IACH;AAEA,QAAI,kBAAkB,QAAQ,eAAe,SAAS,QAAQ;AAC5D,eAAS,KAAK;QACZ,MAAM;QACN,SAAS;QACT,SAAS;MACX,CAAC;IACH;AAEA,UAAM,EAAE,QAAQ,kBAAkB,cAAc,IAC9C,gCAAgC,EAAE,QAAQ,YAAY,CAAC;AAEzD,UAAM,OAAO,CAAC,GAAI,iBAAA,OAAA,gBAAiB,CAAC,GAAI,GAAI,qBAAA,OAAA,oBAAqB,CAAC,CAAE;AAEpE,UAAM,WAAW;;MAEf,OAAO,KAAK;;MAGZ,MAAM,KAAK,SAAS;MACpB,YAAY,KAAK,SAAS;MAC1B,UACE,OAAO,KAAK,SAAS,aAAa,WAC9B,KAAK,SAAS,WACd,OAAO,KAAK,SAAS,aAAa,YAChC,KAAK,SAAS,WACZ,IACA,SACF;MACR,QAAQ,KAAK,SAAS;MACtB,MAAM,KAAK,SAAS;;MAGpB,YAAY;MACZ;MACA,OAAO;MACP,mBAAmB;MACnB,kBAAkB;MAClB;;MAGA,QAAQ;;MAGR,MAAM,KAAK,SAAS,IAAI,OAAO;IACjC;AAEA,YAAQ,MAAM;MACZ,KAAK,WAAW;AACd,aAAIA,OAAA,KAAK,UAAL,OAAA,SAAAA,KAAY,QAAQ;AACtB,gBAAM,IAAII,8BAA8B;YACtC,eAAe;UACjB,CAAC;QACH;AAEA,YAAI,KAAK,YAAY;AACnB,gBAAM,IAAIA,8BAA8B;YACtC,eAAe;UACjB,CAAC;QACH;AAEA,eAAO,EAAE,MAAM,UAAU,SAAS;MACpC;MAEA,KAAK,eAAe;AAClB,cAAM,IAAIA,8BAA8B;UACtC,eAAe;QACjB,CAAC;MACH;MAEA,KAAK,eAAe;AAClB,cAAM,IAAIA,8BAA8B;UACtC,eAAe;QACjB,CAAC;MACH;MAEA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;MACzD;IACF;EACF;EAEA,MAAM,WACJ,SAC6D;AAC7D,UAAM,EAAE,MAAM,SAAS,IAAI,KAAK,QAAQ,OAAO;AAE/C,UAAM;MACJ;MACA,OAAO;MACP,UAAU;IACZ,IAAI,MAAMI,cAAc;MACtB,KAAK,KAAK,OAAO,IAAI;QACnB,MAAM;QACN,SAAS,KAAK;MAChB,CAAC;MACD,SAASC,eAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;MAC9D,MAAM;MACN,uBAAuB;MACvB,2BAA2BC;QACzB;MACF;MACA,aAAa,QAAQ;MACrB,OAAO,KAAK,OAAO;IACrB,CAAC;AAED,UAAM,EAAE,QAAQ,WAAW,GAAG,YAAY,IAAI;AAC9C,UAAM,SAAS,SAAS,QAAQ,CAAC;AAEjC,WAAO;MACL,MAAM,OAAO;MACb,OAAO;QACL,cAAc,SAAS,MAAM;QAC7B,kBAAkB,SAAS,MAAM;MACnC;MACA,cAAc,sBAAsB,OAAO,aAAa;MACxD,UAAU,4BAA4B,OAAO,QAAQ;MACrD,SAAS,EAAE,WAAW,YAAY;MAClC,aAAa,EAAE,SAAS,iBAAiB,MAAM,YAAY;MAC3D,UAAU,oBAAoB,QAAQ;MACtC;MACA,SAAS,EAAE,MAAM,KAAK,UAAU,IAAI,EAAE;IACxC;EACF;EAEA,MAAM,SACJ,SAC2D;AAC3D,UAAM,EAAE,MAAM,SAAS,IAAI,KAAK,QAAQ,OAAO;AAE/C,UAAM,OAAO;MACX,GAAG;MACH,QAAQ;;MAGR,gBACE,KAAK,OAAO,kBAAkB,WAC1B,EAAE,eAAe,KAAK,IACtB;IACR;AAEA,UAAM,EAAE,iBAAiB,OAAO,SAAS,IAAI,MAAMF,cAAc;MAC/D,KAAK,KAAK,OAAO,IAAI;QACnB,MAAM;QACN,SAAS,KAAK;MAChB,CAAC;MACD,SAASC,eAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;MAC9D;MACA,uBAAuB;MACvB,2BAA2BE;QACzB;MACF;MACA,aAAa,QAAQ;MACrB,OAAO,KAAK,OAAO;IACrB,CAAC;AAED,UAAM,EAAE,QAAQ,WAAW,GAAG,YAAY,IAAI;AAE9C,QAAI,eAA4C;AAChD,QAAI,QAA4D;MAC9D,cAAc,OAAO;MACrB,kBAAkB,OAAO;IAC3B;AACA,QAAI;AACJ,QAAI,eAAe;AAEnB,WAAO;MACL,QAAQ,SAAS;QACf,IAAI,gBAGF;UACA,UAAU,OAAO,YAAY;AAE3B,gBAAI,CAAC,MAAM,SAAS;AAClB,6BAAe;AACf,yBAAW,QAAQ,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,CAAC;AACxD;YACF;AAEA,kBAAM,QAAQ,MAAM;AAGpB,gBAAI,WAAW,OAAO;AACpB,6BAAe;AACf,yBAAW,QAAQ,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,CAAC;AACxD;YACF;AAEA,gBAAI,cAAc;AAChB,6BAAe;AAEf,yBAAW,QAAQ;gBACjB,MAAM;gBACN,GAAG,oBAAoB,KAAK;cAC9B,CAAC;YACH;AAEA,gBAAI,MAAM,SAAS,MAAM;AACvB,sBAAQ;gBACN,cAAc,MAAM,MAAM;gBAC1B,kBAAkB,MAAM,MAAM;cAChC;YACF;AAEA,kBAAM,SAAS,MAAM,QAAQ,CAAC;AAE9B,iBAAI,UAAA,OAAA,SAAA,OAAQ,kBAAiB,MAAM;AACjC,6BAAe,sBAAsB,OAAO,aAAa;YAC3D;AAEA,iBAAI,UAAA,OAAA,SAAA,OAAQ,SAAQ,MAAM;AACxB,yBAAW,QAAQ;gBACjB,MAAM;gBACN,WAAW,OAAO;cACpB,CAAC;YACH;AAEA,kBAAM,iBAAiB;cACrB,UAAA,OAAA,SAAA,OAAQ;YACV;AACA,gBAAI,kBAAA,OAAA,SAAA,eAAgB,QAAQ;AAC1B,kBAAI,aAAa,OAAW,YAAW,CAAC;AACxC,uBAAS,KAAK,GAAG,cAAc;YACjC;UACF;UAEA,MAAM,YAAY;AAChB,uBAAW,QAAQ;cACjB,MAAM;cACN;cACA;cACA;YACF,CAAC;UACH;QACF,CAAC;MACH;MACA,SAAS,EAAE,WAAW,YAAY;MAClC,aAAa,EAAE,SAAS,gBAAgB;MACxC;MACA,SAAS,EAAE,MAAM,KAAK,UAAU,IAAI,EAAE;IACxC;EACF;AACF;AAIA,IAAM,iCAAiCJ,iBAAE,OAAO;EAC9C,IAAIA,iBAAE,OAAO,EAAE,QAAQ;EACvB,SAASA,iBAAE,OAAO,EAAE,QAAQ;EAC5B,OAAOA,iBAAE,OAAO,EAAE,QAAQ;EAC1B,SAASA,iBAAE;IACTA,iBAAE,OAAO;MACP,MAAMA,iBAAE,OAAO;MACf,eAAeA,iBAAE,OAAO;MACxB,UAAUA,iBACP,OAAO;QACN,QAAQA,iBAAE,MAAMA,iBAAE,OAAO,CAAC;QAC1B,gBAAgBA,iBAAE,MAAMA,iBAAE,OAAO,CAAC;QAClC,cAAcA,iBAAE,MAAMA,iBAAE,OAAOA,iBAAE,OAAO,GAAGA,iBAAE,OAAO,CAAC,CAAC,EAAE,SAAS;MACnE,CAAC,EACA,QAAQ;IACb,CAAC;EACH;EACA,OAAOA,iBAAE,OAAO;IACd,eAAeA,iBAAE,OAAO;IACxB,mBAAmBA,iBAAE,OAAO;EAC9B,CAAC;AACH,CAAC;AAID,IAAM,8BAA8BA,iBAAE,MAAM;EAC1CA,iBAAE,OAAO;IACP,IAAIA,iBAAE,OAAO,EAAE,QAAQ;IACvB,SAASA,iBAAE,OAAO,EAAE,QAAQ;IAC5B,OAAOA,iBAAE,OAAO,EAAE,QAAQ;IAC1B,SAASA,iBAAE;MACTA,iBAAE,OAAO;QACP,MAAMA,iBAAE,OAAO;QACf,eAAeA,iBAAE,OAAO,EAAE,QAAQ;QAClC,OAAOA,iBAAE,OAAO;QAChB,UAAUA,iBACP,OAAO;UACN,QAAQA,iBAAE,MAAMA,iBAAE,OAAO,CAAC;UAC1B,gBAAgBA,iBAAE,MAAMA,iBAAE,OAAO,CAAC;UAClC,cAAcA,iBAAE,MAAMA,iBAAE,OAAOA,iBAAE,OAAO,GAAGA,iBAAE,OAAO,CAAC,CAAC,EAAE,SAAS;QACnE,CAAC,EACA,QAAQ;MACb,CAAC;IACH;IACA,OAAOA,iBACJ,OAAO;MACN,eAAeA,iBAAE,OAAO;MACxB,mBAAmBA,iBAAE,OAAO;IAC9B,CAAC,EACA,QAAQ;EACb,CAAC;EACD;AACF,CAAC;AGhXM,IAAM,uBAAN,MAA+D;EAmBpE,YACE,SACA,UACA,QACA;AAtBF,SAAS,uBAAuB;AAuB9B,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,SAAS;EAChB;EApBA,IAAI,WAAmB;AACrB,WAAO,KAAK,OAAO;EACrB;EAEA,IAAI,uBAA+B;AA5BrC,QAAAP;AA6BI,YAAOA,OAAA,KAAK,SAAS,yBAAd,OAAAA,OAAsC;EAC/C;EAEA,IAAI,wBAAiC;AAhCvC,QAAAA;AAiCI,YAAOA,OAAA,KAAK,SAAS,0BAAd,OAAAA,OAAuC;EAChD;EAYA,MAAM,QAAQ;IACZ;IACA;IACA;EACF,GAEE;AACA,QAAI,OAAO,SAAS,KAAK,sBAAsB;AAC7C,YAAM,IAAI,mCAAmC;QAC3C,UAAU,KAAK;QACf,SAAS,KAAK;QACd,sBAAsB,KAAK;QAC3B;MACF,CAAC;IACH;AAEA,UAAM,EAAE,iBAAiB,OAAO,SAAS,IAAI,MAAMQ,cAAc;MAC/D,KAAK,KAAK,OAAO,IAAI;QACnB,MAAM;QACN,SAAS,KAAK;MAChB,CAAC;MACD,SAASC,eAAe,KAAK,OAAO,QAAQ,GAAG,OAAO;MACtD,MAAM;QACJ,OAAO,KAAK;QACZ,OAAO;QACP,iBAAiB;QACjB,YAAY,KAAK,SAAS;QAC1B,MAAM,KAAK,SAAS;MACtB;MACA,uBAAuB;MACvB,2BAA2BC;QACzB;MACF;MACA;MACA,OAAO,KAAK,OAAO;IACrB,CAAC;AAED,WAAO;MACL,YAAY,SAAS,KAAK,IAAI,CAAA,SAAQ,KAAK,SAAS;MACpD,OAAO,SAAS,QACZ,EAAE,QAAQ,SAAS,MAAM,cAAc,IACvC;MACJ,aAAa,EAAE,SAAS,gBAAgB;IAC1C;EACF;AACF;AAIA,IAAM,oCAAoCH,iBAAE,OAAO;EACjD,MAAMA,iBAAE,MAAMA,iBAAE,OAAO,EAAE,WAAWA,iBAAE,MAAMA,iBAAE,OAAO,CAAC,EAAE,CAAC,CAAC;EAC1D,OAAOA,iBAAE,OAAO,EAAE,eAAeA,iBAAE,OAAO,EAAE,CAAC,EAAE,QAAQ;AACzD,CAAC;AE3FM,IAAM,wBAA4D;EACvE,YAAY;EACZ,YAAY;EACZ,eAAe;AACjB;AAEO,IAAM,2BAA2B,oBAAI,IAAI,CAAC,aAAa,CAAC;ADSxD,IAAM,mBAAN,MAA+C;EAapD,YACW,SACQ,UACA,QACjB;AAHS,SAAA,UAAA;AACQ,SAAA,WAAA;AACA,SAAA,SAAA;AAfnB,SAAS,uBAAuB;EAgB7B;EAdH,IAAI,mBAA2B;AAzBjC,QAAAP,MAAA;AA0BI,YACE,MAAAA,OAAA,KAAK,SAAS,qBAAd,OAAAA,OAAkC,sBAAsB,KAAK,OAAO,MAApE,OAAA,KAAyE;EAE7E;EAEA,IAAI,WAAmB;AACrB,WAAO,KAAK,OAAO;EACrB;EAQA,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF,GAEE;AApDJ,QAAAA,MAAA,IAAA,IAAA;AAqDI,UAAM,WAA2C,CAAC;AAElD,QAAI,eAAe,MAAM;AACvB,eAAS,KAAK;QACZ,MAAM;QACN,SAAS;QACT,SACE;MACJ,CAAC;IACH;AAEA,QAAI,QAAQ,MAAM;AAChB,eAAS,KAAK,EAAE,MAAM,uBAAuB,SAAS,OAAO,CAAC;IAChE;AAEA,UAAM,eAAc,MAAA,MAAAA,OAAA,KAAK,OAAO,cAAZ,OAAA,SAAAA,KAAuB,gBAAvB,OAAA,SAAA,GAAA,KAAAA,IAAA,MAAA,OAAA,KAA0C,oBAAI,KAAK;AACvE,UAAM,EAAE,OAAO,UAAU,gBAAgB,IAAI,MAAMQ,cAAc;MAC/D,KAAK,KAAK,OAAO,IAAI;QACnB,MAAM;QACN,SAAS,KAAK;MAChB,CAAC;MACD,SAASC,eAAe,KAAK,OAAO,QAAQ,GAAG,OAAO;MACtD,MAAM;QACJ,OAAO,KAAK;QACZ;QACA;QACA;QACA,IAAI,KAAA,gBAAgB,WAAhB,OAAA,KAA0B,CAAC;QAC/B,GAAI,CAAC,yBAAyB,IAAI,KAAK,OAAO,IAC1C,EAAE,iBAAiB,WAAW,IAC9B,CAAC;MACP;MACA,uBAAuB;MACvB,2BAA2BC;QACzB;MACF;MACA;MACA,OAAO,KAAK,OAAO;IACrB,CAAC;AAED,WAAO;MACL,QAAQ,SAAS,KAAK,IAAI,CAAA,SAAQ,KAAK,QAAQ;MAC/C;MACA,UAAU;QACR,WAAW;QACX,SAAS,KAAK;QACd,SAAS;MACX;IACF;EACF;AACF;AAIA,IAAM,4BAA4BH,iBAAE,OAAO;EACzC,MAAMA,iBAAE,MAAMA,iBAAE,OAAO,EAAE,UAAUA,iBAAE,OAAO,EAAE,CAAC,CAAC;AAClD,CAAC;AExFD,IAAM,8BAA8BA,iBAAE,OAAO;EAC3C,SAASA,iBAAE,MAAMA,iBAAE,OAAO,CAAC,EAAE,QAAQ;EACrC,UAAUA,iBAAE,OAAO,EAAE,QAAQ;EAC7B,QAAQA,iBAAE,OAAO,EAAE,QAAQ;EAC3B,aAAaA,iBAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACzD,wBAAwBA,iBACrB,MAAMA,iBAAE,KAAK,CAAC,QAAQ,SAAS,CAAC,CAAC,EACjC,QAAQ,EACR,QAAQ,CAAC,SAAS,CAAC;AACxB,CAAC;AAkBD,IAAM,cAAc;EAClB,WAAW;EACX,QAAQ;EACR,UAAU;EACV,aAAa;EACb,YAAY;EACZ,SAAS;EACT,WAAW;EACX,SAAS;EACT,SAAS;EACT,UAAU;EACV,OAAO;EACP,QAAQ;EACR,OAAO;EACP,SAAS;EACT,UAAU;EACV,SAAS;EACT,QAAQ;EACR,UAAU;EACV,QAAQ;EACR,OAAO;EACP,QAAQ;EACR,OAAO;EACP,WAAW;EACX,WAAW;EACX,YAAY;EACZ,SAAS;EACT,UAAU;EACV,SAAS;EACT,QAAQ;EACR,QAAQ;EACR,SAAS;EACT,YAAY;EACZ,YAAY;EACZ,OAAO;EACP,SAAS;EACT,OAAO;EACP,QAAQ;EACR,WAAW;EACX,SAAS;EACT,QAAQ;EACR,YAAY;EACZ,UAAU;EACV,SAAS;EACT,SAAS;EACT,QAAQ;EACR,WAAW;EACX,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,OAAO;EACP,MAAM;EACN,SAAS;EACT,WAAW;EACX,MAAM;EACN,YAAY;EACZ,OAAO;AACT;AAEO,IAAM,2BAAN,MAA+D;EAOpE,YACW,SACQ,QACjB;AAFS,SAAA,UAAA;AACQ,SAAA,SAAA;AARnB,SAAS,uBAAuB;EAS7B;EAPH,IAAI,WAAmB;AACrB,WAAO,KAAK,OAAO;EACrB;EAOQ,QAAQ;IACd;IACA;IACA;EACF,GAAmC;AA5HrC,QAAAP,MAAA,IAAA,IAAA,IAAA;AA6HI,UAAM,WAA8C,CAAC;AAGrD,UAAM,gBAAgB,qBAAqB;MACzC,UAAU;MACV;MACA,QAAQ;IACV,CAAC;AAGD,UAAM,WAAW,IAAI,SAAS;AAC9B,UAAM,OACJ,iBAAiB,aACb,IAAI,KAAK,CAAC,KAAK,CAAC,IAChB,IAAI,KAAK,CAAC,0BAA0B,KAAK,CAAC,CAAC;AAEjD,aAAS,OAAO,SAAS,KAAK,OAAO;AACrC,aAAS,OAAO,QAAQ,IAAI,KAAK,CAAC,IAAI,GAAG,SAAS,EAAE,MAAM,UAAU,CAAC,CAAC;AAGtE,QAAI,eAAe;AACjB,YAAM,4BAA6D;QACjE,UAASA,OAAA,cAAc,YAAd,OAAAA,OAAyB;QAClC,WAAU,KAAA,cAAc,aAAd,OAAA,KAA0B;QACpC,SAAQ,KAAA,cAAc,WAAd,OAAA,KAAwB;QAChC,cAAa,KAAA,cAAc,gBAAd,OAAA,KAA6B;QAC1C,0BACE,KAAA,cAAc,2BAAd,OAAA,KAAwC;MAC5C;AAEA,iBAAW,OAAO,2BAA2B;AAC3C,cAAM,QACJ,0BACE,GACF;AACF,YAAI,UAAU,QAAW;AACvB,mBAAS,OAAO,KAAK,OAAO,KAAK,CAAC;QACpC;MACF;IACF;AAEA,WAAO;MACL;MACA;IACF;EACF;EAEA,MAAM,WACJ,SACkE;AA9KtE,QAAAA,MAAA,IAAA,IAAA,IAAA,IAAA;AA+KI,UAAM,eAAc,MAAA,MAAAA,OAAA,KAAK,OAAO,cAAZ,OAAA,SAAAA,KAAuB,gBAAvB,OAAA,SAAA,GAAA,KAAAA,IAAA,MAAA,OAAA,KAA0C,oBAAI,KAAK;AACvE,UAAM,EAAE,UAAU,SAAS,IAAI,KAAK,QAAQ,OAAO;AAEnD,UAAM;MACJ,OAAO;MACP;MACA,UAAU;IACZ,IAAI,MAAM,kBAAkB;MAC1B,KAAK,KAAK,OAAO,IAAI;QACnB,MAAM;QACN,SAAS,KAAK;MAChB,CAAC;MACD,SAASS,eAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;MAC9D;MACA,uBAAuB;MACvB,2BAA2BC;QACzB;MACF;MACA,aAAa,QAAQ;MACrB,OAAO,KAAK,OAAO;IACrB,CAAC;AAED,UAAM,WACJ,SAAS,YAAY,QAAQ,SAAS,YAAY,cAC9C,YAAY,SAAS,QAAoC,IACzD;AAEN,WAAO;MACL,MAAM,SAAS;MACf,WACE,MAAA,KAAA,SAAS,UAAT,OAAA,SAAA,GAAgB,IAAI,CAAA,UAAS;QAC3B,MAAM,KAAK;QACX,aAAa,KAAK;QAClB,WAAW,KAAK;MAClB,EAAA,MAJA,OAAA,KAIO,CAAC;MACV;MACA,oBAAmB,KAAA,SAAS,aAAT,OAAA,KAAqB;MACxC;MACA,UAAU;QACR,WAAW;QACX,SAAS,KAAK;QACd,SAAS;QACT,MAAM;MACR;IACF;EACF;AACF;AAEA,IAAM,oCAAoCH,iBAAE,OAAO;EACjD,MAAMA,iBAAE,OAAO;EACf,UAAUA,iBAAE,OAAO,EAAE,QAAQ;EAC7B,UAAUA,iBAAE,OAAO,EAAE,QAAQ;EAC7B,OAAOA,iBACJ;IACCA,iBAAE,OAAO;MACP,MAAMA,iBAAE,OAAO;MACf,OAAOA,iBAAE,OAAO;MAChB,KAAKA,iBAAE,OAAO;IAChB,CAAC;EACH,EACC,QAAQ;AACb,CAAC;AEpOM,SAAS,iCAAiC;EAC/C;EACA;AACF,GAME;AACA,QAAM,WAAkC,CAAC;AACzC,QAAM,WAA8C,CAAC;AAErD,aAAW,EAAE,MAAM,QAAQ,KAAK,QAAQ;AACtC,YAAQ,MAAM;MACZ,KAAK,UAAU;AACb,gBAAQ,mBAAmB;UACzB,KAAK,UAAU;AACb,qBAAS,KAAK,EAAE,MAAM,UAAU,QAAQ,CAAC;AACzC;UACF;UACA,KAAK,aAAa;AAChB,qBAAS,KAAK,EAAE,MAAM,aAAa,QAAQ,CAAC;AAC5C;UACF;UACA,KAAK,UAAU;AACb,qBAAS,KAAK;cACZ,MAAM;cACN,SAAS;YACX,CAAC;AACD;UACF;UACA,SAAS;AACP,kBAAM,mBAA0B;AAChC,kBAAM,IAAI;cACR,oCAAoC,gBAAgB;YACtD;UACF;QACF;AACA;MACF;MAEA,KAAK,QAAQ;AACX,iBAAS,KAAK;UACZ,MAAM;UACN,SAAS,QAAQ,IAAI,CAAC,MAAM,UAAU;AArDhD,gBAAAP,MAAA,IAAA,IAAA;AAsDY,oBAAQ,KAAK,MAAM;cACjB,KAAK,QAAQ;AACX,uBAAO,EAAE,MAAM,cAAc,MAAM,KAAK,KAAK;cAC/C;cACA,KAAK,SAAS;AACZ,uBAAO;kBACL,MAAM;kBACN,WACE,KAAK,iBAAiB,MAClB,KAAK,MAAM,SAAS,IACpB,SACEA,OAAA,KAAK,aAAL,OAAAA,OAAiB,YACnB,WAAWY,0BAA0B,KAAK,KAAK,CAAC;;kBAGtD,SAAQ,MAAA,KAAA,KAAK,qBAAL,OAAA,SAAA,GAAuB,WAAvB,OAAA,SAAA,GAA+B;gBACzC;cACF;cACA,KAAK,QAAQ;AACX,oBAAI,KAAK,gBAAgB,KAAK;AAE5B,wBAAM,IAAIR,8BAA8B;oBACtC,eAAe;kBACjB,CAAC;gBACH;AAEA,wBAAQ,KAAK,UAAU;kBACrB,KAAK,mBAAmB;AACtB,2BAAO;sBACL,MAAM;sBACN,WAAU,KAAA,KAAK,aAAL,OAAA,KAAiB,QAAQ,KAAK;sBACxC,WAAW,+BAA+B,KAAK,IAAI;oBACrD;kBACF;kBACA,SAAS;AACP,0BAAM,IAAIA,8BAA8B;sBACtC,eACE;oBACJ,CAAC;kBACH;gBACF;cACF;YACF;UACF,CAAC;QACH,CAAC;AAED;MACF;MAEA,KAAK,aAAa;AAChB,mBAAW,QAAQ,SAAS;AAC1B,kBAAQ,KAAK,MAAM;YACjB,KAAK,QAAQ;AACX,uBAAS,KAAK;gBACZ,MAAM;gBACN,SAAS,CAAC,EAAE,MAAM,eAAe,MAAM,KAAK,KAAK,CAAC;cACpD,CAAC;AACD;YACF;YACA,KAAK,aAAa;AAChB,uBAAS,KAAK;gBACZ,MAAM;gBACN,SAAS,KAAK;gBACd,MAAM,KAAK;gBACX,WAAW,KAAK,UAAU,KAAK,IAAI;cACrC,CAAC;AACD;YACF;UACF;QACF;AAEA;MACF;MAEA,KAAK,QAAQ;AACX,mBAAW,QAAQ,SAAS;AAC1B,mBAAS,KAAK;YACZ,MAAM;YACN,SAAS,KAAK;YACd,QAAQ,KAAK,UAAU,KAAK,MAAM;UACpC,CAAC;QACH;AAEA;MACF;MAEA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;MACzD;IACF;EACF;AAEA,SAAO,EAAE,UAAU,SAAS;AAC9B;AClJO,SAAS,8BAA8B;EAC5C;EACA;AACF,GAGgC;AAC9B,UAAQ,cAAc;IACpB,KAAK;IACL,KAAK;AACH,aAAO,eAAe,eAAe;IACvC,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT;AACE,aAAO,eAAe,eAAe;EACzC;AACF;ACbO,SAAS,sBAAsB;EACpC;EACA;AACF,GAcE;AAxBF,MAAAJ;AA0BE,QAAM,UAAQA,OAAA,KAAK,UAAL,OAAA,SAAAA,KAAY,UAAS,KAAK,QAAQ;AAEhD,QAAM,eAA6C,CAAC;AAEpD,MAAI,SAAS,MAAM;AACjB,WAAO,EAAE,OAAO,QAAW,aAAa,QAAW,aAAa;EAClE;AAEA,QAAM,aAAa,KAAK;AAExB,QAAMK,eAA0C,CAAC;AAEjD,aAAW,QAAQ,OAAO;AACxB,YAAQ,KAAK,MAAM;MACjB,KAAK;AACHA,qBAAY,KAAK;UACf,MAAM;UACN,MAAM,KAAK;UACX,aAAa,KAAK;UAClB,YAAY,KAAK;UACjB,QAAQ,SAAS,OAAO;QAC1B,CAAC;AACD;MACF,KAAK;AACH,gBAAQ,KAAK,IAAI;UACf,KAAK;AACHA,yBAAY,KAAK;cACf,MAAM;cACN,qBAAqB,KAAK,KAAK;cAI/B,eAAe,KAAK,KAAK;YAK3B,CAAC;AACD;UACF;AACE,yBAAa,KAAK,EAAE,MAAM,oBAAoB,KAAK,CAAC;AACpD;QACJ;AACA;MACF;AACE,qBAAa,KAAK,EAAE,MAAM,oBAAoB,KAAK,CAAC;AACpD;IACJ;EACF;AAEA,MAAI,cAAc,MAAM;AACtB,WAAO,EAAE,OAAOA,cAAa,aAAa,QAAW,aAAa;EACpE;AAEA,QAAM,OAAO,WAAW;AAExB,UAAQ,MAAM;IACZ,KAAK;IACL,KAAK;IACL,KAAK;AACH,aAAO,EAAE,OAAOA,cAAa,aAAa,MAAM,aAAa;IAC/D,KAAK,QAAQ;AACX,UAAI,WAAW,aAAa,sBAAsB;AAChD,eAAO;UACL,OAAOA;UACP,aAAa;YACX,MAAM;UACR;UACA;QACF;MACF;AACA,aAAO;QACL,OAAOA;QACP,aAAa;UACX,MAAM;UACN,MAAM,WAAW;QACnB;QACA;MACF;IACF;IACA,SAAS;AACP,YAAM,mBAA0B;AAChC,YAAM,IAAID,8BAA8B;QACtC,eAAe,iCAAiC,gBAAgB;MAClE,CAAC;IACH;EACF;AACF;AHzFO,IAAM,+BAAN,MAA8D;EASnE,YAAY,SAAiC,QAAsB;AARnE,SAAS,uBAAuB;AAChC,SAAS,8BAA8B;AACvC,SAAS,4BAA4B;AAOnC,SAAK,UAAU;AACf,SAAK,SAAS;EAChB;EAEA,IAAI,WAAmB;AACrB,WAAO,KAAK,OAAO;EACrB;EAEQ,QAAQ;IACd;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF,GAAiD;AAvDnD,QAAAJ,MAAA,IAAA;AAwDI,UAAM,WAAyC,CAAC;AAChD,UAAM,cAAc,wBAAwB,KAAK,OAAO;AACxD,UAAM,OAAO,KAAK;AAElB,QAAI,QAAQ,MAAM;AAChB,eAAS,KAAK;QACZ,MAAM;QACN,SAAS;MACX,CAAC;IACH;AAEA,QAAI,QAAQ,MAAM;AAChB,eAAS,KAAK;QACZ,MAAM;QACN,SAAS;MACX,CAAC;IACH;AAEA,QAAI,mBAAmB,MAAM;AAC3B,eAAS,KAAK;QACZ,MAAM;QACN,SAAS;MACX,CAAC;IACH;AAEA,QAAI,oBAAoB,MAAM;AAC5B,eAAS,KAAK;QACZ,MAAM;QACN,SAAS;MACX,CAAC;IACH;AAEA,QAAI,iBAAiB,MAAM;AACzB,eAAS,KAAK;QACZ,MAAM;QACN,SAAS;MACX,CAAC;IACH;AAEA,UAAM,EAAE,UAAU,UAAU,gBAAgB,IAC1C,iCAAiC;MAC/B;MACA,mBAAmB,YAAY;IACjC,CAAC;AAEH,aAAS,KAAK,GAAG,eAAe;AAEhC,UAAM,gBAAgBa,qBAAqB;MACzC,UAAU;MACV,iBAAiB;MACjB,QAAQ;IACV,CAAC;AAED,UAAM,YAAWb,OAAA,iBAAA,OAAA,SAAA,cAAe,kBAAf,OAAAA,OAAgC;AAEjD,UAAM,WAAW;MACf,OAAO,KAAK;MACZ,OAAO;MACP;MACA,OAAO;MACP,mBAAmB;MAEnB,IAAI,kBAAA,OAAA,SAAA,eAAgB,UAAS,UAAU;QACrC,MAAM;UACJ,QACE,eAAe,UAAU,OACrB;YACE,MAAM;YACN,QAAQ;YACR,OAAM,KAAA,eAAe,SAAf,OAAA,KAAuB;YAC7B,aAAa,eAAe;YAC5B,QAAQ,eAAe;UACzB,IACA,EAAE,MAAM,cAAc;QAC9B;MACF;;MAGA,UAAU,iBAAA,OAAA,SAAA,cAAe;MACzB,qBAAqB,iBAAA,OAAA,SAAA,cAAe;MACpC,sBAAsB,iBAAA,OAAA,SAAA,cAAe;MACrC,OAAO,iBAAA,OAAA,SAAA,cAAe;MACtB,MAAM,iBAAA,OAAA,SAAA,cAAe;MACrB,cAAc,iBAAA,OAAA,SAAA,cAAe;;MAG7B,GAAI,YAAY,sBACb,iBAAA,OAAA,SAAA,cAAe,oBAAmB,SACjC,iBAAA,OAAA,SAAA,cAAe,qBAAoB,SAAS;QAC5C,WAAW;UACT,IAAI,iBAAA,OAAA,SAAA,cAAe,oBAAmB,QAAQ;YAC5C,QAAQ,cAAc;UACxB;UACA,IAAI,iBAAA,OAAA,SAAA,cAAe,qBAAoB,QAAQ;YAC7C,SAAS,cAAc;UACzB;QACF;MACF;MACF,GAAI,YAAY,0BAA0B;QACxC,YAAY;MACd;IACF;AAEA,QAAI,YAAY,kBAAkB;AAGhC,UAAI,SAAS,eAAe,MAAM;AAChC,iBAAS,cAAc;AACvB,iBAAS,KAAK;UACZ,MAAM;UACN,SAAS;UACT,SAAS;QACX,CAAC;MACH;AAEA,UAAI,SAAS,SAAS,MAAM;AAC1B,iBAAS,QAAQ;AACjB,iBAAS,KAAK;UACZ,MAAM;UACN,SAAS;UACT,SAAS;QACX,CAAC;MACH;IACF;AAEA,YAAQ,MAAM;MACZ,KAAK,WAAW;AACd,cAAM,EAAE,OAAO,aAAa,aAAa,IAAI,sBAAsB;UACjE;UACA,QAAQ;;QACV,CAAC;AAED,eAAO;UACL,MAAM;YACJ,GAAG;YACH;YACA;UACF;UACA,UAAU,CAAC,GAAG,UAAU,GAAG,YAAY;QACzC;MACF;MAEA,KAAK,eAAe;AAClB,eAAO;UACL,MAAM;YACJ,GAAG;YACH,MAAM;cACJ,QACE,KAAK,UAAU,OACX;gBACE,MAAM;gBACN,QAAQ;gBACR,OAAM,KAAA,KAAK,SAAL,OAAA,KAAa;gBACnB,aAAa,KAAK;gBAClB,QAAQ,KAAK;cACf,IACA,EAAE,MAAM,cAAc;YAC9B;UACF;UACA;QACF;MACF;MAEA,KAAK,eAAe;AAClB,eAAO;UACL,MAAM;YACJ,GAAG;YACH,aAAa,EAAE,MAAM,YAAY,MAAM,KAAK,KAAK,KAAK;YACtD,OAAO;cACL;gBACE,MAAM;gBACN,MAAM,KAAK,KAAK;gBAChB,aAAa,KAAK,KAAK;gBACvB,YAAY,KAAK,KAAK;gBACtB,QAAQ;cACV;YACF;UACF;UACA;QACF;MACF;MAEA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;MACzD;IACF;EACF;EAEA,MAAM,WACJ,SAC6D;AAvPjE,QAAAA,MAAA,IAAA,IAAA,IAAA,IAAA,IAAA;AAwPI,UAAM,EAAE,MAAM,MAAM,SAAS,IAAI,KAAK,QAAQ,OAAO;AACrD,UAAM,MAAM,KAAK,OAAO,IAAI;MAC1B,MAAM;MACN,SAAS,KAAK;IAChB,CAAC;AAED,UAAM;MACJ;MACA,OAAO;MACP,UAAU;IACZ,IAAI,MAAMQ,cAAc;MACtB;MACA,SAASC,eAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;MAC9D;MACA,uBAAuB;MACvB,2BAA2BC;QACzBH,iBAAE,OAAO;UACP,IAAIA,iBAAE,OAAO;UACb,YAAYA,iBAAE,OAAO;UACrB,OAAOA,iBACJ,OAAO;YACN,SAASA,iBAAE,OAAO;YAClB,MAAMA,iBAAE,OAAO;UACjB,CAAC,EACA,QAAQ;UACX,OAAOA,iBAAE,OAAO;UAChB,QAAQA,iBAAE;YACRA,iBAAE,mBAAmB,QAAQ;cAC3BA,iBAAE,OAAO;gBACP,MAAMA,iBAAE,QAAQ,SAAS;gBACzB,MAAMA,iBAAE,QAAQ,WAAW;gBAC3B,SAASA,iBAAE;kBACTA,iBAAE,OAAO;oBACP,MAAMA,iBAAE,QAAQ,aAAa;oBAC7B,MAAMA,iBAAE,OAAO;oBACf,aAAaA,iBAAE;sBACbA,iBAAE,OAAO;wBACP,MAAMA,iBAAE,QAAQ,cAAc;wBAC9B,aAAaA,iBAAE,OAAO;wBACtB,WAAWA,iBAAE,OAAO;wBACpB,KAAKA,iBAAE,OAAO;wBACd,OAAOA,iBAAE,OAAO;sBAClB,CAAC;oBACH;kBACF,CAAC;gBACH;cACF,CAAC;cACDA,iBAAE,OAAO;gBACP,MAAMA,iBAAE,QAAQ,eAAe;gBAC/B,SAASA,iBAAE,OAAO;gBAClB,MAAMA,iBAAE,OAAO;gBACf,WAAWA,iBAAE,OAAO;cACtB,CAAC;cACDA,iBAAE,OAAO;gBACP,MAAMA,iBAAE,QAAQ,iBAAiB;cACnC,CAAC;cACDA,iBAAE,OAAO;gBACP,MAAMA,iBAAE,QAAQ,eAAe;cACjC,CAAC;cACDA,iBAAE,OAAO;gBACP,MAAMA,iBAAE,QAAQ,WAAW;gBAC3B,SAASA,iBAAE;kBACTA,iBAAE,OAAO;oBACP,MAAMA,iBAAE,QAAQ,cAAc;oBAC9B,MAAMA,iBAAE,OAAO;kBACjB,CAAC;gBACH;cACF,CAAC;YACH,CAAC;UACH;UACA,oBAAoBA,iBAAE,OAAO,EAAE,QAAQA,iBAAE,OAAO,EAAE,CAAC,EAAE,SAAS;UAC9D,OAAO;QACT,CAAC;MACH;MACA,aAAa,QAAQ;MACrB,OAAO,KAAK,OAAO;IACrB,CAAC;AAED,QAAI,SAAS,OAAO;AAClB,YAAM,IAAI,aAAa;QACrB,SAAS,SAAS,MAAM;QACxB;QACA,mBAAmB;QACnB,YAAY;QACZ;QACA,cAAc;QACd,aAAa;MACf,CAAC;IACH;AAEA,UAAM,qBAAqB,SAAS,OACjC,OAAO,CAAA,WAAU,OAAO,SAAS,SAAS,EAC1C,QAAQ,CAAA,WAAU,OAAO,OAAO,EAChC,OAAO,CAAA,YAAW,QAAQ,SAAS,aAAa;AAEnD,UAAM,YAAY,SAAS,OACxB,OAAO,CAAA,WAAU,OAAO,SAAS,eAAe,EAChD,IAAI,CAAA,YAAW;MACd,cAAc;MACd,YAAY,OAAO;MACnB,UAAU,OAAO;MACjB,MAAM,OAAO;IACf,EAAE;AAEJ,UAAM,oBACJ,MAAAP,OAAA,SAAS,OAAO,KAAK,CAAA,SAAQ,KAAK,SAAS,WAAW,MAAtD,OAAA,SAAAA,KAAyD,YAAzD,OAAA,KAAoE;AAEtE,WAAO;MACL,MAAM,mBAAmB,IAAI,CAAA,YAAW,QAAQ,IAAI,EAAE,KAAK,IAAI;MAC/D,SAAS,mBAAmB;QAAQ,CAAA,YAClC,QAAQ,YAAY,IAAI,CAAA,eAAW;AAtW3C,cAAAA,MAAAc,KAAAC;AAsW+C,iBAAA;YACrC,YAAY;YACZ,KAAIA,OAAAD,OAAAd,OAAA,KAAK,QAAO,eAAZ,OAAA,SAAAc,IAAA,KAAAd,IAAAA,MAAA,OAAAe,MAA8BC,WAAW;YAC7C,KAAK,WAAW;YAChB,OAAO,WAAW;UACpB;QAAA,CAAE;MACJ;MACA,cAAc,8BAA8B;QAC1C,eAAc,KAAA,SAAS,uBAAT,OAAA,SAAA,GAA6B;QAC3C,cAAc,UAAU,SAAS;MACnC,CAAC;MACD,WAAW,UAAU,SAAS,IAAI,YAAY;MAC9C,WAAW,mBACP,iBAAiB,IAAI,CAAA,aAAY;QAC/B,MAAM;QACN,MAAM,QAAQ;MAChB,EAAE,IACF;MACJ,OAAO;QACL,cAAc,SAAS,MAAM;QAC7B,kBAAkB,SAAS,MAAM;MACnC;MACA,SAAS;QACP,WAAW;QACX,aAAa,CAAC;MAChB;MACA,aAAa;QACX,SAAS;QACT,MAAM;MACR;MACA,SAAS;QACP,MAAM,KAAK,UAAU,IAAI;MAC3B;MACA,UAAU;QACR,IAAI,SAAS;QACb,WAAW,IAAI,KAAK,SAAS,aAAa,GAAI;QAC9C,SAAS,SAAS;MACpB;MACA,kBAAkB;QAChB,QAAQ;UACN,YAAY,SAAS;UACrB,qBACE,MAAA,KAAA,SAAS,MAAM,yBAAf,OAAA,SAAA,GAAqC,kBAArC,OAAA,KAAsD;UACxD,kBACE,MAAA,KAAA,SAAS,MAAM,0BAAf,OAAA,SAAA,GAAsC,qBAAtC,OAAA,KAA0D;QAC9D;MACF;MACA;IACF;EACF;EAEA,MAAM,SACJ,SAC2D;AAC3D,UAAM,EAAE,MAAM,MAAM,SAAS,IAAI,KAAK,QAAQ,OAAO;AAErD,UAAM,EAAE,iBAAiB,OAAO,SAAS,IAAI,MAAMR,cAAc;MAC/D,KAAK,KAAK,OAAO,IAAI;QACnB,MAAM;QACN,SAAS,KAAK;MAChB,CAAC;MACD,SAASC,eAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;MAC9D,MAAM;QACJ,GAAG;QACH,QAAQ;MACV;MACA,uBAAuB;MACvB,2BAA2BE;QACzB;MACF;MACA,aAAa,QAAQ;MACrB,OAAO,KAAK,OAAO;IACrB,CAAC;AAED,UAAM,OAAO;AAEb,QAAI,eAA4C;AAChD,QAAI,eAAe;AACnB,QAAI,mBAAmB;AACvB,QAAI,qBAAoC;AACxC,QAAI,kBAAiC;AACrC,QAAI,aAA4B;AAChC,UAAM,mBAGF,CAAC;AACL,QAAI,eAAe;AAEnB,WAAO;MACL,QAAQ,SAAS;QACf,IAAI,gBAGF;UACA,UAAU,OAAO,YAAY;AApcvC,gBAAAX,MAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA;AAscY,gBAAI,CAAC,MAAM,SAAS;AAClB,6BAAe;AACf,yBAAW,QAAQ,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,CAAC;AACxD;YACF;AAEA,kBAAM,QAAQ,MAAM;AAEpB,gBAAI,+BAA+B,KAAK,GAAG;AACzC,kBAAI,MAAM,KAAK,SAAS,iBAAiB;AACvC,iCAAiB,MAAM,YAAY,IAAI;kBACrC,UAAU,MAAM,KAAK;kBACrB,YAAY,MAAM,KAAK;gBACzB;AAEA,2BAAW,QAAQ;kBACjB,MAAM;kBACN,cAAc;kBACd,YAAY,MAAM,KAAK;kBACvB,UAAU,MAAM,KAAK;kBACrB,eAAe,MAAM,KAAK;gBAC5B,CAAC;cACH;YACF,WAAW,0CAA0C,KAAK,GAAG;AAC3D,oBAAM,WAAW,iBAAiB,MAAM,YAAY;AAEpD,kBAAI,YAAY,MAAM;AACpB,2BAAW,QAAQ;kBACjB,MAAM;kBACN,cAAc;kBACd,YAAY,SAAS;kBACrB,UAAU,SAAS;kBACnB,eAAe,MAAM;gBACvB,CAAC;cACH;YACF,WAAW,uBAAuB,KAAK,GAAG;AACxC,2BAAa,MAAM,SAAS;AAC5B,yBAAW,QAAQ;gBACjB,MAAM;gBACN,IAAI,MAAM,SAAS;gBACnB,WAAW,IAAI,KAAK,MAAM,SAAS,aAAa,GAAI;gBACpD,SAAS,MAAM,SAAS;cAC1B,CAAC;YACH,WAAW,iBAAiB,KAAK,GAAG;AAClC,yBAAW,QAAQ;gBACjB,MAAM;gBACN,WAAW,MAAM;cACnB,CAAC;YACH,WAAW,yCAAyC,KAAK,GAAG;AAC1D,yBAAW,QAAQ;gBACjB,MAAM;gBACN,WAAW,MAAM;cACnB,CAAC;YACH,WACE,8BAA8B,KAAK,KACnC,MAAM,KAAK,SAAS,iBACpB;AACA,+BAAiB,MAAM,YAAY,IAAI;AACvC,6BAAe;AACf,yBAAW,QAAQ;gBACjB,MAAM;gBACN,cAAc;gBACd,YAAY,MAAM,KAAK;gBACvB,UAAU,MAAM,KAAK;gBACrB,MAAM,MAAM,KAAK;cACnB,CAAC;YACH,WAAW,wBAAwB,KAAK,GAAG;AACzC,6BAAe,8BAA8B;gBAC3C,eAAcA,OAAA,MAAM,SAAS,uBAAf,OAAA,SAAAA,KAAmC;gBACjD;cACF,CAAC;AACD,6BAAe,MAAM,SAAS,MAAM;AACpC,iCAAmB,MAAM,SAAS,MAAM;AACxC,oCACE,MAAA,KAAA,MAAM,SAAS,MAAM,yBAArB,OAAA,SAAA,GAA2C,kBAA3C,OAAA,KACA;AACF,iCACE,MAAA,KAAA,MAAM,SAAS,MAAM,0BAArB,OAAA,SAAA,GAA4C,qBAA5C,OAAA,KACA;YACJ,WAAW,+BAA+B,KAAK,GAAG;AAChD,yBAAW,QAAQ;gBACjB,MAAM;gBACN,QAAQ;kBACN,YAAY;kBACZ,KAAI,MAAA,MAAA,KAAA,KAAK,QAAO,eAAZ,OAAA,SAAA,GAAA,KAAA,EAAA,MAAA,OAAA,KAA8BgB,WAAW;kBAC7C,KAAK,MAAM,WAAW;kBACtB,OAAO,MAAM,WAAW;gBAC1B;cACF,CAAC;YACH,WAAW,aAAa,KAAK,GAAG;AAC9B,yBAAW,QAAQ,EAAE,MAAM,SAAS,OAAO,MAAM,CAAC;YACpD;UACF;UAEA,MAAM,YAAY;AAChB,uBAAW,QAAQ;cACjB,MAAM;cACN;cACA,OAAO,EAAE,cAAc,iBAAiB;cACxC,IAAK,sBAAsB,QAAQ,mBAAmB,SAAS;gBAC7D,kBAAkB;kBAChB,QAAQ;oBACN;oBACA;oBACA;kBACF;gBACF;cACF;YACF,CAAC;UACH;QACF,CAAC;MACH;MACA,SAAS;QACP,WAAW;QACX,aAAa,CAAC;MAChB;MACA,aAAa,EAAE,SAAS,gBAAgB;MACxC,SAAS,EAAE,MAAM,KAAK,UAAU,IAAI,EAAE;MACtC;IACF;EACF;AACF;AAEA,IAAM,cAAcT,iBAAE,OAAO;EAC3B,cAAcA,iBAAE,OAAO;EACvB,sBAAsBA,iBACnB,OAAO,EAAE,eAAeA,iBAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,EAC9C,QAAQ;EACX,eAAeA,iBAAE,OAAO;EACxB,uBAAuBA,iBACpB,OAAO,EAAE,kBAAkBA,iBAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,EACjD,QAAQ;AACb,CAAC;AAED,IAAM,uBAAuBA,iBAAE,OAAO;EACpC,MAAMA,iBAAE,QAAQ,4BAA4B;EAC5C,OAAOA,iBAAE,OAAO;AAClB,CAAC;AAED,IAAM,8BAA8BA,iBAAE,OAAO;EAC3C,MAAMA,iBAAE,KAAK,CAAC,sBAAsB,qBAAqB,CAAC;EAC1D,UAAUA,iBAAE,OAAO;IACjB,oBAAoBA,iBAAE,OAAO,EAAE,QAAQA,iBAAE,OAAO,EAAE,CAAC,EAAE,QAAQ;IAC7D,OAAO;EACT,CAAC;AACH,CAAC;AAED,IAAM,6BAA6BA,iBAAE,OAAO;EAC1C,MAAMA,iBAAE,QAAQ,kBAAkB;EAClC,UAAUA,iBAAE,OAAO;IACjB,IAAIA,iBAAE,OAAO;IACb,YAAYA,iBAAE,OAAO;IACrB,OAAOA,iBAAE,OAAO;EAClB,CAAC;AACH,CAAC;AAED,IAAM,+BAA+BA,iBAAE,OAAO;EAC5C,MAAMA,iBAAE,QAAQ,2BAA2B;EAC3C,cAAcA,iBAAE,OAAO;EACvB,MAAMA,iBAAE,mBAAmB,QAAQ;IACjCA,iBAAE,OAAO;MACP,MAAMA,iBAAE,QAAQ,SAAS;IAC3B,CAAC;IACDA,iBAAE,OAAO;MACP,MAAMA,iBAAE,QAAQ,eAAe;MAC/B,IAAIA,iBAAE,OAAO;MACb,SAASA,iBAAE,OAAO;MAClB,MAAMA,iBAAE,OAAO;MACf,WAAWA,iBAAE,OAAO;MACpB,QAAQA,iBAAE,QAAQ,WAAW;IAC/B,CAAC;EACH,CAAC;AACH,CAAC;AAED,IAAM,2CAA2CA,iBAAE,OAAO;EACxD,MAAMA,iBAAE,QAAQ,wCAAwC;EACxD,SAASA,iBAAE,OAAO;EAClB,cAAcA,iBAAE,OAAO;EACvB,OAAOA,iBAAE,OAAO;AAClB,CAAC;AAED,IAAM,gCAAgCA,iBAAE,OAAO;EAC7C,MAAMA,iBAAE,QAAQ,4BAA4B;EAC5C,cAAcA,iBAAE,OAAO;EACvB,MAAMA,iBAAE,mBAAmB,QAAQ;IACjCA,iBAAE,OAAO;MACP,MAAMA,iBAAE,QAAQ,SAAS;IAC3B,CAAC;IACDA,iBAAE,OAAO;MACP,MAAMA,iBAAE,QAAQ,eAAe;MAC/B,IAAIA,iBAAE,OAAO;MACb,SAASA,iBAAE,OAAO;MAClB,MAAMA,iBAAE,OAAO;MACf,WAAWA,iBAAE,OAAO;IACtB,CAAC;EACH,CAAC;AACH,CAAC;AAED,IAAM,gCAAgCA,iBAAE,OAAO;EAC7C,MAAMA,iBAAE,QAAQ,uCAAuC;EACvD,YAAYA,iBAAE,OAAO;IACnB,MAAMA,iBAAE,QAAQ,cAAc;IAC9B,KAAKA,iBAAE,OAAO;IACd,OAAOA,iBAAE,OAAO;EAClB,CAAC;AACH,CAAC;AAED,IAAM,0CAA0CA,iBAAE,OAAO;EACvD,MAAMA,iBAAE,QAAQ,uCAAuC;EACvD,SAASA,iBAAE,OAAO;EAClB,cAAcA,iBAAE,OAAO;EACvB,eAAeA,iBAAE,OAAO;EACxB,OAAOA,iBAAE,OAAO;AAClB,CAAC;AAED,IAAM,mBAAmBA,iBAAE,OAAO;EAChC,MAAMA,iBAAE,QAAQ,OAAO;EACvB,MAAMA,iBAAE,OAAO;EACf,SAASA,iBAAE,OAAO;EAClB,OAAOA,iBAAE,OAAO,EAAE,QAAQ;EAC1B,iBAAiBA,iBAAE,OAAO;AAC5B,CAAC;AAED,IAAM,6BAA6BA,iBAAE,MAAM;EACzC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAA,iBAAE,OAAO,EAAE,MAAMA,iBAAE,OAAO,EAAE,CAAC,EAAE,YAAY;;AAC7C,CAAC;AAED,SAAS,iBACP,OAC+C;AAC/C,SAAO,MAAM,SAAS;AACxB;AAEA,SAAS,8BACP,OACuD;AACvD,SAAO,MAAM,SAAS;AACxB;AAEA,SAAS,wBACP,OACsD;AACtD,SACE,MAAM,SAAS,wBAAwB,MAAM,SAAS;AAE1D;AAEA,SAAS,uBACP,OACqD;AACrD,SAAO,MAAM,SAAS;AACxB;AAEA,SAAS,0CACP,OACmE;AACnE,SAAO,MAAM,SAAS;AACxB;AAEA,SAAS,+BACP,OACwD;AACxD,SAAO,MAAM,SAAS;AACxB;AAEA,SAAS,+BACP,OACwD;AACxD,SAAO,MAAM,SAAS;AACxB;AAEA,SAAS,yCACP,OACkE;AAClE,SAAO,MAAM,SAAS;AACxB;AAEA,SAAS,aACP,OAC2C;AAC3C,SAAO,MAAM,SAAS;AACxB;AAQA,SAAS,wBAAwB,SAAuC;AAEtE,MAAI,QAAQ,WAAW,GAAG,GAAG;AAC3B,QAAI,QAAQ,WAAW,SAAS,KAAK,QAAQ,WAAW,YAAY,GAAG;AACrE,aAAO;QACL,kBAAkB;QAClB,mBAAmB;QACnB,wBAAwB;MAC1B;IACF;AAEA,WAAO;MACL,kBAAkB;MAClB,mBAAmB;MACnB,wBAAwB;IAC1B;EACF;AAGA,SAAO;IACL,kBAAkB;IAClB,mBAAmB;IACnB,wBAAwB;EAC1B;AACF;AAEA,IAAM,uCAAuCA,iBAAE,OAAO;EACpD,UAAUA,iBAAE,IAAI,EAAE,QAAQ;EAC1B,mBAAmBA,iBAAE,QAAQ,EAAE,QAAQ;EACvC,oBAAoBA,iBAAE,OAAO,EAAE,QAAQ;EACvC,OAAOA,iBAAE,QAAQ,EAAE,QAAQ;EAC3B,MAAMA,iBAAE,OAAO,EAAE,QAAQ;EACzB,iBAAiBA,iBAAE,OAAO,EAAE,QAAQ;EACpC,eAAeA,iBAAE,QAAQ,EAAE,QAAQ;EACnC,cAAcA,iBAAE,OAAO,EAAE,QAAQ;EACjC,kBAAkBA,iBAAE,OAAO,EAAE,QAAQ;AACvC,CAAC;AIlxBD,IAAM,6BAA6BA,iBAAE,OAAO,CAAC,CAAC;AAE9C,SAAS,qBAAqB;EAC5B;EACA;AACF,IASI,CAAC,GAKH;AACA,SAAO;IACL,MAAM;IACN,IAAI;IACJ,MAAM;MACJ;MACA;IACF;IACA,YAAY;EACd;AACF;AAEO,IAAM,cAAc;EACzB,kBAAkB;AACpB;ACrBA,IAAM,8BAA8BA,iBAAE,OAAO;EAC3C,cAAcA,iBAAE,OAAO,EAAE,QAAQ;EACjC,OAAOA,iBAAE,OAAO,EAAE,IAAI,IAAI,EAAE,IAAI,CAAG,EAAE,QAAQ,CAAG,EAAE,QAAQ;AAC5D,CAAC;AAYM,IAAM,oBAAN,MAAiD;EAOtD,YACW,SACQ,QACjB;AAFS,SAAA,UAAA;AACQ,SAAA,SAAA;AARnB,SAAS,uBAAuB;EAS7B;EAPH,IAAI,WAAmB;AACrB,WAAO,KAAK,OAAO;EACrB;EAOQ,QAAQ;IACd;IACA,QAAQ;IACR,eAAe;IACf;IACA;IACA;EACF,GAA+C;AAC7C,UAAM,WAAuC,CAAC;AAG9C,UAAM,gBAAgBM,qBAAqB;MACzC,UAAU;MACV;MACA,QAAQ;IACV,CAAC;AAGD,UAAM,cAAuC;MAC3C,OAAO,KAAK;MACZ,OAAO;MACP;MACA,iBAAiB;MACjB;MACA;IACF;AAEA,QAAI,cAAc;AAChB,UAAI,CAAC,OAAO,QAAQ,OAAO,QAAQ,OAAO,KAAK,EAAE,SAAS,YAAY,GAAG;AACvE,oBAAY,kBAAkB;MAChC,OAAO;AACL,iBAAS,KAAK;UACZ,MAAM;UACN,SAAS;UACT,SAAS,8BAA8B,YAAY;QACrD,CAAC;MACH;IACF;AAGA,QAAI,eAAe;AACjB,YAAM,qBAA2C,CAAC;AAElD,iBAAW,OAAO,oBAAoB;AACpC,cAAM,QAAQ,mBAAmB,GAAiC;AAClE,YAAI,UAAU,QAAW;AACvB,sBAAY,GAAG,IAAI;QACrB;MACF;IACF;AAEA,WAAO;MACL;MACA;IACF;EACF;EAEA,MAAM,WACJ,SAC2D;AApG/D,QAAAb,MAAA,IAAA;AAqGI,UAAM,eAAc,MAAA,MAAAA,OAAA,KAAK,OAAO,cAAZ,OAAA,SAAAA,KAAuB,gBAAvB,OAAA,SAAA,GAAA,KAAAA,IAAA,MAAA,OAAA,KAA0C,oBAAI,KAAK;AACvE,UAAM,EAAE,aAAa,SAAS,IAAI,KAAK,QAAQ,OAAO;AAEtD,UAAM;MACJ,OAAO;MACP;MACA,UAAU;IACZ,IAAI,MAAMQ,cAAc;MACtB,KAAK,KAAK,OAAO,IAAI;QACnB,MAAM;QACN,SAAS,KAAK;MAChB,CAAC;MACD,SAASC,eAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;MAC9D,MAAM;MACN,uBAAuB;MACvB,2BAA2B,4BAA4B;MACvD,aAAa,QAAQ;MACrB,OAAO,KAAK,OAAO;IACrB,CAAC;AAED,WAAO;MACL;MACA;MACA,SAAS;QACP,MAAM,KAAK,UAAU,WAAW;MAClC;MACA,UAAU;QACR,WAAW;QACX,SAAS,KAAK;QACd,SAAS;QACT,MAAM;MACR;IACF;EACF;AACF;ApBiDO,SAAS,aACd,UAAkC,CAAC,GACnB;AA1LlB,MAAAT,MAAA,IAAA;AA2LE,QAAM,WACJA,OAAA,qBAAqB,QAAQ,OAAO,MAApC,OAAAA,OAAyC;AAG3C,QAAM,iBAAgB,KAAA,QAAQ,kBAAR,OAAA,KAAyB;AAE/C,QAAM,gBAAe,KAAA,QAAQ,SAAR,OAAA,KAAgB;AAErC,QAAM,aAAa,OAAO;IACxB,eAAe,UAAU,WAAW;MAClC,QAAQ,QAAQ;MAChB,yBAAyB;MACzB,aAAa;IACf,CAAC,CAAC;IACF,uBAAuB,QAAQ;IAC/B,kBAAkB,QAAQ;IAC1B,GAAG,QAAQ;EACb;AAEA,QAAM,kBAAkB,CACtB,SACA,WAA+B,CAAC,MAEhC,IAAI,wBAAwB,SAAS,UAAU;IAC7C,UAAU,GAAG,YAAY;IACzB,KAAK,CAAC,EAAE,KAAK,MAAM,GAAG,OAAO,GAAG,IAAI;IACpC,SAAS;IACT;IACA,OAAO,QAAQ;EACjB,CAAC;AAEH,QAAM,wBAAwB,CAC5B,SACA,WAAqC,CAAC,MAEtC,IAAI,8BAA8B,SAAS,UAAU;IACnD,UAAU,GAAG,YAAY;IACzB,KAAK,CAAC,EAAE,KAAK,MAAM,GAAG,OAAO,GAAG,IAAI;IACpC,SAAS;IACT;IACA,OAAO,QAAQ;EACjB,CAAC;AAEH,QAAM,uBAAuB,CAC3B,SACA,WAAoC,CAAC,MAErC,IAAI,qBAAqB,SAAS,UAAU;IAC1C,UAAU,GAAG,YAAY;IACzB,KAAK,CAAC,EAAE,KAAK,MAAM,GAAG,OAAO,GAAG,IAAI;IACpC,SAAS;IACT,OAAO,QAAQ;EACjB,CAAC;AAEH,QAAM,mBAAmB,CACvB,SACA,WAAgC,CAAC,MAEjC,IAAI,iBAAiB,SAAS,UAAU;IACtC,UAAU,GAAG,YAAY;IACzB,KAAK,CAAC,EAAE,KAAK,MAAM,GAAG,OAAO,GAAG,IAAI;IACpC,SAAS;IACT,OAAO,QAAQ;EACjB,CAAC;AAEH,QAAM,2BAA2B,CAAC,YAChC,IAAI,yBAAyB,SAAS;IACpC,UAAU,GAAG,YAAY;IACzB,KAAK,CAAC,EAAE,KAAK,MAAM,GAAG,OAAO,GAAG,IAAI;IACpC,SAAS;IACT,OAAO,QAAQ;EACjB,CAAC;AAEH,QAAM,oBAAoB,CAAC,YACzB,IAAI,kBAAkB,SAAS;IAC7B,UAAU,GAAG,YAAY;IACzB,KAAK,CAAC,EAAE,KAAK,MAAM,GAAG,OAAO,GAAG,IAAI;IACpC,SAAS;IACT,OAAO,QAAQ;EACjB,CAAC;AAEH,QAAM,sBAAsB,CAC1B,SACA,aACG;AACH,QAAI,YAAY;AACd,YAAM,IAAI;QACR;MACF;IACF;AAEA,QAAI,YAAY,0BAA0B;AACxC,aAAO;QACL;QACA;MACF;IACF;AAEA,WAAO,gBAAgB,SAAS,QAA8B;EAChE;AAEA,QAAM,uBAAuB,CAAC,YAAoC;AAChE,WAAO,IAAI,6BAA6B,SAAS;MAC/C,UAAU,GAAG,YAAY;MACzB,KAAK,CAAC,EAAE,KAAK,MAAM,GAAG,OAAO,GAAG,IAAI;MACpC,SAAS;MACT,OAAO,QAAQ;IACjB,CAAC;EACH;AAEA,QAAM,WAAW,SACf,SACA,UACA;AACA,WAAO,oBAAoB,SAAS,QAAQ;EAC9C;AAEA,WAAS,gBAAgB;AACzB,WAAS,OAAO;AAChB,WAAS,aAAa;AACtB,WAAS,YAAY;AACrB,WAAS,YAAY;AACrB,WAAS,gBAAgB;AACzB,WAAS,qBAAqB;AAE9B,WAAS,QAAQ;AACjB,WAAS,aAAa;AAEtB,WAAS,gBAAgB;AACzB,WAAS,qBAAqB;AAE9B,WAAS,SAAS;AAClB,WAAS,cAAc;AAEvB,WAAS,QAAQ;AAEjB,SAAO;AACT;AAKO,IAAM,SAAS,aAAa;EACjC,eAAe;;AACjB,CAAC;", "names": ["_AISDKError", "name", "marker", "symbol", "_a", "_a", "symbol", "name", "marker", "_a", "symbol", "name", "marker", "_a", "symbol", "name", "marker", "_TypeValidationError", "validator", "SecureJSON", "TypeValidationError", "InvalidArgumentError", "getOriginalFetch", "APICallError", "APICallError", "APICallError", "_a", "token", "logprob", "type", "UnsupportedFunctionalityError", "openaiTools", "toolCall", "z", "postJsonToApi", "combineHeaders", "createJsonResponseHandler", "createEventSourceResponseHandler", "convertUint8ArrayToBase64", "parseProviderOptions", "_b", "_c", "generateId"]}