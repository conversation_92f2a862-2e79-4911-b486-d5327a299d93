{"version": 3, "sources": ["../../@ai-sdk/google/node_modules/@ai-sdk/provider/src/errors/ai-sdk-error.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider/src/errors/api-call-error.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider/src/errors/empty-response-body-error.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider/src/errors/get-error-message.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider/src/errors/invalid-prompt-error.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider/src/errors/invalid-response-data-error.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider/src/errors/json-parse-error.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider/src/errors/load-api-key-error.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider/src/errors/load-setting-error.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider/src/errors/no-content-generated-error.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider/src/errors/no-such-model-error.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider/src/errors/too-many-embedding-values-for-call-error.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider/src/errors/type-validation-error.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider/src/errors/unsupported-functionality-error.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider/src/json-value/is-json.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider-utils/src/combine-headers.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider-utils/src/convert-async-generator-to-readable-stream.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider-utils/src/extract-response-headers.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider-utils/src/generate-id.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider-utils/src/get-error-message.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider-utils/src/is-abort-error.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider-utils/src/load-api-key.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider-utils/src/load-setting.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider-utils/src/load-optional-setting.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider-utils/src/parse-json.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider-utils/src/validate-types.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider-utils/src/validator.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider-utils/src/post-to-api.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider-utils/src/remove-undefined-entries.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider-utils/src/response-handler.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider-utils/src/uint8-utils.ts", "../../@ai-sdk/google/node_modules/@ai-sdk/provider-utils/src/without-trailing-slash.ts", "../../@ai-sdk/google/src/google-facade.ts", "../../@ai-sdk/google/src/google-generative-ai-language-model.ts", "../../@ai-sdk/google/src/convert-json-schema-to-openapi-schema.ts", "../../@ai-sdk/google/src/convert-to-google-generative-ai-messages.ts", "../../@ai-sdk/google/src/get-model-path.ts", "../../@ai-sdk/google/src/google-error.ts", "../../@ai-sdk/google/src/map-google-generative-ai-finish-reason.ts", "../../@ai-sdk/google/src/google-provider.ts", "../../@ai-sdk/google/src/google-generative-ai-embedding-model.ts"], "sourcesContent": ["/**\n * Symbol used for identifying AI SDK Error instances.\n * Enables checking if an error is an instance of AISDKError across package versions.\n */\nconst marker = 'vercel.ai.error';\nconst symbol = Symbol.for(marker);\n\n/**\n * Custom error class for AI SDK related errors.\n * @extends Error\n */\nexport class AISDKError extends Error {\n  private readonly [symbol] = true; // used in isInstance\n\n  /**\n   * The underlying cause of the error, if any.\n   */\n  readonly cause?: unknown;\n\n  /**\n   * Creates an AI SDK Error.\n   *\n   * @param {Object} params - The parameters for creating the error.\n   * @param {string} params.name - The name of the error.\n   * @param {string} params.message - The error message.\n   * @param {unknown} [params.cause] - The underlying cause of the error.\n   */\n  constructor({\n    name,\n    message,\n    cause,\n  }: {\n    name: string;\n    message: string;\n    cause?: unknown;\n  }) {\n    super(message);\n\n    this.name = name;\n    this.cause = cause;\n  }\n\n  /**\n   * Checks if the given error is an AI SDK Error.\n   * @param {unknown} error - The error to check.\n   * @returns {boolean} True if the error is an AI SDK Error, false otherwise.\n   */\n  static isInstance(error: unknown): error is AISDKError {\n    return AISDKError.hasMarker(error, marker);\n  }\n\n  protected static hasMarker(error: unknown, marker: string): boolean {\n    const markerSymbol = Symbol.for(marker);\n    return (\n      error != null &&\n      typeof error === 'object' &&\n      markerSymbol in error &&\n      typeof error[markerSymbol] === 'boolean' &&\n      error[markerSymbol] === true\n    );\n  }\n\n  /**\n   * Returns a JSON representation of the error.\n   * @returns {Object} An object containing the error's name, message, and cause.\n   *\n   * @deprecated Do not use this method. It will be removed in the next major version.\n   */\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n    };\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_APICallError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class APICallError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly url: string;\n  readonly requestBodyValues: unknown;\n  readonly statusCode?: number;\n\n  readonly responseHeaders?: Record<string, string>;\n  readonly responseBody?: string;\n\n  readonly isRetryable: boolean;\n  readonly data?: unknown;\n\n  constructor({\n    message,\n    url,\n    requestBodyValues,\n    statusCode,\n    responseHeaders,\n    responseBody,\n    cause,\n    isRetryable = statusCode != null &&\n      (statusCode === 408 || // request timeout\n        statusCode === 409 || // conflict\n        statusCode === 429 || // too many requests\n        statusCode >= 500), // server error\n    data,\n  }: {\n    message: string;\n    url: string;\n    requestBodyValues: unknown;\n    statusCode?: number;\n    responseHeaders?: Record<string, string>;\n    responseBody?: string;\n    cause?: unknown;\n    isRetryable?: boolean;\n    data?: unknown;\n  }) {\n    super({ name, message, cause });\n\n    this.url = url;\n    this.requestBodyValues = requestBodyValues;\n    this.statusCode = statusCode;\n    this.responseHeaders = responseHeaders;\n    this.responseBody = responseBody;\n    this.isRetryable = isRetryable;\n    this.data = data;\n  }\n\n  static isInstance(error: unknown): error is APICallError {\n    return AISDKError.hasMarker(error, marker);\n  }\n\n  /**\n   * @deprecated Use isInstance instead.\n   */\n  static isAPICallError(error: unknown): error is APICallError {\n    return (\n      error instanceof Error &&\n      error.name === name &&\n      typeof (error as APICallError).url === 'string' &&\n      typeof (error as APICallError).requestBodyValues === 'object' &&\n      ((error as APICallError).statusCode == null ||\n        typeof (error as APICallError).statusCode === 'number') &&\n      ((error as APICallError).responseHeaders == null ||\n        typeof (error as APICallError).responseHeaders === 'object') &&\n      ((error as APICallError).responseBody == null ||\n        typeof (error as APICallError).responseBody === 'string') &&\n      ((error as APICallError).cause == null ||\n        typeof (error as APICallError).cause === 'object') &&\n      typeof (error as APICallError).isRetryable === 'boolean' &&\n      ((error as APICallError).data == null ||\n        typeof (error as APICallError).data === 'object')\n    );\n  }\n\n  /**\n   * @deprecated Do not use this method. It will be removed in the next major version.\n   */\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      url: this.url,\n      requestBodyValues: this.requestBodyValues,\n      statusCode: this.statusCode,\n      responseHeaders: this.responseHeaders,\n      responseBody: this.responseBody,\n      cause: this.cause,\n      isRetryable: this.isRetryable,\n      data: this.data,\n    };\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_EmptyResponseBodyError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class EmptyResponseBodyError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  constructor({ message = 'Empty response body' }: { message?: string } = {}) {\n    super({ name, message });\n  }\n\n  static isInstance(error: unknown): error is EmptyResponseBodyError {\n    return AISDKError.hasMarker(error, marker);\n  }\n\n  /**\n   * @deprecated use `isInstance` instead\n   */\n  static isEmptyResponseBodyError(\n    error: unknown,\n  ): error is EmptyResponseBodyError {\n    return error instanceof Error && error.name === name;\n  }\n}\n", "export function getErrorMessage(error: unknown | undefined) {\n  if (error == null) {\n    return 'unknown error';\n  }\n\n  if (typeof error === 'string') {\n    return error;\n  }\n\n  if (error instanceof Error) {\n    return error.message;\n  }\n\n  return JSON.stringify(error);\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_InvalidPromptError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\n * A prompt is invalid. This error should be thrown by providers when they cannot\n * process a prompt.\n */\nexport class InvalidPromptError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly prompt: unknown;\n\n  constructor({\n    prompt,\n    message,\n    cause,\n  }: {\n    prompt: unknown;\n    message: string;\n    cause?: unknown;\n  }) {\n    super({ name, message: `Invalid prompt: ${message}`, cause });\n\n    this.prompt = prompt;\n  }\n\n  static isInstance(error: unknown): error is InvalidPromptError {\n    return AISDKError.hasMarker(error, marker);\n  }\n\n  /**\n   * @deprecated use `isInstance` instead\n   */\n  static isInvalidPromptError(error: unknown): error is InvalidPromptError {\n    return error instanceof Error && error.name === name && prompt != null;\n  }\n\n  /**\n   * @deprecated Do not use this method. It will be removed in the next major version.\n   */\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n\n      prompt: this.prompt,\n    };\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_InvalidResponseDataError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\n * Server returned a response with invalid data content.\n * This should be thrown by providers when they cannot parse the response from the API.\n */\nexport class InvalidResponseDataError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly data: unknown;\n\n  constructor({\n    data,\n    message = `Invalid response data: ${JSON.stringify(data)}.`,\n  }: {\n    data: unknown;\n    message?: string;\n  }) {\n    super({ name, message });\n\n    this.data = data;\n  }\n\n  static isInstance(error: unknown): error is InvalidResponseDataError {\n    return AISDKError.hasMarker(error, marker);\n  }\n\n  /**\n   * @deprecated use `isInstance` instead\n   */\n  static isInvalidResponseDataError(\n    error: unknown,\n  ): error is InvalidResponseDataError {\n    return (\n      error instanceof Error &&\n      error.name === name &&\n      (error as InvalidResponseDataError).data != null\n    );\n  }\n\n  /**\n   * @deprecated Do not use this method. It will be removed in the next major version.\n   */\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n\n      data: this.data,\n    };\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\nimport { getErrorMessage } from './get-error-message';\n\nconst name = 'AI_JSONParseError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class JSONParseError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly text: string;\n\n  constructor({ text, cause }: { text: string; cause: unknown }) {\n    super({\n      name,\n      message:\n        `JSON parsing failed: ` +\n        `Text: ${text}.\\n` +\n        `Error message: ${getErrorMessage(cause)}`,\n      cause,\n    });\n\n    this.text = text;\n  }\n\n  static isInstance(error: unknown): error is JSONParseError {\n    return AISDKError.hasMarker(error, marker);\n  }\n\n  /**\n   * @deprecated use `isInstance` instead\n   */\n  static isJSONParseError(error: unknown): error is JSONParseError {\n    return (\n      error instanceof Error &&\n      error.name === name &&\n      'text' in error &&\n      typeof error.text === 'string'\n    );\n  }\n\n  /**\n   * @deprecated Do not use this method. It will be removed in the next major version.\n   */\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      cause: this.cause,\n      stack: this.stack,\n\n      valueText: this.text,\n    };\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_LoadAPIKeyError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class LoadAPIKeyError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  constructor({ message }: { message: string }) {\n    super({ name, message });\n  }\n\n  static isInstance(error: unknown): error is LoadAPIKeyError {\n    return AISDKError.hasMarker(error, marker);\n  }\n\n  /**\n   * @deprecated Use isInstance instead.\n   */\n  static isLoadAPIKeyError(error: unknown): error is LoadAPIKeyError {\n    return error instanceof Error && error.name === name;\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_LoadSettingError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class LoadSettingError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  constructor({ message }: { message: string }) {\n    super({ name, message });\n  }\n\n  static isInstance(error: unknown): error is LoadSettingError {\n    return AISDKError.hasMarker(error, marker);\n  }\n\n  /**\n   * @deprecated Use isInstance instead.\n   */\n  static isLoadSettingError(error: unknown): error is LoadSettingError {\n    return error instanceof Error && error.name === name;\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_NoContentGeneratedError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\nThrown when the AI provider fails to generate any content.\n */\nexport class NoContentGeneratedError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  constructor({\n    message = 'No content generated.',\n  }: { message?: string } = {}) {\n    super({ name, message });\n  }\n\n  static isInstance(error: unknown): error is NoContentGeneratedError {\n    return AISDKError.hasMarker(error, marker);\n  }\n\n  /**\n   * @deprecated Use isInstance instead.\n   */\n  static isNoContentGeneratedError(\n    error: unknown,\n  ): error is NoContentGeneratedError {\n    return error instanceof Error && error.name === name;\n  }\n\n  /**\n   * @deprecated Do not use this method. It will be removed in the next major version.\n   */\n  toJSON() {\n    return {\n      name: this.name,\n      cause: this.cause,\n      message: this.message,\n      stack: this.stack,\n    };\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_NoSuchModelError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class NoSuchModelError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly modelId: string;\n  readonly modelType: 'languageModel' | 'textEmbeddingModel';\n\n  constructor({\n    errorName = name,\n    modelId,\n    modelType,\n    message = `No such ${modelType}: ${modelId}`,\n  }: {\n    errorName?: string;\n    modelId: string;\n    modelType: 'languageModel' | 'textEmbeddingModel';\n    message?: string;\n  }) {\n    super({ name: errorName, message });\n\n    this.modelId = modelId;\n    this.modelType = modelType;\n  }\n\n  static isInstance(error: unknown): error is NoSuchModelError {\n    return AISDKError.hasMarker(error, marker);\n  }\n\n  /**\n   * @deprecated use `isInstance` instead\n   */\n  static isNoSuchModelError(error: unknown): error is NoSuchModelError {\n    return (\n      error instanceof Error &&\n      error.name === name &&\n      typeof (error as NoSuchModelError).modelId === 'string' &&\n      typeof (error as NoSuchModelError).modelType === 'string'\n    );\n  }\n\n  /**\n   * @deprecated Do not use this method. It will be removed in the next major version.\n   */\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n\n      modelId: this.modelId,\n      modelType: this.modelType,\n    };\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_TooManyEmbeddingValuesForCallError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class TooManyEmbeddingValuesForCallError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly provider: string;\n  readonly modelId: string;\n  readonly maxEmbeddingsPerCall: number;\n  readonly values: Array<unknown>;\n\n  constructor(options: {\n    provider: string;\n    modelId: string;\n    maxEmbeddingsPerCall: number;\n    values: Array<unknown>;\n  }) {\n    super({\n      name,\n      message:\n        `Too many values for a single embedding call. ` +\n        `The ${options.provider} model \"${options.modelId}\" can only embed up to ` +\n        `${options.maxEmbeddingsPerCall} values per call, but ${options.values.length} values were provided.`,\n    });\n\n    this.provider = options.provider;\n    this.modelId = options.modelId;\n    this.maxEmbeddingsPerCall = options.maxEmbeddingsPerCall;\n    this.values = options.values;\n  }\n\n  static isInstance(\n    error: unknown,\n  ): error is TooManyEmbeddingValuesForCallError {\n    return AISDKError.hasMarker(error, marker);\n  }\n\n  /**\n   * @deprecated use `isInstance` instead\n   */\n  static isTooManyEmbeddingValuesForCallError(\n    error: unknown,\n  ): error is TooManyEmbeddingValuesForCallError {\n    return (\n      error instanceof Error &&\n      error.name === name &&\n      'provider' in error &&\n      typeof error.provider === 'string' &&\n      'modelId' in error &&\n      typeof error.modelId === 'string' &&\n      'maxEmbeddingsPerCall' in error &&\n      typeof error.maxEmbeddingsPerCall === 'number' &&\n      'values' in error &&\n      Array.isArray(error.values)\n    );\n  }\n\n  /**\n   * @deprecated Do not use this method. It will be removed in the next major version.\n   */\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n\n      provider: this.provider,\n      modelId: this.modelId,\n      maxEmbeddingsPerCall: this.maxEmbeddingsPerCall,\n      values: this.values,\n    };\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\nimport { getErrorMessage } from './get-error-message';\n\nconst name = 'AI_TypeValidationError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class TypeValidationError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly value: unknown;\n\n  constructor({ value, cause }: { value: unknown; cause: unknown }) {\n    super({\n      name,\n      message:\n        `Type validation failed: ` +\n        `Value: ${JSON.stringify(value)}.\\n` +\n        `Error message: ${getErrorMessage(cause)}`,\n      cause,\n    });\n\n    this.value = value;\n  }\n\n  static isInstance(error: unknown): error is TypeValidationError {\n    return AISDKError.hasMarker(error, marker);\n  }\n\n  /**\n   * Wraps an error into a TypeValidationError.\n   * If the cause is already a TypeValidationError with the same value, it returns the cause.\n   * Otherwise, it creates a new TypeValidationError.\n   *\n   * @param {Object} params - The parameters for wrapping the error.\n   * @param {unknown} params.value - The value that failed validation.\n   * @param {unknown} params.cause - The original error or cause of the validation failure.\n   * @returns {TypeValidationError} A TypeValidationError instance.\n   */\n  static wrap({\n    value,\n    cause,\n  }: {\n    value: unknown;\n    cause: unknown;\n  }): TypeValidationError {\n    return TypeValidationError.isInstance(cause) && cause.value === value\n      ? cause\n      : new TypeValidationError({ value, cause });\n  }\n\n  /**\n   * @deprecated use `isInstance` instead\n   */\n  static isTypeValidationError(error: unknown): error is TypeValidationError {\n    return error instanceof Error && error.name === name;\n  }\n\n  /**\n   * @deprecated Do not use this method. It will be removed in the next major version.\n   */\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      cause: this.cause,\n      stack: this.stack,\n\n      value: this.value,\n    };\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_UnsupportedFunctionalityError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class UnsupportedFunctionalityError extends AISD<PERSON>rror {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly functionality: string;\n\n  constructor({ functionality }: { functionality: string }) {\n    super({\n      name,\n      message: `'${functionality}' functionality not supported.`,\n    });\n\n    this.functionality = functionality;\n  }\n\n  static isInstance(error: unknown): error is UnsupportedFunctionalityError {\n    return AISDKError.hasMarker(error, marker);\n  }\n\n  /**\n   * @deprecated Use isInstance instead.\n   */\n  static isUnsupportedFunctionalityError(\n    error: unknown,\n  ): error is UnsupportedFunctionalityError {\n    return (\n      error instanceof Error &&\n      error.name === name &&\n      typeof (error as UnsupportedFunctionalityError).functionality === 'string'\n    );\n  }\n\n  /**\n   * @deprecated Do not use this method. It will be removed in the next major version.\n   */\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n\n      functionality: this.functionality,\n    };\n  }\n}\n", "import { JSONArray, JSONObject, JSONValue } from './json-value';\n\nexport function isJSONValue(value: unknown): value is JSONValue {\n  if (\n    value === null ||\n    typeof value === 'string' ||\n    typeof value === 'number' ||\n    typeof value === 'boolean'\n  ) {\n    return true;\n  }\n\n  if (Array.isArray(value)) {\n    return value.every(isJSONValue);\n  }\n\n  if (typeof value === 'object') {\n    return Object.entries(value).every(\n      ([key, val]) => typeof key === 'string' && isJSONValue(val),\n    );\n  }\n\n  return false;\n}\n\nexport function isJSONArray(value: unknown): value is JSONArray {\n  return Array.isArray(value) && value.every(isJSONValue);\n}\n\nexport function isJSONObject(value: unknown): value is JSONObject {\n  return (\n    value != null &&\n    typeof value === 'object' &&\n    Object.entries(value).every(\n      ([key, val]) => typeof key === 'string' && isJSONValue(val),\n    )\n  );\n}\n", "export function combineHeaders(\n  ...headers: Array<Record<string, string | undefined> | undefined>\n): Record<string, string | undefined> {\n  return headers.reduce(\n    (combinedHeaders, currentHeaders) => ({\n      ...combinedHeaders,\n      ...(currentHeaders ?? {}),\n    }),\n    {},\n  ) as Record<string, string | undefined>;\n}\n", "/**\n * Converts an AsyncGenerator to a ReadableStream.\n *\n * @template T - The type of elements produced by the AsyncGenerator.\n * @param {AsyncGenerator<T>} stream - The AsyncGenerator to convert.\n * @returns {ReadableStream<T>} - A ReadableStream that provides the same data as the AsyncGenerator.\n */\nexport function convertAsyncGeneratorToReadableStream<T>(\n  stream: AsyncGenerator<T>,\n): ReadableStream<T> {\n  return new ReadableStream<T>({\n    /**\n     * Called when the consumer wants to pull more data from the stream.\n     *\n     * @param {ReadableStreamDefaultController<T>} controller - The controller to enqueue data into the stream.\n     * @returns {Promise<void>}\n     */\n    async pull(controller) {\n      try {\n        const { value, done } = await stream.next();\n        if (done) {\n          controller.close();\n        } else {\n          controller.enqueue(value);\n        }\n      } catch (error) {\n        controller.error(error);\n      }\n    },\n    /**\n     * Called when the consumer cancels the stream.\n     */\n    cancel() {},\n  });\n}\n", "/**\nExtracts the headers from a response object and returns them as a key-value object.\n\n@param response - The response object to extract headers from.\n@returns The headers as a key-value object.\n*/\nexport function extractResponseHeaders(\n  response: Response,\n): Record<string, string> {\n  const headers: Record<string, string> = {};\n  response.headers.forEach((value, key) => {\n    headers[key] = value;\n  });\n  return headers;\n}\n", "import { customAlphabet } from 'nanoid/non-secure';\n\n/**\n * Creates an ID generator that uses an alphabet of digits, uppercase and lowercase letters.\n *\n * @param alphabet - The alphabet to use for the ID. Default: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.\n * @param prefix - The prefix of the ID to generate. Default: ''.\n * @param size - The size of the random part of the ID to generate. Default: 7.\n */\n//TODO change default size to 16 in 4.0\nexport const createIdGenerator = ({\n  prefix = '',\n  size: defaultSize = 7,\n  alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',\n}: {\n  prefix?: string;\n  size?: number;\n  alphabet?: string;\n} = {}): ((size?: number) => string) => {\n  const generator = customAlphabet(alphabet, defaultSize);\n  return size => `${prefix}${generator(size)}`;\n};\n\n/**\n * Generates a 7-character random string to use for IDs. Not secure.\n *\n * @param size - The size of the ID to generate. Default: 7.\n */\n//TODO change default size to 16 in 4.0\nexport const generateId = createIdGenerator();\n", "export function getErrorMessage(error: unknown | undefined) {\n  if (error == null) {\n    return 'unknown error';\n  }\n\n  if (typeof error === 'string') {\n    return error;\n  }\n\n  if (error instanceof Error) {\n    return error.message;\n  }\n\n  return JSON.stringify(error);\n}\n", "export function isAbortError(error: unknown): error is Error {\n  return (\n    error instanceof Error &&\n    (error.name === 'AbortError' || error.name === 'TimeoutError')\n  );\n}\n", "import { LoadAPIKeyError } from '@ai-sdk/provider';\n\nexport function loadApiKey({\n  apiKey,\n  environmentVariableName,\n  apiKeyParameterName = 'apiKey',\n  description,\n}: {\n  apiKey: string | undefined;\n  environmentVariableName: string;\n  apiKeyParameterName?: string;\n  description: string;\n}): string {\n  if (typeof apiKey === 'string') {\n    return apiKey;\n  }\n\n  if (apiKey != null) {\n    throw new LoadAPIKeyError({\n      message: `${description} API key must be a string.`,\n    });\n  }\n\n  if (typeof process === 'undefined') {\n    throw new LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter. Environment variables is not supported in this environment.`,\n    });\n  }\n\n  apiKey = process.env[environmentVariableName];\n\n  if (apiKey == null) {\n    throw new LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter or the ${environmentVariableName} environment variable.`,\n    });\n  }\n\n  if (typeof apiKey !== 'string') {\n    throw new LoadAPIKeyError({\n      message: `${description} API key must be a string. The value of the ${environmentVariableName} environment variable is not a string.`,\n    });\n  }\n\n  return apiKey;\n}\n", "import { LoadSettingError } from '@ai-sdk/provider';\n\n/**\n * Loads a `string` setting from the environment or a parameter.\n *\n * @param settingValue - The setting value.\n * @param environmentVariableName - The environment variable name.\n * @param settingName - The setting name.\n * @param description - The description of the setting.\n * @returns The setting value.\n */\nexport function loadSetting({\n  settingValue,\n  environmentVariableName,\n  settingName,\n  description,\n}: {\n  settingValue: string | undefined;\n  environmentVariableName: string;\n  settingName: string;\n  description: string;\n}): string {\n  if (typeof settingValue === 'string') {\n    return settingValue;\n  }\n\n  if (settingValue != null) {\n    throw new LoadSettingError({\n      message: `${description} setting must be a string.`,\n    });\n  }\n\n  if (typeof process === 'undefined') {\n    throw new LoadSettingError({\n      message:\n        `${description} setting is missing. ` +\n        `Pass it using the '${settingName}' parameter. ` +\n        `Environment variables is not supported in this environment.`,\n    });\n  }\n\n  settingValue = process.env[environmentVariableName];\n\n  if (settingValue == null) {\n    throw new LoadSettingError({\n      message:\n        `${description} setting is missing. ` +\n        `Pass it using the '${settingName}' parameter ` +\n        `or the ${environmentVariableName} environment variable.`,\n    });\n  }\n\n  if (typeof settingValue !== 'string') {\n    throw new LoadSettingError({\n      message:\n        `${description} setting must be a string. ` +\n        `The value of the ${environmentVariableName} environment variable is not a string.`,\n    });\n  }\n\n  return settingValue;\n}\n", "/**\n * Loads an optional `string` setting from the environment or a parameter.\n *\n * @param settingValue - The setting value.\n * @param environmentVariableName - The environment variable name.\n * @returns The setting value.\n */\nexport function loadOptionalSetting({\n  settingValue,\n  environmentVariableName,\n}: {\n  settingValue: string | undefined;\n  environmentVariableName: string;\n}): string | undefined {\n  if (typeof settingValue === 'string') {\n    return settingValue;\n  }\n\n  if (settingValue != null || typeof process === 'undefined') {\n    return undefined;\n  }\n\n  settingValue = process.env[environmentVariableName];\n\n  if (settingValue == null || typeof settingValue !== 'string') {\n    return undefined;\n  }\n\n  return settingValue;\n}\n", "import {\n  JSONParseError,\n  <PERSON>SO<PERSON>Val<PERSON>,\n  TypeValidationError,\n} from '@ai-sdk/provider';\nimport SecureJSON from 'secure-json-parse';\nimport { ZodSchema } from 'zod';\nimport { safeValidateTypes, validateTypes } from './validate-types';\nimport { Validator } from './validator';\n\n/**\n * Parses a JSON string into an unknown object.\n *\n * @param text - The JSON string to parse.\n * @returns {JSONValue} - The parsed JSON object.\n */\nexport function parseJSON(options: {\n  text: string;\n  schema?: undefined;\n}): JSONValue;\n/**\n * Parses a JSON string into a strongly-typed object using the provided schema.\n *\n * @template T - The type of the object to parse the JSON into.\n * @param {string} text - The JSON string to parse.\n * @param {Validator<T>} schema - The schema to use for parsing the JSON.\n * @returns {T} - The parsed object.\n */\nexport function parseJSON<T>(options: {\n  text: string;\n  schema: ZodSchema<T> | Validator<T>;\n}): T;\nexport function parseJSON<T>({\n  text,\n  schema,\n}: {\n  text: string;\n  schema?: ZodSchema<T> | Validator<T>;\n}): T {\n  try {\n    const value = SecureJSON.parse(text);\n\n    if (schema == null) {\n      return value;\n    }\n\n    return validateTypes({ value, schema });\n  } catch (error) {\n    if (\n      JSONParseError.isJSONParseError(error) ||\n      TypeValidationError.isTypeValidationError(error)\n    ) {\n      throw error;\n    }\n\n    throw new JSONParseError({ text, cause: error });\n  }\n}\n\nexport type ParseResult<T> =\n  | { success: true; value: T }\n  | { success: false; error: JSONParseError | TypeValidationError };\n\n/**\n * Safely parses a JSON string and returns the result as an object of type `unknown`.\n *\n * @param text - The JSON string to parse.\n * @returns {object} Either an object with `success: true` and the parsed data, or an object with `success: false` and the error that occurred.\n */\nexport function safeParseJSON(options: {\n  text: string;\n  schema?: undefined;\n}): ParseResult<JSONValue>;\n/**\n * Safely parses a JSON string into a strongly-typed object, using a provided schema to validate the object.\n *\n * @template T - The type of the object to parse the JSON into.\n * @param {string} text - The JSON string to parse.\n * @param {Validator<T>} schema - The schema to use for parsing the JSON.\n * @returns An object with either a `success` flag and the parsed and typed data, or a `success` flag and an error object.\n */\nexport function safeParseJSON<T>(options: {\n  text: string;\n  schema: ZodSchema<T> | Validator<T>;\n}): ParseResult<T>;\nexport function safeParseJSON<T>({\n  text,\n  schema,\n}: {\n  text: string;\n  schema?: ZodSchema<T> | Validator<T>;\n}):\n  | { success: true; value: T }\n  | { success: false; error: JSONParseError | TypeValidationError } {\n  try {\n    const value = SecureJSON.parse(text);\n\n    if (schema == null) {\n      return {\n        success: true,\n        value: value as T,\n      };\n    }\n\n    return safeValidateTypes({ value, schema });\n  } catch (error) {\n    return {\n      success: false,\n      error: JSONParseError.isJSONParseError(error)\n        ? error\n        : new JSONParseError({ text, cause: error }),\n    };\n  }\n}\n\nexport function isParsableJson(input: string): boolean {\n  try {\n    SecureJSON.parse(input);\n    return true;\n  } catch {\n    return false;\n  }\n}\n\n/**\n@deprecated Use `isParsableJson` instead.\n */\nexport const isParseableJson = isParsableJson;\n", "import { TypeValidationError } from '@ai-sdk/provider';\nimport { z } from 'zod';\nimport { Validator, asValidator } from './validator';\n\n/**\n * Validates the types of an unknown object using a schema and\n * return a strongly-typed object.\n *\n * @template T - The type of the object to validate.\n * @param {string} options.value - The object to validate.\n * @param {Validator<T>} options.schema - The schema to use for validating the JSON.\n * @returns {T} - The typed object.\n */\nexport function validateTypes<T>({\n  value,\n  schema: inputSchema,\n}: {\n  value: unknown;\n  schema: z.Schema<T, z.ZodTypeDef, any> | Validator<T>;\n}): T {\n  const result = safeValidateTypes({ value, schema: inputSchema });\n\n  if (!result.success) {\n    throw TypeValidationError.wrap({ value, cause: result.error });\n  }\n\n  return result.value;\n}\n\n/**\n * Safely validates the types of an unknown object using a schema and\n * return a strongly-typed object.\n *\n * @template T - The type of the object to validate.\n * @param {string} options.value - The JSON object to validate.\n * @param {Validator<T>} options.schema - The schema to use for validating the JSON.\n * @returns An object with either a `success` flag and the parsed and typed data, or a `success` flag and an error object.\n */\nexport function safeValidateTypes<T>({\n  value,\n  schema,\n}: {\n  value: unknown;\n  schema: z.Schema<T, z.ZodTypeDef, any> | Validator<T>;\n}):\n  | { success: true; value: T }\n  | { success: false; error: TypeValidationError } {\n  const validator = asValidator(schema);\n\n  try {\n    if (validator.validate == null) {\n      return { success: true, value: value as T };\n    }\n\n    const result = validator.validate(value);\n\n    if (result.success) {\n      return result;\n    }\n\n    return {\n      success: false,\n      error: TypeValidationError.wrap({ value, cause: result.error }),\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: TypeValidationError.wrap({ value, cause: error }),\n    };\n  }\n}\n", "import { z } from 'zod';\n\n/**\n * Used to mark validator functions so we can support both Zod and custom schemas.\n */\nexport const validatorSymbol = Symbol.for('vercel.ai.validator');\n\nexport type ValidationResult<OBJECT> =\n  | { success: true; value: OBJECT }\n  | { success: false; error: Error };\n\nexport type Validator<OBJECT = unknown> = {\n  /**\n   * Used to mark validator functions so we can support both Zod and custom schemas.\n   */\n  [validatorSymbol]: true;\n\n  /**\n   * Optional. Validates that the structure of a value matches this schema,\n   * and returns a typed version of the value if it does.\n   */\n  readonly validate?: (value: unknown) => ValidationResult<OBJECT>;\n};\n\n/**\n * Create a validator.\n *\n * @param validate A validation function for the schema.\n */\nexport function validator<OBJECT>(\n  validate?: undefined | ((value: unknown) => ValidationResult<OBJECT>),\n): Validator<OBJECT> {\n  return { [validatorSymbol]: true, validate };\n}\n\nexport function isValidator(value: unknown): value is Validator {\n  return (\n    typeof value === 'object' &&\n    value !== null &&\n    validatorSymbol in value &&\n    value[validatorSymbol] === true &&\n    'validate' in value\n  );\n}\n\nexport function asValidator<OBJECT>(\n  value: Validator<OBJECT> | z.Schema<OBJECT, z.ZodTypeDef, any>,\n): Validator<OBJECT> {\n  return isValidator(value) ? value : zodValidator(value);\n}\n\nexport function zodValidator<OBJECT>(\n  zodSchema: z.Schema<OBJECT, z.ZodTypeDef, any>,\n): Validator<OBJECT> {\n  return validator(value => {\n    const result = zodSchema.safeParse(value);\n    return result.success\n      ? { success: true, value: result.data }\n      : { success: false, error: result.error };\n  });\n}\n", "import { APICallError } from '@ai-sdk/provider';\nimport { extractResponseHeaders } from './extract-response-headers';\nimport { FetchFunction } from './fetch-function';\nimport { isAbortError } from './is-abort-error';\nimport { removeUndefinedEntries } from './remove-undefined-entries';\nimport { ResponseHandler } from './response-handler';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => globalThis.fetch;\n\nexport const postJsonToApi = async <T>({\n  url,\n  headers,\n  body,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch,\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  body: unknown;\n  failedResponseHandler: ResponseHandler<APICallError>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) =>\n  postToApi({\n    url,\n    headers: {\n      'Content-Type': 'application/json',\n      ...headers,\n    },\n    body: {\n      content: JSON.stringify(body),\n      values: body,\n    },\n    failed<PERSON><PERSON>po<PERSON><PERSON><PERSON><PERSON>,\n    successfulResponseHandler,\n    abortSignal,\n    fetch,\n  });\n\nexport const postToApi = async <T>({\n  url,\n  headers = {},\n  body,\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch(),\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  body: {\n    content: string | FormData | Uint8Array;\n    values: unknown;\n  };\n  failedResponseHandler: ResponseHandler<Error>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: 'POST',\n      headers: removeUndefinedEntries(headers),\n      body: body.content,\n      signal: abortSignal,\n    });\n\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!response.ok) {\n      let errorInformation: {\n        value: Error;\n        responseHeaders?: Record<string, string> | undefined;\n      };\n\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: body.values,\n        });\n      } catch (error) {\n        if (isAbortError(error) || APICallError.isAPICallError(error)) {\n          throw error;\n        }\n\n        throw new APICallError({\n          message: 'Failed to process error response',\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: body.values,\n        });\n      }\n\n      throw errorInformation.value;\n    }\n\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: body.values,\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || APICallError.isAPICallError(error)) {\n          throw error;\n        }\n      }\n\n      throw new APICallError({\n        message: 'Failed to process successful response',\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: body.values,\n      });\n    }\n  } catch (error) {\n    if (isAbortError(error)) {\n      throw error;\n    }\n\n    // unwrap original error when fetch failed (for easier debugging):\n    if (error instanceof TypeError && error.message === 'fetch failed') {\n      const cause = (error as any).cause;\n\n      if (cause != null) {\n        // Failed to connect to server:\n        throw new APICallError({\n          message: `Cannot connect to API: ${cause.message}`,\n          cause,\n          url,\n          requestBodyValues: body.values,\n          isRetryable: true, // retry when network error\n        });\n      }\n    }\n\n    throw error;\n  }\n};\n", "export function removeUndefinedEntries<T>(\n  record: Record<string, T | undefined>,\n): Record<string, T> {\n  return Object.fromEntries(\n    Object.entries(record).filter(([_key, value]) => value != null),\n  ) as Record<string, T>;\n}\n", "import { APICallError, EmptyResponseBodyError } from '@ai-sdk/provider';\nimport {\n  EventSourceParserStream,\n  ParsedEvent,\n} from 'eventsource-parser/stream';\nimport { ZodSchema } from 'zod';\nimport { extractResponseHeaders } from './extract-response-headers';\nimport { ParseResult, parseJSON, safeParseJSON } from './parse-json';\n\nexport type ResponseHandler<RETURN_TYPE> = (options: {\n  url: string;\n  requestBodyValues: unknown;\n  response: Response;\n}) => PromiseLike<{\n  value: RETURN_TYPE;\n  responseHeaders?: Record<string, string>;\n}>;\n\nexport const createJsonErrorResponseHandler =\n  <T>({\n    errorSchema,\n    errorToMessage,\n    isRetryable,\n  }: {\n    errorSchema: ZodSchema<T>;\n    errorToMessage: (error: T) => string;\n    isRetryable?: (response: Response, error?: T) => boolean;\n  }): ResponseHandler<APICallError> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseBody = await response.text();\n    const responseHeaders = extractResponseHeaders(response);\n\n    // Some providers return an empty response body for some errors:\n    if (responseBody.trim() === '') {\n      return {\n        responseHeaders,\n        value: new APICallError({\n          message: response.statusText,\n          url,\n          requestBodyValues,\n          statusCode: response.status,\n          responseHeaders,\n          responseBody,\n          isRetryable: isRetryable?.(response),\n        }),\n      };\n    }\n\n    // resilient parsing in case the response is not JSON or does not match the schema:\n    try {\n      const parsedError = parseJSON({\n        text: responseBody,\n        schema: errorSchema,\n      });\n\n      return {\n        responseHeaders,\n        value: new APICallError({\n          message: errorToMessage(parsedError),\n          url,\n          requestBodyValues,\n          statusCode: response.status,\n          responseHeaders,\n          responseBody,\n          data: parsedError,\n          isRetryable: isRetryable?.(response, parsedError),\n        }),\n      };\n    } catch (parseError) {\n      return {\n        responseHeaders,\n        value: new APICallError({\n          message: response.statusText,\n          url,\n          requestBodyValues,\n          statusCode: response.status,\n          responseHeaders,\n          responseBody,\n          isRetryable: isRetryable?.(response),\n        }),\n      };\n    }\n  };\n\nexport const createEventSourceResponseHandler =\n  <T>(\n    chunkSchema: ZodSchema<T>,\n  ): ResponseHandler<ReadableStream<ParseResult<T>>> =>\n  async ({ response }: { response: Response }) => {\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (response.body == null) {\n      throw new EmptyResponseBodyError({});\n    }\n\n    return {\n      responseHeaders,\n      value: response.body\n        .pipeThrough(new TextDecoderStream())\n        .pipeThrough(new EventSourceParserStream())\n        .pipeThrough(\n          new TransformStream<ParsedEvent, ParseResult<T>>({\n            transform({ data }, controller) {\n              // ignore the 'DONE' event that e.g. OpenAI sends:\n              if (data === '[DONE]') {\n                return;\n              }\n\n              controller.enqueue(\n                safeParseJSON({\n                  text: data,\n                  schema: chunkSchema,\n                }),\n              );\n            },\n          }),\n        ),\n    };\n  };\n\nexport const createJsonStreamResponseHandler =\n  <T>(\n    chunkSchema: ZodSchema<T>,\n  ): ResponseHandler<ReadableStream<ParseResult<T>>> =>\n  async ({ response }: { response: Response }) => {\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (response.body == null) {\n      throw new EmptyResponseBodyError({});\n    }\n\n    let buffer = '';\n\n    return {\n      responseHeaders,\n      value: response.body.pipeThrough(new TextDecoderStream()).pipeThrough(\n        new TransformStream<string, ParseResult<T>>({\n          transform(chunkText, controller) {\n            if (chunkText.endsWith('\\n')) {\n              controller.enqueue(\n                safeParseJSON({\n                  text: buffer + chunkText,\n                  schema: chunkSchema,\n                }),\n              );\n              buffer = '';\n            } else {\n              buffer += chunkText;\n            }\n          },\n        }),\n      ),\n    };\n  };\n\nexport const createJsonResponseHandler =\n  <T>(responseSchema: ZodSchema<T>): ResponseHandler<T> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseBody = await response.text();\n\n    const parsedResult = safeParseJSON({\n      text: responseBody,\n      schema: responseSchema,\n    });\n\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!parsedResult.success) {\n      throw new APICallError({\n        message: 'Invalid JSON response',\n        cause: parsedResult.error,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        url,\n        requestBodyValues,\n      });\n    }\n\n    return {\n      responseHeaders,\n      value: parsedResult.value,\n    };\n  };\n", "// btoa and atob need to be invoked as a function call, not as a method call.\n// Otherwise CloudFlare will throw a\n// \"TypeError: Illegal invocation: function called with incorrect this reference\"\nconst { btoa, atob } = globalThis;\n\nexport function convertBase64ToUint8Array(base64String: string) {\n  const base64Url = base64String.replace(/-/g, '+').replace(/_/g, '/');\n  const latin1string = atob(base64Url);\n  return Uint8Array.from(latin1string, byte => byte.codePointAt(0)!);\n}\n\nexport function convertUint8ArrayToBase64(array: Uint8Array): string {\n  let latin1string = '';\n\n  // Note: regular for loop to support older JavaScript versions that\n  // do not support for..of on Uint8Array\n  for (let i = 0; i < array.length; i++) {\n    latin1string += String.fromCodePoint(array[i]);\n  }\n\n  return btoa(latin1string);\n}\n", "export function withoutTrailingSlash(url: string | undefined) {\n  return url?.replace(/\\/$/, '');\n}\n", "import {\n  generateId,\n  loadApiKey,\n  withoutTrailingSlash,\n} from '@ai-sdk/provider-utils';\nimport { GoogleGenerativeAILanguageModel } from './google-generative-ai-language-model';\nimport {\n  GoogleGenerativeAIModelId,\n  GoogleGenerativeAISettings,\n} from './google-generative-ai-settings';\nimport { GoogleGenerativeAIProviderSettings } from './google-provider';\n\n/**\n * @deprecated Use `createGoogleGenerativeAI` instead.\n */\nexport class Google {\n  /**\n   * Base URL for the Google API calls.\n   */\n  readonly baseURL: string;\n\n  readonly apiKey?: string;\n\n  readonly headers?: Record<string, string>;\n\n  private readonly generateId: () => string;\n\n  /**\n   * Creates a new Google provider instance.\n   */\n  constructor(options: GoogleGenerativeAIProviderSettings = {}) {\n    this.baseURL =\n      withoutTrailingSlash(options.baseURL ?? options.baseUrl) ??\n      'https://generativelanguage.googleapis.com/v1beta';\n    this.apiKey = options.apiKey;\n    this.headers = options.headers;\n    this.generateId = options.generateId ?? generateId;\n  }\n\n  private get baseConfig() {\n    return {\n      baseURL: this.baseURL,\n      headers: () => ({\n        'x-goog-api-key': loadApiKey({\n          apiKey: this.apiKey,\n          environmentVariableName: 'GOOGLE_GENERATIVE_AI_API_KEY',\n          description: 'Google Generative AI',\n        }),\n        ...this.headers,\n      }),\n    };\n  }\n\n  /**\n   * @deprecated Use `chat()` instead.\n   */\n  generativeAI(\n    modelId: GoogleGenerativeAIModelId,\n    settings: GoogleGenerativeAISettings = {},\n  ) {\n    return this.chat(modelId, settings);\n  }\n\n  chat(\n    modelId: GoogleGenerativeAIModelId,\n    settings: GoogleGenerativeAISettings = {},\n  ) {\n    return new GoogleGenerativeAILanguageModel(modelId, settings, {\n      provider: 'google.generative-ai',\n      ...this.baseConfig,\n      generateId: this.generateId,\n    });\n  }\n}\n", "import {\n  LanguageModelV1,\n  LanguageModelV1CallWarning,\n  LanguageModelV1FinishReason,\n  LanguageModelV1StreamPart,\n} from '@ai-sdk/provider';\nimport {\n  FetchFunction,\n  ParseResult,\n  combineHeaders,\n  createEventSourceResponseHandler,\n  create<PERSON>sonResponse<PERSON>andler,\n  postJsonToApi,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { convertJSONSchemaToOpenAPISchema } from './convert-json-schema-to-openapi-schema';\nimport { convertToGoogleGenerativeAIMessages } from './convert-to-google-generative-ai-messages';\nimport { getModelPath } from './get-model-path';\nimport { googleFailedResponseHandler } from './google-error';\nimport { GoogleGenerativeAIContentPart } from './google-generative-ai-prompt';\nimport {\n  GoogleGenerativeAIModelId,\n  GoogleGenerativeAISettings,\n} from './google-generative-ai-settings';\nimport { mapGoogleGenerativeAIFinishReason } from './map-google-generative-ai-finish-reason';\n\ntype GoogleGenerativeAIConfig = {\n  provider: string;\n  baseURL: string;\n  headers: () => Record<string, string | undefined>;\n  generateId: () => string;\n  fetch?: FetchFunction;\n};\n\nexport class GoogleGenerativeAILanguageModel implements LanguageModelV1 {\n  readonly specificationVersion = 'v1';\n  readonly defaultObjectGenerationMode = 'json';\n  readonly supportsImageUrls = false;\n\n  get supportsObjectGeneration() {\n    return this.settings.structuredOutputs !== false;\n  }\n\n  readonly modelId: GoogleGenerativeAIModelId;\n  readonly settings: GoogleGenerativeAISettings;\n\n  private readonly config: GoogleGenerativeAIConfig;\n\n  constructor(\n    modelId: GoogleGenerativeAIModelId,\n    settings: GoogleGenerativeAISettings,\n    config: GoogleGenerativeAIConfig,\n  ) {\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  private async getArgs({\n    mode,\n    prompt,\n    maxTokens,\n    temperature,\n    topP,\n    topK,\n    frequencyPenalty,\n    presencePenalty,\n    stopSequences,\n    responseFormat,\n    seed,\n  }: Parameters<LanguageModelV1['doGenerate']>[0]) {\n    const type = mode.type;\n\n    const warnings: LanguageModelV1CallWarning[] = [];\n\n    if (seed != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'seed',\n      });\n    }\n\n    const generationConfig = {\n      // standardized settings:\n      maxOutputTokens: maxTokens,\n      temperature,\n      topK: topK ?? this.settings.topK,\n      topP,\n      frequencyPenalty,\n      presencePenalty,\n      stopSequences,\n\n      // response format:\n      responseMimeType:\n        responseFormat?.type === 'json' ? 'application/json' : undefined,\n      responseSchema:\n        responseFormat?.type === 'json' &&\n        responseFormat.schema != null &&\n        // Google GenAI does not support all OpenAPI Schema features,\n        // so this is needed as an escape hatch:\n        this.supportsObjectGeneration\n          ? convertJSONSchemaToOpenAPISchema(responseFormat.schema)\n          : undefined,\n    };\n\n    const { contents, systemInstruction } =\n      convertToGoogleGenerativeAIMessages(prompt);\n\n    switch (type) {\n      case 'regular': {\n        return {\n          args: {\n            generationConfig,\n            contents,\n            systemInstruction,\n            safetySettings: this.settings.safetySettings,\n            ...prepareToolsAndToolConfig(mode),\n            cachedContent: this.settings.cachedContent,\n          },\n          warnings,\n        };\n      }\n\n      case 'object-json': {\n        return {\n          args: {\n            generationConfig: {\n              ...generationConfig,\n              responseMimeType: 'application/json',\n              responseSchema:\n                mode.schema != null &&\n                // Google GenAI does not support all OpenAPI Schema features,\n                // so this is needed as an escape hatch:\n                this.supportsObjectGeneration\n                  ? convertJSONSchemaToOpenAPISchema(mode.schema)\n                  : undefined,\n            },\n            contents,\n            systemInstruction,\n            safetySettings: this.settings.safetySettings,\n            cachedContent: this.settings.cachedContent,\n          },\n          warnings,\n        };\n      }\n\n      case 'object-tool': {\n        return {\n          args: {\n            generationConfig,\n            contents,\n            tools: {\n              functionDeclarations: [\n                {\n                  name: mode.tool.name,\n                  description: mode.tool.description ?? '',\n                  parameters: convertJSONSchemaToOpenAPISchema(\n                    mode.tool.parameters,\n                  ),\n                },\n              ],\n            },\n            toolConfig: { functionCallingConfig: { mode: 'ANY' } },\n            safetySettings: this.settings.safetySettings,\n            cachedContent: this.settings.cachedContent,\n          },\n          warnings,\n        };\n      }\n\n      default: {\n        const _exhaustiveCheck: never = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  async doGenerate(\n    options: Parameters<LanguageModelV1['doGenerate']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doGenerate']>>> {\n    const { args, warnings } = await this.getArgs(options);\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: `${this.config.baseURL}/${getModelPath(\n        this.modelId,\n      )}:generateContent`,\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body: args,\n      failedResponseHandler: googleFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(responseSchema),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { contents: rawPrompt, ...rawSettings } = args;\n    const candidate = response.candidates[0];\n\n    const toolCalls = getToolCallsFromParts({\n      parts: candidate.content.parts,\n      generateId: this.config.generateId,\n    });\n\n    const usageMetadata = response.usageMetadata;\n\n    return {\n      text: getTextFromParts(candidate.content.parts),\n      toolCalls,\n      finishReason: mapGoogleGenerativeAIFinishReason({\n        finishReason: candidate.finishReason,\n        hasToolCalls: toolCalls != null && toolCalls.length > 0,\n      }),\n      usage: {\n        promptTokens: usageMetadata?.promptTokenCount ?? NaN,\n        completionTokens: usageMetadata?.candidatesTokenCount ?? NaN,\n      },\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings,\n    };\n  }\n\n  async doStream(\n    options: Parameters<LanguageModelV1['doStream']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doStream']>>> {\n    const { args, warnings } = await this.getArgs(options);\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: `${this.config.baseURL}/${getModelPath(\n        this.modelId,\n      )}:streamGenerateContent?alt=sse`,\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body: args,\n      failedResponseHandler: googleFailedResponseHandler,\n      successfulResponseHandler: createEventSourceResponseHandler(chunkSchema),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { contents: rawPrompt, ...rawSettings } = args;\n\n    let finishReason: LanguageModelV1FinishReason = 'unknown';\n    let usage: { promptTokens: number; completionTokens: number } = {\n      promptTokens: Number.NaN,\n      completionTokens: Number.NaN,\n    };\n\n    const generateId = this.config.generateId;\n    let hasToolCalls = false;\n\n    return {\n      stream: response.pipeThrough(\n        new TransformStream<\n          ParseResult<z.infer<typeof chunkSchema>>,\n          LanguageModelV1StreamPart\n        >({\n          transform(chunk, controller) {\n            if (!chunk.success) {\n              controller.enqueue({ type: 'error', error: chunk.error });\n              return;\n            }\n\n            const value = chunk.value;\n\n            const candidate = value.candidates[0];\n\n            if (candidate?.finishReason != null) {\n              finishReason = mapGoogleGenerativeAIFinishReason({\n                finishReason: candidate.finishReason,\n                hasToolCalls,\n              });\n            }\n\n            const usageMetadata = value.usageMetadata;\n\n            if (usageMetadata != null) {\n              usage = {\n                promptTokens: usageMetadata.promptTokenCount ?? NaN,\n                completionTokens: usageMetadata.candidatesTokenCount ?? NaN,\n              };\n            }\n\n            const content = candidate.content;\n\n            if (content == null) {\n              return;\n            }\n\n            const deltaText = getTextFromParts(content.parts);\n            if (deltaText != null) {\n              controller.enqueue({\n                type: 'text-delta',\n                textDelta: deltaText,\n              });\n            }\n\n            const toolCallDeltas = getToolCallsFromParts({\n              parts: content.parts,\n              generateId,\n            });\n\n            if (toolCallDeltas != null) {\n              for (const toolCall of toolCallDeltas) {\n                controller.enqueue({\n                  type: 'tool-call-delta',\n                  toolCallType: 'function',\n                  toolCallId: toolCall.toolCallId,\n                  toolName: toolCall.toolName,\n                  argsTextDelta: toolCall.args,\n                });\n\n                controller.enqueue({\n                  type: 'tool-call',\n                  toolCallType: 'function',\n                  toolCallId: toolCall.toolCallId,\n                  toolName: toolCall.toolName,\n                  args: toolCall.args,\n                });\n\n                hasToolCalls = true;\n              }\n            }\n          },\n\n          flush(controller) {\n            controller.enqueue({ type: 'finish', finishReason, usage });\n          },\n        }),\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings,\n    };\n  }\n}\n\nfunction getToolCallsFromParts({\n  parts,\n  generateId,\n}: {\n  parts: z.infer<typeof contentSchema>['parts'];\n  generateId: () => string;\n}) {\n  const functionCallParts = parts.filter(\n    part => 'functionCall' in part,\n  ) as Array<\n    GoogleGenerativeAIContentPart & {\n      functionCall: { name: string; args: unknown };\n    }\n  >;\n\n  return functionCallParts.length === 0\n    ? undefined\n    : functionCallParts.map(part => ({\n        toolCallType: 'function' as const,\n        toolCallId: generateId(),\n        toolName: part.functionCall.name,\n        args: JSON.stringify(part.functionCall.args),\n      }));\n}\n\nfunction getTextFromParts(parts: z.infer<typeof contentSchema>['parts']) {\n  const textParts = parts.filter(part => 'text' in part) as Array<\n    GoogleGenerativeAIContentPart & { text: string }\n  >;\n\n  return textParts.length === 0\n    ? undefined\n    : textParts.map(part => part.text).join('');\n}\n\nconst contentSchema = z.object({\n  role: z.string(),\n  parts: z.array(\n    z.union([\n      z.object({\n        text: z.string(),\n      }),\n      z.object({\n        functionCall: z.object({\n          name: z.string(),\n          args: z.unknown(),\n        }),\n      }),\n    ]),\n  ),\n});\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst responseSchema = z.object({\n  candidates: z.array(\n    z.object({\n      content: contentSchema,\n      finishReason: z.string().optional(),\n    }),\n  ),\n  usageMetadata: z\n    .object({\n      promptTokenCount: z.number(),\n      candidatesTokenCount: z.number().nullish(),\n      totalTokenCount: z.number(),\n    })\n    .optional(),\n});\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst chunkSchema = z.object({\n  candidates: z.array(\n    z.object({\n      content: contentSchema.optional(),\n      finishReason: z.string().optional(),\n    }),\n  ),\n  usageMetadata: z\n    .object({\n      promptTokenCount: z.number(),\n      candidatesTokenCount: z.number().nullish(),\n      totalTokenCount: z.number(),\n    })\n    .optional(),\n});\n\nfunction prepareToolsAndToolConfig(\n  mode: Parameters<LanguageModelV1['doGenerate']>[0]['mode'] & {\n    type: 'regular';\n  },\n) {\n  // when the tools array is empty, change it to undefined to prevent errors:\n  const tools = mode.tools?.length ? mode.tools : undefined;\n\n  if (tools == null) {\n    return { tools: undefined, toolConfig: undefined };\n  }\n\n  const mappedTools = {\n    functionDeclarations: tools.map(tool => ({\n      name: tool.name,\n      description: tool.description ?? '',\n      parameters: convertJSONSchemaToOpenAPISchema(tool.parameters),\n    })),\n  };\n\n  const toolChoice = mode.toolChoice;\n\n  if (toolChoice == null) {\n    return { tools: mappedTools, toolConfig: undefined };\n  }\n\n  const type = toolChoice.type;\n\n  switch (type) {\n    case 'auto':\n      return {\n        tools: mappedTools,\n        toolConfig: { functionCallingConfig: { mode: 'AUTO' } },\n      };\n    case 'none':\n      return {\n        tools: mappedTools,\n        toolConfig: { functionCallingConfig: { mode: 'NONE' } },\n      };\n    case 'required':\n      return {\n        tools: mappedTools,\n        toolConfig: { functionCallingConfig: { mode: 'ANY' } },\n      };\n    case 'tool':\n      return {\n        tools: mappedTools,\n        toolConfig: {\n          functionCallingConfig: {\n            mode: 'ANY',\n            allowedFunctionNames: [toolChoice.toolName],\n          },\n        },\n      };\n    default: {\n      const _exhaustiveCheck: never = type;\n      throw new Error(`Unsupported tool choice type: ${_exhaustiveCheck}`);\n    }\n  }\n}\n", "import { JSONSchema7Definition } from 'json-schema';\n\n/**\n * Converts JSON Schema 7 to OpenAPI Schema 3.0\n */\nexport function convertJSONSchemaToOpenAPISchema(\n  jsonSchema: JSONSchema7Definition,\n): unknown {\n  if (typeof jsonSchema === 'boolean') {\n    return { type: 'boolean', properties: {} };\n  }\n\n  const {\n    type,\n    description,\n    required,\n    properties,\n    items,\n    allOf,\n    anyOf,\n    oneOf,\n    format,\n    const: constValue,\n    minLength,\n  } = jsonSchema;\n\n  const result: Record<string, unknown> = {};\n\n  if (description) result.description = description;\n  if (required) result.required = required;\n  if (format) result.format = format;\n\n  if (constValue !== undefined) {\n    result.enum = [constValue];\n  }\n\n  // Handle type\n  if (type) {\n    if (Array.isArray(type)) {\n      if (type.includes('null')) {\n        result.type = type.filter(t => t !== 'null')[0];\n        result.nullable = true;\n      } else {\n        result.type = type;\n      }\n    } else if (type === 'null') {\n      result.type = 'null';\n    } else {\n      result.type = type;\n    }\n  }\n\n  if (properties) {\n    result.properties = Object.entries(properties).reduce(\n      (acc, [key, value]) => {\n        acc[key] = convertJSONSchemaToOpenAPISchema(value);\n        return acc;\n      },\n      {} as Record<string, unknown>,\n    );\n  }\n\n  if (items) {\n    result.items = Array.isArray(items)\n      ? items.map(convertJSONSchemaToOpenAPISchema)\n      : convertJSONSchemaToOpenAPISchema(items);\n  }\n\n  if (allOf) {\n    result.allOf = allOf.map(convertJSONSchemaToOpenAPISchema);\n  }\n  if (anyOf) {\n    result.anyOf = anyOf.map(convertJSONSchemaToOpenAPISchema);\n  }\n  if (oneOf) {\n    result.oneOf = oneOf.map(convertJSONSchemaToOpenAPISchema);\n  }\n\n  if (minLength !== undefined) result.minLength = minLength;\n\n  return result;\n}\n", "import {\n  LanguageModelV1Prompt,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport { convertUint8ArrayToBase64 } from '@ai-sdk/provider-utils';\nimport {\n  GoogleGenerativeAIContent,\n  GoogleGenerativeAIContentPart,\n  GoogleGenerativeAIPrompt,\n} from './google-generative-ai-prompt';\n\nexport function convertToGoogleGenerativeAIMessages(\n  prompt: LanguageModelV1Prompt,\n): GoogleGenerativeAIPrompt {\n  const systemInstructionParts: Array<{ text: string }> = [];\n  const contents: Array<GoogleGenerativeAIContent> = [];\n  let systemMessagesAllowed = true;\n\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case 'system': {\n        if (!systemMessagesAllowed) {\n          throw new UnsupportedFunctionalityError({\n            functionality:\n              'system messages are only supported at the beginning of the conversation',\n          });\n        }\n\n        systemInstructionParts.push({ text: content });\n        break;\n      }\n\n      case 'user': {\n        systemMessagesAllowed = false;\n\n        const parts: GoogleGenerativeAIContentPart[] = [];\n\n        for (const part of content) {\n          switch (part.type) {\n            case 'text': {\n              parts.push({ text: part.text });\n              break;\n            }\n            case 'image': {\n              if (part.image instanceof URL) {\n                // The AI SDK automatically downloads images for user image parts with URLs\n                throw new UnsupportedFunctionalityError({\n                  functionality: 'Image URLs in user messages',\n                });\n              }\n\n              parts.push({\n                inlineData: {\n                  mimeType: part.mimeType ?? 'image/jpeg',\n                  data: convertUint8ArrayToBase64(part.image),\n                },\n              });\n\n              break;\n            }\n            case 'file': {\n              if (part.data instanceof URL) {\n                // The AI SDK automatically downloads files for user file parts with URLs\n                throw new UnsupportedFunctionalityError({\n                  functionality: 'File URLs in user messages',\n                });\n              }\n\n              parts.push({\n                inlineData: { mimeType: part.mimeType, data: part.data },\n              });\n\n              break;\n            }\n          }\n        }\n\n        contents.push({ role: 'user', parts });\n        break;\n      }\n\n      case 'assistant': {\n        systemMessagesAllowed = false;\n\n        contents.push({\n          role: 'model',\n          parts: content\n            .map(part => {\n              switch (part.type) {\n                case 'text': {\n                  return part.text.length === 0\n                    ? undefined\n                    : { text: part.text };\n                }\n                case 'tool-call': {\n                  return {\n                    functionCall: {\n                      name: part.toolName,\n                      args: part.args,\n                    },\n                  };\n                }\n              }\n            })\n            .filter(\n              part => part !== undefined,\n            ) as GoogleGenerativeAIContentPart[],\n        });\n        break;\n      }\n\n      case 'tool': {\n        systemMessagesAllowed = false;\n\n        contents.push({\n          role: 'user',\n          parts: content.map(part => ({\n            functionResponse: {\n              name: part.toolName,\n              response: part.result,\n            },\n          })),\n        });\n        break;\n      }\n      default: {\n        const _exhaustiveCheck: never = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  return {\n    systemInstruction:\n      systemInstructionParts.length > 0\n        ? { parts: systemInstructionParts }\n        : undefined,\n    contents,\n  };\n}\n", "export function getModelPath(modelId: string): string {\n  return modelId.includes('/') ? modelId : `models/${modelId}`;\n}\n", "import { createJsonErrorResponseHandler } from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\n\nconst googleErrorDataSchema = z.object({\n  error: z.object({\n    code: z.number().nullable(),\n    message: z.string(),\n    status: z.string(),\n  }),\n});\n\nexport type GoogleErrorData = z.infer<typeof googleErrorDataSchema>;\n\nexport const googleFailedResponseHandler = createJsonErrorResponseHandler({\n  errorSchema: googleErrorDataSchema,\n  errorToMessage: data => data.error.message,\n});\n", "import { LanguageModelV1FinishReason } from '@ai-sdk/provider';\n\nexport function mapGoogleGenerativeAIFinishReason({\n  finishReason,\n  hasToolCalls,\n}: {\n  finishReason: string | null | undefined;\n  hasToolCalls: boolean;\n}): LanguageModelV1FinishReason {\n  switch (finishReason) {\n    case 'STOP':\n      return hasToolCalls ? 'tool-calls' : 'stop';\n    case 'MAX_TOKENS':\n      return 'length';\n    case 'RECITATION':\n    case 'SAFETY':\n      return 'content-filter';\n    case 'FINISH_REASON_UNSPECIFIED':\n    case 'OTHER':\n      return 'other';\n    default:\n      return 'unknown';\n  }\n}\n", "import {\n  FetchFunction,\n  generateId,\n  loadApi<PERSON><PERSON>,\n  withoutTrailingSlash,\n} from '@ai-sdk/provider-utils';\nimport { GoogleGenerativeAILanguageModel } from './google-generative-ai-language-model';\nimport {\n  GoogleGenerativeAIModelId,\n  GoogleGenerativeAISettings,\n} from './google-generative-ai-settings';\nimport { GoogleGenerativeAIEmbeddingModel } from './google-generative-ai-embedding-model';\nimport {\n  GoogleGenerativeAIEmbeddingModelId,\n  GoogleGenerativeAIEmbeddingSettings,\n} from './google-generative-ai-embedding-settings';\nimport {\n  EmbeddingModelV1,\n  LanguageModelV1,\n  ProviderV1,\n} from '@ai-sdk/provider';\n\nexport interface GoogleGenerativeAIProvider extends ProviderV1 {\n  (\n    modelId: GoogleGenerativeAIModelId,\n    settings?: GoogleGenerativeAISettings,\n  ): LanguageModelV1;\n\n  languageModel(\n    modelId: GoogleGenerativeAIModelId,\n    settings?: GoogleGenerativeAISettings,\n  ): LanguageModelV1;\n\n  chat(\n    modelId: GoogleGenerativeAIModelId,\n    settings?: GoogleGenerativeAISettings,\n  ): LanguageModelV1;\n\n  /**\n   * @deprecated Use `chat()` instead.\n   */\n  generativeAI(\n    modelId: GoogleGenerativeAIModelId,\n    settings?: GoogleGenerativeAISettings,\n  ): LanguageModelV1;\n\n  /**\n@deprecated Use `textEmbeddingModel()` instead.\n   */\n  embedding(\n    modelId: GoogleGenerativeAIEmbeddingModelId,\n    settings?: GoogleGenerativeAIEmbeddingSettings,\n  ): EmbeddingModelV1<string>;\n\n  /**\n@deprecated Use `textEmbeddingModel()` instead.\n */\n  textEmbedding(\n    modelId: GoogleGenerativeAIEmbeddingModelId,\n    settings?: GoogleGenerativeAIEmbeddingSettings,\n  ): EmbeddingModelV1<string>;\n\n  textEmbeddingModel(\n    modelId: GoogleGenerativeAIEmbeddingModelId,\n    settings?: GoogleGenerativeAIEmbeddingSettings,\n  ): EmbeddingModelV1<string>;\n}\n\nexport interface GoogleGenerativeAIProviderSettings {\n  /**\nUse a different URL prefix for API calls, e.g. to use proxy servers.\nThe default prefix is `https://generativelanguage.googleapis.com/v1beta`.\n   */\n  baseURL?: string;\n\n  /**\n@deprecated Use `baseURL` instead.\n   */\n  baseUrl?: string;\n\n  /**\nAPI key that is being send using the `x-goog-api-key` header.\nIt defaults to the `GOOGLE_GENERATIVE_AI_API_KEY` environment variable.\n   */\n  apiKey?: string;\n\n  /**\nCustom headers to include in the requests.\n     */\n  headers?: Record<string, string>;\n\n  /**\nCustom fetch implementation. You can use it as a middleware to intercept requests,\nor to provide a custom fetch implementation for e.g. testing.\n    */\n  fetch?: FetchFunction;\n\n  generateId?: () => string;\n}\n\n/**\nCreate a Google Generative AI provider instance.\n */\nexport function createGoogleGenerativeAI(\n  options: GoogleGenerativeAIProviderSettings = {},\n): GoogleGenerativeAIProvider {\n  const baseURL =\n    withoutTrailingSlash(options.baseURL ?? options.baseUrl) ??\n    'https://generativelanguage.googleapis.com/v1beta';\n\n  const getHeaders = () => ({\n    'x-goog-api-key': loadApiKey({\n      apiKey: options.apiKey,\n      environmentVariableName: 'GOOGLE_GENERATIVE_AI_API_KEY',\n      description: 'Google Generative AI',\n    }),\n    ...options.headers,\n  });\n\n  const createChatModel = (\n    modelId: GoogleGenerativeAIModelId,\n    settings: GoogleGenerativeAISettings = {},\n  ) =>\n    new GoogleGenerativeAILanguageModel(modelId, settings, {\n      provider: 'google.generative-ai',\n      baseURL,\n      headers: getHeaders,\n      generateId: options.generateId ?? generateId,\n      fetch: options.fetch,\n    });\n\n  const createEmbeddingModel = (\n    modelId: GoogleGenerativeAIEmbeddingModelId,\n    settings: GoogleGenerativeAIEmbeddingSettings = {},\n  ) =>\n    new GoogleGenerativeAIEmbeddingModel(modelId, settings, {\n      provider: 'google.generative-ai',\n      baseURL,\n      headers: getHeaders,\n      fetch: options.fetch,\n    });\n\n  const provider = function (\n    modelId: GoogleGenerativeAIModelId,\n    settings?: GoogleGenerativeAISettings,\n  ) {\n    if (new.target) {\n      throw new Error(\n        'The Google Generative AI model function cannot be called with the new keyword.',\n      );\n    }\n\n    return createChatModel(modelId, settings);\n  };\n\n  provider.languageModel = createChatModel;\n  provider.chat = createChatModel;\n  provider.generativeAI = createChatModel;\n  provider.embedding = createEmbeddingModel;\n  provider.textEmbedding = createEmbeddingModel;\n  provider.textEmbeddingModel = createEmbeddingModel;\n\n  return provider as GoogleGenerativeAIProvider;\n}\n\n/**\nDefault Google Generative AI provider instance.\n */\nexport const google = createGoogleGenerativeAI();\n", "import {\n  EmbeddingModelV1,\n  TooManyEmbeddingValuesForCallError,\n} from '@ai-sdk/provider';\nimport {\n  combineHeaders,\n  createJsonResponseHandler,\n  FetchFunction,\n  postJsonToApi,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { googleFailedResponseHandler } from './google-error';\nimport {\n  GoogleGenerativeAIEmbeddingModelId,\n  GoogleGenerativeAIEmbeddingSettings,\n} from './google-generative-ai-embedding-settings';\n\ntype GoogleGenerativeAIEmbeddingConfig = {\n  provider: string;\n  baseURL: string;\n  headers: () => Record<string, string | undefined>;\n  fetch?: FetchFunction;\n};\n\nexport class GoogleGenerativeAIEmbeddingModel\n  implements EmbeddingModelV1<string>\n{\n  readonly specificationVersion = 'v1';\n  readonly modelId: GoogleGenerativeAIEmbeddingModelId;\n\n  private readonly config: GoogleGenerativeAIEmbeddingConfig;\n  private readonly settings: GoogleGenerativeAIEmbeddingSettings;\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  get maxEmbeddingsPerCall(): number {\n    return 2048;\n  }\n\n  get supportsParallelCalls(): boolean {\n    return true;\n  }\n\n  constructor(\n    modelId: GoogleGenerativeAIEmbeddingModelId,\n    settings: GoogleGenerativeAIEmbeddingSettings,\n    config: GoogleGenerativeAIEmbeddingConfig,\n  ) {\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n\n  async doEmbed({\n    values,\n    headers,\n    abortSignal,\n  }: Parameters<EmbeddingModelV1<string>['doEmbed']>[0]): Promise<\n    Awaited<ReturnType<EmbeddingModelV1<string>['doEmbed']>>\n  > {\n    if (values.length > this.maxEmbeddingsPerCall) {\n      throw new TooManyEmbeddingValuesForCallError({\n        provider: this.provider,\n        modelId: this.modelId,\n        maxEmbeddingsPerCall: this.maxEmbeddingsPerCall,\n        values,\n      });\n    }\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: `${this.config.baseURL}/models/${this.modelId}:batchEmbedContents`,\n      headers: combineHeaders(this.config.headers(), headers),\n      body: {\n        requests: values.map(value => ({\n          model: `models/${this.modelId}`,\n          content: { role: 'user', parts: [{ text: value }] },\n          outputDimensionality: this.settings.outputDimensionality,\n        })),\n      },\n      failedResponseHandler: googleFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        googleGenerativeAITextEmbeddingResponseSchema,\n      ),\n      abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    return {\n      embeddings: response.embeddings.map(item => item.values),\n      usage: undefined,\n      rawResponse: { headers: responseHeaders },\n    };\n  }\n}\n\n// minimal version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst googleGenerativeAITextEmbeddingResponseSchema = z.object({\n  embeddings: z.array(z.object({ values: z.array(z.number()) })),\n});\n"], "mappings": ";;;;;;;;;;;;;AAIA,IAAM,SAAS;AACf,IAAM,SAAS,OAAO,IAAI,MAAM;AALhC,IAAA;AAWO,IAAM,cAAN,MAAMA,qBAAmB,MAAM;;;;;;;;;EAgBpC,YAAY;IACV,MAAAC;IACA;IACA;EACF,GAIG;AACD,UAAM,OAAO;AAxBf,SAAkB,EAAA,IAAU;AA0B1B,SAAK,OAAOA;AACZ,SAAK,QAAQ;EACf;;;;;;EAOA,OAAO,WAAW,OAAqC;AACrD,WAAOD,aAAW,UAAU,OAAO,MAAM;EAC3C;EAEA,OAAiB,UAAU,OAAgBE,UAAyB;AAClE,UAAM,eAAe,OAAO,IAAIA,QAAM;AACtC,WACE,SAAS,QACT,OAAO,UAAU,YACjB,gBAAgB,SAChB,OAAO,MAAM,YAAY,MAAM,aAC/B,MAAM,YAAY,MAAM;EAE5B;;;;;;;EAQA,SAAS;AACP,WAAO;MACL,MAAM,KAAK;MACX,SAAS,KAAK;IAChB;EACF;AACF;AA9DoB,KAAA;AADb,IAAM,aAAN;ACTP,IAAM,OAAO;AACb,IAAMA,UAAS,mBAAmB,IAAI;AACtC,IAAMC,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,eAAN,cAA2B,WAAW;EAa3C,YAAY;IACV;IACA;IACA;IACA;IACA;IACA;IACA;IACA,cAAc,cAAc,SACzB,eAAe;IACd,eAAe;IACf,eAAe;IACf,cAAc;;IAClB;EACF,GAUG;AACD,UAAM,EAAE,MAAM,SAAS,MAAM,CAAC;AArChC,SAAkBA,GAAAA,IAAU;AAuC1B,SAAK,MAAM;AACX,SAAK,oBAAoB;AACzB,SAAK,aAAa;AAClB,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,cAAc;AACnB,SAAK,OAAO;EACd;EAEA,OAAO,WAAW,OAAuC;AACvD,WAAO,WAAW,UAAU,OAAOF,OAAM;EAC3C;;;;EAKA,OAAO,eAAe,OAAuC;AAC3D,WACE,iBAAiB,SACjB,MAAM,SAAS,QACf,OAAQ,MAAuB,QAAQ,YACvC,OAAQ,MAAuB,sBAAsB,aACnD,MAAuB,cAAc,QACrC,OAAQ,MAAuB,eAAe,cAC9C,MAAuB,mBAAmB,QAC1C,OAAQ,MAAuB,oBAAoB,cACnD,MAAuB,gBAAgB,QACvC,OAAQ,MAAuB,iBAAiB,cAChD,MAAuB,SAAS,QAChC,OAAQ,MAAuB,UAAU,aAC3C,OAAQ,MAAuB,gBAAgB,cAC7C,MAAuB,QAAQ,QAC/B,OAAQ,MAAuB,SAAS;EAE9C;;;;EAKA,SAAS;AACP,WAAO;MACL,MAAM,KAAK;MACX,SAAS,KAAK;MACd,KAAK,KAAK;MACV,mBAAmB,KAAK;MACxB,YAAY,KAAK;MACjB,iBAAiB,KAAK;MACtB,cAAc,KAAK;MACnB,OAAO,KAAK;MACZ,aAAa,KAAK;MAClB,MAAM,KAAK;IACb;EACF;AACF;AA5FoBE,MAAAD;ACLpB,IAAMF,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,yBAAN,cAAqC,WAAW;;EAGrD,YAAY,EAAE,UAAU,sBAAsB,IAA0B,CAAC,GAAG;AAC1E,UAAM,EAAE,MAAAH,OAAM,QAAQ,CAAC;AAHzB,SAAkBG,GAAAA,IAAU;EAI5B;EAEA,OAAO,WAAW,OAAiD;AACjE,WAAO,WAAW,UAAU,OAAOF,OAAM;EAC3C;;;;EAKA,OAAO,yBACL,OACiC;AACjC,WAAO,iBAAiB,SAAS,MAAM,SAASD;EAClD;AACF;AAlBoBG,MAAAD;ACPb,SAAS,gBAAgB,OAA4B;AAC1D,MAAI,SAAS,MAAM;AACjB,WAAO;EACT;AAEA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;EACT;AAEA,MAAI,iBAAiB,OAAO;AAC1B,WAAO,MAAM;EACf;AAEA,SAAO,KAAK,UAAU,KAAK;AAC7B;ACZA,IAAMF,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE;AAWoBC,MAAAC;ACTpB,IAAMC,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAMD,UAAS,OAAO,IAAIE,OAAM;AAJhC,IAAAH;AAWoBI,MAAAC;ACRpB,IAAMC,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAMD,UAAS,OAAO,IAAIE,OAAM;AALhC,IAAAH;AAOO,IAAM,iBAAN,cAA6B,WAAW;EAK7C,YAAY,EAAE,MAAM,MAAM,GAAqC;AAC7D,UAAM;MACJ,MAAAE;MACA,SACE,8BACS,IAAI;iBACK,gBAAgB,KAAK,CAAC;MAC1C;IACF,CAAC;AAZH,SAAkBF,GAAAA,IAAU;AAc1B,SAAK,OAAO;EACd;EAEA,OAAO,WAAW,OAAyC;AACzD,WAAO,WAAW,UAAU,OAAOG,OAAM;EAC3C;;;;EAKA,OAAO,iBAAiB,OAAyC;AAC/D,WACE,iBAAiB,SACjB,MAAM,SAASD,SACf,UAAU,SACV,OAAO,MAAM,SAAS;EAE1B;;;;EAKA,SAAS;AACP,WAAO;MACL,MAAM,KAAK;MACX,SAAS,KAAK;MACd,OAAO,KAAK;MACZ,OAAO,KAAK;MAEZ,WAAW,KAAK;IAClB;EACF;AACF;AA9CoBF,MAAAC;ACNpB,IAAMC,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAMD,UAAS,OAAO,IAAIE,OAAM;AAJhC,IAAAH;AAMO,IAAM,kBAAN,cAA8B,WAAW;;EAG9C,YAAY,EAAE,QAAQ,GAAwB;AAC5C,UAAM,EAAE,MAAAE,OAAM,QAAQ,CAAC;AAHzB,SAAkBF,GAAAA,IAAU;EAI5B;EAEA,OAAO,WAAW,OAA0C;AAC1D,WAAO,WAAW,UAAU,OAAOG,OAAM;EAC3C;;;;EAKA,OAAO,kBAAkB,OAA0C;AACjE,WAAO,iBAAiB,SAAS,MAAM,SAASD;EAClD;AACF;AAhBoBF,MAAAC;ACLpB,IAAMC,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAMD,UAAS,OAAO,IAAIE,OAAM;AAJhC,IAAAH;AAOoBI,MAAAC;ACLpB,IAAMC,QAAO;AACb,IAAMC,UAAS,mBAAmBD,KAAI;AACtC,IAAMD,UAAS,OAAO,IAAIE,OAAM;AAJhC,IAAAH;AAUoBI,MAAAC;ACRpB,IAAMC,QAAO;AACb,IAAMC,WAAS,mBAAmBD,KAAI;AACtC,IAAMD,WAAS,OAAO,IAAIE,QAAM;AAJhC,IAAAH;AAOoBI,OAAAC;ACLpB,IAAMC,SAAO;AACb,IAAMC,WAAS,mBAAmBD,MAAI;AACtC,IAAMD,WAAS,OAAO,IAAIE,QAAM;AAJhC,IAAAH;AAMO,IAAM,qCAAN,cAAiD,WAAW;EAQjE,YAAY,SAKT;AACD,UAAM;MACJ,MAAAE;MACA,SACE,oDACO,QAAQ,QAAQ,WAAW,QAAQ,OAAO,0BAC9C,QAAQ,oBAAoB,yBAAyB,QAAQ,OAAO,MAAM;IACjF,CAAC;AAnBH,SAAkBF,IAAAA,IAAU;AAqB1B,SAAK,WAAW,QAAQ;AACxB,SAAK,UAAU,QAAQ;AACvB,SAAK,uBAAuB,QAAQ;AACpC,SAAK,SAAS,QAAQ;EACxB;EAEA,OAAO,WACL,OAC6C;AAC7C,WAAO,WAAW,UAAU,OAAOG,QAAM;EAC3C;;;;EAKA,OAAO,qCACL,OAC6C;AAC7C,WACE,iBAAiB,SACjB,MAAM,SAASD,UACf,cAAc,SACd,OAAO,MAAM,aAAa,YAC1B,aAAa,SACb,OAAO,MAAM,YAAY,YACzB,0BAA0B,SAC1B,OAAO,MAAM,yBAAyB,YACtC,YAAY,SACZ,MAAM,QAAQ,MAAM,MAAM;EAE9B;;;;EAKA,SAAS;AACP,WAAO;MACL,MAAM,KAAK;MACX,SAAS,KAAK;MACd,OAAO,KAAK;MAEZ,UAAU,KAAK;MACf,SAAS,KAAK;MACd,sBAAsB,KAAK;MAC3B,QAAQ,KAAK;IACf;EACF;AACF;AApEoBF,OAAAC;ACJpB,IAAMC,SAAO;AACb,IAAMC,WAAS,mBAAmBD,MAAI;AACtC,IAAMD,WAAS,OAAO,IAAIE,QAAM;AALhC,IAAAH;AAOO,IAAM,uBAAN,MAAMI,8BAA4B,WAAW;EAKlD,YAAY,EAAE,OAAO,MAAM,GAAuC;AAChE,UAAM;MACJ,MAAAF;MACA,SACE,kCACU,KAAK,UAAU,KAAK,CAAC;iBACb,gBAAgB,KAAK,CAAC;MAC1C;IACF,CAAC;AAZH,SAAkBF,IAAAA,IAAU;AAc1B,SAAK,QAAQ;EACf;EAEA,OAAO,WAAW,OAA8C;AAC9D,WAAO,WAAW,UAAU,OAAOG,QAAM;EAC3C;;;;;;;;;;;EAYA,OAAO,KAAK;IACV;IACA;EACF,GAGwB;AACtB,WAAOC,sBAAoB,WAAW,KAAK,KAAK,MAAM,UAAU,QAC5D,QACA,IAAIA,sBAAoB,EAAE,OAAO,MAAM,CAAC;EAC9C;;;;EAKA,OAAO,sBAAsB,OAA8C;AACzE,WAAO,iBAAiB,SAAS,MAAM,SAASF;EAClD;;;;EAKA,SAAS;AACP,WAAO;MACL,MAAM,KAAK;MACX,SAAS,KAAK;MACd,OAAO,KAAK;MACZ,OAAO,KAAK;MAEZ,OAAO,KAAK;IACd;EACF;AACF;AA/DoBF,OAAAC;AADb,IAAM,sBAAN;ACLP,IAAMC,SAAO;AACb,IAAMC,WAAS,mBAAmBD,MAAI;AACtC,IAAMD,WAAS,OAAO,IAAIE,QAAM;AAJhC,IAAAH;AAMO,IAAM,gCAAN,cAA4C,WAAW;EAK5D,YAAY,EAAE,cAAc,GAA8B;AACxD,UAAM;MACJ,MAAAE;MACA,SAAS,IAAI,aAAa;IAC5B,CAAC;AARH,SAAkBF,IAAAA,IAAU;AAU1B,SAAK,gBAAgB;EACvB;EAEA,OAAO,WAAW,OAAwD;AACxE,WAAO,WAAW,UAAU,OAAOG,QAAM;EAC3C;;;;EAKA,OAAO,gCACL,OACwC;AACxC,WACE,iBAAiB,SACjB,MAAM,SAASD,UACf,OAAQ,MAAwC,kBAAkB;EAEtE;;;;EAKA,SAAS;AACP,WAAO;MACL,MAAM,KAAK;MACX,SAAS,KAAK;MACd,OAAO,KAAK;MAEZ,eAAe,KAAK;IACtB;EACF;AACF;AA1CoBF,OAAAC;;;AWFpB,+BAAuB;ATLhB,SAAS,kBACX,SACiC;AACpC,SAAO,QAAQ;IACb,CAAC,iBAAiB,oBAAoB;MACpC,GAAG;MACH,GAAI,kBAAA,OAAA,iBAAkB,CAAC;IACzB;IACA,CAAC;EACH;AACF;AEJO,SAAS,uBACd,UACwB;AACxB,QAAM,UAAkC,CAAC;AACzC,WAAS,QAAQ,QAAQ,CAAC,OAAO,QAAQ;AACvC,YAAQ,GAAG,IAAI;EACjB,CAAC;AACD,SAAO;AACT;ACJO,IAAM,oBAAoB,CAAC;EAChC,SAAS;EACT,MAAM,cAAc;EACpB,WAAW;AACb,IAII,CAAC,MAAmC;AACtC,QAAM,YAAY,eAAe,UAAU,WAAW;AACtD,SAAO,CAAA,SAAQ,GAAG,MAAM,GAAG,UAAU,IAAI,CAAC;AAC5C;AAQO,IAAM,aAAa,kBAAkB;AE7BrC,SAAS,aAAa,OAAgC;AAC3D,SACE,iBAAiB,UAChB,MAAM,SAAS,gBAAgB,MAAM,SAAS;AAEnD;ACHO,SAAS,WAAW;EACzB;EACA;EACA,sBAAsB;EACtB;AACF,GAKW;AACT,MAAI,OAAO,WAAW,UAAU;AAC9B,WAAO;EACT;AAEA,MAAI,UAAU,MAAM;AAClB,UAAM,IAAI,gBAAgB;MACxB,SAAS,GAAG,WAAW;IACzB,CAAC;EACH;AAEA,MAAI,OAAO,YAAY,aAAa;AAClC,UAAM,IAAI,gBAAgB;MACxB,SAAS,GAAG,WAAW,2CAA2C,mBAAmB;IACvF,CAAC;EACH;AAEA,WAAS,QAAQ,IAAI,uBAAuB;AAE5C,MAAI,UAAU,MAAM;AAClB,UAAM,IAAI,gBAAgB;MACxB,SAAS,GAAG,WAAW,2CAA2C,mBAAmB,sBAAsB,uBAAuB;IACpI,CAAC;EACH;AAEA,MAAI,OAAO,WAAW,UAAU;AAC9B,UAAM,IAAI,gBAAgB;MACxB,SAAS,GAAG,WAAW,+CAA+C,uBAAuB;IAC/F,CAAC;EACH;AAEA,SAAO;AACT;AKvCO,IAAM,kBAAkB,OAAO,IAAI,qBAAqB;AAwBxD,SAAS,UACd,UACmB;AACnB,SAAO,EAAE,CAAC,eAAe,GAAG,MAAM,SAAS;AAC7C;AAEO,SAAS,YAAY,OAAoC;AAC9D,SACE,OAAO,UAAU,YACjB,UAAU,QACV,mBAAmB,SACnB,MAAM,eAAe,MAAM,QAC3B,cAAc;AAElB;AAEO,SAAS,YACd,OACmB;AACnB,SAAO,YAAY,KAAK,IAAI,QAAQ,aAAa,KAAK;AACxD;AAEO,SAAS,aACd,WACmB;AACnB,SAAO,UAAU,CAAA,UAAS;AACxB,UAAM,SAAS,UAAU,UAAU,KAAK;AACxC,WAAO,OAAO,UACV,EAAE,SAAS,MAAM,OAAO,OAAO,KAAK,IACpC,EAAE,SAAS,OAAO,OAAO,OAAO,MAAM;EAC5C,CAAC;AACH;AD/CO,SAAS,cAAiB;EAC/B;EACA,QAAQ;AACV,GAGM;AACJ,QAAM,SAAS,kBAAkB,EAAE,OAAO,QAAQ,YAAY,CAAC;AAE/D,MAAI,CAAC,OAAO,SAAS;AACnB,UAAM,oBAAoB,KAAK,EAAE,OAAO,OAAO,OAAO,MAAM,CAAC;EAC/D;AAEA,SAAO,OAAO;AAChB;AAWO,SAAS,kBAAqB;EACnC;EACA;AACF,GAKmD;AACjD,QAAMI,aAAY,YAAY,MAAM;AAEpC,MAAI;AACF,QAAIA,WAAU,YAAY,MAAM;AAC9B,aAAO,EAAE,SAAS,MAAM,MAAkB;IAC5C;AAEA,UAAM,SAASA,WAAU,SAAS,KAAK;AAEvC,QAAI,OAAO,SAAS;AAClB,aAAO;IACT;AAEA,WAAO;MACL,SAAS;MACT,OAAO,oBAAoB,KAAK,EAAE,OAAO,OAAO,OAAO,MAAM,CAAC;IAChE;EACF,SAAS,OAAO;AACd,WAAO;MACL,SAAS;MACT,OAAO,oBAAoB,KAAK,EAAE,OAAO,OAAO,MAAM,CAAC;IACzD;EACF;AACF;ADtCO,SAAS,UAAa;EAC3B;EACA;AACF,GAGM;AACJ,MAAI;AACF,UAAM,QAAQ,yBAAAC,QAAW,MAAM,IAAI;AAEnC,QAAI,UAAU,MAAM;AAClB,aAAO;IACT;AAEA,WAAO,cAAc,EAAE,OAAO,OAAO,CAAC;EACxC,SAAS,OAAO;AACd,QACE,eAAe,iBAAiB,KAAK,KACrCC,oBAAoB,sBAAsB,KAAK,GAC/C;AACA,YAAM;IACR;AAEA,UAAM,IAAI,eAAe,EAAE,MAAM,OAAO,MAAM,CAAC;EACjD;AACF;AA4BO,SAAS,cAAiB;EAC/B;EACA;AACF,GAKoE;AAClE,MAAI;AACF,UAAM,QAAQ,yBAAAD,QAAW,MAAM,IAAI;AAEnC,QAAI,UAAU,MAAM;AAClB,aAAO;QACL,SAAS;QACT;MACF;IACF;AAEA,WAAO,kBAAkB,EAAE,OAAO,OAAO,CAAC;EAC5C,SAAS,OAAO;AACd,WAAO;MACL,SAAS;MACT,OAAO,eAAe,iBAAiB,KAAK,IACxC,QACA,IAAI,eAAe,EAAE,MAAM,OAAO,MAAM,CAAC;IAC/C;EACF;AACF;AIjHO,SAAS,uBACd,QACmB;AACnB,SAAO,OAAO;IACZ,OAAO,QAAQ,MAAM,EAAE,OAAO,CAAC,CAAC,MAAM,KAAK,MAAM,SAAS,IAAI;EAChE;AACF;ADEA,IAAM,mBAAmB,MAAM,WAAW;AAEnC,IAAM,gBAAgB,OAAU;EACrC;EACA;EACA;EACA;EACA;EACA;EACA;AACF,MASE,UAAU;EACR;EACA,SAAS;IACP,gBAAgB;IAChB,GAAG;EACL;EACA,MAAM;IACJ,SAAS,KAAK,UAAU,IAAI;IAC5B,QAAQ;EACV;EACA;EACA;EACA;EACA;AACF,CAAC;AAEI,IAAM,YAAY,OAAU;EACjC;EACA,UAAU,CAAC;EACX;EACA;EACA;EACA;EACA,QAAQ,iBAAiB;AAC3B,MAWM;AACJ,MAAI;AACF,UAAM,WAAW,MAAM,MAAM,KAAK;MAChC,QAAQ;MACR,SAAS,uBAAuB,OAAO;MACvC,MAAM,KAAK;MACX,QAAQ;IACV,CAAC;AAED,UAAM,kBAAkB,uBAAuB,QAAQ;AAEvD,QAAI,CAAC,SAAS,IAAI;AAChB,UAAI;AAKJ,UAAI;AACF,2BAAmB,MAAM,sBAAsB;UAC7C;UACA;UACA,mBAAmB,KAAK;QAC1B,CAAC;MACH,SAAS,OAAO;AACd,YAAI,aAAa,KAAK,KAAK,aAAa,eAAe,KAAK,GAAG;AAC7D,gBAAM;QACR;AAEA,cAAM,IAAI,aAAa;UACrB,SAAS;UACT,OAAO;UACP,YAAY,SAAS;UACrB;UACA;UACA,mBAAmB,KAAK;QAC1B,CAAC;MACH;AAEA,YAAM,iBAAiB;IACzB;AAEA,QAAI;AACF,aAAO,MAAM,0BAA0B;QACrC;QACA;QACA,mBAAmB,KAAK;MAC1B,CAAC;IACH,SAAS,OAAO;AACd,UAAI,iBAAiB,OAAO;AAC1B,YAAI,aAAa,KAAK,KAAK,aAAa,eAAe,KAAK,GAAG;AAC7D,gBAAM;QACR;MACF;AAEA,YAAM,IAAI,aAAa;QACrB,SAAS;QACT,OAAO;QACP,YAAY,SAAS;QACrB;QACA;QACA,mBAAmB,KAAK;MAC1B,CAAC;IACH;EACF,SAAS,OAAO;AACd,QAAI,aAAa,KAAK,GAAG;AACvB,YAAM;IACR;AAGA,QAAI,iBAAiB,aAAa,MAAM,YAAY,gBAAgB;AAClE,YAAM,QAAS,MAAc;AAE7B,UAAI,SAAS,MAAM;AAEjB,cAAM,IAAI,aAAa;UACrB,SAAS,0BAA0B,MAAM,OAAO;UAChD;UACA;UACA,mBAAmB,KAAK;UACxB,aAAa;;QACf,CAAC;MACH;IACF;AAEA,UAAM;EACR;AACF;AElIO,IAAM,iCACX,CAAI;EACF;EACA;EACA;AACF,MAKA,OAAO,EAAE,UAAU,KAAK,kBAAkB,MAAM;AAC9C,QAAM,eAAe,MAAM,SAAS,KAAK;AACzC,QAAM,kBAAkB,uBAAuB,QAAQ;AAGvD,MAAI,aAAa,KAAK,MAAM,IAAI;AAC9B,WAAO;MACL;MACA,OAAO,IAAIE,aAAa;QACtB,SAAS,SAAS;QAClB;QACA;QACA,YAAY,SAAS;QACrB;QACA;QACA,aAAa,eAAA,OAAA,SAAA,YAAc,QAAA;MAC7B,CAAC;IACH;EACF;AAGA,MAAI;AACF,UAAM,cAAc,UAAU;MAC5B,MAAM;MACN,QAAQ;IACV,CAAC;AAED,WAAO;MACL;MACA,OAAO,IAAIA,aAAa;QACtB,SAAS,eAAe,WAAW;QACnC;QACA;QACA,YAAY,SAAS;QACrB;QACA;QACA,MAAM;QACN,aAAa,eAAA,OAAA,SAAA,YAAc,UAAU,WAAA;MACvC,CAAC;IACH;EACF,SAAS,YAAY;AACnB,WAAO;MACL;MACA,OAAO,IAAIA,aAAa;QACtB,SAAS,SAAS;QAClB;QACA;QACA,YAAY,SAAS;QACrB;QACA;QACA,aAAa,eAAA,OAAA,SAAA,YAAc,QAAA;MAC7B,CAAC;IACH;EACF;AACF;AAEK,IAAM,mCACX,CACEC,iBAEF,OAAO,EAAE,SAAS,MAA8B;AAC9C,QAAM,kBAAkB,uBAAuB,QAAQ;AAEvD,MAAI,SAAS,QAAQ,MAAM;AACzB,UAAM,IAAI,uBAAuB,CAAC,CAAC;EACrC;AAEA,SAAO;IACL;IACA,OAAO,SAAS,KACb,YAAY,IAAI,kBAAkB,CAAC,EACnC,YAAY,IAAI,wBAAwB,CAAC,EACzC;MACC,IAAI,gBAA6C;QAC/C,UAAU,EAAE,KAAK,GAAG,YAAY;AAE9B,cAAI,SAAS,UAAU;AACrB;UACF;AAEA,qBAAW;YACT,cAAc;cACZ,MAAM;cACN,QAAQA;YACV,CAAC;UACH;QACF;MACF,CAAC;IACH;EACJ;AACF;AAqCK,IAAM,4BACX,CAAIC,oBACJ,OAAO,EAAE,UAAU,KAAK,kBAAkB,MAAM;AAC9C,QAAM,eAAe,MAAM,SAAS,KAAK;AAEzC,QAAM,eAAe,cAAc;IACjC,MAAM;IACN,QAAQA;EACV,CAAC;AAED,QAAM,kBAAkB,uBAAuB,QAAQ;AAEvD,MAAI,CAAC,aAAa,SAAS;AACzB,UAAM,IAAIC,aAAa;MACrB,SAAS;MACT,OAAO,aAAa;MACpB,YAAY,SAAS;MACrB;MACA;MACA;MACA;IACF,CAAC;EACH;AAEA,SAAO;IACL;IACA,OAAO,aAAa;EACtB;AACF;ACpLF,IAAM,EAAE,MAAM,KAAK,IAAI;AAQhB,SAAS,0BAA0B,OAA2B;AACnE,MAAI,eAAe;AAInB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,oBAAgB,OAAO,cAAc,MAAM,CAAC,CAAC;EAC/C;AAEA,SAAO,KAAK,YAAY;AAC1B;ACrBO,SAAS,qBAAqB,KAAyB;AAC5D,SAAO,OAAA,OAAA,SAAA,IAAK,QAAQ,OAAO,EAAA;AAC7B;;;AGGO,SAAS,iCACd,YACS;AACT,MAAI,OAAO,eAAe,WAAW;AACnC,WAAO,EAAE,MAAM,WAAW,YAAY,CAAC,EAAE;EAC3C;AAEA,QAAM;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,OAAO;IACP;EACF,IAAI;AAEJ,QAAM,SAAkC,CAAC;AAEzC,MAAI;AAAa,WAAO,cAAc;AACtC,MAAI;AAAU,WAAO,WAAW;AAChC,MAAI;AAAQ,WAAO,SAAS;AAE5B,MAAI,eAAe,QAAW;AAC5B,WAAO,OAAO,CAAC,UAAU;EAC3B;AAGA,MAAI,MAAM;AACR,QAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,UAAI,KAAK,SAAS,MAAM,GAAG;AACzB,eAAO,OAAO,KAAK,OAAO,CAAA,MAAK,MAAM,MAAM,EAAE,CAAC;AAC9C,eAAO,WAAW;MACpB,OAAO;AACL,eAAO,OAAO;MAChB;IACF,WAAW,SAAS,QAAQ;AAC1B,aAAO,OAAO;IAChB,OAAO;AACL,aAAO,OAAO;IAChB;EACF;AAEA,MAAI,YAAY;AACd,WAAO,aAAa,OAAO,QAAQ,UAAU,EAAE;MAC7C,CAAC,KAAK,CAAC,KAAK,KAAK,MAAM;AACrB,YAAI,GAAG,IAAI,iCAAiC,KAAK;AACjD,eAAO;MACT;MACA,CAAC;IACH;EACF;AAEA,MAAI,OAAO;AACT,WAAO,QAAQ,MAAM,QAAQ,KAAK,IAC9B,MAAM,IAAI,gCAAgC,IAC1C,iCAAiC,KAAK;EAC5C;AAEA,MAAI,OAAO;AACT,WAAO,QAAQ,MAAM,IAAI,gCAAgC;EAC3D;AACA,MAAI,OAAO;AACT,WAAO,QAAQ,MAAM,IAAI,gCAAgC;EAC3D;AACA,MAAI,OAAO;AACT,WAAO,QAAQ,MAAM,IAAI,gCAAgC;EAC3D;AAEA,MAAI,cAAc;AAAW,WAAO,YAAY;AAEhD,SAAO;AACT;ACtEO,SAAS,oCACdC,SAC0B;AAb5B,MAAAC;AAcE,QAAM,yBAAkD,CAAC;AACzD,QAAM,WAA6C,CAAC;AACpD,MAAI,wBAAwB;AAE5B,aAAW,EAAE,MAAM,QAAQ,KAAKD,SAAQ;AACtC,YAAQ,MAAM;MACZ,KAAK,UAAU;AACb,YAAI,CAAC,uBAAuB;AAC1B,gBAAM,IAAI,8BAA8B;YACtC,eACE;UACJ,CAAC;QACH;AAEA,+BAAuB,KAAK,EAAE,MAAM,QAAQ,CAAC;AAC7C;MACF;MAEA,KAAK,QAAQ;AACX,gCAAwB;AAExB,cAAM,QAAyC,CAAC;AAEhD,mBAAW,QAAQ,SAAS;AAC1B,kBAAQ,KAAK,MAAM;YACjB,KAAK,QAAQ;AACX,oBAAM,KAAK,EAAE,MAAM,KAAK,KAAK,CAAC;AAC9B;YACF;YACA,KAAK,SAAS;AACZ,kBAAI,KAAK,iBAAiB,KAAK;AAE7B,sBAAM,IAAI,8BAA8B;kBACtC,eAAe;gBACjB,CAAC;cACH;AAEA,oBAAM,KAAK;gBACT,YAAY;kBACV,WAAUC,OAAA,KAAK,aAAL,OAAAA,OAAiB;kBAC3B,MAAM,0BAA0B,KAAK,KAAK;gBAC5C;cACF,CAAC;AAED;YACF;YACA,KAAK,QAAQ;AACX,kBAAI,KAAK,gBAAgB,KAAK;AAE5B,sBAAM,IAAI,8BAA8B;kBACtC,eAAe;gBACjB,CAAC;cACH;AAEA,oBAAM,KAAK;gBACT,YAAY,EAAE,UAAU,KAAK,UAAU,MAAM,KAAK,KAAK;cACzD,CAAC;AAED;YACF;UACF;QACF;AAEA,iBAAS,KAAK,EAAE,MAAM,QAAQ,MAAM,CAAC;AACrC;MACF;MAEA,KAAK,aAAa;AAChB,gCAAwB;AAExB,iBAAS,KAAK;UACZ,MAAM;UACN,OAAO,QACJ,IAAI,CAAA,SAAQ;AACX,oBAAQ,KAAK,MAAM;cACjB,KAAK,QAAQ;AACX,uBAAO,KAAK,KAAK,WAAW,IACxB,SACA,EAAE,MAAM,KAAK,KAAK;cACxB;cACA,KAAK,aAAa;AAChB,uBAAO;kBACL,cAAc;oBACZ,MAAM,KAAK;oBACX,MAAM,KAAK;kBACb;gBACF;cACF;YACF;UACF,CAAC,EACA;YACC,CAAA,SAAQ,SAAS;UACnB;QACJ,CAAC;AACD;MACF;MAEA,KAAK,QAAQ;AACX,gCAAwB;AAExB,iBAAS,KAAK;UACZ,MAAM;UACN,OAAO,QAAQ,IAAI,CAAA,UAAS;YAC1B,kBAAkB;cAChB,MAAM,KAAK;cACX,UAAU,KAAK;YACjB;UACF,EAAE;QACJ,CAAC;AACD;MACF;MACA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;MACzD;IACF;EACF;AAEA,SAAO;IACL,mBACE,uBAAuB,SAAS,IAC5B,EAAE,OAAO,uBAAuB,IAChC;IACN;EACF;AACF;AC3IO,SAAS,aAAa,SAAyB;AACpD,SAAO,QAAQ,SAAS,GAAG,IAAI,UAAU,UAAU,OAAO;AAC5D;ACCA,IAAM,wBAAwB,iBAAE,OAAO;EACrC,OAAO,iBAAE,OAAO;IACd,MAAM,iBAAE,OAAO,EAAE,SAAS;IAC1B,SAAS,iBAAE,OAAO;IAClB,QAAQ,iBAAE,OAAO;EACnB,CAAC;AACH,CAAC;AAIM,IAAM,8BAA8B,+BAA+B;EACxE,aAAa;EACb,gBAAgB,CAAA,SAAQ,KAAK,MAAM;AACrC,CAAC;ACdM,SAAS,kCAAkC;EAChD;EACA;AACF,GAGgC;AAC9B,UAAQ,cAAc;IACpB,KAAK;AACH,aAAO,eAAe,eAAe;IACvC,KAAK;AACH,aAAO;IACT,KAAK;IACL,KAAK;AACH,aAAO;IACT,KAAK;IACL,KAAK;AACH,aAAO;IACT;AACE,aAAO;EACX;AACF;ALWO,IAAM,kCAAN,MAAiE;EActE,YACE,SACA,UACA,QACA;AAjBF,SAAS,uBAAuB;AAChC,SAAS,8BAA8B;AACvC,SAAS,oBAAoB;AAgB3B,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,SAAS;EAChB;EAjBA,IAAI,2BAA2B;AAC7B,WAAO,KAAK,SAAS,sBAAsB;EAC7C;EAiBA,IAAI,WAAmB;AACrB,WAAO,KAAK,OAAO;EACrB;EAEA,MAAc,QAAQ;IACpB;IACA,QAAAD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF,GAAiD;AA1EnD,QAAAC;AA2EI,UAAM,OAAO,KAAK;AAElB,UAAM,WAAyC,CAAC;AAEhD,QAAI,QAAQ,MAAM;AAChB,eAAS,KAAK;QACZ,MAAM;QACN,SAAS;MACX,CAAC;IACH;AAEA,UAAM,mBAAmB;;MAEvB,iBAAiB;MACjB;MACA,MAAM,QAAA,OAAA,OAAQ,KAAK,SAAS;MAC5B;MACA;MACA;MACA;;MAGA,mBACE,kBAAA,OAAA,SAAA,eAAgB,UAAS,SAAS,qBAAqB;MACzD,iBACE,kBAAA,OAAA,SAAA,eAAgB,UAAS,UACzB,eAAe,UAAU;;MAGzB,KAAK,2BACD,iCAAiC,eAAe,MAAM,IACtD;IACR;AAEA,UAAM,EAAE,UAAU,kBAAkB,IAClC,oCAAoCD,OAAM;AAE5C,YAAQ,MAAM;MACZ,KAAK,WAAW;AACd,eAAO;UACL,MAAM;YACJ;YACA;YACA;YACA,gBAAgB,KAAK,SAAS;YAC9B,GAAG,0BAA0B,IAAI;YACjC,eAAe,KAAK,SAAS;UAC/B;UACA;QACF;MACF;MAEA,KAAK,eAAe;AAClB,eAAO;UACL,MAAM;YACJ,kBAAkB;cAChB,GAAG;cACH,kBAAkB;cAClB,gBACE,KAAK,UAAU;;cAGf,KAAK,2BACD,iCAAiC,KAAK,MAAM,IAC5C;YACR;YACA;YACA;YACA,gBAAgB,KAAK,SAAS;YAC9B,eAAe,KAAK,SAAS;UAC/B;UACA;QACF;MACF;MAEA,KAAK,eAAe;AAClB,eAAO;UACL,MAAM;YACJ;YACA;YACA,OAAO;cACL,sBAAsB;gBACpB;kBACE,MAAM,KAAK,KAAK;kBAChB,cAAaC,OAAA,KAAK,KAAK,gBAAV,OAAAA,OAAyB;kBACtC,YAAY;oBACV,KAAK,KAAK;kBACZ;gBACF;cACF;YACF;YACA,YAAY,EAAE,uBAAuB,EAAE,MAAM,MAAM,EAAE;YACrD,gBAAgB,KAAK,SAAS;YAC9B,eAAe,KAAK,SAAS;UAC/B;UACA;QACF;MACF;MAEA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;MACzD;IACF;EACF;EAEA,MAAM,WACJ,SAC6D;AAvLjE,QAAAA,MAAA;AAwLI,UAAM,EAAE,MAAM,SAAS,IAAI,MAAM,KAAK,QAAQ,OAAO;AAErD,UAAM,EAAE,iBAAiB,OAAO,SAAS,IAAI,MAAM,cAAc;MAC/D,KAAK,GAAG,KAAK,OAAO,OAAO,IAAI;QAC7B,KAAK;MACP,CAAC;MACD,SAAS,eAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;MAC9D,MAAM;MACN,uBAAuB;MACvB,2BAA2B,0BAA0B,cAAc;MACnE,aAAa,QAAQ;MACrB,OAAO,KAAK,OAAO;IACrB,CAAC;AAED,UAAM,EAAE,UAAU,WAAW,GAAG,YAAY,IAAI;AAChD,UAAM,YAAY,SAAS,WAAW,CAAC;AAEvC,UAAM,YAAY,sBAAsB;MACtC,OAAO,UAAU,QAAQ;MACzB,YAAY,KAAK,OAAO;IAC1B,CAAC;AAED,UAAM,gBAAgB,SAAS;AAE/B,WAAO;MACL,MAAM,iBAAiB,UAAU,QAAQ,KAAK;MAC9C;MACA,cAAc,kCAAkC;QAC9C,cAAc,UAAU;QACxB,cAAc,aAAa,QAAQ,UAAU,SAAS;MACxD,CAAC;MACD,OAAO;QACL,eAAcA,OAAA,iBAAA,OAAA,SAAA,cAAe,qBAAf,OAAAA,OAAmC;QACjD,mBAAkB,KAAA,iBAAA,OAAA,SAAA,cAAe,yBAAf,OAAA,KAAuC;MAC3D;MACA,SAAS,EAAE,WAAW,YAAY;MAClC,aAAa,EAAE,SAAS,gBAAgB;MACxC;IACF;EACF;EAEA,MAAM,SACJ,SAC2D;AAC3D,UAAM,EAAE,MAAM,SAAS,IAAI,MAAM,KAAK,QAAQ,OAAO;AAErD,UAAM,EAAE,iBAAiB,OAAO,SAAS,IAAI,MAAM,cAAc;MAC/D,KAAK,GAAG,KAAK,OAAO,OAAO,IAAI;QAC7B,KAAK;MACP,CAAC;MACD,SAAS,eAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;MAC9D,MAAM;MACN,uBAAuB;MACvB,2BAA2B,iCAAiC,WAAW;MACvE,aAAa,QAAQ;MACrB,OAAO,KAAK,OAAO;IACrB,CAAC;AAED,UAAM,EAAE,UAAU,WAAW,GAAG,YAAY,IAAI;AAEhD,QAAI,eAA4C;AAChD,QAAI,QAA4D;MAC9D,cAAc,OAAO;MACrB,kBAAkB,OAAO;IAC3B;AAEA,UAAMC,cAAa,KAAK,OAAO;AAC/B,QAAI,eAAe;AAEnB,WAAO;MACL,QAAQ,SAAS;QACf,IAAI,gBAGF;UACA,UAAU,OAAO,YAAY;AAnQvC,gBAAAD,MAAA;AAoQY,gBAAI,CAAC,MAAM,SAAS;AAClB,yBAAW,QAAQ,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,CAAC;AACxD;YACF;AAEA,kBAAM,QAAQ,MAAM;AAEpB,kBAAM,YAAY,MAAM,WAAW,CAAC;AAEpC,iBAAI,aAAA,OAAA,SAAA,UAAW,iBAAgB,MAAM;AACnC,6BAAe,kCAAkC;gBAC/C,cAAc,UAAU;gBACxB;cACF,CAAC;YACH;AAEA,kBAAM,gBAAgB,MAAM;AAE5B,gBAAI,iBAAiB,MAAM;AACzB,sBAAQ;gBACN,eAAcA,OAAA,cAAc,qBAAd,OAAAA,OAAkC;gBAChD,mBAAkB,KAAA,cAAc,yBAAd,OAAA,KAAsC;cAC1D;YACF;AAEA,kBAAM,UAAU,UAAU;AAE1B,gBAAI,WAAW,MAAM;AACnB;YACF;AAEA,kBAAM,YAAY,iBAAiB,QAAQ,KAAK;AAChD,gBAAI,aAAa,MAAM;AACrB,yBAAW,QAAQ;gBACjB,MAAM;gBACN,WAAW;cACb,CAAC;YACH;AAEA,kBAAM,iBAAiB,sBAAsB;cAC3C,OAAO,QAAQ;cACf,YAAAC;YACF,CAAC;AAED,gBAAI,kBAAkB,MAAM;AAC1B,yBAAW,YAAY,gBAAgB;AACrC,2BAAW,QAAQ;kBACjB,MAAM;kBACN,cAAc;kBACd,YAAY,SAAS;kBACrB,UAAU,SAAS;kBACnB,eAAe,SAAS;gBAC1B,CAAC;AAED,2BAAW,QAAQ;kBACjB,MAAM;kBACN,cAAc;kBACd,YAAY,SAAS;kBACrB,UAAU,SAAS;kBACnB,MAAM,SAAS;gBACjB,CAAC;AAED,+BAAe;cACjB;YACF;UACF;UAEA,MAAM,YAAY;AAChB,uBAAW,QAAQ,EAAE,MAAM,UAAU,cAAc,MAAM,CAAC;UAC5D;QACF,CAAC;MACH;MACA,SAAS,EAAE,WAAW,YAAY;MAClC,aAAa,EAAE,SAAS,gBAAgB;MACxC;IACF;EACF;AACF;AAEA,SAAS,sBAAsB;EAC7B;EACA,YAAAA;AACF,GAGG;AACD,QAAM,oBAAoB,MAAM;IAC9B,CAAA,SAAQ,kBAAkB;EAC5B;AAMA,SAAO,kBAAkB,WAAW,IAChC,SACA,kBAAkB,IAAI,CAAA,UAAS;IAC7B,cAAc;IACd,YAAYA,YAAW;IACvB,UAAU,KAAK,aAAa;IAC5B,MAAM,KAAK,UAAU,KAAK,aAAa,IAAI;EAC7C,EAAE;AACR;AAEA,SAAS,iBAAiB,OAA+C;AACvE,QAAM,YAAY,MAAM,OAAO,CAAA,SAAQ,UAAU,IAAI;AAIrD,SAAO,UAAU,WAAW,IACxB,SACA,UAAU,IAAI,CAAA,SAAQ,KAAK,IAAI,EAAE,KAAK,EAAE;AAC9C;AAEA,IAAM,gBAAgBC,iBAAE,OAAO;EAC7B,MAAMA,iBAAE,OAAO;EACf,OAAOA,iBAAE;IACPA,iBAAE,MAAM;MACNA,iBAAE,OAAO;QACP,MAAMA,iBAAE,OAAO;MACjB,CAAC;MACDA,iBAAE,OAAO;QACP,cAAcA,iBAAE,OAAO;UACrB,MAAMA,iBAAE,OAAO;UACf,MAAMA,iBAAE,QAAQ;QAClB,CAAC;MACH,CAAC;IACH,CAAC;EACH;AACF,CAAC;AAID,IAAM,iBAAiBA,iBAAE,OAAO;EAC9B,YAAYA,iBAAE;IACZA,iBAAE,OAAO;MACP,SAAS;MACT,cAAcA,iBAAE,OAAO,EAAE,SAAS;IACpC,CAAC;EACH;EACA,eAAeA,iBACZ,OAAO;IACN,kBAAkBA,iBAAE,OAAO;IAC3B,sBAAsBA,iBAAE,OAAO,EAAE,QAAQ;IACzC,iBAAiBA,iBAAE,OAAO;EAC5B,CAAC,EACA,SAAS;AACd,CAAC;AAID,IAAM,cAAcA,iBAAE,OAAO;EAC3B,YAAYA,iBAAE;IACZA,iBAAE,OAAO;MACP,SAAS,cAAc,SAAS;MAChC,cAAcA,iBAAE,OAAO,EAAE,SAAS;IACpC,CAAC;EACH;EACA,eAAeA,iBACZ,OAAO;IACN,kBAAkBA,iBAAE,OAAO;IAC3B,sBAAsBA,iBAAE,OAAO,EAAE,QAAQ;IACzC,iBAAiBA,iBAAE,OAAO;EAC5B,CAAC,EACA,SAAS;AACd,CAAC;AAED,SAAS,0BACP,MAGA;AA/aF,MAAAF;AAibE,QAAM,UAAQA,OAAA,KAAK,UAAL,OAAA,SAAAA,KAAY,UAAS,KAAK,QAAQ;AAEhD,MAAI,SAAS,MAAM;AACjB,WAAO,EAAE,OAAO,QAAW,YAAY,OAAU;EACnD;AAEA,QAAM,cAAc;IAClB,sBAAsB,MAAM,IAAI,CAAA,SAAK;AAxbzC,UAAAA;AAwb6C,aAAA;QACvC,MAAM,KAAK;QACX,cAAaA,OAAA,KAAK,gBAAL,OAAAA,OAAoB;QACjC,YAAY,iCAAiC,KAAK,UAAU;MAC9D;IAAA,CAAE;EACJ;AAEA,QAAM,aAAa,KAAK;AAExB,MAAI,cAAc,MAAM;AACtB,WAAO,EAAE,OAAO,aAAa,YAAY,OAAU;EACrD;AAEA,QAAM,OAAO,WAAW;AAExB,UAAQ,MAAM;IACZ,KAAK;AACH,aAAO;QACL,OAAO;QACP,YAAY,EAAE,uBAAuB,EAAE,MAAM,OAAO,EAAE;MACxD;IACF,KAAK;AACH,aAAO;QACL,OAAO;QACP,YAAY,EAAE,uBAAuB,EAAE,MAAM,OAAO,EAAE;MACxD;IACF,KAAK;AACH,aAAO;QACL,OAAO;QACP,YAAY,EAAE,uBAAuB,EAAE,MAAM,MAAM,EAAE;MACvD;IACF,KAAK;AACH,aAAO;QACL,OAAO;QACP,YAAY;UACV,uBAAuB;YACrB,MAAM;YACN,sBAAsB,CAAC,WAAW,QAAQ;UAC5C;QACF;MACF;IACF,SAAS;AACP,YAAM,mBAA0B;AAChC,YAAM,IAAI,MAAM,iCAAiC,gBAAgB,EAAE;IACrE;EACF;AACF;ADvdO,IAAM,SAAN,MAAa;;;;EAelB,YAAY,UAA8C,CAAC,GAAG;AA9BhE,QAAAA,MAAA,IAAA;AA+BI,SAAK,WACH,KAAA,sBAAqBA,OAAA,QAAQ,YAAR,OAAAA,OAAmB,QAAQ,OAAO,MAAvD,OAAA,KACA;AACF,SAAK,SAAS,QAAQ;AACtB,SAAK,UAAU,QAAQ;AACvB,SAAK,cAAa,KAAA,QAAQ,eAAR,OAAA,KAAsB;EAC1C;EAEA,IAAY,aAAa;AACvB,WAAO;MACL,SAAS,KAAK;MACd,SAAS,OAAO;QACd,kBAAkB,WAAW;UAC3B,QAAQ,KAAK;UACb,yBAAyB;UACzB,aAAa;QACf,CAAC;QACD,GAAG,KAAK;MACV;IACF;EACF;;;;EAKA,aACE,SACA,WAAuC,CAAC,GACxC;AACA,WAAO,KAAK,KAAK,SAAS,QAAQ;EACpC;EAEA,KACE,SACA,WAAuC,CAAC,GACxC;AACA,WAAO,IAAI,gCAAgC,SAAS,UAAU;MAC5D,UAAU;MACV,GAAG,KAAK;MACR,YAAY,KAAK;IACnB,CAAC;EACH;AACF;AQjDO,IAAM,mCAAN,MAEP;EAmBE,YACE,SACA,UACA,QACA;AAtBF,SAAS,uBAAuB;AAuB9B,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,SAAS;EAChB;EApBA,IAAI,WAAmB;AACrB,WAAO,KAAK,OAAO;EACrB;EAEA,IAAI,uBAA+B;AACjC,WAAO;EACT;EAEA,IAAI,wBAAiC;AACnC,WAAO;EACT;EAYA,MAAM,QAAQ;IACZ;IACA;IACA;EACF,GAEE;AACA,QAAI,OAAO,SAAS,KAAK,sBAAsB;AAC7C,YAAM,IAAI,mCAAmC;QAC3C,UAAU,KAAK;QACf,SAAS,KAAK;QACd,sBAAsB,KAAK;QAC3B;MACF,CAAC;IACH;AAEA,UAAM,EAAE,iBAAiB,OAAO,SAAS,IAAI,MAAMG,cAAc;MAC/D,KAAK,GAAG,KAAK,OAAO,OAAO,WAAW,KAAK,OAAO;MAClD,SAASC,eAAe,KAAK,OAAO,QAAQ,GAAG,OAAO;MACtD,MAAM;QACJ,UAAU,OAAO,IAAI,CAAA,WAAU;UAC7B,OAAO,UAAU,KAAK,OAAO;UAC7B,SAAS,EAAE,MAAM,QAAQ,OAAO,CAAC,EAAE,MAAM,MAAM,CAAC,EAAE;UAClD,sBAAsB,KAAK,SAAS;QACtC,EAAE;MACJ;MACA,uBAAuB;MACvB,2BAA2BC;QACzB;MACF;MACA;MACA,OAAO,KAAK,OAAO;IACrB,CAAC;AAED,WAAO;MACL,YAAY,SAAS,WAAW,IAAI,CAAA,SAAQ,KAAK,MAAM;MACvD,OAAO;MACP,aAAa,EAAE,SAAS,gBAAgB;IAC1C;EACF;AACF;AAIA,IAAM,gDAAgDH,iBAAE,OAAO;EAC7D,YAAYA,iBAAE,MAAMA,iBAAE,OAAO,EAAE,QAAQA,iBAAE,MAAMA,iBAAE,OAAO,CAAC,EAAE,CAAC,CAAC;AAC/D,CAAC;ADEM,SAAS,yBACd,UAA8C,CAAC,GACnB;AAzG9B,MAAAF,MAAA;AA0GE,QAAM,WACJ,KAAAM,sBAAqBN,OAAA,QAAQ,YAAR,OAAAA,OAAmB,QAAQ,OAAO,MAAvD,OAAA,KACA;AAEF,QAAM,aAAa,OAAO;IACxB,kBAAkBO,WAAW;MAC3B,QAAQ,QAAQ;MAChB,yBAAyB;MACzB,aAAa;IACf,CAAC;IACD,GAAG,QAAQ;EACb;AAEA,QAAM,kBAAkB,CACtB,SACA,WAAuC,CAAC,MACxC;AA1HJ,QAAAP;AA2HI,WAAA,IAAI,gCAAgC,SAAS,UAAU;MACrD,UAAU;MACV;MACA,SAAS;MACT,aAAYA,OAAA,QAAQ,eAAR,OAAAA,OAAsBC;MAClC,OAAO,QAAQ;IACjB,CAAC;EAAA;AAEH,QAAM,uBAAuB,CAC3B,SACA,WAAgD,CAAC,MAEjD,IAAI,iCAAiC,SAAS,UAAU;IACtD,UAAU;IACV;IACA,SAAS;IACT,OAAO,QAAQ;EACjB,CAAC;AAEH,QAAM,WAAW,SACf,SACA,UACA;AACA,QAAI,YAAY;AACd,YAAM,IAAI;QACR;MACF;IACF;AAEA,WAAO,gBAAgB,SAAS,QAAQ;EAC1C;AAEA,WAAS,gBAAgB;AACzB,WAAS,OAAO;AAChB,WAAS,eAAe;AACxB,WAAS,YAAY;AACrB,WAAS,gBAAgB;AACzB,WAAS,qBAAqB;AAE9B,SAAO;AACT;AAKO,IAAM,SAAS,yBAAyB;", "names": ["_AISDKError", "name", "marker", "symbol", "_a", "_a", "symbol", "name", "marker", "_a", "symbol", "name", "marker", "_a", "symbol", "name", "marker", "_a", "symbol", "name", "marker", "_a", "symbol", "name", "marker", "_TypeValidationError", "validator", "SecureJSON", "TypeValidationError", "APICallError", "chunkSchema", "responseSchema", "APICallError", "prompt", "_a", "generateId", "z", "postJsonToApi", "combineHeaders", "createJsonResponseHandler", "withoutTrailingSlash", "loadApiKey"]}