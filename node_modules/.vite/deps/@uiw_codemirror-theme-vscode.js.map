{"version": 3, "sources": ["../../@babel/runtime/helpers/esm/extends.js", "../../@uiw/codemirror-themes/esm/index.js", "../../@uiw/codemirror-theme-vscode/esm/light.js", "../../@uiw/codemirror-theme-vscode/esm/dark.js"], "sourcesContent": ["function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nexport { _extends as default };", "import { EditorView } from '@codemirror/view';\nimport { HighlightStyle, syntaxHighlighting } from '@codemirror/language';\nexport var createTheme = _ref => {\n  var {\n    theme,\n    settings = {},\n    styles = []\n  } = _ref;\n  var themeOptions = {\n    '.cm-gutters': {}\n  };\n  var baseStyle = {};\n  if (settings.background) {\n    baseStyle.backgroundColor = settings.background;\n  }\n  if (settings.backgroundImage) {\n    baseStyle.backgroundImage = settings.backgroundImage;\n  }\n  if (settings.foreground) {\n    baseStyle.color = settings.foreground;\n  }\n  if (settings.fontSize) {\n    baseStyle.fontSize = settings.fontSize;\n  }\n  if (settings.background || settings.foreground) {\n    themeOptions['&'] = baseStyle;\n  }\n  if (settings.fontFamily) {\n    themeOptions['&.cm-editor .cm-scroller'] = {\n      fontFamily: settings.fontFamily\n    };\n  }\n  if (settings.gutterBackground) {\n    themeOptions['.cm-gutters'].backgroundColor = settings.gutterBackground;\n  }\n  if (settings.gutterForeground) {\n    themeOptions['.cm-gutters'].color = settings.gutterForeground;\n  }\n  if (settings.gutterBorder) {\n    themeOptions['.cm-gutters'].borderRightColor = settings.gutterBorder;\n  }\n  if (settings.caret) {\n    themeOptions['.cm-content'] = {\n      caretColor: settings.caret\n    };\n    themeOptions['.cm-cursor, .cm-dropCursor'] = {\n      borderLeftColor: settings.caret\n    };\n  }\n  var activeLineGutterStyle = {};\n  if (settings.gutterActiveForeground) {\n    activeLineGutterStyle.color = settings.gutterActiveForeground;\n  }\n  if (settings.lineHighlight) {\n    themeOptions['.cm-activeLine'] = {\n      backgroundColor: settings.lineHighlight\n    };\n    activeLineGutterStyle.backgroundColor = settings.lineHighlight;\n  }\n  themeOptions['.cm-activeLineGutter'] = activeLineGutterStyle;\n  if (settings.selection) {\n    themeOptions['&.cm-focused .cm-selectionBackground, & .cm-line::selection, & .cm-selectionLayer .cm-selectionBackground, .cm-content ::selection'] = {\n      background: settings.selection + ' !important'\n    };\n  }\n  if (settings.selectionMatch) {\n    themeOptions['& .cm-selectionMatch'] = {\n      backgroundColor: settings.selectionMatch\n    };\n  }\n  var themeExtension = EditorView.theme(themeOptions, {\n    dark: theme === 'dark'\n  });\n  var highlightStyle = HighlightStyle.define(styles);\n  var extension = [themeExtension, syntaxHighlighting(highlightStyle)];\n  return extension;\n};\nexport default createTheme;", "import _extends from \"@babel/runtime/helpers/extends\";\n/**\n * https://github.com/uiwjs/react-codemirror/issues/409\n */\nimport { tags as t } from '@lezer/highlight';\nimport { createTheme } from '@uiw/codemirror-themes';\nexport var defaultSettingsVscodeLight = {\n  background: '#ffffff',\n  foreground: '#383a42',\n  caret: '#000',\n  selection: '#add6ff',\n  selectionMatch: '#a8ac94',\n  lineHighlight: '#99999926',\n  gutterBackground: '#fff',\n  gutterForeground: '#237893',\n  gutterActiveForeground: '#0b216f',\n  fontFamily: 'Menlo, Monaco, Consolas, \"Andale Mono\", \"Ubuntu Mono\", \"Courier New\", monospace'\n};\nexport var vscodeLightStyle = [{\n  tag: [t.keyword, t.operatorKeyword, t.modifier, t.color, t.constant(t.name), t.standard(t.name), t.standard(t.tagName), t.special(t.brace), t.atom, t.bool, t.special(t.variableName)],\n  color: '#0000ff'\n}, {\n  tag: [t.moduleKeyword, t.controlKeyword],\n  color: '#af00db'\n}, {\n  tag: [t.name, t.deleted, t.character, t.macroName, t.propertyName, t.variableName, t.labelName, t.definition(t.name)],\n  color: '#0070c1'\n}, {\n  tag: t.heading,\n  fontWeight: 'bold',\n  color: '#0070c1'\n}, {\n  tag: [t.typeName, t.className, t.tagName, t.number, t.changed, t.annotation, t.self, t.namespace],\n  color: '#267f99'\n}, {\n  tag: [t.function(t.variableName), t.function(t.propertyName)],\n  color: '#795e26'\n}, {\n  tag: [t.number],\n  color: '#098658'\n}, {\n  tag: [t.operator, t.punctuation, t.separator, t.url, t.escape, t.regexp],\n  color: '#383a42'\n}, {\n  tag: [t.regexp],\n  color: '#af00db'\n}, {\n  tag: [t.special(t.string), t.processingInstruction, t.string, t.inserted],\n  color: '#a31515'\n}, {\n  tag: [t.angleBracket],\n  color: '#383a42'\n}, {\n  tag: t.strong,\n  fontWeight: 'bold'\n}, {\n  tag: t.emphasis,\n  fontStyle: 'italic'\n}, {\n  tag: t.strikethrough,\n  textDecoration: 'line-through'\n}, {\n  tag: [t.meta, t.comment],\n  color: '#008000'\n}, {\n  tag: t.link,\n  color: '#4078f2',\n  textDecoration: 'underline'\n}, {\n  tag: t.invalid,\n  color: '#e45649'\n}];\nexport function vscodeLightInit(options) {\n  var {\n    theme = 'light',\n    settings = {},\n    styles = []\n  } = options || {};\n  return createTheme({\n    theme: theme,\n    settings: _extends({}, defaultSettingsVscodeLight, settings),\n    styles: [...vscodeLightStyle, ...styles]\n  });\n}\nexport var vscodeLight = vscodeLightInit();", "import _extends from \"@babel/runtime/helpers/extends\";\n/**\n * https://github.com/uiwjs/react-codemirror/issues/409\n */\nimport { tags as t } from '@lezer/highlight';\nimport { createTheme } from '@uiw/codemirror-themes';\nexport var defaultSettingsVscodeDark = {\n  background: '#1e1e1e',\n  foreground: '#9cdcfe',\n  caret: '#c6c6c6',\n  selection: '#6199ff2f',\n  selectionMatch: '#72a1ff59',\n  lineHighlight: '#ffffff0f',\n  gutterBackground: '#1e1e1e',\n  gutterForeground: '#838383',\n  gutterActiveForeground: '#fff',\n  fontFamily: 'Menlo, Monaco, Consolas, \"Andale Mono\", \"Ubuntu Mono\", \"Courier New\", monospace'\n};\nexport var vscodeDarkStyle = [{\n  tag: [t.keyword, t.operatorKeyword, t.modifier, t.color, t.constant(t.name), t.standard(t.name), t.standard(t.tagName), t.special(t.brace), t.atom, t.bool, t.special(t.variableName)],\n  color: '#569cd6'\n}, {\n  tag: [t.controlKeyword, t.moduleKeyword],\n  color: '#c586c0'\n}, {\n  tag: [t.name, t.deleted, t.character, t.macroName, t.propertyName, t.variableName, t.labelName, t.definition(t.name)],\n  color: '#9cdcfe'\n}, {\n  tag: t.heading,\n  fontWeight: 'bold',\n  color: '#9cdcfe'\n}, {\n  tag: [t.typeName, t.className, t.tagName, t.number, t.changed, t.annotation, t.self, t.namespace],\n  color: '#4ec9b0'\n}, {\n  tag: [t.function(t.variableName), t.function(t.propertyName)],\n  color: '#dcdcaa'\n}, {\n  tag: [t.number],\n  color: '#b5cea8'\n}, {\n  tag: [t.operator, t.punctuation, t.separator, t.url, t.escape, t.regexp],\n  color: '#d4d4d4'\n}, {\n  tag: [t.regexp],\n  color: '#d16969'\n}, {\n  tag: [t.special(t.string), t.processingInstruction, t.string, t.inserted],\n  color: '#ce9178'\n}, {\n  tag: [t.angleBracket],\n  color: '#808080'\n}, {\n  tag: t.strong,\n  fontWeight: 'bold'\n}, {\n  tag: t.emphasis,\n  fontStyle: 'italic'\n}, {\n  tag: t.strikethrough,\n  textDecoration: 'line-through'\n}, {\n  tag: [t.meta, t.comment],\n  color: '#6a9955'\n}, {\n  tag: t.link,\n  color: '#6a9955',\n  textDecoration: 'underline'\n}, {\n  tag: t.invalid,\n  color: '#ff0000'\n}];\nexport function vscodeDarkInit(options) {\n  var {\n    theme = 'dark',\n    settings = {},\n    styles = []\n  } = options || {};\n  return createTheme({\n    theme: theme,\n    settings: _extends({}, defaultSettingsVscodeDark, settings),\n    styles: [...vscodeDarkStyle, ...styles]\n  });\n}\nexport var vscodeDark = vscodeDarkInit();"], "mappings": ";;;;;;;;;;AAAA,SAAS,WAAW;AAClB,SAAO,WAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,GAAG;AACpE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,IAAI,UAAU,CAAC;AACnB,eAAS,KAAK,EAAG,EAAC,CAAC,GAAG,eAAe,KAAK,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAChE;AACA,WAAO;AAAA,EACT,GAAG,SAAS,MAAM,MAAM,SAAS;AACnC;;;ACNO,IAAI,cAAc,UAAQ;AAC/B,MAAI;AAAA,IACF;AAAA,IACA,WAAW,CAAC;AAAA,IACZ,SAAS,CAAC;AAAA,EACZ,IAAI;AACJ,MAAI,eAAe;AAAA,IACjB,eAAe,CAAC;AAAA,EAClB;AACA,MAAI,YAAY,CAAC;AACjB,MAAI,SAAS,YAAY;AACvB,cAAU,kBAAkB,SAAS;AAAA,EACvC;AACA,MAAI,SAAS,iBAAiB;AAC5B,cAAU,kBAAkB,SAAS;AAAA,EACvC;AACA,MAAI,SAAS,YAAY;AACvB,cAAU,QAAQ,SAAS;AAAA,EAC7B;AACA,MAAI,SAAS,UAAU;AACrB,cAAU,WAAW,SAAS;AAAA,EAChC;AACA,MAAI,SAAS,cAAc,SAAS,YAAY;AAC9C,iBAAa,GAAG,IAAI;AAAA,EACtB;AACA,MAAI,SAAS,YAAY;AACvB,iBAAa,0BAA0B,IAAI;AAAA,MACzC,YAAY,SAAS;AAAA,IACvB;AAAA,EACF;AACA,MAAI,SAAS,kBAAkB;AAC7B,iBAAa,aAAa,EAAE,kBAAkB,SAAS;AAAA,EACzD;AACA,MAAI,SAAS,kBAAkB;AAC7B,iBAAa,aAAa,EAAE,QAAQ,SAAS;AAAA,EAC/C;AACA,MAAI,SAAS,cAAc;AACzB,iBAAa,aAAa,EAAE,mBAAmB,SAAS;AAAA,EAC1D;AACA,MAAI,SAAS,OAAO;AAClB,iBAAa,aAAa,IAAI;AAAA,MAC5B,YAAY,SAAS;AAAA,IACvB;AACA,iBAAa,4BAA4B,IAAI;AAAA,MAC3C,iBAAiB,SAAS;AAAA,IAC5B;AAAA,EACF;AACA,MAAI,wBAAwB,CAAC;AAC7B,MAAI,SAAS,wBAAwB;AACnC,0BAAsB,QAAQ,SAAS;AAAA,EACzC;AACA,MAAI,SAAS,eAAe;AAC1B,iBAAa,gBAAgB,IAAI;AAAA,MAC/B,iBAAiB,SAAS;AAAA,IAC5B;AACA,0BAAsB,kBAAkB,SAAS;AAAA,EACnD;AACA,eAAa,sBAAsB,IAAI;AACvC,MAAI,SAAS,WAAW;AACtB,iBAAa,oIAAoI,IAAI;AAAA,MACnJ,YAAY,SAAS,YAAY;AAAA,IACnC;AAAA,EACF;AACA,MAAI,SAAS,gBAAgB;AAC3B,iBAAa,sBAAsB,IAAI;AAAA,MACrC,iBAAiB,SAAS;AAAA,IAC5B;AAAA,EACF;AACA,MAAI,iBAAiB,WAAW,MAAM,cAAc;AAAA,IAClD,MAAM,UAAU;AAAA,EAClB,CAAC;AACD,MAAI,iBAAiB,eAAe,OAAO,MAAM;AACjD,MAAI,YAAY,CAAC,gBAAgB,mBAAmB,cAAc,CAAC;AACnE,SAAO;AACT;;;ACtEO,IAAI,6BAA6B;AAAA,EACtC,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,wBAAwB;AAAA,EACxB,YAAY;AACd;AACO,IAAI,mBAAmB,CAAC;AAAA,EAC7B,KAAK,CAAC,KAAE,SAAS,KAAE,iBAAiB,KAAE,UAAU,KAAE,OAAO,KAAE,SAAS,KAAE,IAAI,GAAG,KAAE,SAAS,KAAE,IAAI,GAAG,KAAE,SAAS,KAAE,OAAO,GAAG,KAAE,QAAQ,KAAE,KAAK,GAAG,KAAE,MAAM,KAAE,MAAM,KAAE,QAAQ,KAAE,YAAY,CAAC;AAAA,EACrL,OAAO;AACT,GAAG;AAAA,EACD,KAAK,CAAC,KAAE,eAAe,KAAE,cAAc;AAAA,EACvC,OAAO;AACT,GAAG;AAAA,EACD,KAAK,CAAC,KAAE,MAAM,KAAE,SAAS,KAAE,WAAW,KAAE,WAAW,KAAE,cAAc,KAAE,cAAc,KAAE,WAAW,KAAE,WAAW,KAAE,IAAI,CAAC;AAAA,EACpH,OAAO;AACT,GAAG;AAAA,EACD,KAAK,KAAE;AAAA,EACP,YAAY;AAAA,EACZ,OAAO;AACT,GAAG;AAAA,EACD,KAAK,CAAC,KAAE,UAAU,KAAE,WAAW,KAAE,SAAS,KAAE,QAAQ,KAAE,SAAS,KAAE,YAAY,KAAE,MAAM,KAAE,SAAS;AAAA,EAChG,OAAO;AACT,GAAG;AAAA,EACD,KAAK,CAAC,KAAE,SAAS,KAAE,YAAY,GAAG,KAAE,SAAS,KAAE,YAAY,CAAC;AAAA,EAC5D,OAAO;AACT,GAAG;AAAA,EACD,KAAK,CAAC,KAAE,MAAM;AAAA,EACd,OAAO;AACT,GAAG;AAAA,EACD,KAAK,CAAC,KAAE,UAAU,KAAE,aAAa,KAAE,WAAW,KAAE,KAAK,KAAE,QAAQ,KAAE,MAAM;AAAA,EACvE,OAAO;AACT,GAAG;AAAA,EACD,KAAK,CAAC,KAAE,MAAM;AAAA,EACd,OAAO;AACT,GAAG;AAAA,EACD,KAAK,CAAC,KAAE,QAAQ,KAAE,MAAM,GAAG,KAAE,uBAAuB,KAAE,QAAQ,KAAE,QAAQ;AAAA,EACxE,OAAO;AACT,GAAG;AAAA,EACD,KAAK,CAAC,KAAE,YAAY;AAAA,EACpB,OAAO;AACT,GAAG;AAAA,EACD,KAAK,KAAE;AAAA,EACP,YAAY;AACd,GAAG;AAAA,EACD,KAAK,KAAE;AAAA,EACP,WAAW;AACb,GAAG;AAAA,EACD,KAAK,KAAE;AAAA,EACP,gBAAgB;AAClB,GAAG;AAAA,EACD,KAAK,CAAC,KAAE,MAAM,KAAE,OAAO;AAAA,EACvB,OAAO;AACT,GAAG;AAAA,EACD,KAAK,KAAE;AAAA,EACP,OAAO;AAAA,EACP,gBAAgB;AAClB,GAAG;AAAA,EACD,KAAK,KAAE;AAAA,EACP,OAAO;AACT,CAAC;AACM,SAAS,gBAAgB,SAAS;AACvC,MAAI;AAAA,IACF,QAAQ;AAAA,IACR,WAAW,CAAC;AAAA,IACZ,SAAS,CAAC;AAAA,EACZ,IAAI,WAAW,CAAC;AAChB,SAAO,YAAY;AAAA,IACjB;AAAA,IACA,UAAU,SAAS,CAAC,GAAG,4BAA4B,QAAQ;AAAA,IAC3D,QAAQ,CAAC,GAAG,kBAAkB,GAAG,MAAM;AAAA,EACzC,CAAC;AACH;AACO,IAAI,cAAc,gBAAgB;;;AC9ElC,IAAI,4BAA4B;AAAA,EACrC,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,wBAAwB;AAAA,EACxB,YAAY;AACd;AACO,IAAI,kBAAkB,CAAC;AAAA,EAC5B,KAAK,CAAC,KAAE,SAAS,KAAE,iBAAiB,KAAE,UAAU,KAAE,OAAO,KAAE,SAAS,KAAE,IAAI,GAAG,KAAE,SAAS,KAAE,IAAI,GAAG,KAAE,SAAS,KAAE,OAAO,GAAG,KAAE,QAAQ,KAAE,KAAK,GAAG,KAAE,MAAM,KAAE,MAAM,KAAE,QAAQ,KAAE,YAAY,CAAC;AAAA,EACrL,OAAO;AACT,GAAG;AAAA,EACD,KAAK,CAAC,KAAE,gBAAgB,KAAE,aAAa;AAAA,EACvC,OAAO;AACT,GAAG;AAAA,EACD,KAAK,CAAC,KAAE,MAAM,KAAE,SAAS,KAAE,WAAW,KAAE,WAAW,KAAE,cAAc,KAAE,cAAc,KAAE,WAAW,KAAE,WAAW,KAAE,IAAI,CAAC;AAAA,EACpH,OAAO;AACT,GAAG;AAAA,EACD,KAAK,KAAE;AAAA,EACP,YAAY;AAAA,EACZ,OAAO;AACT,GAAG;AAAA,EACD,KAAK,CAAC,KAAE,UAAU,KAAE,WAAW,KAAE,SAAS,KAAE,QAAQ,KAAE,SAAS,KAAE,YAAY,KAAE,MAAM,KAAE,SAAS;AAAA,EAChG,OAAO;AACT,GAAG;AAAA,EACD,KAAK,CAAC,KAAE,SAAS,KAAE,YAAY,GAAG,KAAE,SAAS,KAAE,YAAY,CAAC;AAAA,EAC5D,OAAO;AACT,GAAG;AAAA,EACD,KAAK,CAAC,KAAE,MAAM;AAAA,EACd,OAAO;AACT,GAAG;AAAA,EACD,KAAK,CAAC,KAAE,UAAU,KAAE,aAAa,KAAE,WAAW,KAAE,KAAK,KAAE,QAAQ,KAAE,MAAM;AAAA,EACvE,OAAO;AACT,GAAG;AAAA,EACD,KAAK,CAAC,KAAE,MAAM;AAAA,EACd,OAAO;AACT,GAAG;AAAA,EACD,KAAK,CAAC,KAAE,QAAQ,KAAE,MAAM,GAAG,KAAE,uBAAuB,KAAE,QAAQ,KAAE,QAAQ;AAAA,EACxE,OAAO;AACT,GAAG;AAAA,EACD,KAAK,CAAC,KAAE,YAAY;AAAA,EACpB,OAAO;AACT,GAAG;AAAA,EACD,KAAK,KAAE;AAAA,EACP,YAAY;AACd,GAAG;AAAA,EACD,KAAK,KAAE;AAAA,EACP,WAAW;AACb,GAAG;AAAA,EACD,KAAK,KAAE;AAAA,EACP,gBAAgB;AAClB,GAAG;AAAA,EACD,KAAK,CAAC,KAAE,MAAM,KAAE,OAAO;AAAA,EACvB,OAAO;AACT,GAAG;AAAA,EACD,KAAK,KAAE;AAAA,EACP,OAAO;AAAA,EACP,gBAAgB;AAClB,GAAG;AAAA,EACD,KAAK,KAAE;AAAA,EACP,OAAO;AACT,CAAC;AACM,SAAS,eAAe,SAAS;AACtC,MAAI;AAAA,IACF,QAAQ;AAAA,IACR,WAAW,CAAC;AAAA,IACZ,SAAS,CAAC;AAAA,EACZ,IAAI,WAAW,CAAC;AAChB,SAAO,YAAY;AAAA,IACjB;AAAA,IACA,UAAU,SAAS,CAAC,GAAG,2BAA2B,QAAQ;AAAA,IAC1D,QAAQ,CAAC,GAAG,iBAAiB,GAAG,MAAM;AAAA,EACxC,CAAC;AACH;AACO,IAAI,aAAa,eAAe;", "names": []}