import {
  autoCloseTags,
  completionPath,
  esLint,
  javascript,
  javascriptLanguage,
  jsxLanguage,
  localCompletionSource,
  scopeCompletionSource,
  snippets,
  tsxLanguage,
  typescriptLanguage,
  typescriptSnippets
} from "./chunk-M34ALQQA.js";
import "./chunk-EDLPEIAY.js";
import "./chunk-WH76PGDX.js";
import "./chunk-BDGW7IEX.js";
import "./chunk-JEVQZFNC.js";
import "./chunk-G3PMV62Z.js";
export {
  autoCloseTags,
  completionPath,
  esLint,
  javascript,
  javascriptLanguage,
  jsxLanguage,
  localCompletionSource,
  scopeCompletionSource,
  snippets,
  tsxLanguage,
  typescriptLanguage,
  typescriptSnippets
};
//# sourceMappingURL=@codemirror_lang-javascript.js.map
