{"hash": "ec66dbad", "configHash": "84bc2551", "lockfileHash": "6d9071ad", "browserHash": "d8b6451a", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "2d1f9078", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "5e4f65b9", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "bc8afa15", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "e8ae9ba4", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "9f07a6c4", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "2da018be", "needsInterop": false}, "react-resizable-panels": {"src": "../../react-resizable-panels/dist/react-resizable-panels.browser.development.esm.js", "file": "react-resizable-panels.js", "fileHash": "6c789da5", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "944a664b", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "56315b63", "needsInterop": false}, "@codemirror/state": {"src": "../../@codemirror/state/dist/index.js", "file": "@codemirror_state.js", "fileHash": "5a72b2aa", "needsInterop": false}, "@codemirror/lang-javascript": {"src": "../../@codemirror/lang-javascript/dist/index.js", "file": "@codemirror_lang-javascript.js", "fileHash": "3a1eddc5", "needsInterop": false}, "@codemirror/lang-html": {"src": "../../@codemirror/lang-html/dist/index.js", "file": "@codemirror_lang-html.js", "fileHash": "6b549003", "needsInterop": false}, "@codemirror/lang-css": {"src": "../../@codemirror/lang-css/dist/index.js", "file": "@codemirror_lang-css.js", "fileHash": "370a9bd8", "needsInterop": false}, "@codemirror/lang-json": {"src": "../../@codemirror/lang-json/dist/index.js", "file": "@codemirror_lang-json.js", "fileHash": "b5781dd8", "needsInterop": false}, "@codemirror/lang-markdown": {"src": "../../@codemirror/lang-markdown/dist/index.js", "file": "@codemirror_lang-markdown.js", "fileHash": "17a96d08", "needsInterop": false}, "@codemirror/lang-python": {"src": "../../@codemirror/lang-python/dist/index.js", "file": "@codemirror_lang-python.js", "fileHash": "3bca70ec", "needsInterop": false}, "@uiw/codemirror-theme-vscode": {"src": "../../@uiw/codemirror-theme-vscode/esm/index.js", "file": "@uiw_codemirror-theme-vscode.js", "fileHash": "66819e23", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "8f3dbb9b", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "58a2cc66", "needsInterop": false}, "@ai-sdk/openai": {"src": "../../@ai-sdk/openai/dist/index.mjs", "file": "@ai-sdk_openai.js", "fileHash": "eba7d0bf", "needsInterop": false}, "@ai-sdk/google": {"src": "../../@ai-sdk/google/dist/index.mjs", "file": "@ai-sdk_google.js", "fileHash": "d644564f", "needsInterop": false}, "@openrouter/ai-sdk-provider": {"src": "../../@openrouter/ai-sdk-provider/dist/index.mjs", "file": "@openrouter_ai-sdk-provider.js", "fileHash": "e598134e", "needsInterop": false}, "ai": {"src": "../../ai/dist/index.mjs", "file": "ai.js", "fileHash": "736e0f82", "needsInterop": false}}, "chunks": {"chunk-3XJMUPD6": {"file": "chunk-3XJMUPD6.js"}, "chunk-3IBHANRB": {"file": "chunk-3IBHANRB.js"}, "chunk-YQYBN3LT": {"file": "chunk-YQYBN3LT.js"}, "chunk-76IQK4IK": {"file": "chunk-76IQK4IK.js"}, "chunk-5EB3PST3": {"file": "chunk-5EB3PST3.js"}, "chunk-WWREERFJ": {"file": "chunk-WWREERFJ.js"}, "chunk-SWGSECCJ": {"file": "chunk-SWGSECCJ.js"}, "chunk-M34ALQQA": {"file": "chunk-M34ALQQA.js"}, "chunk-MPN7ZNYA": {"file": "chunk-MPN7ZNYA.js"}, "chunk-EDLPEIAY": {"file": "chunk-EDLPEIAY.js"}, "chunk-WH76PGDX": {"file": "chunk-WH76PGDX.js"}, "chunk-BDGW7IEX": {"file": "chunk-BDGW7IEX.js"}, "chunk-JEVQZFNC": {"file": "chunk-JEVQZFNC.js"}, "chunk-6PXSGDAH": {"file": "chunk-6PXSGDAH.js"}, "chunk-PJEEZAML": {"file": "chunk-PJEEZAML.js"}, "chunk-DRWLMN53": {"file": "chunk-DRWLMN53.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}