{"name": "jsondiffpatch", "version": "0.6.0", "author": "<PERSON> <<EMAIL>>", "description": "Diff & Patch for Javascript objects", "contributors": ["<PERSON> <<EMAIL>>"], "type": "module", "sideEffects": ["*.css"], "main": "./lib/index.js", "types": "./lib/index.d.ts", "exports": {".": "./lib/index.js", "./with-text-diffs": "./lib/with-text-diffs.js", "./formatters/*": "./lib/formatters/*.js", "./formatters/styles/*.css": "./lib/formatters/styles/*.css"}, "files": ["bin", "lib"], "bin": {"jsondiffpatch": "./bin/jsondiffpatch.js"}, "scripts": {"build": "tsc && ncp ./src/formatters/styles/ ./lib/formatters/styles/", "type-check": "tsc --noEmit", "lint": "eslint . --ext .ts", "test": "jest --coverage", "prepack": "npm run build", "prepublishOnly": "npm run test && npm run lint"}, "repository": {"type": "git", "url": "https://github.com/benjamine/jsondiffpatch.git"}, "keywords": ["json", "diff", "patch"], "dependencies": {"@types/diff-match-patch": "^1.0.36", "chalk": "^5.3.0", "diff-match-patch": "^1.0.5"}, "devDependencies": {"@types/jest": "^29.5.10", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "jest": "^29.7.0", "ncp": "^2.0.0", "ts-jest": "^29.1.1", "tslib": "^2.6.2", "typescript": "~5.3.2"}, "license": "MIT", "engines": {"node": "^18.0.0 || >=20.0.0"}, "homepage": "https://github.com/benjamine/jsondiffpatch"}