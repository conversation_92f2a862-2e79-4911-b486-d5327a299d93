{"version": 3, "sources": ["../src/google-facade.ts", "../src/google-generative-ai-language-model.ts", "../src/convert-json-schema-to-openapi-schema.ts", "../src/convert-to-google-generative-ai-messages.ts", "../src/get-model-path.ts", "../src/google-error.ts", "../src/map-google-generative-ai-finish-reason.ts", "../src/google-provider.ts", "../src/google-generative-ai-embedding-model.ts"], "sourcesContent": ["import {\n  generateId,\n  loadApiKey,\n  withoutTrailingSlash,\n} from '@ai-sdk/provider-utils';\nimport { GoogleGenerativeAILanguageModel } from './google-generative-ai-language-model';\nimport {\n  GoogleGenerativeAIModelId,\n  GoogleGenerativeAISettings,\n} from './google-generative-ai-settings';\nimport { GoogleGenerativeAIProviderSettings } from './google-provider';\n\n/**\n * @deprecated Use `createGoogleGenerativeAI` instead.\n */\nexport class Google {\n  /**\n   * Base URL for the Google API calls.\n   */\n  readonly baseURL: string;\n\n  readonly apiKey?: string;\n\n  readonly headers?: Record<string, string>;\n\n  private readonly generateId: () => string;\n\n  /**\n   * Creates a new Google provider instance.\n   */\n  constructor(options: GoogleGenerativeAIProviderSettings = {}) {\n    this.baseURL =\n      withoutTrailingSlash(options.baseURL ?? options.baseUrl) ??\n      'https://generativelanguage.googleapis.com/v1beta';\n    this.apiKey = options.apiKey;\n    this.headers = options.headers;\n    this.generateId = options.generateId ?? generateId;\n  }\n\n  private get baseConfig() {\n    return {\n      baseURL: this.baseURL,\n      headers: () => ({\n        'x-goog-api-key': loadApiKey({\n          apiKey: this.apiKey,\n          environmentVariableName: 'GOOGLE_GENERATIVE_AI_API_KEY',\n          description: 'Google Generative AI',\n        }),\n        ...this.headers,\n      }),\n    };\n  }\n\n  /**\n   * @deprecated Use `chat()` instead.\n   */\n  generativeAI(\n    modelId: GoogleGenerativeAIModelId,\n    settings: GoogleGenerativeAISettings = {},\n  ) {\n    return this.chat(modelId, settings);\n  }\n\n  chat(\n    modelId: GoogleGenerativeAIModelId,\n    settings: GoogleGenerativeAISettings = {},\n  ) {\n    return new GoogleGenerativeAILanguageModel(modelId, settings, {\n      provider: 'google.generative-ai',\n      ...this.baseConfig,\n      generateId: this.generateId,\n    });\n  }\n}\n", "import {\n  LanguageModelV1,\n  LanguageModelV1CallWarning,\n  LanguageModelV1FinishReason,\n  LanguageModelV1StreamPart,\n} from '@ai-sdk/provider';\nimport {\n  FetchFunction,\n  ParseResult,\n  combineHeaders,\n  createEventSourceResponseHandler,\n  create<PERSON>sonResponse<PERSON>andler,\n  postJsonToApi,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { convertJSONSchemaToOpenAPISchema } from './convert-json-schema-to-openapi-schema';\nimport { convertToGoogleGenerativeAIMessages } from './convert-to-google-generative-ai-messages';\nimport { getModelPath } from './get-model-path';\nimport { googleFailedResponseHandler } from './google-error';\nimport { GoogleGenerativeAIContentPart } from './google-generative-ai-prompt';\nimport {\n  GoogleGenerativeAIModelId,\n  GoogleGenerativeAISettings,\n} from './google-generative-ai-settings';\nimport { mapGoogleGenerativeAIFinishReason } from './map-google-generative-ai-finish-reason';\n\ntype GoogleGenerativeAIConfig = {\n  provider: string;\n  baseURL: string;\n  headers: () => Record<string, string | undefined>;\n  generateId: () => string;\n  fetch?: FetchFunction;\n};\n\nexport class GoogleGenerativeAILanguageModel implements LanguageModelV1 {\n  readonly specificationVersion = 'v1';\n  readonly defaultObjectGenerationMode = 'json';\n  readonly supportsImageUrls = false;\n\n  get supportsObjectGeneration() {\n    return this.settings.structuredOutputs !== false;\n  }\n\n  readonly modelId: GoogleGenerativeAIModelId;\n  readonly settings: GoogleGenerativeAISettings;\n\n  private readonly config: GoogleGenerativeAIConfig;\n\n  constructor(\n    modelId: GoogleGenerativeAIModelId,\n    settings: GoogleGenerativeAISettings,\n    config: GoogleGenerativeAIConfig,\n  ) {\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  private async getArgs({\n    mode,\n    prompt,\n    maxTokens,\n    temperature,\n    topP,\n    topK,\n    frequencyPenalty,\n    presencePenalty,\n    stopSequences,\n    responseFormat,\n    seed,\n  }: Parameters<LanguageModelV1['doGenerate']>[0]) {\n    const type = mode.type;\n\n    const warnings: LanguageModelV1CallWarning[] = [];\n\n    if (seed != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'seed',\n      });\n    }\n\n    const generationConfig = {\n      // standardized settings:\n      maxOutputTokens: maxTokens,\n      temperature,\n      topK: topK ?? this.settings.topK,\n      topP,\n      frequencyPenalty,\n      presencePenalty,\n      stopSequences,\n\n      // response format:\n      responseMimeType:\n        responseFormat?.type === 'json' ? 'application/json' : undefined,\n      responseSchema:\n        responseFormat?.type === 'json' &&\n        responseFormat.schema != null &&\n        // Google GenAI does not support all OpenAPI Schema features,\n        // so this is needed as an escape hatch:\n        this.supportsObjectGeneration\n          ? convertJSONSchemaToOpenAPISchema(responseFormat.schema)\n          : undefined,\n    };\n\n    const { contents, systemInstruction } =\n      convertToGoogleGenerativeAIMessages(prompt);\n\n    switch (type) {\n      case 'regular': {\n        return {\n          args: {\n            generationConfig,\n            contents,\n            systemInstruction,\n            safetySettings: this.settings.safetySettings,\n            ...prepareToolsAndToolConfig(mode),\n            cachedContent: this.settings.cachedContent,\n          },\n          warnings,\n        };\n      }\n\n      case 'object-json': {\n        return {\n          args: {\n            generationConfig: {\n              ...generationConfig,\n              responseMimeType: 'application/json',\n              responseSchema:\n                mode.schema != null &&\n                // Google GenAI does not support all OpenAPI Schema features,\n                // so this is needed as an escape hatch:\n                this.supportsObjectGeneration\n                  ? convertJSONSchemaToOpenAPISchema(mode.schema)\n                  : undefined,\n            },\n            contents,\n            systemInstruction,\n            safetySettings: this.settings.safetySettings,\n            cachedContent: this.settings.cachedContent,\n          },\n          warnings,\n        };\n      }\n\n      case 'object-tool': {\n        return {\n          args: {\n            generationConfig,\n            contents,\n            tools: {\n              functionDeclarations: [\n                {\n                  name: mode.tool.name,\n                  description: mode.tool.description ?? '',\n                  parameters: convertJSONSchemaToOpenAPISchema(\n                    mode.tool.parameters,\n                  ),\n                },\n              ],\n            },\n            toolConfig: { functionCallingConfig: { mode: 'ANY' } },\n            safetySettings: this.settings.safetySettings,\n            cachedContent: this.settings.cachedContent,\n          },\n          warnings,\n        };\n      }\n\n      default: {\n        const _exhaustiveCheck: never = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  async doGenerate(\n    options: Parameters<LanguageModelV1['doGenerate']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doGenerate']>>> {\n    const { args, warnings } = await this.getArgs(options);\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: `${this.config.baseURL}/${getModelPath(\n        this.modelId,\n      )}:generateContent`,\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body: args,\n      failedResponseHandler: googleFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(responseSchema),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { contents: rawPrompt, ...rawSettings } = args;\n    const candidate = response.candidates[0];\n\n    const toolCalls = getToolCallsFromParts({\n      parts: candidate.content.parts,\n      generateId: this.config.generateId,\n    });\n\n    const usageMetadata = response.usageMetadata;\n\n    return {\n      text: getTextFromParts(candidate.content.parts),\n      toolCalls,\n      finishReason: mapGoogleGenerativeAIFinishReason({\n        finishReason: candidate.finishReason,\n        hasToolCalls: toolCalls != null && toolCalls.length > 0,\n      }),\n      usage: {\n        promptTokens: usageMetadata?.promptTokenCount ?? NaN,\n        completionTokens: usageMetadata?.candidatesTokenCount ?? NaN,\n      },\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings,\n    };\n  }\n\n  async doStream(\n    options: Parameters<LanguageModelV1['doStream']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doStream']>>> {\n    const { args, warnings } = await this.getArgs(options);\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: `${this.config.baseURL}/${getModelPath(\n        this.modelId,\n      )}:streamGenerateContent?alt=sse`,\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body: args,\n      failedResponseHandler: googleFailedResponseHandler,\n      successfulResponseHandler: createEventSourceResponseHandler(chunkSchema),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { contents: rawPrompt, ...rawSettings } = args;\n\n    let finishReason: LanguageModelV1FinishReason = 'unknown';\n    let usage: { promptTokens: number; completionTokens: number } = {\n      promptTokens: Number.NaN,\n      completionTokens: Number.NaN,\n    };\n\n    const generateId = this.config.generateId;\n    let hasToolCalls = false;\n\n    return {\n      stream: response.pipeThrough(\n        new TransformStream<\n          ParseResult<z.infer<typeof chunkSchema>>,\n          LanguageModelV1StreamPart\n        >({\n          transform(chunk, controller) {\n            if (!chunk.success) {\n              controller.enqueue({ type: 'error', error: chunk.error });\n              return;\n            }\n\n            const value = chunk.value;\n\n            const candidate = value.candidates[0];\n\n            if (candidate?.finishReason != null) {\n              finishReason = mapGoogleGenerativeAIFinishReason({\n                finishReason: candidate.finishReason,\n                hasToolCalls,\n              });\n            }\n\n            const usageMetadata = value.usageMetadata;\n\n            if (usageMetadata != null) {\n              usage = {\n                promptTokens: usageMetadata.promptTokenCount ?? NaN,\n                completionTokens: usageMetadata.candidatesTokenCount ?? NaN,\n              };\n            }\n\n            const content = candidate.content;\n\n            if (content == null) {\n              return;\n            }\n\n            const deltaText = getTextFromParts(content.parts);\n            if (deltaText != null) {\n              controller.enqueue({\n                type: 'text-delta',\n                textDelta: deltaText,\n              });\n            }\n\n            const toolCallDeltas = getToolCallsFromParts({\n              parts: content.parts,\n              generateId,\n            });\n\n            if (toolCallDeltas != null) {\n              for (const toolCall of toolCallDeltas) {\n                controller.enqueue({\n                  type: 'tool-call-delta',\n                  toolCallType: 'function',\n                  toolCallId: toolCall.toolCallId,\n                  toolName: toolCall.toolName,\n                  argsTextDelta: toolCall.args,\n                });\n\n                controller.enqueue({\n                  type: 'tool-call',\n                  toolCallType: 'function',\n                  toolCallId: toolCall.toolCallId,\n                  toolName: toolCall.toolName,\n                  args: toolCall.args,\n                });\n\n                hasToolCalls = true;\n              }\n            }\n          },\n\n          flush(controller) {\n            controller.enqueue({ type: 'finish', finishReason, usage });\n          },\n        }),\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings,\n    };\n  }\n}\n\nfunction getToolCallsFromParts({\n  parts,\n  generateId,\n}: {\n  parts: z.infer<typeof contentSchema>['parts'];\n  generateId: () => string;\n}) {\n  const functionCallParts = parts.filter(\n    part => 'functionCall' in part,\n  ) as Array<\n    GoogleGenerativeAIContentPart & {\n      functionCall: { name: string; args: unknown };\n    }\n  >;\n\n  return functionCallParts.length === 0\n    ? undefined\n    : functionCallParts.map(part => ({\n        toolCallType: 'function' as const,\n        toolCallId: generateId(),\n        toolName: part.functionCall.name,\n        args: JSON.stringify(part.functionCall.args),\n      }));\n}\n\nfunction getTextFromParts(parts: z.infer<typeof contentSchema>['parts']) {\n  const textParts = parts.filter(part => 'text' in part) as Array<\n    GoogleGenerativeAIContentPart & { text: string }\n  >;\n\n  return textParts.length === 0\n    ? undefined\n    : textParts.map(part => part.text).join('');\n}\n\nconst contentSchema = z.object({\n  role: z.string(),\n  parts: z.array(\n    z.union([\n      z.object({\n        text: z.string(),\n      }),\n      z.object({\n        functionCall: z.object({\n          name: z.string(),\n          args: z.unknown(),\n        }),\n      }),\n    ]),\n  ),\n});\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst responseSchema = z.object({\n  candidates: z.array(\n    z.object({\n      content: contentSchema,\n      finishReason: z.string().optional(),\n    }),\n  ),\n  usageMetadata: z\n    .object({\n      promptTokenCount: z.number(),\n      candidatesTokenCount: z.number().nullish(),\n      totalTokenCount: z.number(),\n    })\n    .optional(),\n});\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst chunkSchema = z.object({\n  candidates: z.array(\n    z.object({\n      content: contentSchema.optional(),\n      finishReason: z.string().optional(),\n    }),\n  ),\n  usageMetadata: z\n    .object({\n      promptTokenCount: z.number(),\n      candidatesTokenCount: z.number().nullish(),\n      totalTokenCount: z.number(),\n    })\n    .optional(),\n});\n\nfunction prepareToolsAndToolConfig(\n  mode: Parameters<LanguageModelV1['doGenerate']>[0]['mode'] & {\n    type: 'regular';\n  },\n) {\n  // when the tools array is empty, change it to undefined to prevent errors:\n  const tools = mode.tools?.length ? mode.tools : undefined;\n\n  if (tools == null) {\n    return { tools: undefined, toolConfig: undefined };\n  }\n\n  const mappedTools = {\n    functionDeclarations: tools.map(tool => ({\n      name: tool.name,\n      description: tool.description ?? '',\n      parameters: convertJSONSchemaToOpenAPISchema(tool.parameters),\n    })),\n  };\n\n  const toolChoice = mode.toolChoice;\n\n  if (toolChoice == null) {\n    return { tools: mappedTools, toolConfig: undefined };\n  }\n\n  const type = toolChoice.type;\n\n  switch (type) {\n    case 'auto':\n      return {\n        tools: mappedTools,\n        toolConfig: { functionCallingConfig: { mode: 'AUTO' } },\n      };\n    case 'none':\n      return {\n        tools: mappedTools,\n        toolConfig: { functionCallingConfig: { mode: 'NONE' } },\n      };\n    case 'required':\n      return {\n        tools: mappedTools,\n        toolConfig: { functionCallingConfig: { mode: 'ANY' } },\n      };\n    case 'tool':\n      return {\n        tools: mappedTools,\n        toolConfig: {\n          functionCallingConfig: {\n            mode: 'ANY',\n            allowedFunctionNames: [toolChoice.toolName],\n          },\n        },\n      };\n    default: {\n      const _exhaustiveCheck: never = type;\n      throw new Error(`Unsupported tool choice type: ${_exhaustiveCheck}`);\n    }\n  }\n}\n", "import { JSONSchema7Definition } from 'json-schema';\n\n/**\n * Converts JSON Schema 7 to OpenAPI Schema 3.0\n */\nexport function convertJSONSchemaToOpenAPISchema(\n  jsonSchema: JSONSchema7Definition,\n): unknown {\n  if (typeof jsonSchema === 'boolean') {\n    return { type: 'boolean', properties: {} };\n  }\n\n  const {\n    type,\n    description,\n    required,\n    properties,\n    items,\n    allOf,\n    anyOf,\n    oneOf,\n    format,\n    const: constValue,\n    minLength,\n  } = jsonSchema;\n\n  const result: Record<string, unknown> = {};\n\n  if (description) result.description = description;\n  if (required) result.required = required;\n  if (format) result.format = format;\n\n  if (constValue !== undefined) {\n    result.enum = [constValue];\n  }\n\n  // Handle type\n  if (type) {\n    if (Array.isArray(type)) {\n      if (type.includes('null')) {\n        result.type = type.filter(t => t !== 'null')[0];\n        result.nullable = true;\n      } else {\n        result.type = type;\n      }\n    } else if (type === 'null') {\n      result.type = 'null';\n    } else {\n      result.type = type;\n    }\n  }\n\n  if (properties) {\n    result.properties = Object.entries(properties).reduce(\n      (acc, [key, value]) => {\n        acc[key] = convertJSONSchemaToOpenAPISchema(value);\n        return acc;\n      },\n      {} as Record<string, unknown>,\n    );\n  }\n\n  if (items) {\n    result.items = Array.isArray(items)\n      ? items.map(convertJSONSchemaToOpenAPISchema)\n      : convertJSONSchemaToOpenAPISchema(items);\n  }\n\n  if (allOf) {\n    result.allOf = allOf.map(convertJSONSchemaToOpenAPISchema);\n  }\n  if (anyOf) {\n    result.anyOf = anyOf.map(convertJSONSchemaToOpenAPISchema);\n  }\n  if (oneOf) {\n    result.oneOf = oneOf.map(convertJSONSchemaToOpenAPISchema);\n  }\n\n  if (minLength !== undefined) result.minLength = minLength;\n\n  return result;\n}\n", "import {\n  LanguageModelV1Prompt,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport { convertUint8ArrayToBase64 } from '@ai-sdk/provider-utils';\nimport {\n  GoogleGenerativeAIContent,\n  GoogleGenerativeAIContentPart,\n  GoogleGenerativeAIPrompt,\n} from './google-generative-ai-prompt';\n\nexport function convertToGoogleGenerativeAIMessages(\n  prompt: LanguageModelV1Prompt,\n): GoogleGenerativeAIPrompt {\n  const systemInstructionParts: Array<{ text: string }> = [];\n  const contents: Array<GoogleGenerativeAIContent> = [];\n  let systemMessagesAllowed = true;\n\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case 'system': {\n        if (!systemMessagesAllowed) {\n          throw new UnsupportedFunctionalityError({\n            functionality:\n              'system messages are only supported at the beginning of the conversation',\n          });\n        }\n\n        systemInstructionParts.push({ text: content });\n        break;\n      }\n\n      case 'user': {\n        systemMessagesAllowed = false;\n\n        const parts: GoogleGenerativeAIContentPart[] = [];\n\n        for (const part of content) {\n          switch (part.type) {\n            case 'text': {\n              parts.push({ text: part.text });\n              break;\n            }\n            case 'image': {\n              if (part.image instanceof URL) {\n                // The AI SDK automatically downloads images for user image parts with URLs\n                throw new UnsupportedFunctionalityError({\n                  functionality: 'Image URLs in user messages',\n                });\n              }\n\n              parts.push({\n                inlineData: {\n                  mimeType: part.mimeType ?? 'image/jpeg',\n                  data: convertUint8ArrayToBase64(part.image),\n                },\n              });\n\n              break;\n            }\n            case 'file': {\n              if (part.data instanceof URL) {\n                // The AI SDK automatically downloads files for user file parts with URLs\n                throw new UnsupportedFunctionalityError({\n                  functionality: 'File URLs in user messages',\n                });\n              }\n\n              parts.push({\n                inlineData: { mimeType: part.mimeType, data: part.data },\n              });\n\n              break;\n            }\n          }\n        }\n\n        contents.push({ role: 'user', parts });\n        break;\n      }\n\n      case 'assistant': {\n        systemMessagesAllowed = false;\n\n        contents.push({\n          role: 'model',\n          parts: content\n            .map(part => {\n              switch (part.type) {\n                case 'text': {\n                  return part.text.length === 0\n                    ? undefined\n                    : { text: part.text };\n                }\n                case 'tool-call': {\n                  return {\n                    functionCall: {\n                      name: part.toolName,\n                      args: part.args,\n                    },\n                  };\n                }\n              }\n            })\n            .filter(\n              part => part !== undefined,\n            ) as GoogleGenerativeAIContentPart[],\n        });\n        break;\n      }\n\n      case 'tool': {\n        systemMessagesAllowed = false;\n\n        contents.push({\n          role: 'user',\n          parts: content.map(part => ({\n            functionResponse: {\n              name: part.toolName,\n              response: part.result,\n            },\n          })),\n        });\n        break;\n      }\n      default: {\n        const _exhaustiveCheck: never = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  return {\n    systemInstruction:\n      systemInstructionParts.length > 0\n        ? { parts: systemInstructionParts }\n        : undefined,\n    contents,\n  };\n}\n", "export function getModelPath(modelId: string): string {\n  return modelId.includes('/') ? modelId : `models/${modelId}`;\n}\n", "import { createJsonErrorResponseHandler } from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\n\nconst googleErrorDataSchema = z.object({\n  error: z.object({\n    code: z.number().nullable(),\n    message: z.string(),\n    status: z.string(),\n  }),\n});\n\nexport type GoogleErrorData = z.infer<typeof googleErrorDataSchema>;\n\nexport const googleFailedResponseHandler = createJsonErrorResponseHandler({\n  errorSchema: googleErrorDataSchema,\n  errorToMessage: data => data.error.message,\n});\n", "import { LanguageModelV1FinishReason } from '@ai-sdk/provider';\n\nexport function mapGoogleGenerativeAIFinishReason({\n  finishReason,\n  hasToolCalls,\n}: {\n  finishReason: string | null | undefined;\n  hasToolCalls: boolean;\n}): LanguageModelV1FinishReason {\n  switch (finishReason) {\n    case 'STOP':\n      return hasToolCalls ? 'tool-calls' : 'stop';\n    case 'MAX_TOKENS':\n      return 'length';\n    case 'RECITATION':\n    case 'SAFETY':\n      return 'content-filter';\n    case 'FINISH_REASON_UNSPECIFIED':\n    case 'OTHER':\n      return 'other';\n    default:\n      return 'unknown';\n  }\n}\n", "import {\n  FetchFunction,\n  generateId,\n  loadApi<PERSON><PERSON>,\n  withoutTrailingSlash,\n} from '@ai-sdk/provider-utils';\nimport { GoogleGenerativeAILanguageModel } from './google-generative-ai-language-model';\nimport {\n  GoogleGenerativeAIModelId,\n  GoogleGenerativeAISettings,\n} from './google-generative-ai-settings';\nimport { GoogleGenerativeAIEmbeddingModel } from './google-generative-ai-embedding-model';\nimport {\n  GoogleGenerativeAIEmbeddingModelId,\n  GoogleGenerativeAIEmbeddingSettings,\n} from './google-generative-ai-embedding-settings';\nimport {\n  EmbeddingModelV1,\n  LanguageModelV1,\n  ProviderV1,\n} from '@ai-sdk/provider';\n\nexport interface GoogleGenerativeAIProvider extends ProviderV1 {\n  (\n    modelId: GoogleGenerativeAIModelId,\n    settings?: GoogleGenerativeAISettings,\n  ): LanguageModelV1;\n\n  languageModel(\n    modelId: GoogleGenerativeAIModelId,\n    settings?: GoogleGenerativeAISettings,\n  ): LanguageModelV1;\n\n  chat(\n    modelId: GoogleGenerativeAIModelId,\n    settings?: GoogleGenerativeAISettings,\n  ): LanguageModelV1;\n\n  /**\n   * @deprecated Use `chat()` instead.\n   */\n  generativeAI(\n    modelId: GoogleGenerativeAIModelId,\n    settings?: GoogleGenerativeAISettings,\n  ): LanguageModelV1;\n\n  /**\n@deprecated Use `textEmbeddingModel()` instead.\n   */\n  embedding(\n    modelId: GoogleGenerativeAIEmbeddingModelId,\n    settings?: GoogleGenerativeAIEmbeddingSettings,\n  ): EmbeddingModelV1<string>;\n\n  /**\n@deprecated Use `textEmbeddingModel()` instead.\n */\n  textEmbedding(\n    modelId: GoogleGenerativeAIEmbeddingModelId,\n    settings?: GoogleGenerativeAIEmbeddingSettings,\n  ): EmbeddingModelV1<string>;\n\n  textEmbeddingModel(\n    modelId: GoogleGenerativeAIEmbeddingModelId,\n    settings?: GoogleGenerativeAIEmbeddingSettings,\n  ): EmbeddingModelV1<string>;\n}\n\nexport interface GoogleGenerativeAIProviderSettings {\n  /**\nUse a different URL prefix for API calls, e.g. to use proxy servers.\nThe default prefix is `https://generativelanguage.googleapis.com/v1beta`.\n   */\n  baseURL?: string;\n\n  /**\n@deprecated Use `baseURL` instead.\n   */\n  baseUrl?: string;\n\n  /**\nAPI key that is being send using the `x-goog-api-key` header.\nIt defaults to the `GOOGLE_GENERATIVE_AI_API_KEY` environment variable.\n   */\n  apiKey?: string;\n\n  /**\nCustom headers to include in the requests.\n     */\n  headers?: Record<string, string>;\n\n  /**\nCustom fetch implementation. You can use it as a middleware to intercept requests,\nor to provide a custom fetch implementation for e.g. testing.\n    */\n  fetch?: FetchFunction;\n\n  generateId?: () => string;\n}\n\n/**\nCreate a Google Generative AI provider instance.\n */\nexport function createGoogleGenerativeAI(\n  options: GoogleGenerativeAIProviderSettings = {},\n): GoogleGenerativeAIProvider {\n  const baseURL =\n    withoutTrailingSlash(options.baseURL ?? options.baseUrl) ??\n    'https://generativelanguage.googleapis.com/v1beta';\n\n  const getHeaders = () => ({\n    'x-goog-api-key': loadApiKey({\n      apiKey: options.apiKey,\n      environmentVariableName: 'GOOGLE_GENERATIVE_AI_API_KEY',\n      description: 'Google Generative AI',\n    }),\n    ...options.headers,\n  });\n\n  const createChatModel = (\n    modelId: GoogleGenerativeAIModelId,\n    settings: GoogleGenerativeAISettings = {},\n  ) =>\n    new GoogleGenerativeAILanguageModel(modelId, settings, {\n      provider: 'google.generative-ai',\n      baseURL,\n      headers: getHeaders,\n      generateId: options.generateId ?? generateId,\n      fetch: options.fetch,\n    });\n\n  const createEmbeddingModel = (\n    modelId: GoogleGenerativeAIEmbeddingModelId,\n    settings: GoogleGenerativeAIEmbeddingSettings = {},\n  ) =>\n    new GoogleGenerativeAIEmbeddingModel(modelId, settings, {\n      provider: 'google.generative-ai',\n      baseURL,\n      headers: getHeaders,\n      fetch: options.fetch,\n    });\n\n  const provider = function (\n    modelId: GoogleGenerativeAIModelId,\n    settings?: GoogleGenerativeAISettings,\n  ) {\n    if (new.target) {\n      throw new Error(\n        'The Google Generative AI model function cannot be called with the new keyword.',\n      );\n    }\n\n    return createChatModel(modelId, settings);\n  };\n\n  provider.languageModel = createChatModel;\n  provider.chat = createChatModel;\n  provider.generativeAI = createChatModel;\n  provider.embedding = createEmbeddingModel;\n  provider.textEmbedding = createEmbeddingModel;\n  provider.textEmbeddingModel = createEmbeddingModel;\n\n  return provider as GoogleGenerativeAIProvider;\n}\n\n/**\nDefault Google Generative AI provider instance.\n */\nexport const google = createGoogleGenerativeAI();\n", "import {\n  EmbeddingModelV1,\n  TooManyEmbeddingValuesForCallError,\n} from '@ai-sdk/provider';\nimport {\n  combineHeaders,\n  createJsonResponseHandler,\n  FetchFunction,\n  postJsonToApi,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { googleFailedResponseHandler } from './google-error';\nimport {\n  GoogleGenerativeAIEmbeddingModelId,\n  GoogleGenerativeAIEmbeddingSettings,\n} from './google-generative-ai-embedding-settings';\n\ntype GoogleGenerativeAIEmbeddingConfig = {\n  provider: string;\n  baseURL: string;\n  headers: () => Record<string, string | undefined>;\n  fetch?: FetchFunction;\n};\n\nexport class GoogleGenerativeAIEmbeddingModel\n  implements EmbeddingModelV1<string>\n{\n  readonly specificationVersion = 'v1';\n  readonly modelId: GoogleGenerativeAIEmbeddingModelId;\n\n  private readonly config: GoogleGenerativeAIEmbeddingConfig;\n  private readonly settings: GoogleGenerativeAIEmbeddingSettings;\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  get maxEmbeddingsPerCall(): number {\n    return 2048;\n  }\n\n  get supportsParallelCalls(): boolean {\n    return true;\n  }\n\n  constructor(\n    modelId: GoogleGenerativeAIEmbeddingModelId,\n    settings: GoogleGenerativeAIEmbeddingSettings,\n    config: GoogleGenerativeAIEmbeddingConfig,\n  ) {\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n\n  async doEmbed({\n    values,\n    headers,\n    abortSignal,\n  }: Parameters<EmbeddingModelV1<string>['doEmbed']>[0]): Promise<\n    Awaited<ReturnType<EmbeddingModelV1<string>['doEmbed']>>\n  > {\n    if (values.length > this.maxEmbeddingsPerCall) {\n      throw new TooManyEmbeddingValuesForCallError({\n        provider: this.provider,\n        modelId: this.modelId,\n        maxEmbeddingsPerCall: this.maxEmbeddingsPerCall,\n        values,\n      });\n    }\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: `${this.config.baseURL}/models/${this.modelId}:batchEmbedContents`,\n      headers: combineHeaders(this.config.headers(), headers),\n      body: {\n        requests: values.map(value => ({\n          model: `models/${this.modelId}`,\n          content: { role: 'user', parts: [{ text: value }] },\n          outputDimensionality: this.settings.outputDimensionality,\n        })),\n      },\n      failedResponseHandler: googleFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        googleGenerativeAITextEmbeddingResponseSchema,\n      ),\n      abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    return {\n      embeddings: response.embeddings.map(item => item.values),\n      usage: undefined,\n      rawResponse: { headers: responseHeaders },\n    };\n  }\n}\n\n// minimal version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst googleGenerativeAITextEmbeddingResponseSchema = z.object({\n  embeddings: z.array(z.object({ values: z.array(z.number()) })),\n});\n"], "mappings": ";AAAA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,OACK;;;ACEP;AAAA,EAGE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,SAAS,KAAAA,UAAS;;;ACTX,SAAS,iCACd,YACS;AACT,MAAI,OAAO,eAAe,WAAW;AACnC,WAAO,EAAE,MAAM,WAAW,YAAY,CAAC,EAAE;AAAA,EAC3C;AAEA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO;AAAA,IACP;AAAA,EACF,IAAI;AAEJ,QAAM,SAAkC,CAAC;AAEzC,MAAI;AAAa,WAAO,cAAc;AACtC,MAAI;AAAU,WAAO,WAAW;AAChC,MAAI;AAAQ,WAAO,SAAS;AAE5B,MAAI,eAAe,QAAW;AAC5B,WAAO,OAAO,CAAC,UAAU;AAAA,EAC3B;AAGA,MAAI,MAAM;AACR,QAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,UAAI,KAAK,SAAS,MAAM,GAAG;AACzB,eAAO,OAAO,KAAK,OAAO,OAAK,MAAM,MAAM,EAAE,CAAC;AAC9C,eAAO,WAAW;AAAA,MACpB,OAAO;AACL,eAAO,OAAO;AAAA,MAChB;AAAA,IACF,WAAW,SAAS,QAAQ;AAC1B,aAAO,OAAO;AAAA,IAChB,OAAO;AACL,aAAO,OAAO;AAAA,IAChB;AAAA,EACF;AAEA,MAAI,YAAY;AACd,WAAO,aAAa,OAAO,QAAQ,UAAU,EAAE;AAAA,MAC7C,CAAC,KAAK,CAAC,KAAK,KAAK,MAAM;AACrB,YAAI,GAAG,IAAI,iCAAiC,KAAK;AACjD,eAAO;AAAA,MACT;AAAA,MACA,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,OAAO;AACT,WAAO,QAAQ,MAAM,QAAQ,KAAK,IAC9B,MAAM,IAAI,gCAAgC,IAC1C,iCAAiC,KAAK;AAAA,EAC5C;AAEA,MAAI,OAAO;AACT,WAAO,QAAQ,MAAM,IAAI,gCAAgC;AAAA,EAC3D;AACA,MAAI,OAAO;AACT,WAAO,QAAQ,MAAM,IAAI,gCAAgC;AAAA,EAC3D;AACA,MAAI,OAAO;AACT,WAAO,QAAQ,MAAM,IAAI,gCAAgC;AAAA,EAC3D;AAEA,MAAI,cAAc;AAAW,WAAO,YAAY;AAEhD,SAAO;AACT;;;ACjFA;AAAA,EAEE;AAAA,OACK;AACP,SAAS,iCAAiC;AAOnC,SAAS,oCACd,QAC0B;AAb5B;AAcE,QAAM,yBAAkD,CAAC;AACzD,QAAM,WAA6C,CAAC;AACpD,MAAI,wBAAwB;AAE5B,aAAW,EAAE,MAAM,QAAQ,KAAK,QAAQ;AACtC,YAAQ,MAAM;AAAA,MACZ,KAAK,UAAU;AACb,YAAI,CAAC,uBAAuB;AAC1B,gBAAM,IAAI,8BAA8B;AAAA,YACtC,eACE;AAAA,UACJ,CAAC;AAAA,QACH;AAEA,+BAAuB,KAAK,EAAE,MAAM,QAAQ,CAAC;AAC7C;AAAA,MACF;AAAA,MAEA,KAAK,QAAQ;AACX,gCAAwB;AAExB,cAAM,QAAyC,CAAC;AAEhD,mBAAW,QAAQ,SAAS;AAC1B,kBAAQ,KAAK,MAAM;AAAA,YACjB,KAAK,QAAQ;AACX,oBAAM,KAAK,EAAE,MAAM,KAAK,KAAK,CAAC;AAC9B;AAAA,YACF;AAAA,YACA,KAAK,SAAS;AACZ,kBAAI,KAAK,iBAAiB,KAAK;AAE7B,sBAAM,IAAI,8BAA8B;AAAA,kBACtC,eAAe;AAAA,gBACjB,CAAC;AAAA,cACH;AAEA,oBAAM,KAAK;AAAA,gBACT,YAAY;AAAA,kBACV,WAAU,UAAK,aAAL,YAAiB;AAAA,kBAC3B,MAAM,0BAA0B,KAAK,KAAK;AAAA,gBAC5C;AAAA,cACF,CAAC;AAED;AAAA,YACF;AAAA,YACA,KAAK,QAAQ;AACX,kBAAI,KAAK,gBAAgB,KAAK;AAE5B,sBAAM,IAAI,8BAA8B;AAAA,kBACtC,eAAe;AAAA,gBACjB,CAAC;AAAA,cACH;AAEA,oBAAM,KAAK;AAAA,gBACT,YAAY,EAAE,UAAU,KAAK,UAAU,MAAM,KAAK,KAAK;AAAA,cACzD,CAAC;AAED;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,KAAK,EAAE,MAAM,QAAQ,MAAM,CAAC;AACrC;AAAA,MACF;AAAA,MAEA,KAAK,aAAa;AAChB,gCAAwB;AAExB,iBAAS,KAAK;AAAA,UACZ,MAAM;AAAA,UACN,OAAO,QACJ,IAAI,UAAQ;AACX,oBAAQ,KAAK,MAAM;AAAA,cACjB,KAAK,QAAQ;AACX,uBAAO,KAAK,KAAK,WAAW,IACxB,SACA,EAAE,MAAM,KAAK,KAAK;AAAA,cACxB;AAAA,cACA,KAAK,aAAa;AAChB,uBAAO;AAAA,kBACL,cAAc;AAAA,oBACZ,MAAM,KAAK;AAAA,oBACX,MAAM,KAAK;AAAA,kBACb;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF,CAAC,EACA;AAAA,YACC,UAAQ,SAAS;AAAA,UACnB;AAAA,QACJ,CAAC;AACD;AAAA,MACF;AAAA,MAEA,KAAK,QAAQ;AACX,gCAAwB;AAExB,iBAAS,KAAK;AAAA,UACZ,MAAM;AAAA,UACN,OAAO,QAAQ,IAAI,WAAS;AAAA,YAC1B,kBAAkB;AAAA,cAChB,MAAM,KAAK;AAAA,cACX,UAAU,KAAK;AAAA,YACjB;AAAA,UACF,EAAE;AAAA,QACJ,CAAC;AACD;AAAA,MACF;AAAA,MACA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AAAA,IACL,mBACE,uBAAuB,SAAS,IAC5B,EAAE,OAAO,uBAAuB,IAChC;AAAA,IACN;AAAA,EACF;AACF;;;AC3IO,SAAS,aAAa,SAAyB;AACpD,SAAO,QAAQ,SAAS,GAAG,IAAI,UAAU,UAAU,OAAO;AAC5D;;;ACFA,SAAS,sCAAsC;AAC/C,SAAS,SAAS;AAElB,IAAM,wBAAwB,EAAE,OAAO;AAAA,EACrC,OAAO,EAAE,OAAO;AAAA,IACd,MAAM,EAAE,OAAO,EAAE,SAAS;AAAA,IAC1B,SAAS,EAAE,OAAO;AAAA,IAClB,QAAQ,EAAE,OAAO;AAAA,EACnB,CAAC;AACH,CAAC;AAIM,IAAM,8BAA8B,+BAA+B;AAAA,EACxE,aAAa;AAAA,EACb,gBAAgB,UAAQ,KAAK,MAAM;AACrC,CAAC;;;ACdM,SAAS,kCAAkC;AAAA,EAChD;AAAA,EACA;AACF,GAGgC;AAC9B,UAAQ,cAAc;AAAA,IACpB,KAAK;AACH,aAAO,eAAe,eAAe;AAAA,IACvC,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;;;ALWO,IAAM,kCAAN,MAAiE;AAAA,EActE,YACE,SACA,UACA,QACA;AAjBF,SAAS,uBAAuB;AAChC,SAAS,8BAA8B;AACvC,SAAS,oBAAoB;AAgB3B,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,SAAS;AAAA,EAChB;AAAA,EAjBA,IAAI,2BAA2B;AAC7B,WAAO,KAAK,SAAS,sBAAsB;AAAA,EAC7C;AAAA,EAiBA,IAAI,WAAmB;AACrB,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EAEA,MAAc,QAAQ;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAiD;AA1EnD;AA2EI,UAAM,OAAO,KAAK;AAElB,UAAM,WAAyC,CAAC;AAEhD,QAAI,QAAQ,MAAM;AAChB,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,UAAM,mBAAmB;AAAA;AAAA,MAEvB,iBAAiB;AAAA,MACjB;AAAA,MACA,MAAM,sBAAQ,KAAK,SAAS;AAAA,MAC5B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAGA,mBACE,iDAAgB,UAAS,SAAS,qBAAqB;AAAA,MACzD,iBACE,iDAAgB,UAAS,UACzB,eAAe,UAAU;AAAA;AAAA,MAGzB,KAAK,2BACD,iCAAiC,eAAe,MAAM,IACtD;AAAA,IACR;AAEA,UAAM,EAAE,UAAU,kBAAkB,IAClC,oCAAoC,MAAM;AAE5C,YAAQ,MAAM;AAAA,MACZ,KAAK,WAAW;AACd,eAAO;AAAA,UACL,MAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA,gBAAgB,KAAK,SAAS;AAAA,YAC9B,GAAG,0BAA0B,IAAI;AAAA,YACjC,eAAe,KAAK,SAAS;AAAA,UAC/B;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,MAEA,KAAK,eAAe;AAClB,eAAO;AAAA,UACL,MAAM;AAAA,YACJ,kBAAkB;AAAA,cAChB,GAAG;AAAA,cACH,kBAAkB;AAAA,cAClB,gBACE,KAAK,UAAU;AAAA;AAAA,cAGf,KAAK,2BACD,iCAAiC,KAAK,MAAM,IAC5C;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,YACA,gBAAgB,KAAK,SAAS;AAAA,YAC9B,eAAe,KAAK,SAAS;AAAA,UAC/B;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,MAEA,KAAK,eAAe;AAClB,eAAO;AAAA,UACL,MAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA,OAAO;AAAA,cACL,sBAAsB;AAAA,gBACpB;AAAA,kBACE,MAAM,KAAK,KAAK;AAAA,kBAChB,cAAa,UAAK,KAAK,gBAAV,YAAyB;AAAA,kBACtC,YAAY;AAAA,oBACV,KAAK,KAAK;AAAA,kBACZ;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,YACA,YAAY,EAAE,uBAAuB,EAAE,MAAM,MAAM,EAAE;AAAA,YACrD,gBAAgB,KAAK,SAAS;AAAA,YAC9B,eAAe,KAAK,SAAS;AAAA,UAC/B;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,MAEA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,WACJ,SAC6D;AAvLjE;AAwLI,UAAM,EAAE,MAAM,SAAS,IAAI,MAAM,KAAK,QAAQ,OAAO;AAErD,UAAM,EAAE,iBAAiB,OAAO,SAAS,IAAI,MAAM,cAAc;AAAA,MAC/D,KAAK,GAAG,KAAK,OAAO,OAAO,IAAI;AAAA,QAC7B,KAAK;AAAA,MACP,CAAC;AAAA,MACD,SAAS,eAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;AAAA,MAC9D,MAAM;AAAA,MACN,uBAAuB;AAAA,MACvB,2BAA2B,0BAA0B,cAAc;AAAA,MACnE,aAAa,QAAQ;AAAA,MACrB,OAAO,KAAK,OAAO;AAAA,IACrB,CAAC;AAED,UAAM,EAAE,UAAU,WAAW,GAAG,YAAY,IAAI;AAChD,UAAM,YAAY,SAAS,WAAW,CAAC;AAEvC,UAAM,YAAY,sBAAsB;AAAA,MACtC,OAAO,UAAU,QAAQ;AAAA,MACzB,YAAY,KAAK,OAAO;AAAA,IAC1B,CAAC;AAED,UAAM,gBAAgB,SAAS;AAE/B,WAAO;AAAA,MACL,MAAM,iBAAiB,UAAU,QAAQ,KAAK;AAAA,MAC9C;AAAA,MACA,cAAc,kCAAkC;AAAA,QAC9C,cAAc,UAAU;AAAA,QACxB,cAAc,aAAa,QAAQ,UAAU,SAAS;AAAA,MACxD,CAAC;AAAA,MACD,OAAO;AAAA,QACL,eAAc,oDAAe,qBAAf,YAAmC;AAAA,QACjD,mBAAkB,oDAAe,yBAAf,YAAuC;AAAA,MAC3D;AAAA,MACA,SAAS,EAAE,WAAW,YAAY;AAAA,MAClC,aAAa,EAAE,SAAS,gBAAgB;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,SACJ,SAC2D;AAC3D,UAAM,EAAE,MAAM,SAAS,IAAI,MAAM,KAAK,QAAQ,OAAO;AAErD,UAAM,EAAE,iBAAiB,OAAO,SAAS,IAAI,MAAM,cAAc;AAAA,MAC/D,KAAK,GAAG,KAAK,OAAO,OAAO,IAAI;AAAA,QAC7B,KAAK;AAAA,MACP,CAAC;AAAA,MACD,SAAS,eAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;AAAA,MAC9D,MAAM;AAAA,MACN,uBAAuB;AAAA,MACvB,2BAA2B,iCAAiC,WAAW;AAAA,MACvE,aAAa,QAAQ;AAAA,MACrB,OAAO,KAAK,OAAO;AAAA,IACrB,CAAC;AAED,UAAM,EAAE,UAAU,WAAW,GAAG,YAAY,IAAI;AAEhD,QAAI,eAA4C;AAChD,QAAI,QAA4D;AAAA,MAC9D,cAAc,OAAO;AAAA,MACrB,kBAAkB,OAAO;AAAA,IAC3B;AAEA,UAAMC,cAAa,KAAK,OAAO;AAC/B,QAAI,eAAe;AAEnB,WAAO;AAAA,MACL,QAAQ,SAAS;AAAA,QACf,IAAI,gBAGF;AAAA,UACA,UAAU,OAAO,YAAY;AAnQvC;AAoQY,gBAAI,CAAC,MAAM,SAAS;AAClB,yBAAW,QAAQ,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,CAAC;AACxD;AAAA,YACF;AAEA,kBAAM,QAAQ,MAAM;AAEpB,kBAAM,YAAY,MAAM,WAAW,CAAC;AAEpC,iBAAI,uCAAW,iBAAgB,MAAM;AACnC,6BAAe,kCAAkC;AAAA,gBAC/C,cAAc,UAAU;AAAA,gBACxB;AAAA,cACF,CAAC;AAAA,YACH;AAEA,kBAAM,gBAAgB,MAAM;AAE5B,gBAAI,iBAAiB,MAAM;AACzB,sBAAQ;AAAA,gBACN,eAAc,mBAAc,qBAAd,YAAkC;AAAA,gBAChD,mBAAkB,mBAAc,yBAAd,YAAsC;AAAA,cAC1D;AAAA,YACF;AAEA,kBAAM,UAAU,UAAU;AAE1B,gBAAI,WAAW,MAAM;AACnB;AAAA,YACF;AAEA,kBAAM,YAAY,iBAAiB,QAAQ,KAAK;AAChD,gBAAI,aAAa,MAAM;AACrB,yBAAW,QAAQ;AAAA,gBACjB,MAAM;AAAA,gBACN,WAAW;AAAA,cACb,CAAC;AAAA,YACH;AAEA,kBAAM,iBAAiB,sBAAsB;AAAA,cAC3C,OAAO,QAAQ;AAAA,cACf,YAAAA;AAAA,YACF,CAAC;AAED,gBAAI,kBAAkB,MAAM;AAC1B,yBAAW,YAAY,gBAAgB;AACrC,2BAAW,QAAQ;AAAA,kBACjB,MAAM;AAAA,kBACN,cAAc;AAAA,kBACd,YAAY,SAAS;AAAA,kBACrB,UAAU,SAAS;AAAA,kBACnB,eAAe,SAAS;AAAA,gBAC1B,CAAC;AAED,2BAAW,QAAQ;AAAA,kBACjB,MAAM;AAAA,kBACN,cAAc;AAAA,kBACd,YAAY,SAAS;AAAA,kBACrB,UAAU,SAAS;AAAA,kBACnB,MAAM,SAAS;AAAA,gBACjB,CAAC;AAED,+BAAe;AAAA,cACjB;AAAA,YACF;AAAA,UACF;AAAA,UAEA,MAAM,YAAY;AAChB,uBAAW,QAAQ,EAAE,MAAM,UAAU,cAAc,MAAM,CAAC;AAAA,UAC5D;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,SAAS,EAAE,WAAW,YAAY;AAAA,MAClC,aAAa,EAAE,SAAS,gBAAgB;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,sBAAsB;AAAA,EAC7B;AAAA,EACA,YAAAA;AACF,GAGG;AACD,QAAM,oBAAoB,MAAM;AAAA,IAC9B,UAAQ,kBAAkB;AAAA,EAC5B;AAMA,SAAO,kBAAkB,WAAW,IAChC,SACA,kBAAkB,IAAI,WAAS;AAAA,IAC7B,cAAc;AAAA,IACd,YAAYA,YAAW;AAAA,IACvB,UAAU,KAAK,aAAa;AAAA,IAC5B,MAAM,KAAK,UAAU,KAAK,aAAa,IAAI;AAAA,EAC7C,EAAE;AACR;AAEA,SAAS,iBAAiB,OAA+C;AACvE,QAAM,YAAY,MAAM,OAAO,UAAQ,UAAU,IAAI;AAIrD,SAAO,UAAU,WAAW,IACxB,SACA,UAAU,IAAI,UAAQ,KAAK,IAAI,EAAE,KAAK,EAAE;AAC9C;AAEA,IAAM,gBAAgBC,GAAE,OAAO;AAAA,EAC7B,MAAMA,GAAE,OAAO;AAAA,EACf,OAAOA,GAAE;AAAA,IACPA,GAAE,MAAM;AAAA,MACNA,GAAE,OAAO;AAAA,QACP,MAAMA,GAAE,OAAO;AAAA,MACjB,CAAC;AAAA,MACDA,GAAE,OAAO;AAAA,QACP,cAAcA,GAAE,OAAO;AAAA,UACrB,MAAMA,GAAE,OAAO;AAAA,UACf,MAAMA,GAAE,QAAQ;AAAA,QAClB,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF,CAAC;AAID,IAAM,iBAAiBA,GAAE,OAAO;AAAA,EAC9B,YAAYA,GAAE;AAAA,IACZA,GAAE,OAAO;AAAA,MACP,SAAS;AAAA,MACT,cAAcA,GAAE,OAAO,EAAE,SAAS;AAAA,IACpC,CAAC;AAAA,EACH;AAAA,EACA,eAAeA,GACZ,OAAO;AAAA,IACN,kBAAkBA,GAAE,OAAO;AAAA,IAC3B,sBAAsBA,GAAE,OAAO,EAAE,QAAQ;AAAA,IACzC,iBAAiBA,GAAE,OAAO;AAAA,EAC5B,CAAC,EACA,SAAS;AACd,CAAC;AAID,IAAM,cAAcA,GAAE,OAAO;AAAA,EAC3B,YAAYA,GAAE;AAAA,IACZA,GAAE,OAAO;AAAA,MACP,SAAS,cAAc,SAAS;AAAA,MAChC,cAAcA,GAAE,OAAO,EAAE,SAAS;AAAA,IACpC,CAAC;AAAA,EACH;AAAA,EACA,eAAeA,GACZ,OAAO;AAAA,IACN,kBAAkBA,GAAE,OAAO;AAAA,IAC3B,sBAAsBA,GAAE,OAAO,EAAE,QAAQ;AAAA,IACzC,iBAAiBA,GAAE,OAAO;AAAA,EAC5B,CAAC,EACA,SAAS;AACd,CAAC;AAED,SAAS,0BACP,MAGA;AA/aF;AAibE,QAAM,UAAQ,UAAK,UAAL,mBAAY,UAAS,KAAK,QAAQ;AAEhD,MAAI,SAAS,MAAM;AACjB,WAAO,EAAE,OAAO,QAAW,YAAY,OAAU;AAAA,EACnD;AAEA,QAAM,cAAc;AAAA,IAClB,sBAAsB,MAAM,IAAI,UAAK;AAxbzC,UAAAC;AAwb6C;AAAA,QACvC,MAAM,KAAK;AAAA,QACX,cAAaA,MAAA,KAAK,gBAAL,OAAAA,MAAoB;AAAA,QACjC,YAAY,iCAAiC,KAAK,UAAU;AAAA,MAC9D;AAAA,KAAE;AAAA,EACJ;AAEA,QAAM,aAAa,KAAK;AAExB,MAAI,cAAc,MAAM;AACtB,WAAO,EAAE,OAAO,aAAa,YAAY,OAAU;AAAA,EACrD;AAEA,QAAM,OAAO,WAAW;AAExB,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO;AAAA,QACL,OAAO;AAAA,QACP,YAAY,EAAE,uBAAuB,EAAE,MAAM,OAAO,EAAE;AAAA,MACxD;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,OAAO;AAAA,QACP,YAAY,EAAE,uBAAuB,EAAE,MAAM,OAAO,EAAE;AAAA,MACxD;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,OAAO;AAAA,QACP,YAAY,EAAE,uBAAuB,EAAE,MAAM,MAAM,EAAE;AAAA,MACvD;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,OAAO;AAAA,QACP,YAAY;AAAA,UACV,uBAAuB;AAAA,YACrB,MAAM;AAAA,YACN,sBAAsB,CAAC,WAAW,QAAQ;AAAA,UAC5C;AAAA,QACF;AAAA,MACF;AAAA,IACF,SAAS;AACP,YAAM,mBAA0B;AAChC,YAAM,IAAI,MAAM,iCAAiC,gBAAgB,EAAE;AAAA,IACrE;AAAA,EACF;AACF;;;ADvdO,IAAM,SAAN,MAAa;AAAA;AAAA;AAAA;AAAA,EAelB,YAAY,UAA8C,CAAC,GAAG;AA9BhE;AA+BI,SAAK,WACH,2BAAqB,aAAQ,YAAR,YAAmB,QAAQ,OAAO,MAAvD,YACA;AACF,SAAK,SAAS,QAAQ;AACtB,SAAK,UAAU,QAAQ;AACvB,SAAK,cAAa,aAAQ,eAAR,YAAsB;AAAA,EAC1C;AAAA,EAEA,IAAY,aAAa;AACvB,WAAO;AAAA,MACL,SAAS,KAAK;AAAA,MACd,SAAS,OAAO;AAAA,QACd,kBAAkB,WAAW;AAAA,UAC3B,QAAQ,KAAK;AAAA,UACb,yBAAyB;AAAA,UACzB,aAAa;AAAA,QACf,CAAC;AAAA,QACD,GAAG,KAAK;AAAA,MACV;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,aACE,SACA,WAAuC,CAAC,GACxC;AACA,WAAO,KAAK,KAAK,SAAS,QAAQ;AAAA,EACpC;AAAA,EAEA,KACE,SACA,WAAuC,CAAC,GACxC;AACA,WAAO,IAAI,gCAAgC,SAAS,UAAU;AAAA,MAC5D,UAAU;AAAA,MACV,GAAG,KAAK;AAAA,MACR,YAAY,KAAK;AAAA,IACnB,CAAC;AAAA,EACH;AACF;;;AOzEA;AAAA,EAEE,cAAAC;AAAA,EACA,cAAAC;AAAA,EACA,wBAAAC;AAAA,OACK;;;ACLP;AAAA,EAEE;AAAA,OACK;AACP;AAAA,EACE,kBAAAC;AAAA,EACA,6BAAAC;AAAA,EAEA,iBAAAC;AAAA,OACK;AACP,SAAS,KAAAC,UAAS;AAcX,IAAM,mCAAN,MAEP;AAAA,EAmBE,YACE,SACA,UACA,QACA;AAtBF,SAAS,uBAAuB;AAuB9B,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,SAAS;AAAA,EAChB;AAAA,EApBA,IAAI,WAAmB;AACrB,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EAEA,IAAI,uBAA+B;AACjC,WAAO;AAAA,EACT;AAAA,EAEA,IAAI,wBAAiC;AACnC,WAAO;AAAA,EACT;AAAA,EAYA,MAAM,QAAQ;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAEE;AACA,QAAI,OAAO,SAAS,KAAK,sBAAsB;AAC7C,YAAM,IAAI,mCAAmC;AAAA,QAC3C,UAAU,KAAK;AAAA,QACf,SAAS,KAAK;AAAA,QACd,sBAAsB,KAAK;AAAA,QAC3B;AAAA,MACF,CAAC;AAAA,IACH;AAEA,UAAM,EAAE,iBAAiB,OAAO,SAAS,IAAI,MAAMC,eAAc;AAAA,MAC/D,KAAK,GAAG,KAAK,OAAO,OAAO,WAAW,KAAK,OAAO;AAAA,MAClD,SAASC,gBAAe,KAAK,OAAO,QAAQ,GAAG,OAAO;AAAA,MACtD,MAAM;AAAA,QACJ,UAAU,OAAO,IAAI,YAAU;AAAA,UAC7B,OAAO,UAAU,KAAK,OAAO;AAAA,UAC7B,SAAS,EAAE,MAAM,QAAQ,OAAO,CAAC,EAAE,MAAM,MAAM,CAAC,EAAE;AAAA,UAClD,sBAAsB,KAAK,SAAS;AAAA,QACtC,EAAE;AAAA,MACJ;AAAA,MACA,uBAAuB;AAAA,MACvB,2BAA2BC;AAAA,QACzB;AAAA,MACF;AAAA,MACA;AAAA,MACA,OAAO,KAAK,OAAO;AAAA,IACrB,CAAC;AAED,WAAO;AAAA,MACL,YAAY,SAAS,WAAW,IAAI,UAAQ,KAAK,MAAM;AAAA,MACvD,OAAO;AAAA,MACP,aAAa,EAAE,SAAS,gBAAgB;AAAA,IAC1C;AAAA,EACF;AACF;AAIA,IAAM,gDAAgDC,GAAE,OAAO;AAAA,EAC7D,YAAYA,GAAE,MAAMA,GAAE,OAAO,EAAE,QAAQA,GAAE,MAAMA,GAAE,OAAO,CAAC,EAAE,CAAC,CAAC;AAC/D,CAAC;;;ADEM,SAAS,yBACd,UAA8C,CAAC,GACnB;AAzG9B;AA0GE,QAAM,WACJ,KAAAC,uBAAqB,aAAQ,YAAR,YAAmB,QAAQ,OAAO,MAAvD,YACA;AAEF,QAAM,aAAa,OAAO;AAAA,IACxB,kBAAkBC,YAAW;AAAA,MAC3B,QAAQ,QAAQ;AAAA,MAChB,yBAAyB;AAAA,MACzB,aAAa;AAAA,IACf,CAAC;AAAA,IACD,GAAG,QAAQ;AAAA,EACb;AAEA,QAAM,kBAAkB,CACtB,SACA,WAAuC,CAAC,MACxC;AA1HJ,QAAAC;AA2HI,eAAI,gCAAgC,SAAS,UAAU;AAAA,MACrD,UAAU;AAAA,MACV;AAAA,MACA,SAAS;AAAA,MACT,aAAYA,MAAA,QAAQ,eAAR,OAAAA,MAAsBC;AAAA,MAClC,OAAO,QAAQ;AAAA,IACjB,CAAC;AAAA;AAEH,QAAM,uBAAuB,CAC3B,SACA,WAAgD,CAAC,MAEjD,IAAI,iCAAiC,SAAS,UAAU;AAAA,IACtD,UAAU;AAAA,IACV;AAAA,IACA,SAAS;AAAA,IACT,OAAO,QAAQ;AAAA,EACjB,CAAC;AAEH,QAAM,WAAW,SACf,SACA,UACA;AACA,QAAI,YAAY;AACd,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO,gBAAgB,SAAS,QAAQ;AAAA,EAC1C;AAEA,WAAS,gBAAgB;AACzB,WAAS,OAAO;AAChB,WAAS,eAAe;AACxB,WAAS,YAAY;AACrB,WAAS,gBAAgB;AACzB,WAAS,qBAAqB;AAE9B,SAAO;AACT;AAKO,IAAM,SAAS,yBAAyB;", "names": ["z", "generateId", "z", "_a", "generateId", "loadApiKey", "withoutTrailingSlash", "combineHeaders", "createJsonResponseHandler", "postJsonToApi", "z", "postJsonToApi", "combineHeaders", "createJsonResponseHandler", "z", "withoutTrailingSlash", "loadApiKey", "_a", "generateId"]}