{"version": 3, "sources": ["../src/index.ts", "../src/anthropic-facade.ts", "../src/anthropic-messages-language-model.ts", "../src/anthropic-error.ts", "../src/convert-to-anthropic-messages-prompt.ts", "../src/map-anthropic-stop-reason.ts", "../src/anthropic-provider.ts"], "sourcesContent": ["export * from './anthropic-facade';\nexport * from './anthropic-provider';\n", "import { loadApiKey, withoutTrailingSlash } from '@ai-sdk/provider-utils';\nimport { AnthropicMessagesLanguageModel } from './anthropic-messages-language-model';\nimport {\n  AnthropicMessagesModelId,\n  AnthropicMessagesSettings,\n} from './anthropic-messages-settings';\nimport { AnthropicProviderSettings } from './anthropic-provider';\n\n/**\n * @deprecated Use `createAnthropic` instead.\n */\nexport class Anthropic {\n  /**\n   * Base URL for Anthropic API calls.\n   */\n  readonly baseURL: string;\n\n  readonly apiKey?: string;\n\n  readonly headers?: Record<string, string>;\n\n  /**\n   * Creates a new Anthropic provider instance.\n   */\n  constructor(options: AnthropicProviderSettings = {}) {\n    this.baseURL =\n      withoutTrailingSlash(options.baseURL ?? options.baseUrl) ??\n      'https://api.anthropic.com/v1';\n    this.apiKey = options.apiKey;\n    this.headers = options.headers;\n  }\n\n  private get baseConfig() {\n    return {\n      baseURL: this.baseURL,\n      headers: () => ({\n        'anthropic-version': '2023-06-01',\n        'anthropic-beta': 'tools-2024-04-04',\n        'x-api-key': loadApiKey({\n          apiKey: this.apiKey,\n          environmentVariableName: 'ANTHROPIC_API_KEY',\n          description: 'Anthropic',\n        }),\n        ...this.headers,\n      }),\n    };\n  }\n\n  /**\n   * @deprecated Use `chat()` instead.\n   */\n  messages(\n    modelId: AnthropicMessagesModelId,\n    settings: AnthropicMessagesSettings = {},\n  ) {\n    return this.chat(modelId, settings);\n  }\n\n  chat(\n    modelId: AnthropicMessagesModelId,\n    settings: AnthropicMessagesSettings = {},\n  ) {\n    return new AnthropicMessagesLanguageModel(modelId, settings, {\n      provider: 'anthropic.messages',\n      ...this.baseConfig,\n    });\n  }\n}\n", "import {\n  LanguageModelV1,\n  LanguageModelV1<PERSON>all<PERSON><PERSON>ning,\n  LanguageModelV1FinishReason,\n  LanguageModelV1FunctionToolCall,\n  LanguageModelV1StreamPart,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport {\n  ParseR<PERSON>ult,\n  combineHeaders,\n  createEventSourceResponseHandler,\n  create<PERSON>sonResponseHandler,\n  postJsonToApi,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { anthropicFailedResponseHandler } from './anthropic-error';\nimport {\n  AnthropicMessagesModelId,\n  AnthropicMessagesSettings,\n} from './anthropic-messages-settings';\nimport { convertToAnthropicMessagesPrompt } from './convert-to-anthropic-messages-prompt';\nimport { mapAnthropicStopReason } from './map-anthropic-stop-reason';\n\ntype AnthropicMessagesConfig = {\n  provider: string;\n  baseURL: string;\n  headers: () => Record<string, string | undefined>;\n  fetch?: typeof fetch;\n};\n\nexport class AnthropicMessagesLanguageModel implements LanguageModelV1 {\n  readonly specificationVersion = 'v1';\n  readonly defaultObjectGenerationMode = 'tool';\n  readonly supportsImageUrls = false;\n\n  readonly modelId: AnthropicMessagesModelId;\n  readonly settings: AnthropicMessagesSettings;\n\n  private readonly config: AnthropicMessagesConfig;\n\n  constructor(\n    modelId: AnthropicMessagesModelId,\n    settings: AnthropicMessagesSettings,\n    config: AnthropicMessagesConfig,\n  ) {\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  private async getArgs({\n    mode,\n    prompt,\n    maxTokens,\n    temperature,\n    topP,\n    topK,\n    frequencyPenalty,\n    presencePenalty,\n    stopSequences,\n    responseFormat,\n    seed,\n  }: Parameters<LanguageModelV1['doGenerate']>[0]) {\n    const type = mode.type;\n\n    const warnings: LanguageModelV1CallWarning[] = [];\n\n    if (frequencyPenalty != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'frequencyPenalty',\n      });\n    }\n\n    if (presencePenalty != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'presencePenalty',\n      });\n    }\n\n    if (seed != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'seed',\n      });\n    }\n\n    if (responseFormat != null && responseFormat.type !== 'text') {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'responseFormat',\n        details: 'JSON response format is not supported.',\n      });\n    }\n\n    const messagesPrompt = convertToAnthropicMessagesPrompt(prompt);\n\n    const baseArgs = {\n      // model id:\n      model: this.modelId,\n\n      // model specific settings:\n      top_k: topK ?? this.settings.topK,\n\n      // standardized settings:\n      max_tokens: maxTokens ?? 4096, // 4096: max model output tokens\n      temperature,\n      top_p: topP,\n      stop_sequences: stopSequences,\n\n      // prompt:\n      system: messagesPrompt.system,\n      messages: messagesPrompt.messages,\n    };\n\n    switch (type) {\n      case 'regular': {\n        return {\n          args: { ...baseArgs, ...prepareToolsAndToolChoice(mode) },\n          warnings,\n        };\n      }\n\n      case 'object-json': {\n        throw new UnsupportedFunctionalityError({\n          functionality: 'json-mode object generation',\n        });\n      }\n\n      case 'object-tool': {\n        const { name, description, parameters } = mode.tool;\n\n        return {\n          args: {\n            ...baseArgs,\n            tools: [{ name, description, input_schema: parameters }],\n            tool_choice: { type: 'tool', name },\n          },\n          warnings,\n        };\n      }\n\n      default: {\n        const _exhaustiveCheck: never = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  async doGenerate(\n    options: Parameters<LanguageModelV1['doGenerate']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doGenerate']>>> {\n    const { args, warnings } = await this.getArgs(options);\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: `${this.config.baseURL}/messages`,\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body: args,\n      failedResponseHandler: anthropicFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        anthropicMessagesResponseSchema,\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { messages: rawPrompt, ...rawSettings } = args;\n\n    // extract text\n    let text = '';\n    for (const content of response.content) {\n      if (content.type === 'text') {\n        text += content.text;\n      }\n    }\n\n    // extract tool calls\n    let toolCalls: LanguageModelV1FunctionToolCall[] | undefined = undefined;\n    if (response.content.some(content => content.type === 'tool_use')) {\n      toolCalls = [];\n      for (const content of response.content) {\n        if (content.type === 'tool_use') {\n          toolCalls.push({\n            toolCallType: 'function',\n            toolCallId: content.id,\n            toolName: content.name,\n            args: JSON.stringify(content.input),\n          });\n        }\n      }\n    }\n\n    return {\n      text,\n      toolCalls,\n      finishReason: mapAnthropicStopReason(response.stop_reason),\n      usage: {\n        promptTokens: response.usage.input_tokens,\n        completionTokens: response.usage.output_tokens,\n      },\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings,\n    };\n  }\n\n  async doStream(\n    options: Parameters<LanguageModelV1['doStream']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doStream']>>> {\n    const { args, warnings } = await this.getArgs(options);\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: `${this.config.baseURL}/messages`,\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body: {\n        ...args,\n        stream: true,\n      },\n      failedResponseHandler: anthropicFailedResponseHandler,\n      successfulResponseHandler: createEventSourceResponseHandler(\n        anthropicMessagesChunkSchema,\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { messages: rawPrompt, ...rawSettings } = args;\n\n    let finishReason: LanguageModelV1FinishReason = 'other';\n    const usage: { promptTokens: number; completionTokens: number } = {\n      promptTokens: Number.NaN,\n      completionTokens: Number.NaN,\n    };\n\n    const toolCallContentBlocks: Record<\n      number,\n      {\n        toolCallId: string;\n        toolName: string;\n        jsonText: string;\n      }\n    > = {};\n\n    return {\n      stream: response.pipeThrough(\n        new TransformStream<\n          ParseResult<z.infer<typeof anthropicMessagesChunkSchema>>,\n          LanguageModelV1StreamPart\n        >({\n          transform(chunk, controller) {\n            if (!chunk.success) {\n              controller.enqueue({ type: 'error', error: chunk.error });\n              return;\n            }\n\n            const value = chunk.value;\n\n            switch (value.type) {\n              case 'ping': {\n                return; // ignored\n              }\n\n              case 'content_block_start': {\n                const contentBlockType = value.content_block.type;\n\n                switch (contentBlockType) {\n                  case 'text': {\n                    return; // ignored\n                  }\n\n                  case 'tool_use': {\n                    toolCallContentBlocks[value.index] = {\n                      toolCallId: value.content_block.id,\n                      toolName: value.content_block.name,\n                      jsonText: '',\n                    };\n                    return;\n                  }\n\n                  default: {\n                    const _exhaustiveCheck: never = contentBlockType;\n                    throw new Error(\n                      `Unsupported content block type: ${_exhaustiveCheck}`,\n                    );\n                  }\n                }\n              }\n\n              case 'content_block_stop': {\n                // when finishing a tool call block, send the full tool call:\n                if (toolCallContentBlocks[value.index] != null) {\n                  const contentBlock = toolCallContentBlocks[value.index];\n\n                  controller.enqueue({\n                    type: 'tool-call',\n                    toolCallType: 'function',\n                    toolCallId: contentBlock.toolCallId,\n                    toolName: contentBlock.toolName,\n                    args: contentBlock.jsonText,\n                  });\n\n                  delete toolCallContentBlocks[value.index];\n                }\n\n                return;\n              }\n\n              case 'content_block_delta': {\n                const deltaType = value.delta.type;\n                switch (deltaType) {\n                  case 'text_delta': {\n                    controller.enqueue({\n                      type: 'text-delta',\n                      textDelta: value.delta.text,\n                    });\n\n                    return;\n                  }\n\n                  case 'input_json_delta': {\n                    const contentBlock = toolCallContentBlocks[value.index];\n\n                    controller.enqueue({\n                      type: 'tool-call-delta',\n                      toolCallType: 'function',\n                      toolCallId: contentBlock.toolCallId,\n                      toolName: contentBlock.toolName,\n                      argsTextDelta: value.delta.partial_json,\n                    });\n\n                    contentBlock.jsonText += value.delta.partial_json;\n\n                    return;\n                  }\n\n                  default: {\n                    const _exhaustiveCheck: never = deltaType;\n                    throw new Error(\n                      `Unsupported delta type: ${_exhaustiveCheck}`,\n                    );\n                  }\n                }\n              }\n\n              case 'message_start': {\n                usage.promptTokens = value.message.usage.input_tokens;\n                usage.completionTokens = value.message.usage.output_tokens;\n                return;\n              }\n\n              case 'message_delta': {\n                usage.completionTokens = value.usage.output_tokens;\n                finishReason = mapAnthropicStopReason(value.delta.stop_reason);\n                return;\n              }\n\n              case 'message_stop': {\n                controller.enqueue({ type: 'finish', finishReason, usage });\n                return;\n              }\n\n              case 'error': {\n                controller.enqueue({ type: 'error', error: value.error });\n                return;\n              }\n\n              default: {\n                const _exhaustiveCheck: never = value;\n                throw new Error(`Unsupported chunk type: ${_exhaustiveCheck}`);\n              }\n            }\n          },\n        }),\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings,\n    };\n  }\n}\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst anthropicMessagesResponseSchema = z.object({\n  type: z.literal('message'),\n  content: z.array(\n    z.discriminatedUnion('type', [\n      z.object({\n        type: z.literal('text'),\n        text: z.string(),\n      }),\n      z.object({\n        type: z.literal('tool_use'),\n        id: z.string(),\n        name: z.string(),\n        input: z.unknown(),\n      }),\n    ]),\n  ),\n  stop_reason: z.string().optional().nullable(),\n  usage: z.object({\n    input_tokens: z.number(),\n    output_tokens: z.number(),\n  }),\n});\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst anthropicMessagesChunkSchema = z.discriminatedUnion('type', [\n  z.object({\n    type: z.literal('message_start'),\n    message: z.object({\n      usage: z.object({\n        input_tokens: z.number(),\n        output_tokens: z.number(),\n      }),\n    }),\n  }),\n  z.object({\n    type: z.literal('content_block_start'),\n    index: z.number(),\n    content_block: z.discriminatedUnion('type', [\n      z.object({\n        type: z.literal('text'),\n        text: z.string(),\n      }),\n      z.object({\n        type: z.literal('tool_use'),\n        id: z.string(),\n        name: z.string(),\n      }),\n    ]),\n  }),\n  z.object({\n    type: z.literal('content_block_delta'),\n    index: z.number(),\n    delta: z.discriminatedUnion('type', [\n      z.object({\n        type: z.literal('input_json_delta'),\n        partial_json: z.string(),\n      }),\n      z.object({\n        type: z.literal('text_delta'),\n        text: z.string(),\n      }),\n    ]),\n  }),\n  z.object({\n    type: z.literal('content_block_stop'),\n    index: z.number(),\n  }),\n  z.object({\n    type: z.literal('error'),\n    error: z.object({\n      type: z.string(),\n      message: z.string(),\n    }),\n  }),\n  z.object({\n    type: z.literal('message_delta'),\n    delta: z.object({ stop_reason: z.string().optional().nullable() }),\n    usage: z.object({ output_tokens: z.number() }),\n  }),\n  z.object({\n    type: z.literal('message_stop'),\n  }),\n  z.object({\n    type: z.literal('ping'),\n  }),\n]);\n\nfunction prepareToolsAndToolChoice(\n  mode: Parameters<LanguageModelV1['doGenerate']>[0]['mode'] & {\n    type: 'regular';\n  },\n) {\n  // when the tools array is empty, change it to undefined to prevent errors:\n  const tools = mode.tools?.length ? mode.tools : undefined;\n\n  if (tools == null) {\n    return { tools: undefined, tool_choice: undefined };\n  }\n\n  const mappedTools = tools.map(tool => ({\n    name: tool.name,\n    description: tool.description,\n    input_schema: tool.parameters,\n  }));\n\n  const toolChoice = mode.toolChoice;\n\n  if (toolChoice == null) {\n    return { tools: mappedTools, tool_choice: undefined };\n  }\n\n  const type = toolChoice.type;\n\n  switch (type) {\n    case 'auto':\n      return { tools: mappedTools, tool_choice: { type: 'auto' } };\n    case 'required':\n      return { tools: mappedTools, tool_choice: { type: 'any' } };\n    case 'none':\n      // Anthropic does not support 'none' tool choice, so we remove the tools:\n      return { tools: undefined, tool_choice: undefined };\n    case 'tool':\n      return {\n        tools: mappedTools,\n        tool_choice: { type: 'tool', name: toolChoice.toolName },\n      };\n    default: {\n      const _exhaustiveCheck: never = type;\n      throw new Error(`Unsupported tool choice type: ${_exhaustiveCheck}`);\n    }\n  }\n}\n", "import { createJsonErrorResponseHandler } from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\n\nconst anthropicErrorDataSchema = z.object({\n  type: z.literal('error'),\n  error: z.object({\n    type: z.string(),\n    message: z.string(),\n  }),\n});\n\nexport type AnthropicErrorData = z.infer<typeof anthropicErrorDataSchema>;\n\nexport const anthropicFailedResponseHandler = createJsonErrorResponseHandler({\n  errorSchema: anthropicErrorDataSchema,\n  errorToMessage: data => data.error.message,\n});\n", "import {\n  LanguageModelV1Message,\n  LanguageModelV1Prompt,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport { convertUint8ArrayToBase64 } from '@ai-sdk/provider-utils';\nimport {\n  AnthropicAssistantMessage,\n  AnthropicMessage,\n  AnthropicMessagesPrompt,\n  AnthropicUserMessage,\n} from './anthropic-messages-prompt';\n\nexport function convertToAnthropicMessagesPrompt(\n  prompt: LanguageModelV1Prompt,\n): AnthropicMessagesPrompt {\n  const blocks = groupIntoBlocks(prompt);\n\n  let system: string | undefined = undefined;\n  const messages: AnthropicMessage[] = [];\n\n  for (let i = 0; i < blocks.length; i++) {\n    const block = blocks[i];\n    const type = block.type;\n\n    switch (type) {\n      case 'system': {\n        if (system != null) {\n          throw new UnsupportedFunctionalityError({\n            functionality:\n              'Multiple system messages that are separated by user/assistant messages',\n          });\n        }\n\n        system = block.messages.map(({ content }) => content).join('\\n');\n        break;\n      }\n\n      case 'user': {\n        // combines all user and tool messages in this block into a single message:\n        const anthropicContent: AnthropicUserMessage['content'] = [];\n\n        for (const { role, content } of block.messages) {\n          switch (role) {\n            case 'user': {\n              for (const part of content) {\n                switch (part.type) {\n                  case 'text': {\n                    anthropicContent.push({ type: 'text', text: part.text });\n                    break;\n                  }\n                  case 'image': {\n                    if (part.image instanceof URL) {\n                      // The AI SDK automatically downloads images for user image parts with URLs\n                      throw new UnsupportedFunctionalityError({\n                        functionality: 'Image URLs in user messages',\n                      });\n                    }\n\n                    anthropicContent.push({\n                      type: 'image',\n                      source: {\n                        type: 'base64',\n                        media_type: part.mimeType ?? 'image/jpeg',\n                        data: convertUint8ArrayToBase64(part.image),\n                      },\n                    });\n\n                    break;\n                  }\n                }\n              }\n\n              break;\n            }\n            case 'tool': {\n              for (const part of content) {\n                anthropicContent.push({\n                  type: 'tool_result',\n                  tool_use_id: part.toolCallId,\n                  content: JSON.stringify(part.result),\n                  is_error: part.isError,\n                });\n              }\n\n              break;\n            }\n            default: {\n              const _exhaustiveCheck: never = role;\n              throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n            }\n          }\n        }\n\n        messages.push({ role: 'user', content: anthropicContent });\n\n        break;\n      }\n\n      case 'assistant': {\n        // combines multiple assistant messages in this block into a single message:\n        const anthropicContent: AnthropicAssistantMessage['content'] = [];\n\n        for (const { content } of block.messages) {\n          for (let j = 0; j < content.length; j++) {\n            const part = content[j];\n            switch (part.type) {\n              case 'text': {\n                anthropicContent.push({\n                  type: 'text',\n                  text:\n                    // trim the last text part if it's the last message in the block\n                    // because Anthropic does not allow trailing whitespace\n                    // in pre-filled assistant responses\n                    i === blocks.length - 1 && j === block.messages.length - 1\n                      ? part.text.trim()\n                      : part.text,\n                });\n                break;\n              }\n\n              case 'tool-call': {\n                anthropicContent.push({\n                  type: 'tool_use',\n                  id: part.toolCallId,\n                  name: part.toolName,\n                  input: part.args,\n                });\n                break;\n              }\n            }\n          }\n        }\n\n        messages.push({ role: 'assistant', content: anthropicContent });\n\n        break;\n      }\n\n      default: {\n        const _exhaustiveCheck: never = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  return {\n    system,\n    messages,\n  };\n}\n\ntype SystemBlock = {\n  type: 'system';\n  messages: Array<LanguageModelV1Message & { role: 'system' }>;\n};\ntype AssistantBlock = {\n  type: 'assistant';\n  messages: Array<LanguageModelV1Message & { role: 'assistant' }>;\n};\ntype UserBlock = {\n  type: 'user';\n  messages: Array<LanguageModelV1Message & { role: 'user' | 'tool' }>;\n};\n\nfunction groupIntoBlocks(\n  prompt: LanguageModelV1Prompt,\n): Array<SystemBlock | AssistantBlock | UserBlock> {\n  const blocks: Array<SystemBlock | AssistantBlock | UserBlock> = [];\n  let currentBlock: SystemBlock | AssistantBlock | UserBlock | undefined =\n    undefined;\n\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case 'system': {\n        if (currentBlock?.type !== 'system') {\n          currentBlock = { type: 'system', messages: [] };\n          blocks.push(currentBlock);\n        }\n\n        currentBlock.messages.push({ role, content });\n        break;\n      }\n      case 'assistant': {\n        if (currentBlock?.type !== 'assistant') {\n          currentBlock = { type: 'assistant', messages: [] };\n          blocks.push(currentBlock);\n        }\n\n        currentBlock.messages.push({ role, content });\n        break;\n      }\n      case 'user': {\n        if (currentBlock?.type !== 'user') {\n          currentBlock = { type: 'user', messages: [] };\n          blocks.push(currentBlock);\n        }\n\n        currentBlock.messages.push({ role, content });\n        break;\n      }\n      case 'tool': {\n        if (currentBlock?.type !== 'user') {\n          currentBlock = { type: 'user', messages: [] };\n          blocks.push(currentBlock);\n        }\n\n        currentBlock.messages.push({ role, content });\n        break;\n      }\n      default: {\n        const _exhaustiveCheck: never = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  return blocks;\n}\n", "import { LanguageModelV1FinishReason } from '@ai-sdk/provider';\n\nexport function mapAnthropicStopReason(\n  finishReason: string | null | undefined,\n): LanguageModelV1FinishReason {\n  switch (finishReason) {\n    case 'end_turn':\n    case 'stop_sequence':\n      return 'stop';\n    case 'tool_use':\n      return 'tool-calls';\n    case 'max_tokens':\n      return 'length';\n    default:\n      return 'other';\n  }\n}\n", "import { loadApi<PERSON><PERSON>, withoutTrailingSlash } from '@ai-sdk/provider-utils';\nimport { AnthropicMessagesLanguageModel } from './anthropic-messages-language-model';\nimport {\n  AnthropicMessagesModelId,\n  AnthropicMessagesSettings,\n} from './anthropic-messages-settings';\n\nexport interface AnthropicProvider {\n  /**\nCreates a model for text generation.\n*/\n  (\n    modelId: AnthropicMessagesModelId,\n    settings?: AnthropicMessagesSettings,\n  ): AnthropicMessagesLanguageModel;\n\n  /**\nCreates a model for text generation.\n*/\n  languageModel(\n    modelId: AnthropicMessagesModelId,\n    settings?: AnthropicMessagesSettings,\n  ): AnthropicMessagesLanguageModel;\n\n  /**\nCreates a model for text generation.\n*/\n  chat(\n    modelId: AnthropicMessagesModelId,\n    settings?: AnthropicMessagesSettings,\n  ): AnthropicMessagesLanguageModel;\n\n  /**\n   * @deprecated Use `chat()` instead.\n   */\n  messages(\n    modelId: AnthropicMessagesModelId,\n    settings?: AnthropicMessagesSettings,\n  ): AnthropicMessagesLanguageModel;\n}\n\nexport interface AnthropicProviderSettings {\n  /**\nUse a different URL prefix for API calls, e.g. to use proxy servers.\nThe default prefix is `https://api.anthropic.com/v1`.\n   */\n  baseURL?: string;\n\n  /**\n@deprecated Use `baseURL` instead.\n   */\n  baseUrl?: string;\n\n  /**\nAPI key that is being send using the `x-api-key` header.\nIt defaults to the `ANTHROPIC_API_KEY` environment variable.\n   */\n  apiKey?: string;\n\n  /**\nCustom headers to include in the requests.\n     */\n  headers?: Record<string, string>;\n\n  /**\nCustom fetch implementation. You can use it as a middleware to intercept requests,\nor to provide a custom fetch implementation for e.g. testing.\n    */\n  fetch?: typeof fetch;\n\n  generateId?: () => string;\n}\n\n/**\nCreate an Anthropic provider instance.\n */\nexport function createAnthropic(\n  options: AnthropicProviderSettings = {},\n): AnthropicProvider {\n  const baseURL =\n    withoutTrailingSlash(options.baseURL ?? options.baseUrl) ??\n    'https://api.anthropic.com/v1';\n\n  const getHeaders = () => ({\n    'anthropic-version': '2023-06-01',\n    'x-api-key': loadApiKey({\n      apiKey: options.apiKey,\n      environmentVariableName: 'ANTHROPIC_API_KEY',\n      description: 'Anthropic',\n    }),\n    ...options.headers,\n  });\n\n  const createChatModel = (\n    modelId: AnthropicMessagesModelId,\n    settings: AnthropicMessagesSettings = {},\n  ) =>\n    new AnthropicMessagesLanguageModel(modelId, settings, {\n      provider: 'anthropic.messages',\n      baseURL,\n      headers: getHeaders,\n      fetch: options.fetch,\n    });\n\n  const provider = function (\n    modelId: AnthropicMessagesModelId,\n    settings?: AnthropicMessagesSettings,\n  ) {\n    if (new.target) {\n      throw new Error(\n        'The Anthropic model function cannot be called with the new keyword.',\n      );\n    }\n\n    return createChatModel(modelId, settings);\n  };\n\n  provider.languageModel = createChatModel;\n  provider.chat = createChatModel;\n  provider.messages = createChatModel;\n\n  return provider as AnthropicProvider;\n}\n\n/**\nDefault Anthropic provider instance.\n */\nexport const anthropic = createAnthropic();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAAAA,yBAAiD;;;ACAjD,IAAAC,mBAOO;AACP,IAAAC,yBAMO;AACP,IAAAC,cAAkB;;;ACflB,4BAA+C;AAC/C,iBAAkB;AAElB,IAAM,2BAA2B,aAAE,OAAO;AAAA,EACxC,MAAM,aAAE,QAAQ,OAAO;AAAA,EACvB,OAAO,aAAE,OAAO;AAAA,IACd,MAAM,aAAE,OAAO;AAAA,IACf,SAAS,aAAE,OAAO;AAAA,EACpB,CAAC;AACH,CAAC;AAIM,IAAM,qCAAiC,sDAA+B;AAAA,EAC3E,aAAa;AAAA,EACb,gBAAgB,UAAQ,KAAK,MAAM;AACrC,CAAC;;;AChBD,sBAIO;AACP,IAAAC,yBAA0C;AAQnC,SAAS,iCACd,QACyB;AAf3B;AAgBE,QAAM,SAAS,gBAAgB,MAAM;AAErC,MAAI,SAA6B;AACjC,QAAM,WAA+B,CAAC;AAEtC,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAM,QAAQ,OAAO,CAAC;AACtB,UAAM,OAAO,MAAM;AAEnB,YAAQ,MAAM;AAAA,MACZ,KAAK,UAAU;AACb,YAAI,UAAU,MAAM;AAClB,gBAAM,IAAI,8CAA8B;AAAA,YACtC,eACE;AAAA,UACJ,CAAC;AAAA,QACH;AAEA,iBAAS,MAAM,SAAS,IAAI,CAAC,EAAE,QAAQ,MAAM,OAAO,EAAE,KAAK,IAAI;AAC/D;AAAA,MACF;AAAA,MAEA,KAAK,QAAQ;AAEX,cAAM,mBAAoD,CAAC;AAE3D,mBAAW,EAAE,MAAM,QAAQ,KAAK,MAAM,UAAU;AAC9C,kBAAQ,MAAM;AAAA,YACZ,KAAK,QAAQ;AACX,yBAAW,QAAQ,SAAS;AAC1B,wBAAQ,KAAK,MAAM;AAAA,kBACjB,KAAK,QAAQ;AACX,qCAAiB,KAAK,EAAE,MAAM,QAAQ,MAAM,KAAK,KAAK,CAAC;AACvD;AAAA,kBACF;AAAA,kBACA,KAAK,SAAS;AACZ,wBAAI,KAAK,iBAAiB,KAAK;AAE7B,4BAAM,IAAI,8CAA8B;AAAA,wBACtC,eAAe;AAAA,sBACjB,CAAC;AAAA,oBACH;AAEA,qCAAiB,KAAK;AAAA,sBACpB,MAAM;AAAA,sBACN,QAAQ;AAAA,wBACN,MAAM;AAAA,wBACN,aAAY,UAAK,aAAL,YAAiB;AAAA,wBAC7B,UAAM,kDAA0B,KAAK,KAAK;AAAA,sBAC5C;AAAA,oBACF,CAAC;AAED;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAEA;AAAA,YACF;AAAA,YACA,KAAK,QAAQ;AACX,yBAAW,QAAQ,SAAS;AAC1B,iCAAiB,KAAK;AAAA,kBACpB,MAAM;AAAA,kBACN,aAAa,KAAK;AAAA,kBAClB,SAAS,KAAK,UAAU,KAAK,MAAM;AAAA,kBACnC,UAAU,KAAK;AAAA,gBACjB,CAAC;AAAA,cACH;AAEA;AAAA,YACF;AAAA,YACA,SAAS;AACP,oBAAM,mBAA0B;AAChC,oBAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,YACzD;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,KAAK,EAAE,MAAM,QAAQ,SAAS,iBAAiB,CAAC;AAEzD;AAAA,MACF;AAAA,MAEA,KAAK,aAAa;AAEhB,cAAM,mBAAyD,CAAC;AAEhE,mBAAW,EAAE,QAAQ,KAAK,MAAM,UAAU;AACxC,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,kBAAM,OAAO,QAAQ,CAAC;AACtB,oBAAQ,KAAK,MAAM;AAAA,cACjB,KAAK,QAAQ;AACX,iCAAiB,KAAK;AAAA,kBACpB,MAAM;AAAA,kBACN;AAAA;AAAA;AAAA;AAAA,oBAIE,MAAM,OAAO,SAAS,KAAK,MAAM,MAAM,SAAS,SAAS,IACrD,KAAK,KAAK,KAAK,IACf,KAAK;AAAA;AAAA,gBACb,CAAC;AACD;AAAA,cACF;AAAA,cAEA,KAAK,aAAa;AAChB,iCAAiB,KAAK;AAAA,kBACpB,MAAM;AAAA,kBACN,IAAI,KAAK;AAAA,kBACT,MAAM,KAAK;AAAA,kBACX,OAAO,KAAK;AAAA,gBACd,CAAC;AACD;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,KAAK,EAAE,MAAM,aAAa,SAAS,iBAAiB,CAAC;AAE9D;AAAA,MACF;AAAA,MAEA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAeA,SAAS,gBACP,QACiD;AACjD,QAAM,SAA0D,CAAC;AACjE,MAAI,eACF;AAEF,aAAW,EAAE,MAAM,QAAQ,KAAK,QAAQ;AACtC,YAAQ,MAAM;AAAA,MACZ,KAAK,UAAU;AACb,aAAI,6CAAc,UAAS,UAAU;AACnC,yBAAe,EAAE,MAAM,UAAU,UAAU,CAAC,EAAE;AAC9C,iBAAO,KAAK,YAAY;AAAA,QAC1B;AAEA,qBAAa,SAAS,KAAK,EAAE,MAAM,QAAQ,CAAC;AAC5C;AAAA,MACF;AAAA,MACA,KAAK,aAAa;AAChB,aAAI,6CAAc,UAAS,aAAa;AACtC,yBAAe,EAAE,MAAM,aAAa,UAAU,CAAC,EAAE;AACjD,iBAAO,KAAK,YAAY;AAAA,QAC1B;AAEA,qBAAa,SAAS,KAAK,EAAE,MAAM,QAAQ,CAAC;AAC5C;AAAA,MACF;AAAA,MACA,KAAK,QAAQ;AACX,aAAI,6CAAc,UAAS,QAAQ;AACjC,yBAAe,EAAE,MAAM,QAAQ,UAAU,CAAC,EAAE;AAC5C,iBAAO,KAAK,YAAY;AAAA,QAC1B;AAEA,qBAAa,SAAS,KAAK,EAAE,MAAM,QAAQ,CAAC;AAC5C;AAAA,MACF;AAAA,MACA,KAAK,QAAQ;AACX,aAAI,6CAAc,UAAS,QAAQ;AACjC,yBAAe,EAAE,MAAM,QAAQ,UAAU,CAAC,EAAE;AAC5C,iBAAO,KAAK,YAAY;AAAA,QAC1B;AAEA,qBAAa,SAAS,KAAK,EAAE,MAAM,QAAQ,CAAC;AAC5C;AAAA,MACF;AAAA,MACA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;ACxNO,SAAS,uBACd,cAC6B;AAC7B,UAAQ,cAAc;AAAA,IACpB,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;;;AHeO,IAAM,iCAAN,MAAgE;AAAA,EAUrE,YACE,SACA,UACA,QACA;AAbF,SAAS,uBAAuB;AAChC,SAAS,8BAA8B;AACvC,SAAS,oBAAoB;AAY3B,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,SAAS;AAAA,EAChB;AAAA,EAEA,IAAI,WAAmB;AACrB,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EAEA,MAAc,QAAQ;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAiD;AAC/C,UAAM,OAAO,KAAK;AAElB,UAAM,WAAyC,CAAC;AAEhD,QAAI,oBAAoB,MAAM;AAC5B,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,QAAI,mBAAmB,MAAM;AAC3B,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,QAAI,QAAQ,MAAM;AAChB,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,QAAI,kBAAkB,QAAQ,eAAe,SAAS,QAAQ;AAC5D,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,UAAM,iBAAiB,iCAAiC,MAAM;AAE9D,UAAM,WAAW;AAAA;AAAA,MAEf,OAAO,KAAK;AAAA;AAAA,MAGZ,OAAO,sBAAQ,KAAK,SAAS;AAAA;AAAA,MAG7B,YAAY,gCAAa;AAAA;AAAA,MACzB;AAAA,MACA,OAAO;AAAA,MACP,gBAAgB;AAAA;AAAA,MAGhB,QAAQ,eAAe;AAAA,MACvB,UAAU,eAAe;AAAA,IAC3B;AAEA,YAAQ,MAAM;AAAA,MACZ,KAAK,WAAW;AACd,eAAO;AAAA,UACL,MAAM,EAAE,GAAG,UAAU,GAAG,0BAA0B,IAAI,EAAE;AAAA,UACxD;AAAA,QACF;AAAA,MACF;AAAA,MAEA,KAAK,eAAe;AAClB,cAAM,IAAI,+CAA8B;AAAA,UACtC,eAAe;AAAA,QACjB,CAAC;AAAA,MACH;AAAA,MAEA,KAAK,eAAe;AAClB,cAAM,EAAE,MAAM,aAAa,WAAW,IAAI,KAAK;AAE/C,eAAO;AAAA,UACL,MAAM;AAAA,YACJ,GAAG;AAAA,YACH,OAAO,CAAC,EAAE,MAAM,aAAa,cAAc,WAAW,CAAC;AAAA,YACvD,aAAa,EAAE,MAAM,QAAQ,KAAK;AAAA,UACpC;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,MAEA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,WACJ,SAC6D;AAC7D,UAAM,EAAE,MAAM,SAAS,IAAI,MAAM,KAAK,QAAQ,OAAO;AAErD,UAAM,EAAE,iBAAiB,OAAO,SAAS,IAAI,UAAM,sCAAc;AAAA,MAC/D,KAAK,GAAG,KAAK,OAAO,OAAO;AAAA,MAC3B,aAAS,uCAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;AAAA,MAC9D,MAAM;AAAA,MACN,uBAAuB;AAAA,MACvB,+BAA2B;AAAA,QACzB;AAAA,MACF;AAAA,MACA,aAAa,QAAQ;AAAA,MACrB,OAAO,KAAK,OAAO;AAAA,IACrB,CAAC;AAED,UAAM,EAAE,UAAU,WAAW,GAAG,YAAY,IAAI;AAGhD,QAAI,OAAO;AACX,eAAW,WAAW,SAAS,SAAS;AACtC,UAAI,QAAQ,SAAS,QAAQ;AAC3B,gBAAQ,QAAQ;AAAA,MAClB;AAAA,IACF;AAGA,QAAI,YAA2D;AAC/D,QAAI,SAAS,QAAQ,KAAK,aAAW,QAAQ,SAAS,UAAU,GAAG;AACjE,kBAAY,CAAC;AACb,iBAAW,WAAW,SAAS,SAAS;AACtC,YAAI,QAAQ,SAAS,YAAY;AAC/B,oBAAU,KAAK;AAAA,YACb,cAAc;AAAA,YACd,YAAY,QAAQ;AAAA,YACpB,UAAU,QAAQ;AAAA,YAClB,MAAM,KAAK,UAAU,QAAQ,KAAK;AAAA,UACpC,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,cAAc,uBAAuB,SAAS,WAAW;AAAA,MACzD,OAAO;AAAA,QACL,cAAc,SAAS,MAAM;AAAA,QAC7B,kBAAkB,SAAS,MAAM;AAAA,MACnC;AAAA,MACA,SAAS,EAAE,WAAW,YAAY;AAAA,MAClC,aAAa,EAAE,SAAS,gBAAgB;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,SACJ,SAC2D;AAC3D,UAAM,EAAE,MAAM,SAAS,IAAI,MAAM,KAAK,QAAQ,OAAO;AAErD,UAAM,EAAE,iBAAiB,OAAO,SAAS,IAAI,UAAM,sCAAc;AAAA,MAC/D,KAAK,GAAG,KAAK,OAAO,OAAO;AAAA,MAC3B,aAAS,uCAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;AAAA,MAC9D,MAAM;AAAA,QACJ,GAAG;AAAA,QACH,QAAQ;AAAA,MACV;AAAA,MACA,uBAAuB;AAAA,MACvB,+BAA2B;AAAA,QACzB;AAAA,MACF;AAAA,MACA,aAAa,QAAQ;AAAA,MACrB,OAAO,KAAK,OAAO;AAAA,IACrB,CAAC;AAED,UAAM,EAAE,UAAU,WAAW,GAAG,YAAY,IAAI;AAEhD,QAAI,eAA4C;AAChD,UAAM,QAA4D;AAAA,MAChE,cAAc,OAAO;AAAA,MACrB,kBAAkB,OAAO;AAAA,IAC3B;AAEA,UAAM,wBAOF,CAAC;AAEL,WAAO;AAAA,MACL,QAAQ,SAAS;AAAA,QACf,IAAI,gBAGF;AAAA,UACA,UAAU,OAAO,YAAY;AAC3B,gBAAI,CAAC,MAAM,SAAS;AAClB,yBAAW,QAAQ,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,CAAC;AACxD;AAAA,YACF;AAEA,kBAAM,QAAQ,MAAM;AAEpB,oBAAQ,MAAM,MAAM;AAAA,cAClB,KAAK,QAAQ;AACX;AAAA,cACF;AAAA,cAEA,KAAK,uBAAuB;AAC1B,sBAAM,mBAAmB,MAAM,cAAc;AAE7C,wBAAQ,kBAAkB;AAAA,kBACxB,KAAK,QAAQ;AACX;AAAA,kBACF;AAAA,kBAEA,KAAK,YAAY;AACf,0CAAsB,MAAM,KAAK,IAAI;AAAA,sBACnC,YAAY,MAAM,cAAc;AAAA,sBAChC,UAAU,MAAM,cAAc;AAAA,sBAC9B,UAAU;AAAA,oBACZ;AACA;AAAA,kBACF;AAAA,kBAEA,SAAS;AACP,0BAAM,mBAA0B;AAChC,0BAAM,IAAI;AAAA,sBACR,mCAAmC,gBAAgB;AAAA,oBACrD;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,cAEA,KAAK,sBAAsB;AAEzB,oBAAI,sBAAsB,MAAM,KAAK,KAAK,MAAM;AAC9C,wBAAM,eAAe,sBAAsB,MAAM,KAAK;AAEtD,6BAAW,QAAQ;AAAA,oBACjB,MAAM;AAAA,oBACN,cAAc;AAAA,oBACd,YAAY,aAAa;AAAA,oBACzB,UAAU,aAAa;AAAA,oBACvB,MAAM,aAAa;AAAA,kBACrB,CAAC;AAED,yBAAO,sBAAsB,MAAM,KAAK;AAAA,gBAC1C;AAEA;AAAA,cACF;AAAA,cAEA,KAAK,uBAAuB;AAC1B,sBAAM,YAAY,MAAM,MAAM;AAC9B,wBAAQ,WAAW;AAAA,kBACjB,KAAK,cAAc;AACjB,+BAAW,QAAQ;AAAA,sBACjB,MAAM;AAAA,sBACN,WAAW,MAAM,MAAM;AAAA,oBACzB,CAAC;AAED;AAAA,kBACF;AAAA,kBAEA,KAAK,oBAAoB;AACvB,0BAAM,eAAe,sBAAsB,MAAM,KAAK;AAEtD,+BAAW,QAAQ;AAAA,sBACjB,MAAM;AAAA,sBACN,cAAc;AAAA,sBACd,YAAY,aAAa;AAAA,sBACzB,UAAU,aAAa;AAAA,sBACvB,eAAe,MAAM,MAAM;AAAA,oBAC7B,CAAC;AAED,iCAAa,YAAY,MAAM,MAAM;AAErC;AAAA,kBACF;AAAA,kBAEA,SAAS;AACP,0BAAM,mBAA0B;AAChC,0BAAM,IAAI;AAAA,sBACR,2BAA2B,gBAAgB;AAAA,oBAC7C;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,cAEA,KAAK,iBAAiB;AACpB,sBAAM,eAAe,MAAM,QAAQ,MAAM;AACzC,sBAAM,mBAAmB,MAAM,QAAQ,MAAM;AAC7C;AAAA,cACF;AAAA,cAEA,KAAK,iBAAiB;AACpB,sBAAM,mBAAmB,MAAM,MAAM;AACrC,+BAAe,uBAAuB,MAAM,MAAM,WAAW;AAC7D;AAAA,cACF;AAAA,cAEA,KAAK,gBAAgB;AACnB,2BAAW,QAAQ,EAAE,MAAM,UAAU,cAAc,MAAM,CAAC;AAC1D;AAAA,cACF;AAAA,cAEA,KAAK,SAAS;AACZ,2BAAW,QAAQ,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,CAAC;AACxD;AAAA,cACF;AAAA,cAEA,SAAS;AACP,sBAAM,mBAA0B;AAChC,sBAAM,IAAI,MAAM,2BAA2B,gBAAgB,EAAE;AAAA,cAC/D;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,SAAS,EAAE,WAAW,YAAY;AAAA,MAClC,aAAa,EAAE,SAAS,gBAAgB;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AACF;AAIA,IAAM,kCAAkC,cAAE,OAAO;AAAA,EAC/C,MAAM,cAAE,QAAQ,SAAS;AAAA,EACzB,SAAS,cAAE;AAAA,IACT,cAAE,mBAAmB,QAAQ;AAAA,MAC3B,cAAE,OAAO;AAAA,QACP,MAAM,cAAE,QAAQ,MAAM;AAAA,QACtB,MAAM,cAAE,OAAO;AAAA,MACjB,CAAC;AAAA,MACD,cAAE,OAAO;AAAA,QACP,MAAM,cAAE,QAAQ,UAAU;AAAA,QAC1B,IAAI,cAAE,OAAO;AAAA,QACb,MAAM,cAAE,OAAO;AAAA,QACf,OAAO,cAAE,QAAQ;AAAA,MACnB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,aAAa,cAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAAA,EAC5C,OAAO,cAAE,OAAO;AAAA,IACd,cAAc,cAAE,OAAO;AAAA,IACvB,eAAe,cAAE,OAAO;AAAA,EAC1B,CAAC;AACH,CAAC;AAID,IAAM,+BAA+B,cAAE,mBAAmB,QAAQ;AAAA,EAChE,cAAE,OAAO;AAAA,IACP,MAAM,cAAE,QAAQ,eAAe;AAAA,IAC/B,SAAS,cAAE,OAAO;AAAA,MAChB,OAAO,cAAE,OAAO;AAAA,QACd,cAAc,cAAE,OAAO;AAAA,QACvB,eAAe,cAAE,OAAO;AAAA,MAC1B,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACP,MAAM,cAAE,QAAQ,qBAAqB;AAAA,IACrC,OAAO,cAAE,OAAO;AAAA,IAChB,eAAe,cAAE,mBAAmB,QAAQ;AAAA,MAC1C,cAAE,OAAO;AAAA,QACP,MAAM,cAAE,QAAQ,MAAM;AAAA,QACtB,MAAM,cAAE,OAAO;AAAA,MACjB,CAAC;AAAA,MACD,cAAE,OAAO;AAAA,QACP,MAAM,cAAE,QAAQ,UAAU;AAAA,QAC1B,IAAI,cAAE,OAAO;AAAA,QACb,MAAM,cAAE,OAAO;AAAA,MACjB,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACP,MAAM,cAAE,QAAQ,qBAAqB;AAAA,IACrC,OAAO,cAAE,OAAO;AAAA,IAChB,OAAO,cAAE,mBAAmB,QAAQ;AAAA,MAClC,cAAE,OAAO;AAAA,QACP,MAAM,cAAE,QAAQ,kBAAkB;AAAA,QAClC,cAAc,cAAE,OAAO;AAAA,MACzB,CAAC;AAAA,MACD,cAAE,OAAO;AAAA,QACP,MAAM,cAAE,QAAQ,YAAY;AAAA,QAC5B,MAAM,cAAE,OAAO;AAAA,MACjB,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACP,MAAM,cAAE,QAAQ,oBAAoB;AAAA,IACpC,OAAO,cAAE,OAAO;AAAA,EAClB,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACP,MAAM,cAAE,QAAQ,OAAO;AAAA,IACvB,OAAO,cAAE,OAAO;AAAA,MACd,MAAM,cAAE,OAAO;AAAA,MACf,SAAS,cAAE,OAAO;AAAA,IACpB,CAAC;AAAA,EACH,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACP,MAAM,cAAE,QAAQ,eAAe;AAAA,IAC/B,OAAO,cAAE,OAAO,EAAE,aAAa,cAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC;AAAA,IACjE,OAAO,cAAE,OAAO,EAAE,eAAe,cAAE,OAAO,EAAE,CAAC;AAAA,EAC/C,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACP,MAAM,cAAE,QAAQ,cAAc;AAAA,EAChC,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACP,MAAM,cAAE,QAAQ,MAAM;AAAA,EACxB,CAAC;AACH,CAAC;AAED,SAAS,0BACP,MAGA;AAjeF;AAmeE,QAAM,UAAQ,UAAK,UAAL,mBAAY,UAAS,KAAK,QAAQ;AAEhD,MAAI,SAAS,MAAM;AACjB,WAAO,EAAE,OAAO,QAAW,aAAa,OAAU;AAAA,EACpD;AAEA,QAAM,cAAc,MAAM,IAAI,WAAS;AAAA,IACrC,MAAM,KAAK;AAAA,IACX,aAAa,KAAK;AAAA,IAClB,cAAc,KAAK;AAAA,EACrB,EAAE;AAEF,QAAM,aAAa,KAAK;AAExB,MAAI,cAAc,MAAM;AACtB,WAAO,EAAE,OAAO,aAAa,aAAa,OAAU;AAAA,EACtD;AAEA,QAAM,OAAO,WAAW;AAExB,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO,EAAE,OAAO,aAAa,aAAa,EAAE,MAAM,OAAO,EAAE;AAAA,IAC7D,KAAK;AACH,aAAO,EAAE,OAAO,aAAa,aAAa,EAAE,MAAM,MAAM,EAAE;AAAA,IAC5D,KAAK;AAEH,aAAO,EAAE,OAAO,QAAW,aAAa,OAAU;AAAA,IACpD,KAAK;AACH,aAAO;AAAA,QACL,OAAO;AAAA,QACP,aAAa,EAAE,MAAM,QAAQ,MAAM,WAAW,SAAS;AAAA,MACzD;AAAA,IACF,SAAS;AACP,YAAM,mBAA0B;AAChC,YAAM,IAAI,MAAM,iCAAiC,gBAAgB,EAAE;AAAA,IACrE;AAAA,EACF;AACF;;;AD9fO,IAAM,YAAN,MAAgB;AAAA;AAAA;AAAA;AAAA,EAarB,YAAY,UAAqC,CAAC,GAAG;AAxBvD;AAyBI,SAAK,WACH,uDAAqB,aAAQ,YAAR,YAAmB,QAAQ,OAAO,MAAvD,YACA;AACF,SAAK,SAAS,QAAQ;AACtB,SAAK,UAAU,QAAQ;AAAA,EACzB;AAAA,EAEA,IAAY,aAAa;AACvB,WAAO;AAAA,MACL,SAAS,KAAK;AAAA,MACd,SAAS,OAAO;AAAA,QACd,qBAAqB;AAAA,QACrB,kBAAkB;AAAA,QAClB,iBAAa,mCAAW;AAAA,UACtB,QAAQ,KAAK;AAAA,UACb,yBAAyB;AAAA,UACzB,aAAa;AAAA,QACf,CAAC;AAAA,QACD,GAAG,KAAK;AAAA,MACV;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,SACE,SACA,WAAsC,CAAC,GACvC;AACA,WAAO,KAAK,KAAK,SAAS,QAAQ;AAAA,EACpC;AAAA,EAEA,KACE,SACA,WAAsC,CAAC,GACvC;AACA,WAAO,IAAI,+BAA+B,SAAS,UAAU;AAAA,MAC3D,UAAU;AAAA,MACV,GAAG,KAAK;AAAA,IACV,CAAC;AAAA,EACH;AACF;;;AKnEA,IAAAC,yBAAiD;AA4E1C,SAAS,gBACd,UAAqC,CAAC,GACnB;AA9ErB;AA+EE,QAAM,WACJ,uDAAqB,aAAQ,YAAR,YAAmB,QAAQ,OAAO,MAAvD,YACA;AAEF,QAAM,aAAa,OAAO;AAAA,IACxB,qBAAqB;AAAA,IACrB,iBAAa,mCAAW;AAAA,MACtB,QAAQ,QAAQ;AAAA,MAChB,yBAAyB;AAAA,MACzB,aAAa;AAAA,IACf,CAAC;AAAA,IACD,GAAG,QAAQ;AAAA,EACb;AAEA,QAAM,kBAAkB,CACtB,SACA,WAAsC,CAAC,MAEvC,IAAI,+BAA+B,SAAS,UAAU;AAAA,IACpD,UAAU;AAAA,IACV;AAAA,IACA,SAAS;AAAA,IACT,OAAO,QAAQ;AAAA,EACjB,CAAC;AAEH,QAAM,WAAW,SACf,SACA,UACA;AACA,QAAI,YAAY;AACd,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO,gBAAgB,SAAS,QAAQ;AAAA,EAC1C;AAEA,WAAS,gBAAgB;AACzB,WAAS,OAAO;AAChB,WAAS,WAAW;AAEpB,SAAO;AACT;AAKO,IAAM,YAAY,gBAAgB;", "names": ["import_provider_utils", "import_provider", "import_provider_utils", "import_zod", "import_provider_utils", "import_provider_utils"]}