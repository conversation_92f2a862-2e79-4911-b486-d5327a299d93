## 6.3.1 (2024-11-26)

### Bug fixes

When completing a property name, insert a colon and space after the name.

## 6.3.0 (2024-09-07)

### New features

CSS autocompletion now completes `@`-keywords.

## 6.2.1 (2023-08-04)

### Bug fixes

Allow keyframe blocks to be code-folded.

## 6.2.0 (2023-04-26)

### Bug fixes

Explicitly list @lezer/common as a package dependency.

### New features

Export `defineCSSCompletionSource`, which allows one to define a CSS-style completion source for dialects with their own variable syntax.

## 6.1.1 (2023-03-08)

### Bug fixes

Provide better completions when completing directly in a `Styles` top node.

## 6.1.0 (2023-03-06)

### New features

CSS completion can now complete variable names.

## 6.0.2 (2023-01-28)

### Bug fixes

Fetch the available CSS property names in a way that works on Chrome.

## 6.0.1 (2022-10-24)

### Bug fixes

CSS completion now supports a number of additional recent and semi-standardized pseudo-class names.

## 6.0.0 (2022-06-08)

### Breaking changes

Update dependencies to 6.0.0

## 0.20.0 (2022-04-20)

### Breaking changes

Update dependencies to 0.20.0

## 0.19.3 (2021-09-24)

### Bug fixes

Use more appropriate highlighting tags for attribute names, tag names, and CSS variables.

## 0.19.2 (2021-09-22)

### New features

The package now exports a completion source function, rather than a prebuilt completion extension.

## 0.19.1 (2021-08-11)

### Bug fixes

Fix incorrect versions for @lezer dependencies.

## 0.19.0 (2021-08-11)

### Breaking changes

Update dependencies to 0.19.0

## 0.18.0 (2021-03-03)

### Breaking changes

Update dependencies to 0.18.

## 0.17.1 (2021-01-06)

### New features

The package now also exports a CommonJS module.

## 0.17.0 (2020-12-29)

### Breaking changes

First numbered release.

