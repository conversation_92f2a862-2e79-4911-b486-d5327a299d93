{"name": "@codemirror/lang-python", "version": "6.2.1", "description": "Python language support for the CodeMirror code editor", "scripts": {"test": "cm-runtests", "prepare": "cm-buildhelper src/python.ts"}, "keywords": ["editor", "code"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://marijnhaverbeke.nl"}, "type": "module", "main": "dist/index.cjs", "exports": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "types": "dist/index.d.ts", "module": "dist/index.js", "sideEffects": false, "license": "MIT", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/autocomplete": "^6.3.2", "@codemirror/language": "^6.8.0", "@lezer/common": "^1.2.1", "@lezer/python": "^1.1.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "repository": {"type": "git", "url": "https://github.com/codemirror/lang-python.git"}}